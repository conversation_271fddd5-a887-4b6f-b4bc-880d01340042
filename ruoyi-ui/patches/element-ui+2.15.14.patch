diff --git a/node_modules/element-ui/packages/date-picker/src/basic/month-table.vue b/node_modules/element-ui/packages/date-picker/src/basic/month-table.vue
index 1808307..c25e6a0 100644
--- a/node_modules/element-ui/packages/date-picker/src/basic/month-table.vue
+++ b/node_modules/element-ui/packages/date-picker/src/basic/month-table.vue
@@ -37,14 +37,6 @@
       return NaN;
     }
   };
-
-  // remove the first element that satisfies `pred` from arr
-  // return a new array if modification occurs
-  // return the original array otherwise
-  const removeFromArray = function(arr, pred) {
-    const idx = typeof pred === 'function' ? arrayFindIndex(arr, pred) : arr.indexOf(pred);
-    return idx >= 0 ? [...arr.slice(0, idx), ...arr.slice(idx + 1)] : arr;
-  };
   export default {
     props: {
       disabledDate: {},
@@ -213,13 +205,6 @@
             }
             this.rangeState.selecting = false;
           }
-        } else if (this.selectionMode === 'months') {
-          const value = this.value || [];
-          const year = this.date.getFullYear();
-          const newValue = arrayFindIndex(value, date => date.getFullYear() === year && date.getMonth() === month) >= 0
-            ? removeFromArray(value, date => date.getTime() === newDate.getTime())
-            : [...value, newDate];
-          this.$emit('pick', newValue);
         } else {
           this.$emit('pick', month);
         }
diff --git a/node_modules/element-ui/packages/date-picker/src/basic/quarter-table.vue b/node_modules/element-ui/packages/date-picker/src/basic/quarter-table.vue
new file mode 100644
index 0000000..f44fd0f
--- /dev/null
+++ b/node_modules/element-ui/packages/date-picker/src/basic/quarter-table.vue
@@ -0,0 +1,258 @@
+<template>
+  <table @click="handleQuarterTableClick" @mousemove="handleMouseMove" class="el-quarter-table">
+    <tbody>
+      <tr v-for="(row, key) in rows" :key="key">
+        <td :class="getCellStyle(cell)" v-for="(cell, key) in row" :key="key" :cellNum="quarter[cell.text]">
+          <div>
+            <a class="cell">{{ t('el.datepicker.quarter' + quarter[cell.text]) }}</a>
+          </div>
+        </td>
+      </tr>
+    </tbody>
+  </table>
+</template>
+
+<script type="text/babel">
+  import Locale from 'element-ui/src/mixins/locale';
+  import { isDate, range, getDayCountOfMonth, nextDate, getMonthNumberInQuarter } from 'element-ui/src/utils/date-util';
+  import { hasClass } from 'element-ui/src/utils/dom';
+  import { arrayFindIndex, coerceTruthyValueToArray, arrayFind } from 'element-ui/src/utils/util';
+
+  const datesInQuarter = (year, quarter) => {
+    const monthList = getMonthNumberInQuarter(quarter);
+    const numOfDays = monthList
+      .map(m => getDayCountOfMonth(year, m))
+      .reduce((prev, cur) => prev + cur, 0);
+    const firstDay = new Date(year, monthList[0], 1);
+    return range(numOfDays).map(n => nextDate(firstDay, n));
+  };
+
+  const clearDate = (date) => {
+    return new Date(date.getFullYear(), date.getMonth());
+  };
+
+  const getMonthTimestamp = function(time) {
+    if (typeof time === 'number' || typeof time === 'string') {
+      return clearDate(new Date(time)).getTime();
+    } else if (time instanceof Date) {
+      return clearDate(time).getTime();
+    } else {
+      return NaN;
+    }
+  };
+  export default {
+    props: {
+      disabledDate: {},
+      date: {},
+      value: {},
+      minDate: {},
+      maxDate: {},
+      selectionMode: {
+        default: 'quarter'
+      },
+      defaultValue: {
+        validator(val) {
+          // null or valid Date Object
+          return val === null || isDate(val) || (Array.isArray(val) && val.every(isDate));
+        }
+      },
+      rangeState: {
+        default() {
+          return {
+            endDate: null,
+            selecting: false
+          };
+        }
+      }
+    },
+
+    mixins: [Locale],
+
+    watch: {
+      'rangeState.endDate'(newVal) {
+        this.markRange(this.minDate, newVal);
+      },
+
+      minDate(newVal, oldVal) {
+        if (getMonthTimestamp(newVal) !== getMonthTimestamp(oldVal)) {
+          this.markRange(this.minDate, this.maxDate);
+        }
+      },
+
+      maxDate(newVal, oldVal) {
+        if (getMonthTimestamp(newVal) !== getMonthTimestamp(oldVal)) {
+          this.markRange(this.minDate, this.maxDate);
+        }
+      }
+    },
+
+    data() {
+      return {
+        quarter: [1, 2, 3, 4],
+        tableRows: [ [], [] ],
+        lastRow: null,
+        lastColumn: null
+      };
+    },
+
+    methods: {
+      cellMatchesDate(cell, date) {
+        const value = new Date(date);
+        return this.date.getFullYear() === value.getFullYear() && Number(cell.text * 3) === value.getMonth();
+      },
+      getCellStyle(cell) {
+        const style = {};
+        const year = this.date.getFullYear();
+        const monthList = getMonthNumberInQuarter(cell.text + 1);
+        // const month = cell.text * 3;
+        const defaultValue = this.defaultValue ? Array.isArray(this.defaultValue) ? this.defaultValue : [this.defaultValue] : [];
+        const monthMatch = month => monthList.some(item => item === month);
+        const quarter = new Date(this.date.getFullYear(), monthList[0]);
+        const today = new Date();
+        style.disabled = typeof this.disabledDate === 'function'
+          ? datesInQuarter(quarter.getFullYear(), cell.text + 1).every(this.disabledDate)
+          : false;
+        style.current = arrayFindIndex(coerceTruthyValueToArray(this.value), date => date.getFullYear() === year && monthMatch(date.getMonth())) >= 0;
+        style.today = today.getFullYear() === year && monthMatch(today.getMonth());
+        style.default = defaultValue.some(date => this.cellMatchesDate(cell, date));
+
+        if (cell.inRange) {
+          style['in-range'] = true;
+
+          if (cell.start) {
+            style['start-date'] = true;
+          }
+
+          if (cell.end) {
+            style['end-date'] = true;
+          }
+        }
+        return style;
+      },
+      getDateOfQuarter(quarter) {
+        const year = this.date.getFullYear();
+        const month = quarter * 3 - 3;
+        return new Date(year, month, 1);
+      },
+      markRange(minDate, maxDate) {
+        minDate = getMonthTimestamp(minDate);
+        maxDate = getMonthTimestamp(maxDate) || minDate;
+        [minDate, maxDate] = [Math.min(minDate, maxDate), Math.max(minDate, maxDate)];
+        const rows = this.rows;
+        for (let i = 0, k = rows.length; i < k; i++) {
+          const row = rows[i];
+          for (let j = 0, l = row.length; j < l; j++) {
+
+            const cell = row[j];
+            const index = i * 2 + j;
+            const time = new Date(this.date.getFullYear(), index * 3).getTime();
+
+            cell.inRange = minDate && time >= minDate && time <= maxDate;
+            cell.start = minDate && time === minDate;
+            cell.end = maxDate && time === maxDate;
+          }
+        }
+      },
+      handleMouseMove(event) {
+        if (!this.rangeState.selecting) return;
+
+        let target = event.target;
+        if (target.tagName === 'A') {
+          target = target.parentNode.parentNode;
+        }
+        if (target.tagName === 'DIV') {
+          target = target.parentNode;
+        }
+        if (target.tagName !== 'TD') return;
+
+        const row = target.parentNode.rowIndex;
+        const column = target.cellIndex;
+        // can not select disabled date
+        if (this.rows[row][column].disabled) return;
+
+        // only update rangeState when mouse moves to a new cell
+        // this avoids frequent Date object creation and improves performance
+        if (row !== this.lastRow || column !== this.lastColumn) {
+          this.lastRow = row;
+          this.lastColumn = column;
+          this.$emit('changerange', {
+            minDate: this.minDate,
+            maxDate: this.maxDate,
+            rangeState: {
+              selecting: true,
+              endDate: this.getDateOfQuarter(row * 2 + column + 1)
+            }
+          });
+        }
+      },
+      handleQuarterTableClick(event) {
+        let target = event.target;
+        if (target.tagName === 'A') {
+          target = target.parentNode.parentNode;
+        }
+        if (target.tagName === 'DIV') {
+          target = target.parentNode;
+        }
+        if (target.tagName !== 'TD') return;
+        if (hasClass(target, 'disabled')) return;
+        const quarter = target.getAttribute('cellNum');
+        const newDate = this.getDateOfQuarter(quarter);
+        if (this.selectionMode === 'range') {
+          if (!this.rangeState.selecting) {
+            this.$emit('pick', {minDate: newDate, maxDate: null});
+            this.rangeState.selecting = true;
+          } else {
+            if (newDate >= this.minDate) {
+              this.$emit('pick', {minDate: this.minDate, maxDate: newDate});
+            } else {
+              this.$emit('pick', {minDate: newDate, maxDate: this.minDate});
+            }
+            this.rangeState.selecting = false;
+          }
+        } else {
+          this.$emit('pick', quarter);
+        }
+      }
+    },
+    computed: {
+      rows() {
+        // TODO: refactory rows / getCellClasses
+        const rows = this.tableRows;
+        const disabledDate = this.disabledDate;
+        const selectedDate = [];
+        const now = getMonthTimestamp(new Date());
+
+        for (let i = 0; i < 2; i++) {
+          const row = rows[i];
+          for (let j = 0; j < 2; j++) {
+            let cell = row[j];
+            if (!cell) {
+              cell = { row: i, column: j, type: 'normal', inRange: false, start: false, end: false };
+            }
+
+            cell.type = 'normal';
+
+            const index = i * 2 + j;
+            const time = new Date(this.date.getFullYear(), index * 3).getTime();
+            const nextTime = new Date(this.date.getFullYear(), index * 3 + 3).getTime();
+            cell.inRange = time >= getMonthTimestamp(this.minDate) && time <= getMonthTimestamp(this.maxDate);
+            cell.start = this.minDate && time === getMonthTimestamp(this.minDate);
+            cell.end = this.maxDate && time === getMonthTimestamp(this.maxDate);
+            const isToday = now >= time && now < nextTime;
+
+            if (isToday) {
+              cell.type = 'today';
+            }
+            cell.text = index;
+            let cellDate = new Date(time);
+            cell.disabled = typeof disabledDate === 'function' && disabledDate(cellDate);
+            cell.selected = arrayFind(selectedDate, date => date.getTime() === cellDate.getTime());
+
+            this.$set(row, j, cell);
+          }
+        }
+        return rows;
+      }
+    }
+  };
+</script>
diff --git a/node_modules/element-ui/packages/date-picker/src/basic/time-spinner.vue b/node_modules/element-ui/packages/date-picker/src/basic/time-spinner.vue
index d73c539..aaf9cf3 100644
--- a/node_modules/element-ui/packages/date-picker/src/basic/time-spinner.vue
+++ b/node_modules/element-ui/packages/date-picker/src/basic/time-spinner.vue
@@ -218,16 +218,16 @@
       },
 
       bindScrollEvent() {
-        const bindFunction = (type) => {
+        const bindFuntion = (type) => {
           this.$refs[type].wrap.onscroll = (e) => {
             // TODO: scroll is emitted when set scrollTop programatically
             // should find better solutions in the future!
             this.handleScroll(type, e);
           };
         };
-        bindFunction('hours');
-        bindFunction('minutes');
-        bindFunction('seconds');
+        bindFuntion('hours');
+        bindFuntion('minutes');
+        bindFuntion('seconds');
       },
 
       handleScroll(type) {
diff --git a/node_modules/element-ui/packages/date-picker/src/basic/year-table.vue b/node_modules/element-ui/packages/date-picker/src/basic/year-table.vue
index 74eb59a..895826f 100644
--- a/node_modules/element-ui/packages/date-picker/src/basic/year-table.vue
+++ b/node_modules/element-ui/packages/date-picker/src/basic/year-table.vue
@@ -1,40 +1,60 @@
 <template>
-  <table @click="handleYearTableClick" class="el-year-table">
+  <table @click="handleYearTableClick" class="el-year-table"  @mousemove="handleMouseMove">
     <tbody>
     <tr>
       <td class="available" :class="getCellStyle(startYear + 0)">
-        <a class="cell">{{ startYear }}</a>
+        <div>
+          <a class="cell">{{ startYear }}</a>
+        </div>
       </td>
       <td class="available" :class="getCellStyle(startYear + 1)">
-        <a class="cell">{{ startYear + 1 }}</a>
+        <div>
+          <a class="cell">{{ startYear + 1 }}</a>
+        </div>
       </td>
       <td class="available" :class="getCellStyle(startYear + 2)">
-        <a class="cell">{{ startYear + 2 }}</a>
+        <div>
+          <a class="cell">{{ startYear + 2 }}</a>
+        </div>
       </td>
       <td class="available" :class="getCellStyle(startYear + 3)">
-        <a class="cell">{{ startYear + 3 }}</a>
+        <div>
+          <a class="cell">{{ startYear + 3 }}</a>
+        </div>
       </td>
     </tr>
     <tr>
       <td class="available" :class="getCellStyle(startYear + 4)">
-        <a class="cell">{{ startYear + 4 }}</a>
+        <div>
+          <a class="cell">{{ startYear + 4 }}</a>
+        </div>
       </td>
       <td class="available" :class="getCellStyle(startYear + 5)">
-        <a class="cell">{{ startYear + 5 }}</a>
+        <div>
+          <a class="cell">{{ startYear + 5 }}</a>
+        </div>
       </td>
       <td class="available" :class="getCellStyle(startYear + 6)">
-        <a class="cell">{{ startYear + 6 }}</a>
+        <div>
+          <a class="cell">{{ startYear + 6 }}</a>
+        </div>
       </td>
       <td class="available" :class="getCellStyle(startYear + 7)">
-        <a class="cell">{{ startYear + 7 }}</a>
+        <div>
+          <a class="cell">{{ startYear + 7 }}</a>
+        </div>
       </td>
     </tr>
     <tr>
       <td class="available" :class="getCellStyle(startYear + 8)">
-        <a class="cell">{{ startYear + 8 }}</a>
+        <div>
+          <a class="cell">{{ startYear + 8 }}</a>
+        </div>
       </td>
       <td class="available" :class="getCellStyle(startYear + 9)">
-        <a class="cell">{{ startYear + 9 }}</a>
+        <div>
+          <a class="cell">{{ startYear + 9 }}</a>
+        </div>
       </td>
       <td></td>
       <td></td>
@@ -45,65 +65,123 @@
 
 <script type="text/babel">
   import { hasClass } from 'element-ui/src/utils/dom';
-  import { isDate, range, nextDate, getDayCountOfYear } from 'element-ui/src/utils/date-util';
+  import { range, nextDate, getDayCountOfYear, isDate} from 'element-ui/src/utils/date-util';
   import { arrayFindIndex, coerceTruthyValueToArray } from 'element-ui/src/utils/util';
-
   const datesInYear = year => {
     const numOfDays = getDayCountOfYear(year);
     const firstDay = new Date(year, 0, 1);
     return range(numOfDays).map(n => nextDate(firstDay, n));
   };
-
   export default {
     props: {
-      disabledDate: {},
-      value: {},
       defaultValue: {
         validator(val) {
           // null or valid Date Object
           return val === null || (val instanceof Date && isDate(val));
         }
       },
+      disabledDate: {},
       date: {},
-      selectionMode: {}
+      value: {},
+      year: {},
+      minDate: {},
+      maxDate: {},
+      selectionMode: {
+        default: 'year'
+      },
+      rangeState: {
+        default() {
+          return {
+            endDate: null,
+            selecting: false
+          };
+        }
+      }
     },
-
     computed: {
       startYear() {
         return Math.floor(this.date.getFullYear() / 10) * 10;
       }
     },
-
     methods: {
       getCellStyle(year) {
         const style = {};
         const today = new Date();
-
         style.disabled = typeof this.disabledDate === 'function'
           ? datesInYear(year).every(this.disabledDate)
           : false;
-        style.current = arrayFindIndex(coerceTruthyValueToArray(this.value), date => date.getFullYear() === year) >= 0;
         style.today = today.getFullYear() === year;
         style.default = this.defaultValue && this.defaultValue.getFullYear() === year;
-
+        if (this.selectionMode === 'range') {
+          let minYear = this.minDate;
+          let maxYear = this.rangeState.endDate ? this.rangeState.endDate : this.maxDate;
+          [minYear, maxYear] = [Math.min(minYear, maxYear), Math.max(minYear, maxYear)];
+          style['start-date'] = year === minYear;
+          style['end-date'] = minYear && year === maxYear;
+          style['in-range'] = minYear && year >= minYear && year <= maxYear;
+        } else {
+          style.current = arrayFindIndex(coerceTruthyValueToArray(this.value), date => date.getFullYear() === year) >= 0;
+        }
         return style;
       },
-
+      nextTenYear() {
+        this.$emit('pick', Number(this.year) + 10, false);
+      },
+      prevTenYear() {
+        this.$emit('pick', Number(this.year) - 10, false);
+      },
       handleYearTableClick(event) {
-        const target = event.target;
+        let target = event.target;
         if (target.tagName === 'A') {
-          if (hasClass(target.parentNode, 'disabled')) return;
-          const year = target.textContent || target.innerText;
-          if (this.selectionMode === 'years') {
-            const value = this.value || [];
-            const idx = arrayFindIndex(value, date => date.getFullYear() === Number(year));
-            const newValue = idx > -1
-              ? [...value.slice(0, idx), ...value.slice(idx + 1)]
-              : [...value, new Date(year)];
-            this.$emit('pick', newValue);
+          target = target.parentNode.parentNode;
+        }
+        if (target.tagName === 'DIV') {
+          target = target.parentNode;
+        }
+        if (target.tagName !== 'TD') return;
+        if (hasClass(target, 'disabled')) return;
+        /* istanbul ignore if  */
+        const year = Number(target.textContent || target.innerText);
+        if (this.selectionMode === 'range') {
+          if (!this.rangeState.selecting) {
+            this.$emit('pick', {minDate: year, maxDate: null});
+            this.rangeState.selecting = true;
+            this.rangeState.endDate = null;
           } else {
-            this.$emit('pick', Number(year));
+            this.rangeState.selecting = false;
+            if (year >= this.minDate) {
+              this.$emit('pick', {minDate: this.minDate, maxDate: year});
+            } else {
+              this.$emit('pick', {minDate: year, maxDate: this.minDate});
+            }
           }
+        } else {
+          this.$emit('pick', year);
+        }
+      },
+      handleMouseMove(event) {
+        let target = event.target;
+        if (!this.rangeState.selecting) return;
+        if (target.tagName === 'A') {
+          target = target.parentNode.parentNode;
+        }
+        if (target.tagName === 'DIV') {
+          target = target.parentNode;
+        }
+        if (target.tagName !== 'TD') return;
+        const row = target.parentNode.rowIndex;
+        const column = target.cellIndex;
+        if (hasClass(target.parentNode, 'disabled')) return;
+        if (row !== this.lastRow || column !== this.lastColumn) {
+          this.$emit('changerange', {
+            minDate: this.minDate,
+            maxDate: this.maxDate,
+            rangeState: {
+              selecting: true,
+              endDate: Number(target.textContent || target.innerText)
+            },
+            el: target
+          });
         }
       }
     }
diff --git a/node_modules/element-ui/packages/date-picker/src/panel/date.vue b/node_modules/element-ui/packages/date-picker/src/panel/date.vue
index a0fa95b..b16db7f 100644
--- a/node_modules/element-ui/packages/date-picker/src/panel/date.vue
+++ b/node_modules/element-ui/packages/date-picker/src/panel/date.vue
@@ -47,7 +47,7 @@
           </div>
           <div
             class="el-date-picker__header"
-            :class="{ 'el-date-picker__header--bordered': currentView === 'year' || currentView === 'month' }"
+            :class="{ 'el-date-picker__header--bordered': currentView === 'year' || currentView === 'month' || currentView === 'quarter' }"
             v-show="currentView !== 'time'">
             <button
               type="button"
@@ -102,7 +102,6 @@
             <year-table
               v-show="currentView === 'year'"
               @pick="handleYearPick"
-              :selection-mode="selectionMode"
               :value="value"
               :default-value="defaultValue ? new Date(defaultValue) : null"
               :date="date"
@@ -111,25 +110,32 @@
             <month-table
               v-show="currentView === 'month'"
               @pick="handleMonthPick"
-              :selection-mode="selectionMode"
               :value="value"
               :default-value="defaultValue ? new Date(defaultValue) : null"
               :date="date"
               :disabled-date="disabledDate">
             </month-table>
+            <quarter-table
+              v-show="currentView === 'quarter'"
+              @pick="handleQuarterPick"
+              :value="new Date(value)"
+              :default-value="defaultValue ? new Date(defaultValue) : null"
+              :date="date"
+              :disabled-date="disabledDate">
+            </quarter-table>
           </div>
         </div>
       </div>
 
       <div
         class="el-picker-panel__footer"
-        v-show="footerVisible && (currentView === 'date' || currentView === 'month' || currentView === 'year')">
+        v-show="footerVisible && currentView === 'date'">
         <el-button
           size="mini"
           type="text"
           class="el-picker-panel__link-btn"
           @click="changeToNow"
-          v-show="selectionMode !== 'dates' && selectionMode !== 'months' && selectionMode !== 'years'">
+          v-show="selectionMode !== 'dates'">
           {{ t('el.datepicker.now') }}
         </el-button>
         <el-button
@@ -172,6 +178,7 @@
   import YearTable from '../basic/year-table';
   import MonthTable from '../basic/month-table';
   import DateTable from '../basic/date-table';
+  import QuarterTable from '../basic/quarter-table';
 
   export default {
     mixins: [Locale],
@@ -192,8 +199,6 @@
 
       value(val) {
         if (this.selectionMode === 'dates' && this.value) return;
-        if (this.selectionMode === 'months' && this.value) return;
-        if (this.selectionMode === 'years' && this.value) return;
         if (isDate(val)) {
           this.date = new Date(val);
         } else {
@@ -219,10 +224,6 @@
           }
         } else if (newVal === 'dates') {
           this.currentView = 'date';
-        } else if (newVal === 'years') {
-          this.currentView = 'year';
-        } else if (newVal === 'months') {
-          this.currentView = 'month';
         }
       }
     },
@@ -336,8 +337,6 @@
         if (this.selectionMode === 'month') {
           this.date = modifyDate(this.date, this.year, month, 1);
           this.emit(this.date);
-        } else if (this.selectionMode === 'months') {
-          this.emit(month, true);
         } else {
           this.date = changeYearMonthAndClampDate(this.date, this.year, month);
           // TODO: should emit intermediate value ??
@@ -346,6 +345,20 @@
         }
       },
 
+      handleQuarterPick(quarter) {
+        const month = quarter * 3 - 3;
+        if (this.selectionMode === 'quarter') {
+          this.date = modifyDate(this.date, this.year, month, 1);
+          this.emit(this.date);
+        }
+      },
+
+      handleDateSelect(value) {
+        if (this.selectionMode === 'dates') {
+          this.selectedDate = value;
+        }
+      },
+
       handleDatePick(value) {
         if (this.selectionMode === 'day') {
           let newDate = this.value
@@ -368,8 +381,10 @@
         if (this.selectionMode === 'year') {
           this.date = modifyDate(this.date, year, 0, 1);
           this.emit(this.date);
-        } else if (this.selectionMode === 'years') {
-          this.emit(year, true);
+        } else if (this.selectionMode === 'quarter') {
+          this.date = changeYearMonthAndClampDate(this.date, year, this.month);
+          // this.emit(this.date, true);
+          this.currentView = 'quarter';
         } else {
           this.date = changeYearMonthAndClampDate(this.date, year, this.month);
           // TODO: should emit intermediate value ??
@@ -388,7 +403,7 @@
       },
 
       confirm() {
-        if (this.selectionMode === 'dates' || this.selectionMode === 'months' || this.selectionMode === 'years') {
+        if (this.selectionMode === 'dates') {
           this.emit(this.value);
         } else {
           // value were emitted in handle{Date,Time}Pick, nothing to update here
@@ -402,10 +417,12 @@
       },
 
       resetView() {
-        if (this.selectionMode === 'month' || this.selectionMode === 'months') {
+        if (this.selectionMode === 'month') {
           this.currentView = 'month';
-        } else if (this.selectionMode === 'year' || this.selectionMode === 'years') {
+        } else if (this.selectionMode === 'year') {
           this.currentView = 'year';
+        } else if (this.selectionMode === 'quarter') {
+          this.currentView = 'quarter';
         } else {
           this.currentView = 'date';
         }
@@ -512,7 +529,7 @@
     },
 
     components: {
-      TimePicker, YearTable, MonthTable, DateTable, ElInput, ElButton
+      TimePicker, YearTable, MonthTable, DateTable, ElInput, ElButton, QuarterTable
     },
 
     data() {
@@ -558,7 +575,7 @@
       },
 
       footerVisible() {
-        return this.showTime || this.selectionMode === 'dates' || this.selectionMode === 'months' || this.selectionMode === 'years';
+        return this.showTime || this.selectionMode === 'dates';
       },
 
       visibleTime() {
diff --git a/node_modules/element-ui/packages/date-picker/src/panel/quarter-range.vue b/node_modules/element-ui/packages/date-picker/src/panel/quarter-range.vue
new file mode 100644
index 0000000..3b27bc9
--- /dev/null
+++ b/node_modules/element-ui/packages/date-picker/src/panel/quarter-range.vue
@@ -0,0 +1,274 @@
+<template>
+    <transition name="el-zoom-in-top" @after-leave="$emit('dodestroy')">
+        <div
+            v-show="visible"
+            class="el-picker-panel el-date-range-picker el-popper"
+            :class="[{
+                    'has-sidebar': $slots.sidebar || shortcuts
+            }, popperClass]">
+            <div class="el-picker-panel__body-wrapper">
+                <slot name="sidebar" class="el-picker-panel__sidebar"></slot>
+                <div class="el-picker-panel__sidebar" v-if="shortcuts">
+                    <button
+                        type="button"
+                        class="el-picker-panel__shortcut"
+                        v-for="(shortcut,index) in shortcuts"
+                        @click="handleShortcutClick(shortcut)"
+                        :key="index">
+                        {{shortcut.text}}
+                    </button>
+                </div>
+                <div class="el-picker-panel__body">
+                    <div class="el-picker-panel__content el-date-range-picker__content is-left">
+                        <div class="el-date-range-picker__header">
+                            <button
+                                type="button"
+                                @click="leftPrevYear"
+                                class="el-picker-panel__icon-btn el-icon-d-arrow-left">
+                            </button>
+                            <button
+                                type="button"
+                                v-if="unlinkPanels"
+                                @click="leftNextYear"
+                                :disabled="!enableYearArrow"
+                                :class="{ 'is-disabled': !enableYearArrow }"
+                                class="el-picker-panel__icon-btn el-icon-d-arrow-right"></button>
+                            <div>{{ leftLabel }}</div>
+                        </div>
+                        <quarter-table
+                            :date="leftDate"
+                            :min-date="minDate"
+                            :max-date="maxDate"
+                            :year="leftYear"
+                            selection-mode="range"
+                            :rangeState="rangeState"
+                            :disabled-date="disabledDate"
+                            @changerange="handleChangeRange"
+                            @pick="handleRangePick">
+                        </quarter-table>
+                    </div>
+                    <div class="el-picker-panel__content el-date-range-picker__content is-right">
+                        <div class="el-date-range-picker__header">
+                           <button
+                               type="button"
+                               v-if="unlinkPanels"
+                               @click="rightPrevYear"
+                               :disabled="!enableYearArrow"
+                               :class="{ 'is-disabled': !enableYearArrow }"
+                               class="el-picker-panel__icon-btn el-icon-d-arrow-left">
+                            </button>
+                            <button
+                                type="button"
+                                @click="rightNextYear"
+                                class="el-picker-panel__icon-btn el-icon-d-arrow-right">
+                            </button>
+                            <div>{{ rightLabel }}</div>
+                        </div>
+                        <quarter-table
+                            :min-date="minDate"
+                            :max-date="maxDate"
+                            :date="rightDate"
+                            :year="rightYear"
+                            selection-mode="range"
+                            :rangeState="rangeState"
+                            :disabled-date="disabledDate"
+                            @changerange="handleChangeRange"
+                            @pick="handleRangePick">
+                        </quarter-table>
+                    </div>
+                </div>
+            </div>
+        </div>
+    </transition>
+</template>
+
+<script type="text/babel">
+import Clickoutside from 'element-ui/src/utils/clickoutside';
+import Locale from 'element-ui/src/mixins/locale';
+import { isDate, modifyWithTimeString, prevYear, nextYear} from 'element-ui/src/utils/date-util';
+import quarterTable from '../basic/quarter-table';
+const calcDefaultValue = (defaultValue) => {
+  var date;
+  if (Array.isArray(defaultValue)) {
+    return [new Date(defaultValue[0]), new Date(defaultValue[1])];
+  } else if (defaultValue) {
+    date = new Date(defaultValue);
+    return [new Date(date), new Date(date.setFullYear(date.getFullYear() + 10))];
+  } else {
+    date = new Date();
+    return [new Date(), new Date(date.setFullYear(date.getFullYear() + 10))];
+  }
+};
+export default {
+  mixins: [Locale],
+  directives: { Clickoutside },
+  computed: {
+    btnDisabled() {
+      return !(this.minDate && this.maxDate && !this.selecting);
+    },
+    leftLabel() {
+      return this.leftDate.getFullYear() + ' ' + this.t('el.datepicker.year');
+    },
+
+    rightLabel() {
+      return this.rightDate.getFullYear() + ' ' + this.t('el.datepicker.year');
+    },
+    leftYear() {
+      return this.leftDate.getFullYear();
+    },
+    rightYear() {
+      return this.rightDate.getFullYear() === this.leftDate.getFullYear() ? this.leftDate.getFullYear() + 1 : this.rightDate.getFullYear();
+    },
+    enableYearArrow() {
+      return this.unlinkPanels && this.rightYear > this.leftYear + 1;
+    }
+  },
+  data() {
+    return {
+      defaultTime: null,
+      popperClass: '',
+      defaultValue: null,
+      date: calcDefaultValue(this.defaultValue)[0],
+      minDate: '',
+      maxDate: '',
+      leftDate: new Date(),
+      rightDate: nextYear(new Date()),
+      rangeState: {
+        endDate: null,
+        selecting: false
+      },
+      showTime: false,
+      shortcuts: '',
+      value: [],
+      visible: '',
+      disabledDate: '',
+      format: '',
+      unlinkPanels: false,
+      arrowControl: false
+    };
+  },
+  watch: {
+    value: {
+      handler(newVal) {
+        if (!newVal) {
+          this.minDate = null;
+          this.maxDate = null;
+        } else if (Array.isArray(newVal)) {
+          this.minDate = isDate(newVal[0]) ? new Date(newVal[0]) : null;
+          this.maxDate = isDate(newVal[1]) ? new Date(newVal[1]) : null;
+          if (this.minDate) {
+            this.leftDate = this.minDate;
+            if (this.unlinkPanels && this.maxDate) {
+              const minDateYear = this.minDate.getFullYear();
+              const maxDateYear = this.maxDate.getFullYear();
+              this.rightDate = minDateYear === maxDateYear
+                ? nextYear(this.maxDate)
+                : this.maxDate;
+            } else {
+              this.rightDate = nextYear(this.leftDate);
+            }
+          } else {
+            this.leftDate = calcDefaultValue(this.defaultValue)[0];
+            this.rightDate = nextYear(this.leftDate);
+          }
+          this.date = this.minDate;
+        }
+      },
+      immediate: true
+    },
+    defaultValue(val) {
+      if (!Array.isArray(this.value)) {
+        const [left, right] = calcDefaultValue(val);
+        this.leftDate = left;
+        this.rightDate = val && val[1] && left.getFullYear() !== right.getFullYear() && this.unlinkPanels
+          ? right
+          : nextYear(this.leftDate);
+      }
+    }
+  },
+  methods: {
+    handleClear() {
+      this.minDate = null;
+      this.maxDate = null;
+      this.leftDate = calcDefaultValue(this.defaultValue)[0];
+      this.rightDate = nextYear(this.leftDate);
+      this.$emit('pick', null);
+    },
+    handleChangeRange(val) {
+      this.minDate = val.minDate;
+      this.maxDate = val.maxDate;
+      this.rangeState = val.rangeState;
+    },
+    handleRangePick(val, close = true) {
+      const defaultTime = this.defaultTime || [];
+      const minDate = modifyWithTimeString(val.minDate, defaultTime[0]);
+      const maxDate = modifyWithTimeString(val.maxDate, defaultTime[1]);
+      if (this.maxDate === maxDate && this.minDate === minDate) {
+        return;
+      }
+      this.onPick && this.onPick(val);
+      this.maxDate = maxDate;
+      this.minDate = minDate;
+      // workaround for https://github.com/ElemeFE/element/issues/7539, should remove this block when we don't have to care about Chromium 55 - 57
+      setTimeout(() => {
+        this.maxDate = maxDate;
+        this.minDate = minDate;
+      }, 10);
+      if (!close) return;
+      this.handleConfirm();
+    },
+    handleShortcutClick(shortcut) {
+      if (shortcut.onClick) {
+        shortcut.onClick(this);
+      }
+    },
+
+    leftPrevYear() {
+      this.leftDate = prevYear(this.leftDate);
+      if (!this.unlinkPanels) {
+        this.rightDate = prevYear(this.rightDate);
+      }
+    },
+
+    rightNextYear() {
+      if (!this.unlinkPanels) {
+        this.leftDate = nextYear(this.leftDate);
+      }
+      this.rightDate = nextYear(this.rightDate);
+    },
+
+    leftNextYear() {
+      this.leftDate = nextYear(this.leftDate);
+    },
+
+    rightPrevYear() {
+      this.rightDate = prevYear(this.rightDate);
+    },
+
+    handleConfirm(visible = false) {
+      if (this.isValidValue([this.minDate, this.maxDate])) {
+        this.$emit('pick', [this.minDate, this.maxDate], visible);
+      }
+    },
+
+    isValidValue(value) {
+      return Array.isArray(value) &&
+        value && value[0] && value[1] &&
+        isDate(value[0]) && isDate(value[1]) &&
+        value[0].getTime() <= value[1].getTime() && (
+        typeof this.disabledDate === 'function'
+          ? !this.disabledDate(value[0]) && !this.disabledDate(value[1])
+          : true
+      );
+    },
+
+    resetDate() {
+      this.minDate = this.value && isDate(this.value[0]) ? new Date(this.value[0]) : null;
+      this.maxDate = this.value && isDate(this.value[0]) ? new Date(this.value[1]) : null;
+    }
+  },
+  components: {
+    quarterTable
+  }
+};
+</script>
\ No newline at end of file
diff --git a/node_modules/element-ui/packages/date-picker/src/panel/year-range.vue b/node_modules/element-ui/packages/date-picker/src/panel/year-range.vue
new file mode 100644
index 0000000..937f082
--- /dev/null
+++ b/node_modules/element-ui/packages/date-picker/src/panel/year-range.vue
@@ -0,0 +1,237 @@
+<template>
+    <transition name="el-zoom-in-top" @after-leave="$emit('dodestroy')">
+        <div
+            v-show="visible"
+            class="el-picker-panel el-date-range-picker el-popper"
+            :class="[{
+                    'has-sidebar': $slots.sidebar || shortcuts
+            }, popperClass]">
+            <div class="el-picker-panel__body-wrapper">
+                <slot name="sidebar" class="el-picker-panel__sidebar"></slot>
+                <div class="el-picker-panel__sidebar" v-if="shortcuts">
+                    <button
+                        type="button"
+                        class="el-picker-panel__shortcut"
+                        v-for="(shortcut,index) in shortcuts"
+                        @click="handleShortcutClick(shortcut)"
+                        :key="index">
+                        {{shortcut.text}}
+                    </button>
+                </div>
+                <div class="el-picker-panel__body">
+                    <div class="el-picker-panel__content el-date-range-picker__content is-left">
+                        <div class="el-date-range-picker__header">
+                            <button type="button" @click="prevYear(true)" class="el-picker-panel__icon-btn el-icon-d-arrow-left"></button>
+                            <button
+                                type="button"
+                                v-if="unlinkPanels"
+                                @click="nextYear(true)"
+                                :disabled="!enableYearArrow"
+                                :class="{ 'is-disabled': !enableYearArrow }"
+                                class="el-picker-panel__icon-btn el-icon-d-arrow-right"></button>
+                            <div>{{ leftLabel }}</div>
+                        </div>
+                        <year-table
+                            :date="date"
+                            :min-date="minDate"
+                            :max-date="maxDate"
+                            :year="leftYear"
+                            selection-mode="range"
+                            :rangeState="rangeState"
+                            :disabled-date="disabledDate"
+                            @changerange="handleChangeRange"
+                            @pick="handleRangePick">
+                        </year-table>
+                    </div>
+                    <div class="el-picker-panel__content el-date-range-picker__content is-right">
+                        <div class="el-date-range-picker__header">
+                            <button
+                                type="button"
+                                v-if="unlinkPanels"
+                                @click="prevYear(false)"
+                                :disabled="!enableYearArrow"
+                                :class="{ 'is-disabled': !enableYearArrow }"
+                                class="el-picker-panel__icon-btn el-icon-d-arrow-left"></button>
+                            <button type="button" @click="nextYear(false)" class="el-picker-panel__icon-btn el-icon-d-arrow-right"></button>
+                            <div>{{ rightLabel }}</div>
+                        </div>
+                        <year-table
+                            :min-date="minDate"
+                            :max-date="maxDate"
+                            :date="rightDate"
+                            :year="rightYear"
+                            selection-mode="range"
+                            :rangeState="rangeState"
+                            :disabled-date="disabledDate"
+                            @changerange="handleChangeRange"
+                            @pick="handleRangePick">
+                        </year-table>
+                    </div>
+                </div>
+            </div>
+        </div>
+    </transition>
+</template>
+
+<script type="text/babel">
+import Clickoutside from 'element-ui/src/utils/clickoutside';
+import Locale from 'element-ui/src/mixins/locale';
+import {
+  toDate,
+  modifyWithTimeString
+} from 'element-ui/src/utils/date-util';
+import yearTable from '../basic/year-table';
+const calcDefaultValue = (defaultValue) => {
+  var date;
+  if (Array.isArray(defaultValue)) {
+    return [new Date(defaultValue[0]), new Date(defaultValue[1])];
+  } else if (defaultValue) {
+    date = new Date(defaultValue);
+    return [new Date(date), new Date(date.setFullYear(date.getFullYear() + 10))];
+  } else {
+    date = new Date();
+    return [new Date(), new Date(date.setFullYear(date.getFullYear() + 10))];
+  }
+};
+export default {
+  mixins: [Locale],
+  directives: { Clickoutside },
+  computed: {
+    btnDisabled() {
+      return !(this.minDate && this.maxDate && !this.selecting);
+    },
+    leftLabel() {
+      const startYear = Math.floor(this.date.getFullYear() / 10) * 10;
+      const yearTranslation = this.t('el.datepicker.year');
+      return startYear + ' ' + yearTranslation + ' - ' + (startYear + 9) + ' ' + yearTranslation;
+    },
+
+    rightLabel() {
+      const endYear = Math.floor(this.rightDate.getFullYear() / 10) * 10;
+      const yearTranslation = this.t('el.datepicker.year');
+      return endYear + ' ' + yearTranslation + ' - ' + (endYear + 9) + ' ' + yearTranslation;
+    },
+    leftYear() {
+      return Math.floor(this.date.getFullYear() / 10) * 10;
+    },
+    rightYear() {
+      return Math.floor(this.rightDate.getFullYear() / 10) * 10;
+    },
+    enableYearArrow() {
+      return this.rightYear - 10 > this.leftYear;
+    }
+  },
+  data() {
+    return {
+      defaultTime: null,
+      popperClass: '',
+      defaultValue: null,
+      date: calcDefaultValue(this.defaultValue)[0],
+      rightDate: calcDefaultValue(this.defaultValue)[1],
+      minDate: '',
+      maxDate: '',
+      rangeState: {
+        endDate: null,
+        selecting: false
+      },
+      showTime: false,
+      shortcuts: '',
+      value: '',
+      visible: '',
+      disabledDate: '',
+      firstDayOfWeek: 7,
+      format: '',
+      toolTipTop: 0,
+      toolTipLeft: 0,
+      unlinkPanels: false,
+      arrowControl: false
+    };
+  },
+  watch: {
+    value: {
+      handler(newVal) {
+        if (!newVal) {
+          this.minDate = null;
+          this.maxDate = null;
+        } else if (Array.isArray(newVal)) {
+          this.minDate = newVal[0] ? toDate(newVal[0]).getFullYear() : null;
+          this.maxDate = newVal[1] ? toDate(newVal[1]).getFullYear() : null;
+          this.date = new Date(this.minDate, 0, 1);
+          this.rangeState.endDate = null;
+        }
+      },
+      immediate: true
+    },
+    date(val) {
+      const date = new Date(val);
+      if (!this.unlinkPanels || date.getFullYear() + 10 > this.rightDate.getFullYear()) {
+        this.rightDate = new Date(date.setFullYear(date.getFullYear() + 10));
+      }
+    },
+    defaultValue(val) {
+      if (!Array.isArray(this.value) || !this.value.length) {
+        const [left, right] = calcDefaultValue(val);
+        this.date = left;
+        if (this.unlinkPanels) this.rightDate = right;
+      }
+    }
+  },
+  methods: {
+    handleClear() {
+      this.minDate = null;
+      this.maxDate = null;
+      this.date = calcDefaultValue(this.defaultValue)[0];
+      this.handleConfirm(false);
+    },
+    handleChangeRange(val) {
+      this.minDate = val.minDate;
+      this.maxDate = val.maxDate;
+      this.rangeState = val.rangeState;
+    },
+    handleRangePick(val, close = true) {
+      const defaultTime = this.defaultTime || [];
+      const minDate = modifyWithTimeString(val.minDate, defaultTime[0]);
+      const maxDate = modifyWithTimeString(val.maxDate, defaultTime[1]);
+      if (this.maxDate === maxDate && this.minDate === minDate) {
+        return;
+      }
+      this.onPick && this.onPick(val);
+      this.maxDate = maxDate;
+      this.minDate = minDate;
+      // workaround for https://github.com/ElemeFE/element/issues/7539, should remove this block when we don't have to care about Chromium 55 - 57
+      setTimeout(() => {
+        this.maxDate = maxDate;
+        this.minDate = minDate;
+      }, 10);
+      if (!close) return;
+      this.handleConfirm();
+    },
+    handleShortcutClick(shortcut) {
+      if (shortcut.onClick) {
+        shortcut.onClick(this);
+      }
+    },
+    nextYear(left = false) {
+      const date = left || !this.unlinkPanels ? this.date : this.rightDate;
+      date.setFullYear(date.getFullYear() + 10);
+      this.resetDate();
+    },
+    prevYear(left = false) {
+      const date = left ? this.date : this.rightDate;
+      date.setFullYear(date.getFullYear() - 10);
+      this.resetDate();
+    },
+    handleConfirm(visible = false) {
+      if (!this.minDate || !this.maxDate) return;
+      this.$emit('pick', [new Date(this.minDate, 0, 1), new Date(this.maxDate, 0, 1)], visible);
+    },
+    resetDate() {
+      this.date = new Date(this.date);
+      this.rightDate = new Date(this.rightDate);
+    }
+  },
+  components: {
+    yearTable
+  }
+};
+</script>
\ No newline at end of file
diff --git a/node_modules/element-ui/packages/date-picker/src/picker.vue b/node_modules/element-ui/packages/date-picker/src/picker.vue
index 36b7ebf..402c656 100644
--- a/node_modules/element-ui/packages/date-picker/src/picker.vue
+++ b/node_modules/element-ui/packages/date-picker/src/picker.vue
@@ -2,7 +2,7 @@
   <el-input
     class="el-date-editor"
     :class="'el-date-editor--' + type"
-    :readonly="!editable || readonly || type === 'dates' || type === 'week' || type === 'years' || type === 'months'"
+    :readonly="!editable || readonly || type === 'dates' || type === 'week' || type === 'quarter'"
     :disabled="pickerDisabled"
     :size="pickerSize"
     :name="name"
@@ -86,7 +86,7 @@
 <script>
 import Vue from 'vue';
 import Clickoutside from 'element-ui/src/utils/clickoutside';
-import { formatDate, parseDate, isDateObject, getWeekNumber } from 'element-ui/src/utils/date-util';
+import { formatDate, parseDate, isDateObject, getWeekNumber, getQuarterNumber } from 'element-ui/src/utils/date-util';
 import Popper from 'element-ui/src/utils/vue-popper';
 import Emitter from 'element-ui/src/mixins/emitter';
 import ElInput from 'element-ui/packages/input';
@@ -97,8 +97,7 @@ const NewPopper = {
     appendToBody: Popper.props.appendToBody,
     offset: Popper.props.offset,
     boundariesPadding: Popper.props.boundariesPadding,
-    arrowOffset: Popper.props.arrowOffset,
-    transformOrigin: Popper.props.transformOrigin
+    arrowOffset: Popper.props.arrowOffset
   },
   methods: Popper.methods,
   data() {
@@ -110,7 +109,6 @@ const NewPopper = {
 const DEFAULT_FORMATS = {
   date: 'yyyy-MM-dd',
   month: 'yyyy-MM',
-  months: 'yyyy-MM',
   datetime: 'yyyy-MM-dd HH:mm:ss',
   time: 'HH:mm:ss',
   week: 'yyyywWW',
@@ -119,7 +117,9 @@ const DEFAULT_FORMATS = {
   monthrange: 'yyyy-MM',
   datetimerange: 'yyyy-MM-dd HH:mm:ss',
   year: 'yyyy',
-  years: 'yyyy'
+  yearrange: 'yyyy',
+  quarter: 'yyyy年 第Q季度',
+  quarterrange: 'yyyy年 第Q季度'
 };
 const HAVE_TRIGGER_TYPES = [
   'date',
@@ -129,13 +129,14 @@ const HAVE_TRIGGER_TYPES = [
   'week',
   'month',
   'year',
+  'yearrange',
   'daterange',
   'monthrange',
   'timerange',
   'datetimerange',
   'dates',
-  'months',
-  'years'
+  'quarter',
+  'quarterrange'
 ];
 const DATE_FORMATTER = function(value, format) {
   if (format === 'timestamp') return value.getTime();
@@ -168,6 +169,24 @@ const RANGE_PARSER = function(array, format, separator) {
   }
   return [];
 };
+const QUARTER_FORMATTER = function(value, format) {
+  const quarter = getQuarterNumber(value);
+  let date = formatDate(value, format);
+  if (/Q/.test(date)) {
+    date = date.replace(/Q/, quarter);
+  }
+  return date;
+};
+const QUARTER_PARSER = function(value, format) {
+  const yearPosition = format.indexOf('yyyy');
+  const quarterPosition = format.indexOf('Q');
+  let year = /yyyy/.test(format)
+    ? value.substring(yearPosition, yearPosition + 4)
+    : (new Date()).getFullYear();
+  let quarter = value.substring(quarterPosition, quarterPosition + 1);
+
+  return new Date(year, (quarter - 1) * 3 + 1);
+};
 const TYPE_VALUE_RESOLVER_MAP = {
   default: {
     formatter(value) {
@@ -197,7 +216,17 @@ const TYPE_VALUE_RESOLVER_MAP = {
     },
     parser(text, format) {
       // parse as if a normal date
-      return TYPE_VALUE_RESOLVER_MAP.date.parser(text, format);
+      let yearPosition = format.indexOf('yyyy');
+      let weekPosition = format.indexOf('W') !== -1 ? format.indexOf('W') : format.indexOf('WW');
+      let year = /yyyy/.test(format)
+        ? text.substring(yearPosition, yearPosition + 4)
+        : (new Date()).getFullYear();
+      let weeks = isNaN(text.substring(weekPosition, weekPosition + 2))
+        ? text.substring(weekPosition, weekPosition + 1)
+        : text.substring(weekPosition, weekPosition + 2);
+      let timestamp = (new Date(year)).getTime() + weeks * 7 * 24 * 60 * 60 * 1000;
+
+      return new Date(timestamp);
     }
   },
   date: {
@@ -212,6 +241,10 @@ const TYPE_VALUE_RESOLVER_MAP = {
     formatter: RANGE_FORMATTER,
     parser: RANGE_PARSER
   },
+  yearrange: {
+    formatter: RANGE_FORMATTER,
+    parser: RANGE_PARSER
+  },
   monthrange: {
     formatter: RANGE_FORMATTER,
     parser: RANGE_PARSER
@@ -260,22 +293,32 @@ const TYPE_VALUE_RESOLVER_MAP = {
         .map(date => date instanceof Date ? date : DATE_PARSER(date, format));
     }
   },
-  months: {
-    formatter(value, format) {
-      return value.map(date => DATE_FORMATTER(date, format));
-    },
-    parser(value, format) {
-      return (typeof value === 'string' ? value.split(', ') : value)
-        .map(date => date instanceof Date ? date : DATE_PARSER(date, format));
-    }
+  quarter: {
+    formatter: QUARTER_FORMATTER,
+    parser: QUARTER_PARSER
   },
-  years: {
+  quarterrange: {
     formatter(value, format) {
-      return value.map(date => DATE_FORMATTER(date, format));
+      if (Array.isArray(value) && value.length === 2) {
+        const start = value[0];
+        const end = value[1];
+
+        if (start && end) {
+          return [QUARTER_FORMATTER(start, format), QUARTER_FORMATTER(end, format)];
+        }
+      }
+      return '';
     },
     parser(value, format) {
-      return (typeof value === 'string' ? value.split(', ') : value)
-        .map(date => date instanceof Date ? date : DATE_PARSER(date, format));
+      if (Array.isArray(value) && value.length === 2) {
+        const start = value[0];
+        const end = value[1];
+
+        if (start && end) {
+          return [QUARTER_PARSER(start, format), QUARTER_PARSER(end, format)];
+        }
+      }
+      return '';
     }
   }
 };
@@ -411,6 +454,10 @@ export default {
     validateEvent: {
       type: Boolean,
       default: true
+    },
+    type: {
+      type: String,
+      default: 'date'
     }
   },
 
@@ -512,10 +559,8 @@ export default {
         return 'year';
       } else if (this.type === 'dates') {
         return 'dates';
-      } else if (this.type === 'months') {
-        return 'months';
-      } else if (this.type === 'years') {
-        return 'years';
+      } else if (this.type === 'quarter') {
+        return 'quarter';
       }
 
       return 'day';
@@ -538,7 +583,7 @@ export default {
       } else if (this.userInput !== null) {
         return this.userInput;
       } else if (formattedValue) {
-        return (this.type === 'dates' || this.type === 'years' || this.type === 'months')
+        return this.type === 'dates'
           ? formattedValue.join(', ')
           : formattedValue;
       } else {
@@ -740,7 +785,7 @@ export default {
       if (!this.pickerVisible) return;
       this.pickerVisible = false;
 
-      if (this.type === 'dates' || this.type === 'years' || this.type === 'months') {
+      if (this.type === 'dates') {
         // restore to former value
         const oldValue = parseAsFormatAndType(this.valueOnOpen, this.valueFormat, this.type, this.rangeSeparator) || this.valueOnOpen;
         this.emitInput(oldValue);
diff --git a/node_modules/element-ui/packages/date-picker/src/picker/date-picker.js b/node_modules/element-ui/packages/date-picker/src/picker/date-picker.js
index 6efc0ed..f90ccca 100644
--- a/node_modules/element-ui/packages/date-picker/src/picker/date-picker.js
+++ b/node_modules/element-ui/packages/date-picker/src/picker/date-picker.js
@@ -2,12 +2,18 @@ import Picker from '../picker';
 import DatePanel from '../panel/date';
 import DateRangePanel from '../panel/date-range';
 import MonthRangePanel from '../panel/month-range';
+import YearRangePanel from '../panel/year-range';
+import QuarterRangePanel from '../panel/quarter-range.vue';
 
 const getPanel = function(type) {
   if (type === 'daterange' || type === 'datetimerange') {
     return DateRangePanel;
   } else if (type === 'monthrange') {
     return MonthRangePanel;
+  } else if (type === 'yearrange') {
+    return YearRangePanel;
+  } else if (type === 'quarterrange') {
+    return QuarterRangePanel;
   }
   return DatePanel;
 };
@@ -15,7 +21,7 @@ const getPanel = function(type) {
 export default {
   mixins: [Picker],
 
-  name: 'ElDatePicker',
+  name: 'ElDatePicker1',
 
   props: {
     type: {
