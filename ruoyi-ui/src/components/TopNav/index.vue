<template>
  <el-menu :default-active="activeMenu" mode="horizontal" @select="handleSelect">
    <template v-for="(item, index) in topMenus">
      <el-menu-item :style="{ '--theme': theme }" :index="item.path" :key="index">
        <svg-icon v-if="item.meta && item.meta.icon && item.meta.icon !== '#'" :icon-class="item.meta.icon" />
        {{ item.meta.title }}
      </el-menu-item>
    </template>

    <!-- 顶部菜单超出数量折叠 -->
    <el-submenu :style="{ '--theme': theme }" index="more">
      <template slot="title">更多菜单</template>
      <template v-for="(item, index) in moreMenus">
        <el-menu-item :index="item.path" :key="index">
          <svg-icon v-if="item.meta && item.meta.icon && item.meta.icon !== '#'" :icon-class="item.meta.icon" />
          {{ item.meta.title }}
        </el-menu-item>
      </template>
    </el-submenu>
  </el-menu>
</template>

<script>
import { constantRoutes } from "@/router";
// 防抖函数
function debounce(func, delay = 300) {
  let timer;
  return function (...args) {
    const context = this;
    clearTimeout(timer);
    timer = setTimeout(() => {
      func.apply(context, args);
    }, delay);
  };
}
// 隐藏侧边栏路由
const hideList = ['/index', '/user/profile'];

export default {
  data() {
    return {
      // 当前激活菜单的 index
      currentIndex: undefined,
      topMenus: [],
      moreMenus: [],
      resizeObserver: null
    };
  },
  computed: {
    theme() {
      return this.$store.state.settings.theme;
    },

    // 所有的路由信息
    routers() {
      return this.$store.state.permission.topbarRouters;
    },
    // 设置子路由
    childrenMenus() {
      var childrenMenus = [];
      this.routers.map((router) => {
        for (var item in router.children) {
          if (router.children[item].parentPath === undefined) {
            if (router.path === "/") {
              router.children[item].path = "/" + router.children[item].path;
            } else {
              if (!this.ishttp(router.children[item].path)) {
                router.children[item].path = router.path + "/" + router.children[item].path;
              }
            }
            router.children[item].parentPath = router.path;
          }
          childrenMenus.push(router.children[item]);
        }
      });
      return constantRoutes.concat(childrenMenus);
    },
    // 默认激活的菜单
    activeMenu() {
      const path = this.$route.path;
      let activePath = path;
      if (path !== undefined && path.lastIndexOf("/") > 0 && hideList.indexOf(path) === -1) {
        const tmpPath = path.substring(1, path.length);
        activePath = "/" + tmpPath.substring(0, tmpPath.indexOf("/"));
        if (!this.$route.meta.link) {
          this.$store.dispatch('app/toggleSideBarHide', false);
        }
      } else if (!this.$route.children) {
        activePath = path;
        this.$store.dispatch('app/toggleSideBarHide', true);
      }
      this.activeRoutes(activePath);
      return activePath;
    },
  },
  beforeMount() {
    window.addEventListener('resize', this.initMenu)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.initMenu)
  },
  mounted() {
    const targetElement = document.getElementsByClassName("topmenu-container")[0]
    this.resizeObserver = new ResizeObserver((entries) => {
      // 遍历所有观察到的元素
      entries.forEach((entry) => {
        // 获取目标元素的宽度
        // const width = entry.contentRect.width;
        this.initMenuDebounced()
      });
    });

    // 开始观察目标元素
    this.resizeObserver.observe(targetElement);
    // this.initMenu();
  },
  methods: {
    getTextWidth(innerText) {
      let width = 0
      const span = document.createElement('span')
      span.innerText = innerText;
      span.className = `get-text-width-${innerText.length}-${Math.random()}`;
      // 向body添加每一行的节点 获取节点的宽度 完成之后在移除节点
      document.body.appendChild(span);
      width = span.offsetWidth;
      span.remove();
      return width;
    },


    // 原方法，加入防抖逻辑
    initMenu() {
      const routers = this.routers
        .map((menu) => {
          if (menu.hidden !== true) {
            // 兼容顶部栏一级菜单内部跳转
            return menu.path === "/" ? menu.children[0] : menu;
          }
        })
        .filter((item) => item);

      const menuContainer = document.getElementsByClassName("topmenu-container")[0];
      let currentWidth = 0;

      if (menuContainer) {
        const containerWidth = menuContainer.offsetWidth - 110;
        this.topMenus = [];
        this.moreMenus = [];
        routers.forEach((item) => {
          currentWidth += this.getTextWidth(item.meta.title) + 20 + 10 + 14;
          if (containerWidth < currentWidth) {
            this.moreMenus.push(item);
          } else {
            this.topMenus.push(item);
          }
        });
      }
    },

    // 包装带有防抖功能的方法
    initMenuDebounced: debounce(function () {
      this.initMenu();
    }, 300),

    // 菜单选择事件
    handleSelect(key, keyPath) {
      this.currentIndex = key;
      const route = this.routers.find(item => item.path === key);
      if (this.ishttp(key)) {
        // http(s):// 路径新窗口打开
        window.open(key, "_blank");
      } else if (!route || !route.children) {
        // 没有子路由路径内部打开
        const routeMenu = this.childrenMenus.find(item => item.path === key);
        if (routeMenu && routeMenu.query) {
          let query = JSON.parse(routeMenu.query);
          this.$router.push({ path: key, query: query });
        } else {
          this.$router.push({ path: key });
        }
        this.$store.dispatch('app/toggleSideBarHide', true);
      } else {
        // 显示左侧联动菜单
        this.activeRoutes(key);
        this.$store.dispatch('app/toggleSideBarHide', false);
      }
    },
    // 当前激活的路由
    activeRoutes(key) {
      const routes = [];
      if (this.childrenMenus && this.childrenMenus.length > 0) {
        this.childrenMenus.map((item) => {
          if (key == item.parentPath || (key == "index" && "" == item.path)) {
            routes.push(item);
          }
        });
      }
      if (routes.length > 0) {
        this.$store.commit("SET_SIDEBAR_ROUTERS", routes);
      } else {
        this.$store.dispatch('app/toggleSideBarHide', true);
      }
    },
    ishttp(url) {
      return url.indexOf('http://') !== -1 || url.indexOf('https://') !== -1
    }
  },
};
</script>

<style lang="scss">
.topmenu-container.el-menu--horizontal>.el-menu-item {
  float: left;
  height: 50px !important;
  line-height: 50px !important;
  color: #999093 !important;
  padding: 0 5px !important;
  margin: 0 10px !important;
}

.topmenu-container.el-menu--horizontal>.el-menu-item.is-active,
.el-menu--horizontal>.el-submenu.is-active .el-submenu__title {
  border-bottom: 2px solid #{'var(--theme)'} !important;
  color: #303133;
}

/* submenu item */
.topmenu-container.el-menu--horizontal>.el-submenu .el-submenu__title {
  float: left;
  height: 50px !important;
  line-height: 50px !important;
  color: #999093 !important;
  padding: 0 5px !important;
  margin: 0 10px !important;
}
</style>
