


// export default class BaseNode {
//     getConnectedSourceRules() {
//         const rules = super.getConnectedSourceRules();
//         const sameGroup = {
//             message: '分组内节点只能连接同分组节点',
//             validate: (sourceNode, targetNode) => {
//                 const sourceNodeId = sourceNode.id
//                 const targetNodeId = targetNode.id
//                 const nodeGroupMap = sourceNode.graphModel?.group?.nodeGroupMap
//                 return nodeGroupMap.get(sourceNodeId) === nodeGroupMap.get(targetNodeId)
//             },
//         };
//         rules.push(sameGroup);
//         return rules;
//     }
// }