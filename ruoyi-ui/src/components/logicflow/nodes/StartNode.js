
// import { CircleNode, CircleNodeModel } from "@logicflow/core";

// // 开始节点
// class StartNode extends CircleNode { }

// class StartModel extends CircleNodeModel {

//     initNodeData(data) {
//         super.initNodeData(data);
//     }
//     // 修改圆形节点的样式
//     getNodeStyle() {
//         const style = super.getNodeStyle();
//         style.stroke = '#2987ff';
//         return style;
//     }

//     getConnectedTargetRules() {
//         const rules = super.getConnectedTargetRules();
//         const notAsTarget = {
//             message: '起始节点不能作为边的终点',
//             validate: () => false,
//         };
//         rules.push(notAsTarget);
//         return rules;
//     }
// }

// export default {
//     type: "StartNode",
//     view: StartNode,
//     model: StartModel
// }

import { CircleNode, CircleNodeModel } from "@logicflow/core";

// 开始节点
class StartNode extends CircleNode { }

class StartModel extends CircleNodeModel {
    initNodeData(data) {
        super.initNodeData(data);
    }

    // 修改圆形节点的样式
    getNodeStyle() {
        const style = super.getNodeStyle();
        style.stroke = '#2987ff';
        return style;
    }
    /**
     * 不能作为边的终点
     * @returns 
     */
    getConnectedTargetRules() {
        const rules = super.getConnectedTargetRules();
        const notAsTarget = {
            message: '起始节点不能作为边的终点',
            validate: false
        };
        rules.push(notAsTarget);
        // 作为结束节点只能连接一条线
        const onlyOne = {
            message: '结束节点只能连接一条线',
            validate: (_sourceNode, targetNode) => {
                const edges = targetNode.graphModel.edges;
                return edges.length < 1
            }
        }
        rules.push(onlyOne)
        return rules
    }
    
    getConnectedSourceRules() {
        const rules = super.getConnectedSourceRules();
        // 必须同一分组才能链接
        const sameGroup = {
            message: '分组内节点只能连接同分组节点',
            validate: (sourceNode, targetNode) => {
                const sourceNodeId = sourceNode.id
                const targetNodeId = targetNode.id
                const nodeGroupMap = sourceNode.graphModel?.group?.nodeGroupMap
                return nodeGroupMap.get(sourceNodeId) === nodeGroupMap.get(targetNodeId)
            },
        };
        rules.push(sameGroup);
        return rules;
    }
}

export default {
    type: "StartNode",
    view: StartNode,
    model: StartModel
};