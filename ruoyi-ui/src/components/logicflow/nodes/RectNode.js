import { RectNode, RectNodeModel } from "@logicflow/core";

// 自定义矩形节点
class CustomRectNode extends RectNode {
  // getShape() {
  //   const { model, graphModel } = this.props;
  // }
}

// 自定义矩形节点
class CustomRectModel extends RectNodeModel {
  initNodeData(data) {
    super.initNodeData(data);
    // this.width = 60;
    // this.height = 50;
  }
  getNodeStyle() {
    const style = super.getNodeStyle();
    style.stroke = '#2987ff';
    return style;
  }


  getConnectedSourceRules() {
    const rules = super.getConnectedSourceRules();
    const sameGroup = {
      message: '分组内节点只能连接同分组节点',
      validate: (sourceNode, targetNode) => {
        const sourceNodeId = sourceNode.id
        const targetNodeId = targetNode.id
        const nodeGroupMap = sourceNode.graphModel?.group?.nodeGroupMap
        return nodeGroupMap.get(sourceNodeId) === nodeGroupMap.get(targetNodeId)
      },
    };
    rules.push(sameGroup);
    return rules;
  }
}

export default {
  type: "RectNode",
  view: CustomRectNode,
  model: CustomRectModel
}