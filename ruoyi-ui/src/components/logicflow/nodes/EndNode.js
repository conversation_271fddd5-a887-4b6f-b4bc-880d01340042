
import { CircleNode, CircleNodeModel, h } from "@logicflow/core";
import { get } from "jquery";

// 自定义结束节点
class EndNode extends CircleNode {

    getShape() {
        const { model, graphModel } = this.props;
        const { x, y, r } = model
        const { fill, stroke, strokeWidth } = model.getNodeStyle()
        return h('g', {},
            [
                h(
                    'circle',
                    {
                        cx: x,
                        cy: y,
                        r,
                        fill,
                        stroke,
                        strokeWidth,
                    }
                ),
                h(
                    'circle',
                    {
                        cx: x,
                        cy: y,
                        r: r - 10,
                        fill,
                        stroke,
                        strokeWidth,
                    }
                )
            ]
        )
    }

}

// 提供节点的样式
class EndModel extends CircleNodeModel {

    initNodeData(data) {
        super.initNodeData(data);
        this.r = 50;
    }

    getNodeStyle() {
        const style = super.getNodeStyle();
        style.stroke = '#2987ff';
        style.fill = 'transparent';
        style.strokeWidth = 2;
        return style;
    }

    getConnectedSourceRules() {
        const rules = super.getConnectedSourceRules()
        const notAsTarget = {
            message: '终止节点不能作为连线的起点',
            validate: () => false
        }
        rules.push(notAsTarget)
        // 必须同一分组才能链接
        const sameGroup = {
            message: '分组内节点只能连接同分组节点',
            validate: (sourceNode, targetNode) => {
                const sourceNodeId = sourceNode.id
                const targetNodeId = targetNode.id
                const nodeGroupMap = sourceNode.graphModel?.group?.nodeGroupMap
                return nodeGroupMap.get(sourceNodeId) === nodeGroupMap.get(targetNodeId)
            },
        };
        rules.push(sameGroup);
        return rules
    }

    getConnectedTargetRules() {
        const rules = super.getConnectedTargetRules()
        // 作为结束节点只能连接一条线
        // const onlyOne = {
        //     message: '结束节点只能连接一条线',
        //     validate: (_sourceNode, targetNode) => {
        //         console.log("targetNode--targetNode",targetNode);
                
        //         const edges = targetNode.graphModel.edges;
                

        //         return edges.length < 1
        //         return true
        //     }
        // }
        // rules.push(onlyOne)
        return rules
    }

}

export default {
    type: "EndNode",
    view: EndNode,
    model: EndModel
}