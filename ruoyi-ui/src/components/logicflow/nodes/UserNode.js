import { RectNode, RectNodeModel, h } from "@logicflow/core";

// 用户节点视图
class CustomUserNodeView extends RectNode {
    getShape() {
        const { model } = this.props;
        const { x, y, width, height } = model;
        const style = model.getNodeStyle();
        const iconSize = Math.min(width, height) * 0.4; // 图标大小

        return h(
            "g",
            null,
            [
                // 外边框矩形
                h("rect", {
                    x: x - width / 2,
                    y: y - height / 2,
                    width: width,
                    height: height,
                    rx: model.radius,
                    ry: model.radius,
                    fill: style.fill,
                    stroke: style.stroke,
                    strokeWidth: style.strokeWidth
                }),
                // 左上角的图标
                h(
                    "foreignObject",
                    {
                        x: x - width / 2 + 5, // 左边留5px边距
                        y: y - height / 2 + 5, // 上边留5px边距
                        width: iconSize,
                        height: iconSize
                    },
                    h(
                        "div",
                        {
                            xmlns: "http://www.w3.org/1999/xhtml",
                            style: {
                                width: "100%",
                                height: "100%",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center"
                            }
                        },
                        h("div", {
                            dangerouslySetInnerHTML: {
                                __html: `<svg width="${iconSize}" height="${iconSize}" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="24" cy="12" r="8" fill="none" stroke="#2987ff" stroke-width="4"/>
                                    <path d="M42 44C42 34.0589 33.9411 26 24 26C14.0589 26 6 34.0589 6 44" stroke="#2987ff" stroke-width="4"/>
                                </svg>`
                            }
                        })
                    )
                ),
                // 节点文本（放在中间偏右位置）
                h("text", {
                    x: x,
                    y: y,
                    textAnchor: "middle",
                    dominantBaseline: "middle",
                    fontSize: 12,
                    fill: "#333"
                }, model.text?.value)
            ]
        );
    }
}

// 用户节点模型
class CustomUserNodeModel extends RectNodeModel {
    initNodeData(data) {
        super.initNodeData(data);
        this.width = 120;  // 稍微加宽以便更好地显示文本
        this.height = 60;  // 稍微降低高度
        this.radius = 5;  // 圆角半径
    }

    getNodeStyle() {
        const style = super.getNodeStyle();
        style.stroke = '#2987ff';
        style.fill = '#f0f7ff';
        style.strokeWidth = 2;
        return style;
    }
    getConnectedSourceRules() {
        const rules = super.getConnectedSourceRules();
        const sameGroup = {
            message: '分组内节点只能连接同分组节点',
            validate: (sourceNode, targetNode) => {
                const sourceNodeId = sourceNode.id
                const targetNodeId = targetNode.id
                const nodeGroupMap = sourceNode.graphModel?.group?.nodeGroupMap
                return nodeGroupMap.get(sourceNodeId) === nodeGroupMap.get(targetNodeId)
            },
        };
        rules.push(sameGroup);
        return rules;
    }
}

export default {
    type: "CustomUserNode",
    view: CustomUserNodeView,
    model: CustomUserNodeModel
};