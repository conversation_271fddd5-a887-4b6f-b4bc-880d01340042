import { GroupNode } from "@logicflow/extension";

class CustomSubGroupView extends GroupNode.view { }


class CustomSubGroupModel extends GroupNode.model {


    initNodeData(data) {
        super.initNodeData(data);
        this.foldable = true;
        this.resizable = true;
        this.width = 400;
        this.height = 200;
    }

    getNodeStyle() {
        const style = super.getNodeStyle();
        style.stroke = '#989891';
        style.strokeWidth = 1;
        style.strokeDasharray = '3 3';
        if (this.isSelected) {
            style.stroke = 'rgb(124, 15, 255)';
        }
        if (this.isFolded) {
            style.fill = '#47C769';
        }
        return style;
    }

    getAnchorStyle() {
        const style = super.getAnchorStyle();
        style.stroke = "#acacac";
        style.fill = "#fff";
        return style;
    }

}

export default {
    type: "CustomSubGroup",
    model: CustomSubGroupModel,
    view: CustomSubGroupView
};