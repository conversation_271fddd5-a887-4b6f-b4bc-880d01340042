

import { GroupNode } from "@logicflow/extension";

class CustomGroupView extends GroupNode.view { }

class CustomGroupModel extends GroupNode.model {
    initNodeData(data) {
        super.initNodeData(data);
        this.isRestrict = true;
        this.resizable = true;
        this.width = 480;
        this.height = 280;
    }

    getNodeStyle() {
        const style = super.getNodeStyle();
        style.stroke = '#AEAFAE';
        style.strokeWidth = 1;
        return style;
    }

    getAnchorStyle() {
        const style = super.getAnchorStyle();
        style.stroke = "#acacac";
        style.fill = "#fff";
        return style;
    }


    getConnectedSourceRules() {
        const rules = super.getConnectedSourceRules();
        const groupRule = {
            message: '分组只能连接分组',
            validate: (_sourceNode, targetNode) => {
                if (targetNode.isGroup) {
                    // TODO 是否可以分组和子分组连接？
                    return true
                }
                return false
            },
        };
        rules.push(groupRule);
        return rules;
    }
}

export default {
    type: "CustomGroup",
    model: CustomGroupModel,
    view: CustomGroupView
};