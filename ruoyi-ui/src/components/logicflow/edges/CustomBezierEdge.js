import { BezierEdge, BezierEdgeModel } from "@logicflow/core";

class CustomBezierEdgeView extends BezierEdge {
    // 可以在这里自定义边的外观，如果需要的话
}

class CustomBezierEdgeModel extends BezierEdgeModel {
    // 初始化自定义边的属性
    initEdgeData(data) {
        super.initEdgeData(data);
        this.properties = {
            name: "我是自定义边"
        };
    }

    // 重写样式方法，设置边的颜色为红色
    getEdgeStyle() {
        const style = super.getEdgeStyle();
        style.stroke = '#3D8BFF';
        style.strokeWidth = 3;
        return style;
    }

    // 如果需要，可以重写获取边文本的方法
    getTextStyle() {
        const style = super.getTextStyle();
        return style;
    }

}

export default {
    type: "CustomBezierEdge",
    view: CustomBezierEdgeView,
    model: CustomBezierEdgeModel,
};