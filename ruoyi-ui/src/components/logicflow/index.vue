<template>
    <div class="flow-canvas">
        <div class="container" ref="container"></div>
        <!-- 节点面板 -->
        <CustomNodePanel v-if="lf" :lf="lf"></CustomNodePanel>
        <!-- 数据面板 -->
        <BpmnDataPanel v-if="lf" :lf="lf"></BpmnDataPanel>
        <!-- 弹窗编辑 -->
        <el-drawer title="编辑节点" :visible.sync="drawerVisible" direction="rtl">
            <el-form style="padding: 0 20px;" :model="formData" :rules="formRules" ref="logicflowFormRef"
                label-width="auto">
                <el-form-item label="节点名称" prop="name">
                    <template #label>
                        <span style="margin-right: 10px;">节点名称</span>
                        <el-tooltip class="item" effect="dark" content="我是文字提示" placement="top">
                            <i class="el-icon-question"></i>
                        </el-tooltip>
                    </template>
                    <el-input v-model="formData.name" placeholder="我是placeholder"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button @click="handleSaveProperty">保存</el-button>
                </el-form-item>
            </el-form>
        </el-drawer>
    </div>
</template>

<script>
import * as CustomNodes from "./nodes"
import * as CustomEdges from "./edges";
import * as CustomGroups from "./groups"
import LogicFlow from "@logicflow/core";
import "@logicflow/core/dist/style/index.css";
import "@logicflow/extension/lib/style/index.css";
import { Group, Control, Menu, DndPanel, SelectionSelect } from "@logicflow/extension";
import CustomNodePanel from "./components/CustomNodePanel.vue";
import BpmnDataPanel from "./components/BpnmDataPanel.vue";

export default {
    name: "logicflowCustom",
    data() {
        return {
            lf: null,
            drawerVisible: false,
            formData: {
                name: ""
            },
            formRules: {
                name: [
                    {
                        required: true,
                        message: "节点名称",
                        trigger: "blur"
                    }
                ]
            },
            currentComponentId: null
        };
    },
    components: { CustomNodePanel, BpmnDataPanel },

    mounted() {
        this.initlLf()
        function imageToBase64(url) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.crossOrigin = 'Anonymous'; // 处理跨域问题
                img.onload = () => {
                    const canvas = document.createElement('canvas');
                    canvas.width = img.width;
                    canvas.height = img.height;
                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(img, 0, 0);
                    const dataURL = canvas.toDataURL('image/png');
                    resolve(dataURL);
                };
                img.onerror = reject;
                img.src = url;
            });
        }

        // 使用示例
        imageToBase64('https://cdn.jsdelivr.net/gh/Logic-Flow/static@latest/docs/examples/extension/group/group.png')
            .then(base64 => {
                console.log(base64); // 输出 Base64 编码字符串
            })
            .catch(error => {
                console.error('转换失败:', error);
            });

    },
    methods: {

        async handleSaveProperty() {
            const valid = await this.$refs["logicflowFormRef"].validate().catch(() => false)
            if (!valid) return
            this.lf.updateText(this.currentComponentId, this.formData.name)
            this.lf.setProperties(this.currentComponentId, this.formData)
            this.drawerVisible = false
        },
        initlLf() {
            this.lf = new LogicFlow({
                container: this.$refs.container,
                grid: {
                    size: 10,
                    visible: true,
                    type: "mesh",
                    config: {
                        color: "#ababab",
                        thickness: 1,
                    },
                },
                plugins: [Group, Menu, Control, DndPanel, SelectionSelect],
            });
            this.addControItem()
            // 注册连线
            this.registerEdge()
            // 注册节点
            this.registerNode();
            // 注册分组
            this.registerGroup()
            // 注册事件
            this.registerEvent();

            this.lf.render({
                "nodes": [
                    {
                        "id": "124b2d78-d2cf-4b97-8ee9-19cff617157c",
                        "type": "CustomGroup",
                        "x": 490,
                        "y": 270,
                        "properties": {
                            "name": "分组分组",
                            "nodeSize": {
                                "width": 472,
                                "height": 510
                            }
                        },
                        "text": {
                            "x": 490,
                            "y": 270,
                            "value": "分组分组"
                        },
                        "children": [
                            "2cb4d001-b92f-4492-89d0-64cc3b7945f9",
                            "ebe15f48-8095-4324-a555-9a53eeee17de",
                            "e7ef5610-b6aa-465d-bbba-263ab9ae4362",
                            "f16dbcbe-6090-496b-a5d9-d41d5db4da46",
                            "0d2b9d9d-72ab-4b40-b102-6157dc4cb713"
                        ]
                    },
                    {
                        "id": "2cb4d001-b92f-4492-89d0-64cc3b7945f9",
                        "type": "StartNode",
                        "x": 364,
                        "y": 125,
                        "properties": {
                            "name": "开始"
                        },
                        "text": {
                            "x": 364,
                            "y": 125,
                            "value": "开始"
                        }
                    },
                    {
                        "id": "ebe15f48-8095-4324-a555-9a53eeee17de",
                        "type": "RectNode",
                        "x": 594,
                        "y": 115,
                        "properties": {
                            "name": "矩形"
                        },
                        "text": {
                            "x": 594,
                            "y": 115,
                            "value": "普通节点"
                        }
                    },
                    {
                        "id": "1fcc3ab7-568b-4202-a953-5ce700a88e55",
                        "type": "CustomUserNode",
                        "x": 860,
                        "y": 430,
                        "properties": {
                            "name": "用户节点"
                        },
                        "text": {
                            "x": 860,
                            "y": 430,
                            "value": "用户节点"
                        }
                    },
                    {
                        "id": "e7ef5610-b6aa-465d-bbba-263ab9ae4362",
                        "type": "CustomUserNode",
                        "x": 370,
                        "y": 240,
                        "properties": {
                            "name": "用户节点"
                        },
                        "text": {
                            "x": 370,
                            "y": 240,
                            "value": "用户节点"
                        }
                    },
                    {
                        "id": "f16dbcbe-6090-496b-a5d9-d41d5db4da46",
                        "type": "PolygonNode",
                        "x": 550,
                        "y": 250,
                        "properties": {
                            "name": "条件节点"
                        },
                        "text": {
                            "x": 550,
                            "y": 250,
                            "value": "条件节点"
                        }
                    },
                    {
                        "id": "0d2b9d9d-72ab-4b40-b102-6157dc4cb713",
                        "type": "EndNode",
                        "x": 390,
                        "y": 360,
                        "properties": {
                            "name": "结束节点"
                        },
                        "text": {
                            "x": 390,
                            "y": 360,
                            "value": "结束节点"
                        }
                    }
                ],
                "edges": []
            });
        },
        /**
         * 批量注册线
         */
        registerEdge() {
            Object.values(CustomEdges).forEach(customEdge => {
                this.lf.register(customEdge)
            })
            this.lf.setDefaultEdgeType("CustomBezierEdge")
        },

        /**
         * 批量注册节点
         */
        registerNode() {
            Object.values(CustomNodes).forEach(customNode => {
                this.lf.register(customNode)
            })
        },
        /**
         * 批量注册分组
         */
        registerGroup() {
            Object.values(CustomGroups).forEach(customGroup => {
                this.lf.register(customGroup)
            })
        },
        /**
         * 绑定事件
         */
        registerEvent() {
            const { eventCenter } = this.lf.graphModel;

            eventCenter.on("node:click,edge:click", (args) => {
                this.currentComponentId = args.data.id
                this.formData = args.data.properties;
                this.drawerVisible = true;
            });

            // 注册非业务事件，业务事件需要放在上一层
            this.lf.on("connection:not-allowed", (data) => {
                this.$message.error(data.msg)
            });
        },
        /**
         * 添加控制拦的按钮
         */
        addControItem() {
            // 控制面板-清空画布
            this.lf.extension.control.addItem({
                iconClass: 'lf-control-clear',
                title: 'clear',
                text: '清空',
                onClick: (lf) => {
                    lf.clearData()
                }
            })
            // 控制面板-保存画布
            this.lf.extension.control.addItem({
                iconClass: 'el-icon-document',
                title: 'save',
                text: '保存',
                onClick: (lf) => {
                    const rowData = lf.getGraphRawData()
                    console.log("rowData", JSON.stringify(rowData, null, 2));
                }
            });
            this.lf.extension.control.addItem({
                iconClass: 'el-icon-back',
                title: 'goback',
                text: '返回',
                onClick: (_lf) => {
                    this.$router.go(-1)
                }
            })
        }
    },
};
</script>

<style lang="scss">
.flow-canvas {
    position: relative;
    width: 100%;
    height: 100vh;
    margin: 0px;
    display: flex;


    .container {
        display: flex;
        flex-grow: 1;
        width: 100%;
        height: 100%;
        /* 关键 */
        border: 3px solid #ababab;
        overflow: hidden;
    }

    .lf-control-clear {
        background-image: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNjQ1Nzg5MTYyODczIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjIwNDYiIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PGRlZnM+PHN0eWxlIHR5cGU9InRleHQvY3NzIj48L3N0eWxlPjwvZGVmcz48cGF0aCBkPSJNODk5LjEgODY5LjZsLTUzLTMwNS42SDg2NGMxNC40IDAgMjYtMTEuNiAyNi0yNlYzNDZjMC0xNC40LTExLjYtMjYtMjYtMjZINjE4VjEzOGMwLTE0LjQtMTEuNi0yNi0yNi0yNkg0MzJjLTE0LjQgMC0yNiAxMS42LTI2IDI2djE4MkgxNjBjLTE0LjQgMC0yNiAxMS42LTI2IDI2djE5MmMwIDE0LjQgMTEuNiAyNiAyNiAyNmgxNy45bC01MyAzMDUuNmMtMC4zIDEuNS0wLjQgMy0wLjQgNC40IDAgMTQuNCAxMS42IDI2IDI2IDI2aDcyM2MxLjUgMCAzLTAuMSA0LjQtMC40IDE0LjItMi40IDIzLjctMTUuOSAyMS4yLTMwek0yMDQgMzkwaDI3MlYxODJoNzJ2MjA4aDI3MnYxMDRIMjA0VjM5MHogbTQ2OCA0NDBWNjc0YzAtNC40LTMuNi04LTgtOGgtNDhjLTQuNCAwLTggMy42LTggOHYxNTZINDE2VjY3NGMwLTQuNC0zLjYtOC04LThoLTQ4Yy00LjQgMC04IDMuNi04IDh2MTU2SDIwMi44bDQ1LjEtMjYwSDc3Nmw0NS4xIDI2MEg2NzJ6IiBwLWlkPSIyMDQ3Ij48L3BhdGg+PC9zdmc+');
    }
}
</style>