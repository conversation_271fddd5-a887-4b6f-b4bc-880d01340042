<template>
  <div class="between">
    <el-form label-position="left" ref="form" :model="form" label-width="120px" :rules="rules" size="small" :disabled="disabled">
      <el-tabs v-model="activeName" @tab-click="handleClick" style="margin: auto 20px">
        <el-tab-pane label="基础设置" name="0" v-if="!hideFirstTab">
      <slot name="form-item-task-nodeCode" :model="form" field="nodeCode">
        <el-form-item label="节点编码">
          <el-input v-model="form.nodeCode" :disabled="disabled"></el-input>
        </el-form-item>
      </slot>
      <slot name="form-item-task-nodeName" :model="form" field="nodeName">
        <el-form-item label="节点名称">
          <el-input v-model="form.nodeName" :disabled="disabled"></el-input>
        </el-form-item>
      </slot>
      <slot name="form-item-task-collaborativeWay" :model="form" field="collaborativeWay">
        <el-form-item label="协作方式">
          <el-radio-group v-model="form.collaborativeWay" @change="collaborativeWayChange">
            <el-radio label="1">
              或签
              <el-tooltip class="item" effect="dark" content="只需一个人审批">
                <i class="el-icon-question" style="color: #000"></i>
              </el-tooltip>
            </el-radio>
            <el-radio label="2">
              票签
              <el-tooltip class="item" effect="dark" content="部分办理人审批，只支持选择用户">
                <i class="el-icon-question" style="color: #000"></i>
              </el-tooltip>
            </el-radio>
            <el-radio label="3">
              会签
              <el-tooltip class="item" effect="dark" content="所有办理都需要审批，只支持选择用户">
                <i class="el-icon-question" style="color: #000"></i>
              </el-tooltip>
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </slot>
      <slot name="form-item-task-nodeRatio" :model="form" field="nodeRatio" v-if="form.collaborativeWay === '2'">
        <el-form-item label="票签占比" prop="nodeRatio">
          <el-input v-model="form.nodeRatio" type="number" placeholder="请输入"></el-input>
          <div class="placeholder">票签比例范围：(0-100)的值</div>
        </el-form-item>
      </slot>
      <slot name="form-item-task-approvalMode" :model="form" field="approvalMode">
        <el-form-item label="审批模式选择">
          <el-select v-model="form.approvalMode" @change = "clearPermissionFlagName">
            <el-option v-for="dict in dict.type.approval_mode" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
      </slot>
      <slot name="form-item-task-approvalField" :model="form" field="approvalField" v-if="form.approvalMode === '4'">
        <el-form-item label="字段名称">
          <el-input v-model="form.approvalField" :disabled="disabled"></el-input>
        </el-form-item>
      </slot>
      <slot name="form-item-task-permissionFlag" :model="form" field="permissionFlag" v-if="form.approvalMode !== '4'">
        <el-form-item label="审批人">
          <el-input v-model="form.permissionFlagName" disabled></el-input>
          <el-button class="btn" @click="initUser">选择</el-button>
        </el-form-item>
      </slot>
      <slot name="form-item-task-skipAnyNode" :model="form" field="skipAnyNode">
        <el-form-item label="是否可以跳转任意节点">
          <el-radio-group v-model="form.skipAnyNode">
            <el-radio label="N">否</el-radio>
            <el-radio label="Y">是</el-radio>
          </el-radio-group>
        </el-form-item>
      </slot>
        </el-tab-pane>
        <el-tab-pane label="按钮配置" name="1" v-if="!hideFirstTab">
          <slot name="form-item-task-updateType" :model="form" field="updateType">
            <el-form-item label="是否可以修改数据">
              <el-radio-group v-model="form.updateType">
                <el-radio label="N">否</el-radio>
                <el-radio label="Y">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </slot>
          <slot name="form-item-task-uploadFiles" :model="form" field="uploadFiles">
            <el-form-item label="是否可以上传文件">
              <el-radio-group v-model="form.uploadFiles">
                <el-radio label="N">否</el-radio>
                <el-radio label="Y">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </slot>
          <slot name="form-item-task-isReturn" :model="form" field="isReturn">
            <el-form-item label="是否可以退回">
              <el-radio-group v-model="form.isReturn">
                <el-radio label="N">否</el-radio>
                <el-radio label="Y">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </slot>
          <slot name="form-item-task-isCountersign" :model="form" field="isCountersign">
            <el-form-item label="是否可以加签">
              <el-radio-group v-model="form.isCountersign">
                <el-radio label="N">否</el-radio>
                <el-radio label="Y">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </slot>
          <slot name="form-item-task-isTransfer" :model="form" field="isTransfer">
            <el-form-item label="是否可以转办">
              <el-radio-group v-model="form.isTransfer">
                <el-radio label="N">否</el-radio>
                <el-radio label="Y">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </slot>
          <slot name="form-item-task-passName" :model="form" field="passName">
            <el-form-item label="通过按钮名称配置">
              <el-input v-model="form.passName" :disabled="disabled"></el-input>
            </el-form-item>
          </slot>
          <slot name="form-item-task-returnName" :model="form" field="returnName">
            <el-form-item label="退回按钮名称配置">
              <el-input v-model="form.returnName" :disabled="disabled"></el-input>
            </el-form-item>
          </slot>
        </el-tab-pane>
        <el-tab-pane label="监听设置" name="2" v-if="!hideFirstTab">
          <slot name="form-item-task-listenerType" :model="form" field="listenerType">
            <el-form-item label="监听器类型">
              <el-select v-model="form.listenerType" multiple>
                <el-option label="任务创建" value="create"></el-option>
                <el-option label="任务开始办理" value="start"></el-option>
                <el-option label="在任务被分配给用户或组时触发" value="assignment"></el-option>
                <el-option label="权限认证" value="permission"></el-option>
                <el-option label="任务完成" value="finish"></el-option>
              </el-select>
            </el-form-item>
          </slot>
          <slot name="form-item-task-listenerPath" :model="form" field="listenerPath">
            <el-form-item label="监听器路径" description="输入监听器的路径，以@@分隔,顺序与监听器类型一致">
              <el-input type="textarea" v-model="form.listenerPath" rows="8"></el-input>
              <el-tooltip class="item" effect="dark" content="输入监听器的路径，以@@分隔，顺序与监听器类型一致">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </el-form-item>
          </slot>
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <!-- 权限标识：会签票签选择用户 -->
    <el-dialog title="用户选择" :visible.sync="userVisible" width="90%" append-to-body>
      <selectUser  v-if="userVisible" :userIds="form.permissionFlagName" :selectUser.sync="form.permissionFlag"
        :userVisible.sync="userVisible" @handleUserSelect="handleUserSelect"></selectUser>
    </el-dialog>
  </div>
</template>

<script>
import selectUser from '@/views/components/selectUser.vue'

export default {
  name: 'Between',
  dicts: ['approval_mode'],
  components: {
    selectUser
  },
  props: {
    value: {
      type: Object,
      default() {
        return {}
      }
    },
    disabled: { // 是否禁止
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: this.value,
      activeName: '0',
      hideFirstTab: false,
      rules: {
        nodeRatio: [
          { required: false, message: "请输入", trigger: "change" },
          {
            pattern: /^(?:[1-9]\d?|0\.\d{1,3}|[1-9]\d?\.\d{1,3})$/, message: "请输入(0, 100)的值，最多保留三位小数",
            trigger: ["change", "blur"]
          }
        ],
        approvalMode: [
          { required: true, message: "审批模式不能为空", trigger: "change" },
        ],
      },
      userVisible: false
    }
  },
  watch: {
    // value: {
    //   handler(n) {
    //     this.form = JSON.parse(JSON.stringify(n))
    //   },
    //   deep: true,
    //   immediate: true
    // },
    form: {
      handler(n) {
        this.$emit('change', n)
      },
      hideFirstTab(newValue) {
        if (newValue) {
          this.activeName = '1';
        } else {
          this.activeName = '0';
        }
      },
      deep: true
    }
  },
  created() {
  },
  methods: {
    collaborativeWayChange(val) {
      this.form.permissionFlag = ''
      this.$set(this.form, 'nodeRatio', val === '1' ? '0.000' : val === '3' ? '100.000' : '')
    },
    // 切换tab页
    handleClick(tab) {

    },
    // 打开用户选择弹窗
    initUser() {
      this.userVisible = true
    },
    // 获取选中用户数据
    handleUserSelect(checkedItemList) {
      this.form.permissionFlag = checkedItemList.map(e => {
        return e.userId
      }).join(',')
      this.form.permissionFlagName = checkedItemList.map(e => {
        return e.userName + ':' + e.nickName
      }).join(',')
      this.$emit('input', JSON.parse(JSON.stringify(this.form)));
    },
    clearPermissionFlagName(){
      this.form.permissionFlagName = ''
      this.form.permissionFlag = ''
    }
  }
}
</script>

<style scoped>
.dialogSelect {
  display: none;
}

.placeholder {
  color: #828f9e;
  font-size: 12px;
}
</style>
