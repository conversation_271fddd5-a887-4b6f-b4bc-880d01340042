import tab from './tab'
import auth from './auth'
import cache from './cache'
import modal from './modal'
import download from './download'
import commonUtil from "./common"
import selectUtil from "./select-value"
import filterRules from "./filter-rules" 
import commonConstants from "./constants"
import reportApis from "./report"
export default {
  install(Vue) {
    // 页签操作
    Vue.prototype.$tab = tab
    // 认证对象
    Vue.prototype.$auth = auth
    // 缓存对象
    Vue.prototype.$cache = cache
    // 模态框对象
    Vue.prototype.$modal = modal
    // 下载文件
    Vue.prototype.$download = download
    // springreport common 处理工具
    Vue.prototype.$commonUtil = commonUtil
    // springreport selectUtil 工具
    Vue.prototype.$selectUtil = selectUtil
    // springreport $filterRules 工具
    Vue.prototype.$filterRules = filterRules
    // springreport commonConstants 工具
    Vue.prototype.$commonConstants = commonConstants
    // springreport 请求接口
    Vue.prototype.$reportApis = reportApis
  }
}
