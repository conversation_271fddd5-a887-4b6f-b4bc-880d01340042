import request from '@/utils/request'

// 登录方法
export function login(username, password, code, uuid,forcedLogin) {
  return request({
    url: '/auth/login',
    headers: {
      isToken: false,
      repeatSubmit: false
    },
    method: 'post',
    data: { username, password, code, uuid,forcedLogin }
  })
}

// 注册方法
export function register(data) {
  return request({
    url: '/auth/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 刷新方法
export function refreshToken() {
  return request({
    url: '/auth/refresh',
    method: 'post'
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/system/user/getInfo',
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/auth/logout',
    method: 'delete'
  })
}

// 根据token退出方法
export function logoutByToken(token) {
  return request({
    url: '/auth/logout/' + token,
    method: 'delete'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/code',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}

// 校验用户是否登录
export function checkLogin(userName) {
  const url = `/system/online/check?userName=${userName}`
  return request({
    url,
    method: 'get'
  })
}

// 清退用户
export function clearUsers(userName) {
  const url = `/system/online/forceLogoutByUserName?userName=${userName}`
  return request({
    url,
    method: 'post'
  })
}

//查询钉钉信息
export function dingLogin(code) {
  return request({
    url: '/auth/dingLogin?code=' + code,
    headers: {
      isToken: false
    },
    method: 'post'
  })
}
