import request from '@/utils/request'

// 查询formImportTemplate列表
export function listFormImportTemplate(query) {
  return request({
    url: '/codeDev/formImportTemplate/list',
    method: 'get',
    params: query
  })
}

// 查询formImportTemplate详细
export function getFormImportTemplate(id) {
  return request({
    url: '/codeDev/formImportTemplate/' + id,
    method: 'get'
  })
}

// 新增formImportTemplate
export function addFormImportTemplate(data) {
  return request({
    url: '/codeDev/formImportTemplate',
    method: 'post',
    data: data
  })
}

// 修改formImportTemplate
export function updateFormImportTemplate(data) {
  return request({
    url: '/codeDev/formImportTemplate',
    method: 'put',
    data: data
  })
}

// 删除formImportTemplate
export function delFormImportTemplate(id) {
  return request({
    url: '/codeDev/formImportTemplate/' + id,
    method: 'delete'
  })
}
