import request from '@/utils/request'

// 查询表单设计列表
export function listFormDesign(query) {
  return request({
    url: '/codeDev/formDesign/list',
    method: 'get',
    params: query
  })
}


// 查询表单设计列表
export function getFormDesignList(query) {
  return request({
    url: '/codeDev/formDesign/getFormDesignList',
    method: 'post',
    data: query
  })
}

// 查询表单设计详细
export function getFormDesign(id) {
  return request({
    url: '/codeDev/formDesign/' + id,
    method: 'get'
  })
}

// 新增表单设计
export function addFormDesign(data) {
  return request({
    url: '/codeDev/formDesign',
    method: 'post',
    data: data
  })
}

// 修改表单设计
export function updateFormDesign(data) {
  return request({
    url: '/codeDev/formDesign',
    method: 'put',
    data: data
  })
}

// 删除表单设计
export function delFormDesign(id) {
  return request({
    url: '/codeDev/formDesign/' + id,
    method: 'delete'
  })
}

/** 获取所有表单设计中的表编码 */
export function getAllTableCodeFromFormDesign() {
  return request({
    url: '/codeDev/formDesign/getAllTableCodeFromFormDesign',
    method: 'get'
  })
}
//查询流程设计列表
export function listWarmFlow(query) {
  return request({
    url: '/flow/definition/publishList',
    method: 'get',
    params: query
  })
}

// 获取表搜索头配置
export function getSearchConfigList(id) {
  return request({
    url: `/codeDev/formDesign/getFormDesign/${id}`,
    method: 'get'
  })
}

export function getButtonPermissions(tableCode) {
  return request({
    url: `/codeDev/formDesign/getButtonPermissions/${tableCode}`,
    method: 'get'
  })
}


/**
 * 获取当前角色未分配的报表
 * @returns 
 */
export function getUnassignedReportsForRole(roleCode) {
  return request({
    url: '/codeDev/formDesign/listByRoleCode',
    method: 'get',
    params:{
      roleCode
    }
  })
}

export function getTableSort(formId) {
  return request({
    url: `/codeDev/sort/form/${formId}`,
    method: 'get'
  })
}

export function updateTableSort(data) {
  return request({
    url: '/codeDev/sort/batch',
    method: 'post',
    data: data
  })
}

export function deleteTableSort(id) {
  return request({
    url: `/codeDev/sort/${id}`,
    method: 'delete',
  })
}
