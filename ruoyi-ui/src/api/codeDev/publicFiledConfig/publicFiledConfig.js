import request from '@/utils/request'

// 查询publicFieldConfig列表
export function listPublicFieldConfig(query) {
  return request({
    url: '/codeDev/publicFieldConfig/list',
    method: 'get',
    params: query
  })
}

// 查询publicFieldConfig详细
export function getPublicFieldConfig(id) {
  return request({
    url: '/codeDev/publicFieldConfig/' + id,
    method: 'get'
  })
}

// 新增publicFieldConfig
export function addPublicFieldConfig(data) {
  return request({
    url: '/codeDev/publicFieldConfig',
    method: 'post',
    data: data
  })
}

// 修改publicFieldConfig
export function updatePublicFieldConfig(data) {
  return request({
    url: '/codeDev/publicFieldConfig',
    method: 'put',
    data: data
  })
}

// 删除publicFieldConfig
export function delPublicFieldConfig(id) {
  return request({
    url: '/codeDev/publicFieldConfig/' + id,
    method: 'delete'
  })
}
