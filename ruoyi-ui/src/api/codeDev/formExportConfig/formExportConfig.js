import request from '@/utils/request'

// 查询formExportConfig列表
export function listFormExportConfig(query) {
  return request({
    url: '/codeDev/formExportConfig/list',
    method: 'get',
    params: query
  })
}

// 查询formExportConfig详细
export function getFormExportConfig(id) {
  return request({
    url: '/codeDev/formExportConfig/' + id,
    method: 'get'
  })
}

// 新增formExportConfig
export function addFormExportConfig(data) {
  return request({
    url: '/codeDev/formExportConfig',
    method: 'post',
    data: data
  })
}

// 修改formExportConfig
export function updateFormExportConfig(data) {
  return request({
    url: '/codeDev/formExportConfig',
    method: 'put',
    data: data
  })
}

// 删除formExportConfig
export function delFormExportConfig(id) {
  return request({
    url: '/codeDev/formExportConfig/' + id,
    method: 'delete'
  })
}


// 修改formExportConfig
export function updateFormExportConfigBatch(data) {
  return request({
    url: '/codeDev/formExportConfig/updateFormExportConfigBatch',
    method: 'put',
    data: data
  })
}

// 获取应用列表配置顺序
export function getFieldSortList(id) {
  return request({
    url: '/codeDev/formExportConfig/getFieldSortList/' + id,
    method: 'get'
  })
}
