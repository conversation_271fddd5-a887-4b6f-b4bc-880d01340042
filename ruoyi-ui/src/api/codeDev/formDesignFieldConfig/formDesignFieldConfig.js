import request from '@/utils/request'

// 查询formDesignFieldConfig列表
export function listFormDesignFieldConfig(query) {
  return request({
    url: '/codeDev/formDesignFieldConfig/list',
    method: 'get',
    params: query
  })
}

// 查询formDesignFieldConfig详细
export function getFormDesignFieldConfig(id) {
  return request({
    url: '/codeDev/formDesignFieldConfig/' + id,
    method: 'get'
  })
}


// 查询表单设计的公共字段信息 且与最新的字典数据合并
export function queryPublicFieldWithDictInfo(data) {
  return request({
    url: '/codeDev/queryPublicFieldWithDictInfo',
    method: 'post',
    data: data
  })
}

// 新增formDesignFieldConfig
export function addFormDesignFieldConfig(data) {
  return request({
    url: '/codeDev/formDesignFieldConfig',
    method: 'post',
    data: data
  })
}

// 修改formDesignFieldConfig
export function updateFormDesignFieldConfig(data) {
  return request({
    url: '/codeDev/formDesignFieldConfig/batch',
    method: 'put',
    data: data
  })
}

// 删除formDesignFieldConfig
export function delFormDesignFieldConfig(id) {
  return request({
    url: '/codeDev/formDesignFieldConfig/' + id,
    method: 'delete'
  })
}

