import request from '@/utils/request'

// 查询dataLock列表
export function listDataLock(query) {
  return request({
    url: '/codeDev/dataLock/list',
    method: 'get',
    params: query
  })
}

// 查询dataLock详细
export function getDataLock(id) {
  return request({
    url: '/codeDev/dataLock/' + id,
    method: 'get'
  })
}

// 新增dataLock
export function addDataLock(data) {
  return request({
    url: '/codeDev/dataLock',
    method: 'post',
    data: data
  })
}

// 修改dataLock
export function updateDataLock(data) {
  return request({
    url: '/codeDev/dataLock',
    method: 'put',
    data: data
  })
}

// 删除dataLock
export function delDataLock(id) {
  return request({
    url: '/codeDev/dataLock/' + id,
    method: 'delete'
  })
}
