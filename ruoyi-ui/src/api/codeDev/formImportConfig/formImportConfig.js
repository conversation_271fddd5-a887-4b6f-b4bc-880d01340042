import request from '@/utils/request'

// 查询formImportConfig列表
export function listFormImportConfig(query) {
  return request({
    url: '/codeDev/formImportConfig/list',
    method: 'get',
    params: query
  })
}

// 查询formImportConfig详细
export function getFormImportConfig(id) {
  return request({
    url: '/codeDev/formImportConfig/' + id,
    method: 'get'
  })
}

// 新增formImportConfig
export function addFormImportConfig(data) {
  return request({
    url: '/codeDev/formImportConfig',
    method: 'post',
    data: data
  })
}

// 修改formImportConfig
export function updateFormImportConfig(data) {
  return request({
    url: '/codeDev/formImportConfig',
    method: 'put',
    data: data
  })
}

// 删除formImportConfig
export function delFormImportConfig(id) {
  return request({
    url: '/codeDev/formImportConfig/' + id,
    method: 'delete'
  })
}
