import request from '@/utils/request'

// 表单设计完后 根据表单的字段 在mongoDB中创建字段
export function createCollectionColumns(data) {
  return request({
    url: '/codeDev/formData/createCollectionColumns',
    method: 'post',
    data: data
  })
}

// 表单设计完后 根据表单的字段 在mongoDB中创建字段
export function getFormDataList(data) {
  return request({
    url: '/codeDev/formData/getFormDataList',
    method: 'post',
    data: data
  })
}

// 表单数据新增
export function insertFormData(data) {
  return request({
    url: '/codeDev/formData/insertFormData',
    method: 'post',
    data: data
  })
}

// 表单数据新增
export function updateFormData(data) {
  return request({
    url: '/codeDev/formData/updateFormData',
    method: 'post',
    data: data
  })
}

// 导出文件
export function downloadFileData(data) {
  return request({
    url: '/codeDev/formData/exportFormData',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json' // 设置请求头
    }
  })
}

// 表单数据删除
export function deleteFormDataById(data) {
  return request({
    url: '/codeDev/formData/deleteFormDataById',
    method: 'post',
    data: data
  })
}

// 表单数据批量删除
export function deleteFormDataBatch(data) {
  return request({
    url: '/codeDev/formData/deleteFormDataBatch',
    method: 'post',
    data: data
  })
}

// 获取开启默认查询值配置的字段及类型
export function findFieldConfigByFormDesignId(designId) {
  return request({
    url: '/codeDev/formData/findFieldConfigByFormDesignId?designId=' + designId,
    method: 'get'
  })
}

// 获取date类型且开启时间范围查询的字段
export function findDateTypeFieldsByFormDesignId(designId) {
  return request({
    url: '/codeDev/formData/findDateTypeFieldsByFormDesignId?designId=' + designId,
    method: 'get'
  })
}

// 删除Mongodb的表
export function deleteMongodbCollection(data) {
  return request({
    url: '/codeDev/formData/deleteMongodbCollection',
    method: 'post',
    data: data
  })
}

// 导出数据层（含审批历史）
export function exportFormDataApproval(data) {
  return request({
    url: '/codeDev/formData/exportFormDataApproval',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}

