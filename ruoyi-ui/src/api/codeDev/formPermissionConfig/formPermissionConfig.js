import request from '@/utils/request'

// 查询数据权限配置列表
export function listPermissionConfig(query) {
  return request({
    url: '/codeDev/permissionConfig/list',
    method: 'get',
    params: query
  })
}

// 查询数据权限配置详细
export function getPermissionConfig(id) {
  return request({
    url: '/codeDev/permissionConfig/' + id,
    method: 'get'
  })
}

// 新增数据权限配置
export function addPermissionConfig(data) {
  return request({
    url: '/codeDev/permissionConfig',
    method: 'post',
    data: data
  })
}

// 修改数据权限配置
export function updatePermissionConfig(data) {
  return request({
    url: '/codeDev/permissionConfig',
    method: 'put',
    data: data
  })
}

// 删除数据权限配置
export function delPermissionConfig(id) {
  return request({
    url: '/codeDev/permissionConfig/' + id,
    method: 'delete'
  })
}


