import request from '@/utils/request'


export function list(params) {
  return request({
    url: '/datax/api/createTableJobInfo',
    method: 'get',
    params
  })
}

export function updated(data) {
  return request({
    url: '/datax/api/createTableJobInfo',
    method: 'put',
    data
  })
}

export function created(data) {
  return request({
    url: '/datax/api/createTableJobInfo',
    method: 'post',
    data: data
  })
}

export function deleted(data) {
  return request({
    url: '/datax/api/createTableJobInfo',
    method: 'delete',
    params: data
  })
}

// 从表单设计器获取字段
export function getFields(params) {
  return request({
    url: '/data/dts/metadata/getFields',
    method: 'get',
    params
  })
}

export function getColumns(params) {
  return request({
    url: '/data/dts/metadata/getColumns',
    method: 'get',
    params
  })
}

// export function getJobProjectList(params) {
//   return request({
//     url: '/datax/api/jobProject/list',
//     method: 'get',
//     params
//   })
// }
