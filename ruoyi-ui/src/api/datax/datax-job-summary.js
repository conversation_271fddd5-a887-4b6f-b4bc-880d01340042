import request from '@/utils/request'

// datax插件api

export function getList(params) {
  return request({
    url: '/datax/api/job/summary/pageList',
    method: 'get',
    params
  })
}

export function triggerJob(data) {
  return request({
    url: '/datax/api/job/trigger',
    method: 'post',
    data
  })
}

export function startJob(id) {
  return request({
    url: '/datax/api/job/summary/start?id=' + id,
    method: 'post'
  })
}

export function stopJob(id) {
  return request({
    url: '/datax/api/job/summary/stop?id=' + id,
    method: 'post'
  })
}

export function getExecutorList() {
  return request({
    url: '/datax/api/jobGroup/list',
    method: 'get'
  })
}

export function updateJob(data) {
  return request({
    url: '/datax/api/job/summary/update',
    method: 'post',
    data
  })
}

export function createJob(data) {
  return request({
    url: '/datax/api/job/summary/add/',
    method: 'post',
    data
  })
}

export function removeJob(id) {
  return request({
    url: '/datax/api/job/summary/remove/' + id,
    method: 'post'
  })
}

export function nextTriggerTime(cron) {
  return request({
    url: '/datax/api/job/summary/nextTriggerTime?cron=' + cron,
    method: 'get'
  })
}
export function recentExecuteStatus(jobId) {
  return request({
    url: '/datax/api/job/summary/recentExecuteStatus/' + jobId,
    method: 'GET'
  })
}
export function queryChildTask(parentJobId) {
  return request({
    url: '/datax/api/job/summary/queryChildTask/' + parentJobId,
    method: 'GET'
  })
}
export function viewJobLog(id) {
  return request({
    url: '/datax/api/log/logDetailCat?id=' + id,
    method: 'get'
  })
}

export function getUsersList(params) {
  return request({
    url: '/datax/api/user/list',
    method: 'get',
    params
  })
}

export function getJobIdList(params) {
  return request({
    url: '/datax/api/job/summary/list',
    method: 'get',
    params
  })
}
// batchAdd
export function batchAddJob(data) {
  return request({
    url: '/datax/api/job/summary/batchAdd',
    method: 'post',
    data
  })
}

