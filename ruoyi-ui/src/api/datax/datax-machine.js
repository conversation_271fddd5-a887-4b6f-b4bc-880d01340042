import request from '@/utils/request'

// 执行器管理

export function getList() {
  return request({
    url: '/datax/api/machine/query',
    method: 'get'
  })
}

export function created(data) {
  return request({
    url: '/datax/api/machine/add',
    method: 'post',
    data
  })
}

export function updated(data) {
  return request({
    url: '/datax/api/machine/update',
    method: 'put',
    data
  })
}

export function deleted(data) {
  return request({
    url: '/datax/api/machine/delete',
    method: 'delete',
    params: data
  })
}


export function updateStatus(data) {
  return request({
    url: '/datax/api/machine/updateStatus',
    method: 'post',
    data
  })
}



