import request from '@/utils/request'


export function list(params) {
  return request({
    url: '/datax/api/createTableJobLog',
    method: 'get',
    params
  })
}

export function updated(data) {
  return request({
    url: '/datax/api/createTableJobLog',
    method: 'put',
    data
  })
}

export function created(data) {
  return request({
    url: '/datax/api/createTableJobLog',
    method: 'post',
    data: data
  })
}

export function deleted(data) {
  return request({
    url: '/datax/api/createTableJobLog',
    method: 'delete',
    params: data
  })
}
export function clearLog(type) {
  return request({
    url: '/datax/api/createTableJobLog/clearLog?type=' + type,
    method: 'post'
  })
}

// export function excuteJob(data) {
//   return request({
//     url: '/datax/api/qualityJobInfo/excuteJob',
//     method: 'post',
//     data: data
//   })
// }

// export function getJobProjectList(params) {
//   return request({
//     url: '/datax/api/jobProject/list',
//     method: 'get',
//     params
//   })
// }
