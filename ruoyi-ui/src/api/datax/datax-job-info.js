import request from '@/utils/request'

// datax插件api

export function getList(params) {
  return request({
    url: '/datax/api/job/pageList',
    method: 'get',
    params
  })
}

export function triggerJob(data) {
  return request({
    url: '/data/dts/job/trigger',
    method: 'post',
    data
  })
}

export function startJob(id) {
  return request({
    url: '/datax/api/job/start?id=' + id,
    method: 'post'
  })
}

export function stopJob(id) {
  return request({
    url: '/datax/api/job/stop?id=' + id,
    method: 'post'
  })
}

export function startWfJob(id) {
  return request({
    url: '/datax/api/job/wfStart?id=' + id,
    method: 'post'
  })
}

export function stopWfJob(id) {
  return request({
    url: '/datax/api/job/wfStop?id=' + id,
    method: 'post'
  })
}

export function getExecutorList() {
  return request({
    url: '/datax/api/jobGroup/list',
    method: 'get'
  })
}

export function updateJob(data) {
  return request({
    url: '/datax/api/job/update',
    method: 'post',
    data
  })
}

export function createJob(data) {
  return request({
    url: '/datax/api/job/add/',
    method: 'post',
    data
  })
}

export function removeJob(id) {
  return request({
    url: '/datax/api/job/remove/' + id,
    method: 'post'
  })
}

export function nextTriggerTime(cron) {
  return request({
    url: '/datax/api/job/nextTriggerTime?cron=' + cron,
    method: 'get'
  })
}

export function nextIncrementTime(cron) {
  return request({
    url: '/datax/api/job/nextIncrementTime?cron=' + cron,
    method: 'get'
  })
}

export function previousIncrementTime(cron) {
  return request({
    url: '/datax/api/job/previousIncrementTime?cron=' + cron,
    method: 'get'
  })
}

export function viewJobLog(id) {
  return request({
    url: '/datax/api/log/logDetailCat?id=' + id,
    method: 'get'
  })
}

export function getUsersList(params) {
  return request({
    url: '/datax/api/user/list',
    method: 'get',
    params
  })
}

export function getJobIdList(params) {
  return request({
    url: '/datax/api/job/list',
    method: 'get',
    params
  })
}
// batchAdd
export function batchAddJob(data) {
  return request({
    url: '/datax/api/job/batchAdd',
    method: 'post',
    data
  })
}
export function getParentJobList() {
  return request({
    url: '/datax/api/job/getParentJobList',
    method: 'get'
  })
}

