import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/dbms/group/pageList',
    method: 'get',
    params
  })
}

export function updateGroup(data) {
  return request({
    url: '/dbms/group',
    method: 'put',
    data
  })
}

export function createGroup(data) {
  return request({
    url: '/dbms/group',
    method: 'post',
    data
  })
}

export function removeGroup(data) {
  return request({
    url: '/dbms/group',
    method: 'delete',
    params: data
  })
}

export function getDataList() {
  return request({
    url: '/dbms/group/list',
    method: 'get'
  })
}

export function triggerJob(data) {
  return request({
    url: '/dbms/group/trigger',
    method: 'post',
    data
  })
}

