import request from '@/utils/request'

// datax插件api

export function getList(params) {
  return request({
    url: '/dbms/api/pageList',
    method: 'get',
    params
  })
}

export function triggerJob(data) {
  return request({
    url: '/data/dts/job/trigger',
    method: 'post',
    data
  })
}

export function startJob(id) {
  return request({
    url: '/dbms/api/start?id=' + id,
    method: 'post'
  })
}

export function stopJob(id) {
  return request({
    url: '/dbms/api/stop?id=' + id,
    method: 'post'
  })
}

export function startWfJob(id) {
  return request({
    url: '/dbms/api/wfStart?id=' + id,
    method: 'post'
  })
}

export function stopWfJob(id) {
  return request({
    url: '/dbms/api/wfStop?id=' + id,
    method: 'post'
  })
}

export function getExecutorList() {
  return request({
    url: '/datax/api/jobGroup/list',
    method: 'get'
  })
}

export function updateJob(data) {
  return request({
    url: '/dbms/api/update',
    method: 'post',
    data
  })
}

export function createJob(data) {
  return request({
    url: '/dbms/api/add',
    method: 'post',
    data
  })
}

export function removeJob(id) {
  return request({
    url: '/dbms/api/remove/' + id,
    method: 'post'
  })
}

export function nextTriggerTime(cron) {
  return request({
    url: '/datax/api/job/nextTriggerTime?cron=' + cron,
    method: 'get'
  })
}
export function viewJobLog(id) {
  return request({
    url: '/datax/api/log/logDetailCat?id=' + id,
    method: 'get'
  })
}

export function getUsersList(params) {
  return request({
    url: '/datax/api/user/list',
    method: 'get',
    params
  })
}

export function getJobIdList(params) {
  return request({
    url: '/dbms/api/list',
    method: 'get',
    params
  })
}
// batchAdd
export function batchAddJob(data) {
  return request({
    url: '/dbms/api/batchAdd',
    method: 'post',
    data
  })
}

// 获取表名
export function getTables(params) {
  return request({
    url: '/data/dts/metadata/getTables',
    method: 'get',
    params
  })
}

// 获取schema
export function getTableSchema(params) {
  return request({
    url: '/data/dts/metadata/getDBSchema',
    method: 'get',
    params
  })
}

