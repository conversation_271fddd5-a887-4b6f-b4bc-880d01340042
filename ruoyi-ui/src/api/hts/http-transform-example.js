import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/hts/httpTransformExample/queryExample',
    method: 'get',
    params
  })
}

export function getPageList(params) {
  return request({
    url: '/hts/httpTransformExample/queryExampleWithPage',
    method: 'get',
    params
  })
}

export function createExample(data) {
  return request({
    url: '/hts/httpTransformExample/addExample',
    method: 'post',
    data
  })
}

export function updateExample(data) {
  return request({
    url: '/hts/httpTransformExample/updateExample',
    method: 'post',
    data
  })
}

export function removeExample(id) {
  return request({
    url: '/hts/httpTransformExample/' + id,
    method: 'delete'
  })
}

