import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/hts/httpJobSummary/queryHttpJobSummary',
    method: 'get',
    params
  })
}

export function getPageList(params) {
  return request({
    url: '/hts/httpJobSummary/queryHttpJobSummaryByPage',
    method: 'get',
    params
  })
}

export function createSummary(data) {
  return request({
    url: '/hts/httpJobSummary/addHttpJobSummary',
    method: 'post',
    data
  })
}

export function updateSummary(data) {
  return request({
    url: '/hts/httpJobSummary/updateHttpJobSummary',
    method: 'post',
    data
  })
}

export function removeSummary(id) {
  return request({
    url: '/hts/httpJobSummary/' + id,
    method: 'delete'
  })
}

export function triggerJob(data) {
  return request({
    url: `/hts/httpJobSummary/executeHttpJob?id=${data}`,
    method: 'post',
    data
  })
}

