import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/hts/httpJobInfoLog/queryHttpJobInfoLogList',
    method: 'get',
    params
  })
}

export function getPageList(params) {
  return request({
    url: '/hts/httpJobInfoLog/queryHttpJobInfoLogByPage',
    method: 'get',
    params
  })
}

export function remove(id) {
  return request({
    url: '/hts/httpJobInfoLog/' + id,
    method: 'delete'
  })
}

export function clearLog(type) {
  return request({
    url: '/hts/httpJobInfoLog/clearLog?type=' + type,
    method: 'post'
  })
}
