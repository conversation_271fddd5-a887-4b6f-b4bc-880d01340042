import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/hts/httpJobInfo/queryHttpJobInfoList',
    method: 'get',
    params
  })
}

export function getPageList(params) {
  return request({
    url: '/hts/httpJobInfo/queryHttpJobInfoByPage',
    method: 'get',
    params
  })
}

export function create(data) {
  return request({
    url: '/hts/httpJobInfo/addHttpJobInfo',
    method: 'post',
    data
  })
}

export function update(data) {
  return request({
    url: '/hts/httpJobInfo/updateHttpJobInfo',
    method: 'post',
    data
  })
}

export function remove(id) {
  return request({
    url: '/hts/httpJobInfo/' + id,
    method: 'delete'
  })
}

export function triggerJob(data) {
  return request({
    url: `/hts/httpJobInfo/executeHttpJob?id=${data}`,
    method: 'post',
    data
  })
}

export function startWfJob(id) {
  return request({
    url: '/hts/httpJobInfo/wfStart?id=' + id,
    method: 'post'
  })
}

export function stopWfJob(id) {
  return request({
    url: '/hts/httpJobInfo/wfStop?id=' + id,
    method: 'post'
  })
}

export function transformIMData(param) {
  return request({
    url: '/hts/transformIMData',
    method: 'post',
    data: param
  })
}
