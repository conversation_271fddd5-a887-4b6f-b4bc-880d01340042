import request from '@/utils/request'

// 查询数据监控配置列表
export function listMonitorconfig(query) {
  return request({
    url: '/alert/monitorconfig/list',
    method: 'get',
    params: query
  })
}

// 查询数据监控配置详细
export function getMonitorconfig(id) {
  return request({
    url: '/alert/monitorconfig/' + id,
    method: 'get'
  })
}

// 新增数据监控配置
export function addMonitorconfig(data) {
  return request({
    url: '/alert/monitorconfig',
    method: 'post',
    data: data
  })
}

// 修改数据监控配置
export function updateMonitorconfig(data) {
  return request({
    url: '/alert/monitorconfig',
    method: 'put',
    data: data
  })
}

// 删除数据监控配置
export function delMonitorconfig(id) {
  return request({
    url: '/alert/monitorconfig/' + id,
    method: 'delete'
  })
}


// 获取选项数据
export function getOptionList() {
  return request({
    url: '/alert/monitorconfig/option/list',
    method: 'get',
  })
}

// 修改状态
export function updateStatus(data) {
  return request({
    url: '/alert/monitorconfig/update/status',
    method: 'put',
    data: data
  })
}

// 触发一次预警任务
export function taskTrigger(id) {
  return request({
    url: '/alert/monitorconfig/taskTrigger/' + id,
    method: 'post'
  })
}
