import request from '@/utils/request'

// 查询预警动作列表
export function listAction(query) {
  return request({
    url: '/alert/action/list',
    method: 'get',
    params: query
  })
}

// 查询预警动作详细
export function getAction(id) {
  return request({
    url: '/alert/action/' + id,
    method: 'get'
  })
}

// 新增预警动作
export function addAction(data) {
  return request({
    url: '/alert/action',
    method: 'post',
    data: data
  })
}

// 修改预警动作
export function updateAction(data) {
  return request({
    url: '/alert/action',
    method: 'put',
    data: data
  })
}

// 删除预警动作
export function delAction(id) {
  return request({
    url: '/alert/action/' + id,
    method: 'delete'
  })
}

// 修改预警动作状态
export function updateActionStatus(data) {
  return request({
    url: '/alert/action/update/status',
    method: 'put',
    data: data
  })
}
