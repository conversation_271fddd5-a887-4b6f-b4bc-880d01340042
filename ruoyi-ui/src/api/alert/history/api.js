import request from '@/utils/request'

// 查询监控历史数据列表
export function listHistory(query) {
  return request({
    url: '/alert/history/list',
    method: 'get',
    params: query
  })
}

// 查询监控历史数据详细
export function getHistory(id) {
  return request({
    url: '/alert/history/' + id,
    method: 'get'
  })
}

// 新增监控历史数据
export function addHistory(data) {
  return request({
    url: '/alert/history',
    method: 'post',
    data: data
  })
}

// 修改监控历史数据
export function updateHistory(data) {
  return request({
    url: '/alert/history',
    method: 'put',
    data: data
  })
}

// 删除监控历史数据
export function delHistory(id) {
  return request({
    url: '/alert/history/' + id,
    method: 'delete'
  })
}

export function clearHistory(type) {
  return request({
    url: '/alert/history/clear/' + type,
    method: 'delete'
  })
}
