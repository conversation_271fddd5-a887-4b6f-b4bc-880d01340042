import request from '@/utils/request'

// 查询预警规则列表
export function listRule(query) {
  return request({
    url: '/alert/rule/list',
    method: 'get',
    params: query
  })
}

// 查询预警规则详细
export function getRule(id) {
  return request({
    url: '/alert/rule/' + id,
    method: 'get'
  })
}

// 新增预警规则
export function addRule(data) {
  return request({
    url: '/alert/rule',
    method: 'post',
    data: data
  })
}

// 修改预警规则
export function updateRule(data) {
  return request({
    url: '/alert/rule',
    method: 'put',
    data: data
  })
}

// 删除预警规则
export function delRule(id) {
  return request({
    url: '/alert/rule/' + id,
    method: 'delete'
  })
}

// 获取选项数据
export function getRuleOptionList() {
  return request({
    url: '/alert/rule/getOptionList',
    method: 'get',
  })
}

// 修改预警规则状态
export function updateRulStatus(data) {
  return request({
    url: '/alert/rule/update/status',
    method: 'put',
    data: data
  })
}
