import request from '@/utils/request'

// 查询监控历史明细列表
export function listHistoricaldetails(query) {
    return request({
    url: '/alert/historicaldetails/list',
    method: 'get',
    params: query
  })
}

// 查询监控历史明细详细
export function getHistoricaldetails(id) {
    return request({
        url: '/alert/historicaldetails/' + id,
        method: 'get'
    })
}

// 新增监控历史明细
export function addHistoricaldetails(data) {
    return request({
        url: '/alert/historicaldetails',
        method: 'post',
        data: data
    })
}

// 修改监控历史明细
export function updateHistoricaldetails(data) {
    return request({
        url: '/alert/historicaldetails',
        method: 'put',
        data: data
    })
}

// 删除监控历史明细
export function delHistoricaldetails(id) {
    return request({
        url: '/alert/historicaldetails/' + id,
        method: 'delete'
    })
}
