import request from '@/utils/request'

// 查询附件管理列表
export function listAttachment(query) {
  return request({
    url: '/file/attachment/list',
    method: 'get',
    params: query
  })
}

// 查询附件管理详细
export function getAttachment(id) {
  return request({
    url: '/file/attachment/' + id,
    method: 'get'
  })
}

// 新增附件管理
export function addAttachment(data) {
  return request({
    url: '/file/attachment',
    method: 'post',
    data: data
  })
}

// 修改附件管理
export function updateAttachment(data) {
  return request({
    url: '/file/attachment',
    method: 'put',
    data: data
  })
}

// 删除附件管理
export function delAttachment(id) {
  return request({
    url: '/file/attachment/' + id,
    method: 'delete'
  })
}
