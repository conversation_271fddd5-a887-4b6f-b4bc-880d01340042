import request from '@/utils/request'


export function list(params) {
  return request({
    url: '/workflow-server/wf_log/api',
    method: 'get',
    params
  })
}

export function updated(data) {
  return request({
    url: '/workflow-server/wf_log/api',
    method: 'put',
    data
  })
}

export function created(data) {
  return request({
    url: '/workflow-server/wf_log/api',
    method: 'post',
    data: data
  })
}

export function deleted(data) {
  return request({
    url: '/workflow-server/wf_log/api',
    method: 'delete',
    params: data
  })
}

export function deleteLog(type) {
  return request({
    url: '/workflow-server/wf_log/api/clearLog?type=' + type,
    method: 'post'
  })
}

// export function getJobProjectList(params) {
//   return request({
//     url: '/datax/api/qualityJobLog/list',
//     method: 'get',
//     params
//   })
// }

export function updateFlowStatus(wfId) {
  return request({
    url: '/workflow-server/api/updateFlowStatus',
    method: 'post',
    params: { wfId }
  })
}
