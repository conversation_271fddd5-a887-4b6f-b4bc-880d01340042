import request from '@/utils/request'

// datax插件api

export function getList(params) {
  return request({
    url: '/workflow-server/task/api/pageList',
    method: 'get',
    params
  })
}

export function triggerJob(data) {
  return request({
    url: '/data/dts/job/trigger',
    method: 'post',
    data
  })
}

export function startJob(id) {
  return request({
    url: '/workflow-server/task/api/start?id=' + id,
    method: 'post'
  })
}

export function stopJob(id) {
  return request({
    url: '/workflow-server/task/api/stop?id=' + id,
    method: 'post'
  })
}

export function getExecutorList() {
  return request({
    url: '/datax/api/jobGroup/list',
    method: 'get'
  })
}

export function updateJob(data) {
  return request({
    url: '/workflow-server/task/api/update',
    method: 'post',
    data
  })
}

export function createJob(data) {
  return request({
    url: '/workflow-server/task/api/add',
    method: 'post',
    data
  })
}

export function removeWorkFLow(id) {
  return request({
    url: '/workflow-server/task/api/remove/' + id,
    method: 'post'
  })
}

export function nextTriggerTime(cron) {
  return request({
    url: '/datax/api/job/nextTriggerTime?cron=' + cron,
    method: 'get'
  })
}
export function viewJobLog(id) {
  return request({
    url: '/datax/api/log/logDetailCat?id=' + id,
    method: 'get'
  })
}

export function getUsersList(params) {
  return request({
    url: '/datax/api/user/list',
    method: 'get',
    params
  })
}

export function getJobIdList(params) {
  return request({
    url: '/workflow-server/task/api/list',
    method: 'get',
    params
  })
}
// batchAdd
export function batchAddJob(data) {
  return request({
    url: '/workflow-server/task/api/batchAdd',
    method: 'post',
    data
  })
}

// 根据类型获取任务
export function getSqlTaskList() {
  return request({
    url: '/workflow-server/task/api/getSqlTask',
    method: 'GET'
  })
}

// 根据类型获取任务
export function getDataXTaskList() {
  return request({
    url: '/workflow-server/task/api/getDataxTask',
    method: 'GET'
  })
}

// 根据类型获取任务
export function getShellTaskList() {
  return request({
    url: '/workflow-server/task/api/getShellTask',
    method: 'GET'
  })
}

// 根据类型获取任务
export function getHttpTaskList() {
  return request({
    url: '/workflow-server/task/api/getHttpTask',
    method: 'GET'
  })
}

// 获取数据工作流任务
export function getWorkflow() {
  return request({
    url: '/workflow-server/task/api/getWorkflow',
    method: 'GET'
  })
}

// 获取表名
export function getTables(params) {
  return request({
    url: '/data/dts/metadata/getTables',
    method: 'get',
    params
  })
}

// 获取schema
export function getTableSchema(params) {
  return request({
    url: '/data/dts/metadata/getDBSchema',
    method: 'get',
    params
  })
}

// 获取节点名称
export function getNodeList() {
  return request({
    url: '/workflow-server/task/api/nodeNameList',
    method: 'get'
  })
}

