import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listAttachment(query) {
    return request({
        url: '/file/attachment/list',
        method: 'get',
        params: query
    })
}

// 修改【请填写功能名称】
export function updateAttachment(data) {
    return request({
        url: '/file/attachment/binding',
        method: 'put',
        data: data
    })
}

export function batchUpdateAttachment(data){
  return request({
    url: '/file/attachment/batchBinding',
    method: 'put',
    data: data
  })
}

// 根据文件id查询附件
export function batchBindAttachment(bindParam) {
  return request({
    url: '/file/attachment/bindingAttachment',
    method: 'post',
    data: bindParam
  })
}
