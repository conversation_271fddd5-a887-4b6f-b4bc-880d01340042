import request from '@/utils/request'

// 保存配置
export function addOrUpdLoginConfig(data) {
  return request({
    url: '/system/loginConfig/addOrUpdLoginConfig',
    method: 'post',
    data: data
  })
}

// 加载配置
export function getLoginConfig() {
  return request({
    url: '/system/loginConfig/getLoginConfig',
    method: 'get'
  })
}


// 通过授权码获取 accessToken
export function getAccessToken(code) {
  const formData = new FormData()
  formData.append('code', code)
  return request({
    url: '/auth/oauth2-login',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
