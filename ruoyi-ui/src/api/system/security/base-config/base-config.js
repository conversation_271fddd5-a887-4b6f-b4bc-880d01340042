import request from '@/utils/request'

// 保存配置
export function addOrUpdBaseConfig(data) {
  return request({
    url: '/system/baseConfig/addOrUpdBaseConfig',
    method: 'post',
    data: data
  })
}

// 加载配置
export function getBaseConfig() {
  return request({
    url: '/system/baseConfig/queryBaseConfig',
    method: 'get'
  })
}

// 恢复默认配置
export function getDefaultBaseConfig() {
  return request({
    url: '/system/baseConfig/queryDefaultBaseConfig',
    method: 'get'
  })
}
