import request from '@/utils/request'

// 保存配置
export function addOrUpdLevel3ProtectConfig(data) {
  return request({
    url: '/system/level3Protect/addOrUpdLevel3ProtectConfig',
    method: 'post',
    data: data
  })
}

// 加载配置
export function getLevel3ProtectConfig() {
  return request({
    url: '/system/level3Protect/queryLevel3ProtectConfig',
    method: 'get'
  })
}

// 恢复默认配置
export function getDefaultLevel3ProtectConfig() {
  return request({
    url: '/system/level3Protect/queryDefaultLevel3ProtectConfig',
    method: 'get'
  })
}
