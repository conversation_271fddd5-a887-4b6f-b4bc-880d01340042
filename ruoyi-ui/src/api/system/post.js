import request from '@/utils/request'
import { Loading } from 'element-ui';
// 查询岗位列表
export function listPost(query) {
  return request({
    url: '/system/post/list',
    method: 'get',
    params: query
  })
}

// 查询岗位详细
export function getPost(postId) {
  return request({
    url: '/system/post/' + postId,
    method: 'get'
  })
}

// 新增岗位
export function addPost(data) {
  return request({
    url: '/system/post',
    method: 'post',
    data: data
  })
}

// 修改岗位
export function updatePost(data) {
  return request({
    url: '/system/post',
    method: 'put',
    data: data
  })
}

// 删除岗位
export function delPost(postId) {
  return request({
    url: '/system/post/' + postId,
    method: 'delete'
  })
}


// 查询角色已授权用户列表
export function allocatedUserList(query) {
  return request({
    url: 'system/post/users',
    method: 'get',
    params: query
  })
}

// 查询角色未授权用户列表
export function selectNotUserList(query) {
  return request({
    url: '/system/post/selectNotUserList',
    method: 'get',
    params: query
  })
}


export function batchAddUserByPost(data) {
  const loadingInstance = Loading.service({ fullscreen: true, background: "rgba(0, 0, 0, 0.8)" });
  return request({
    url: '/system/post/add',
    method: 'put',
    params: data
  }).finally(() => {
    loadingInstance.close()
  })
}

export function authUserCancelAll(data) {
  const loadingInstance = Loading.service({ fullscreen: true, background: "rgba(0, 0, 0, 0.8)" });
  return request({
    url: '/system/post/remove',
    method: 'put',
    params: data
  }).finally(() => {

    loadingInstance.close()
  })
}

// 查询岗位列表
export function listPostAll() {
  return request({
    url: '/system/post/optionselect',
    method: 'get',
  })
}
