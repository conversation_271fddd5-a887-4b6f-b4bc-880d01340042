import request from '@/utils/request'

// 查询菜单列表
export function listMenu(query) {
  return request({
    url: '/system/menu/list',
    method: 'get',
    params: query
  })
}

// 查询app菜单
export function appListMenu() {
  return request({
    url: "/system/appMenu/treeSelect",
    method: "get"
  })
}
// 获取收藏菜单
export function collectMenu(data) {
  return request({
    url: "/system/appMenu/list",
    method: "get",
    data
  })
}
// 新增收藏
export function addCollect(data){
  const url = "/system/appMenu"
  return request({
    url,
    method: "post",
    data
  })
}
// 取消收藏
export function cancelCollect(menuId){
  const url = `/system/appMenu/${menuId}`
  return request({
    url,
    method: "delete",
  })
}
// 查询菜单详细
export function getMenu(menuId) {
  return request({
    url: '/system/menu/' + menuId,
    method: 'get'
  })
}

// 查询菜单下拉树结构
export function treeselect() {
  return request({
    url: '/system/menu/treeselect',
    method: 'get'
  })
}

// 根据角色ID查询菜单下拉树结构
export function roleMenuTreeselect(roleId) {
  return request({
    url: '/system/menu/roleMenuTreeselect/' + roleId,
    method: 'get'
  })
}

// 新增菜单
export function addMenu(data) {
  return request({
    url: '/system/menu',
    method: 'post',
    data: data
  })
}

// 修改菜单
export function updateMenu(data) {
  return request({
    url: '/system/menu',
    method: 'put',
    data: data
  })
}

// 删除菜单
export function delMenu(menuId) {
  return request({
    url: '/system/menu/' + menuId,
    method: 'delete'
  })
}

// 查询菜单角色关系
export function getMenuRole(query) {
  return request({
    url: '/system/menu/getMenuRole',
    method: 'get',
    params: query
  })
}
