import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询用户列表
export function listUser(query) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: query
  })
}

// 查询所有用户列表
export function allUserList(query) {
  return request({
    url: '/system/user/allUserList',
    method: 'get',
    params: query
  })
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: '/system/user/' + parseStrEmpty(userId),
    method: 'get'
  })
}

// 新增用户
export function addUser(data) {
  return request({
    url: '/system/user',
    method: 'post',
    data: data
  })
}

// 修改用户
export function updateUser(data) {
  return request({
    url: '/system/user',
    method: 'put',
    data: data
  })
}

// 删除用户
export function delUser(userId) {
  return request({
    url: '/system/user/' + userId,
    method: 'delete'
  })
}

// 用户密码重置
export function resetUserPwd(userId, password) {
  const data = {
    userId,
    password
  }
  return request({
    url: '/system/user/resetPwd',
    method: 'put',
    data: data
  })
}

// 用户状态修改
export function changeUserStatus(userId, status) {
  const data = {
    userId,
    status
  }
  return request({
    url: '/system/user/changeStatus',
    method: 'put',
    data: data
  })
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: '/system/user/profile',
    method: 'get'
  })
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: '/system/user/profile',
    method: 'put',
    data: data
  })
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword
  }
  return request({
    url: '/system/user/profile/updatePwd',
    method: 'put',
    params: data
  })
}

// 忘记密码？
export function forgetPwd(username, oldPassword, newPassword) {
  const data = {
    username,
    oldPassword,
    newPassword
  }
  return request({
    url: '/system/user/profile/forgetPwd',
    method: 'put',
    params: data
  })
}

// 用户头像上传
export function uploadAvatar(data) {
  return request({
    url: '/system/user/profile/avatar',
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data: data
  })
}

// 用户头像上传
export function uploadAvatar1(data) {
  return request({
    url: '/file/attachments/upload',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data: data
  })
}

// 查询授权角色
export function getAuthRole(userId, roleName,category) {
  return request({
    url: `/system/user/authRole`,
    method: 'get',
    params:{
      userId,
      roleName,
      category
    }
  })
}

// 保存授权角色
export function updateAuthRole(data) {
  return request({
    url: '/system/user/authRole',
    method: 'put',
    params: data
  })
}

// 查询部门下拉树结构
export function deptTreeSelect() {
  return request({
    url: '/system/user/deptTree',
    method: 'get'
  })
}

// 全量同步钉钉userid
export function syncDingUserId() {
  return request({
    url: '/system/user/updateDingUserId',
    method: 'get'
  })
}

// 批量同步钉钉userid
export function syncBatchDingUserId(ids) {
  return request({
    url: '/system/user/syncBatchDingUserId',
    method: 'post',
    data: { ids }
  })
}

// 查询用户列表-转办|加签|委派|减签
export function interactiveTypeSysUser(query) {
  return request({
    url: '/flow/execute/interactiveTypeSysUser',
    method: 'get',
    params: query
  })
}

// 查询用户列表-流程设计
export function listContainPost(query) {
  return request({
    url: '/system/user/listContainPost',
    method: 'get',
    params: query
  })
}

/**
 * 获取任意层级的部门树
 */
export function getDeptTreeById(parentId = 100,userId = null) {
  return request({
    url: '/system/user/deptAndUserList',
    method: 'get',
    params: {
      parentId: parentId,
      userId: userId
    }
  })
}


export function getUserInfoApp(keyword) {
  return request({
    url: '/system/user/getUserInfoApp',
    method: 'get',
    params: {
      userInfo: keyword
    }
  })
}
