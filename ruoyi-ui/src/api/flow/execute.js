import request from '@/utils/request'

// 查询待办任务列表
export function toDoPage(query) {
  return request({
    url: '/flow/execute/toDoPage',
    method: 'get',
    params: query
  })
}

// 查询已办任务列表
export function donePage(query) {
  return request({
    url: '/flow/execute/donePage',
    method: 'get',
    params: query
  })
}

// 查询抄送任务列表
export function copyPage(query) {
  return request({
    url: '/flow/execute/copyPage',
    method: 'get',
    params: query
  })
}

// 查询已办任务列表
export function doneList(instanceId) {
  return request({
    url: '/flow/execute/doneList/' + instanceId,
    method: 'get'
  })
}
// 查询已办任务列表
export function doneList1(instanceIds) {
  return request({
    url: '/flow/execute/formDataDoneList',
    params:{
      instanceIds,
    },
    method: 'get'
  })
}

// 查询跳转任意节点列表
export function anyNodeList(instanceId, nodeCode) {
  return request({
    url: '/flow/execute/anyNodeList/' + instanceId + '/' + nodeCode,
    method: 'get'
  })
}

// 转办|加签|委派|减签
export function interactiveType(taskId, assigneePermission, operatorType) {
  return request({
    url: '/flow/execute/interactiveType',
    method: 'post',
    params: {
              taskId: taskId,
              addHandlers: assigneePermission,
              operatorType: operatorType
            }
  })
}

// 查询跳转任意节点列表
export function getTaskById(taskId) {
  return request({
    url: '/flow/execute/getTaskById/' + taskId,
    method: 'get'
  })
}

// 待办办理时修改数据
export function updateFormData(data) {
  return request({
    url: '/flow/execute/updateFormData',
    method: 'post',
    data: data
  })
}
//获取流程节点
export function getNodesByDefCode(defCode) {
  return request({
    url: '/flow/execute/getNodesByDefCode/'+defCode,
    method: 'get'
  })
}

// 将审批人信息转换
export function getApproverInfo(data) {
  return request({
    url: '/flow/execute/getApproverInfo',
    method: 'post',
    data: data
  })
}

//获取流程节点
export function getNodesByDefCode1(flowCode, instanceId) {
  return request({
    url: '/flow/execute/getNodes/'+ flowCode + '/' + instanceId,
    method: 'get'
  })
}
