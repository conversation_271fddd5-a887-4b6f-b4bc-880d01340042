import request from '@/utils/request'
//发起流程
export function flowStart(data) {
  return request({
    url: '/flow/ins/start',
    method: 'post',
    data: data
  })
}

//查询业务数据
export function getTableCodeAndBusinessId(businessId) {
  return request({
    url: '/flow/business/getTableCodeAndDataId/' + businessId,
    method: 'get'
  })
}

export const getInstanceIds = (dataId) => {
  return request({
    url: `/flow/business/getInstanceIds/${dataId}`,
    method: 'get'
  })
}

//批量同意
export function batchHandle(data) {
  return request({
    url: '/flow/ins/batchHandle',
    method: 'post',
    data: data
  })
}
//同意处理
export function handleCommit(data) {
  return request({
    url: '/flow/ins/handle',
    method: 'post',
    data: data
  })
}
//退回
export function reject(data) {
  return request({
    url: '/flow/ins/reject',
    method: 'post',
    data: data
  })
}
//退回
export function withDraw(data) {
  return request({
    url: '/flow/ins/withDraw',
    method: 'post',
    data: data
  })
}

