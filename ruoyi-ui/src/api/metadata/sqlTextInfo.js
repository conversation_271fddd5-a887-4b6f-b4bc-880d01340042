import request from '@/utils/request'


export function pageList(params) {
  return request({
    url: '/datax/api/sqlTextInfo',
    method: 'get',
    params
  })
}

export function list(params) {
  return request({
    url: '/datax/api/sqlTextInfo/list',
    method: 'get',
    params
  })
}

export function updated(data) {
  return request({
    url: '/datax/api/sqlTextInfo',
    method: 'put',
    data
  })
}

export function created(data) {
  return request({
    url: '/datax/api/sqlTextInfo',
    method: 'post',
    data: data
  })
}

export function deleted(data) {
  return request({
    url: '/datax/api/sqlTextInfo',
    method: 'delete',
    params: data
  })
}


