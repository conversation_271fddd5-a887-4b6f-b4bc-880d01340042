import request from '@/utils/request'

export function getAllJob() {
  const params = {
    page: 0,
    size: 9999,
    enabled: true
  }
  return request({
    url: '/energyAudit/energy/energyBaseDataWater',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: '/energyAudit/energy/energyBaseDataWater',
    method: 'post',
    data
  })
}

// 查询检测结果详细
export function getAdsWaterById(id) {
  return request({
    url: '/energyAudit/energy/energyBaseDataWater/' + id,
    method: 'get'
  })
}


export function del(ids) {
  return request({
    url: '/energyAudit/energy/energyBaseDataWater',
    method: 'delete',
    data: ids
  })
}

// 修改岗位
export function edit(data) {
  return request({
    url: '/energyAudit/energy/energyBaseDataWater',
    method: 'put',
    data
  })
}

// export default { add, edit, del,getAllJob }
