import request from '@/utils/request'

export function getAllJob() {
  const params = {
    page: 0,
    size: 9999,
    enabled: true
  }
  
  return request({
    url: '/energyAudit/energy/adsPackSkuEquipDowntimeRecord',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: '/energyAudit/energy/adsPackSkuEquipDowntimeRecord',
    method: 'post',
    data
  })
}

// 查询检测结果详细
export function getPackSkuEquipDowntimeRecordId(id) {
  return request({
    url: '/energyAudit/energy/adsPackSkuEquipDowntimeRecord/' + id,
    method: 'get'
  })
}


export function del(ids) {
  return request({
    url: '/energyAudit/energy/adsPackSkuEquipDowntimeRecord',
    method: 'delete',
    data: ids
  })
}

// 修改岗位
export function edit(data) {
  return request({
    url: '/energyAudit/energy/adsPackSkuEquipDowntimeRecord',
    method: 'put',
    data
  })
}

// export default { add, edit, del,getAllJob }
