import request from '@/utils/request'

export function getAllJob() {
  const params = {
    page: 0,
    size: 9999,
    enabled: true
  }
  return request({
    url: '/energyAudit/energy/adsLineYieldFull',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: '/energyAudit/energy/adsLineYieldFull',
    method: 'post',
    data
  })
}

// 查询检测结果详细
export function getLineYieldById(id) {
  return request({
    url: '/energyAudit/energy/adsLineYieldFull/' + id,
    method: 'get'
  })
}


export function del(ids) {
  return request({
    url: '/energyAudit/energy/adsLineYieldFull',
    method: 'delete',
    data: ids
  })
}

// 修改岗位
export function edit(data) {
  return request({
    url: '/energyAudit/energy/adsLineYieldFull',
    method: 'put',
    data
  })
}

// export default { add, edit, del,getAllJob }
