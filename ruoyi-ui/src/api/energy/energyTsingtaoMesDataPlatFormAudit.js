import request from '@/utils/request'

export function getAllJob() {
  const params = {
    page: 0,
    size: 9999,
    enabled: true
  }
  return request({
    url: '/energyAudit/energy/audit',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: '/energyAudit/energy/audit',
    method: 'post',
    data
  })
}
// 查询检测结果详细
export function getDetail(id) {
  return request({
    url: '/energyAudit/energy/audit' + id,
    method: 'get'
  })
}

// 修改

export function del(ids) {
  return request({
    url: '/energyAudit/energy/audit',
    method: 'delete',
    data: ids
  })
}

export function editTsingtaoAuditStatus(id) {
  return request({
    url: '/energyAudit/energy/audit',
    method: 'put',
    data: id
  })
}

// export default { add, edit, del }
