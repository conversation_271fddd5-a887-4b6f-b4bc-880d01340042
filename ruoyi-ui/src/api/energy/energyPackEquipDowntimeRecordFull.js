import request from '@/utils/request'

export function getAllJob() {
  const params = {
    page: 0,
    size: 9999,
    enabled: true
  }
  return request({
    url: '/energyAudit/energy/adsPackequipDowntimeRecord',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: '/energyAudit/energy/adsPackequipDowntimeRecord',
    method: 'post',
    data
  })
}
// 查询检测结果详细
export function getAdsPackequipDowntimeRecord(id) {
  return request({
    url: '/energyAudit/energy/adsPackequipDowntimeRecord/' + id,
    method: 'get'
  })
}

// 修改

export function del(ids) {
  return request({
    url: '/energyAudit/energy/adsPackequipDowntimeRecord',
    method: 'delete',
    data: ids
  })
}

export function editTsingtaoAuditStatus(id) {
  return request({
    url: '/energyAudit/energy/adsPackequipDowntimeRecord',
    method: 'put',
    data: id
  })
}

// export default { add, edit, del }
