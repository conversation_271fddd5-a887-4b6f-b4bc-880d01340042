import request from '@/utils/request'

export function getAllJob() {
  const params = {
    page: 0,
    size: 9999,
    enabled: true
  }
  return request({
    url: '/energyAudit/energy/adsPackSkuDayYieldFull',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: '/energyAudit/energy/adsPackSkuDayYieldFull',
    method: 'post',
    data
  })
}

// 查询检测结果详细
export function getPackSkuDayYieldById(id) {
  return request({
    url: '/energyAudit/energy/adsPackSkuDayYieldFull/' + id,
    method: 'get'
  })
}


export function del(ids) {
  return request({
    url: '/energyAudit/energy/adsPackSkuDayYieldFull',
    method: 'delete',
    data: ids
  })
}

// 修改岗位
export function edit(data) {
  return request({
    url: '/energyAudit/energy/adsPackSkuDayYieldFull',
    method: 'put',
    data
  })
}

// export default { add, edit, del,getAllJob }
