import request from '@/utils/request'

// 查询API接口日志列表
export function listLog(query) {
  return request({
    url: 'mengniu-api/callApiLog/list',
    method: 'get',
    params: query
  })
}

// 查询API接口日志详细
export function getLog(id) {
  return request({
    url: 'mengniu-api/callApiLog/' + id,
    method: 'get'
  })
}

// 新增API接口日志
export function addLog(data) {
  return request({
    url: 'mengniu-api/callApiLog',
    method: 'post',
    data: data
  })
}

// 修改API接口日志
export function updateLog(data) {
  return request({
    url: 'mengniu-api/callApiLog',
    method: 'put',
    data: data
  })
}

// 删除API接口日志
export function delLog(id) {
  return request({
    url: 'mengniu-api/callApiLog/' + id,
    method: 'delete'
  })
}

// 清除API接口日志
export function cleanLog(type) {
  return request({
    url: 'mengniu-api/callApiLog/cleanLog/' + type,
    method: 'delete'
  })
}
