import request from '@/utils/request'

// 查询请求配置列表
export function listCfg(query) {
  return request({
    url: '/mengniu-api/cfg/list',
    method: 'get',
    params: query
  })
}

// 查询请求配置详细
export function getCfg(id) {
  return request({
    url: '/mengniu-api/cfg/' + id,
    method: 'get'
  })
}

// 新增请求配置
export function addCfg(data) {
  return request({
    url: '/mengniu-api/cfg',
    method: 'post',
    data: data
  })
}

// 修改请求配置
export function updateCfg(data) {
  return request({
    url: '/mengniu-api/cfg',
    method: 'put',
    data: data
  })
}

// 删除请求配置
export function delCfg(id) {
  return request({
    url: '/mengniu-api/cfg/' + id,
    method: 'delete'
  })
}

// 执行接口
export function sendRequest(apiCode) {
  return request({
    url: '/mengniu-api/send/getData/' + apiCode,
    method: 'post'
  })
}
