import request from '@/utils/request'

// 查询数据保存接口配置列表
export function listDataSaveCfg(query) {
  return request({
    url: '/mengniu-api/dataSaveCfg/list',
    method: 'get',
    params: query
  })
}

// 查询数据保存接口配置详细
export function getDataSaveCfg(id) {
  return request({
    url: '/mengniu-api/dataSaveCfg/' + id,
    method: 'get'
  })
}

// 新增数据保存接口配置
export function addDataSaveCfg(data) {
  return request({
    url: '/mengniu-api/dataSaveCfg',
    method: 'post',
    data: data
  })
}

// 修改数据保存接口配置
export function updateDataSaveCfg(data) {
  return request({
    url: '/mengniu-api/dataSaveCfg',
    method: 'put',
    data: data
  })
}

// 删除数据保存接口配置
export function delDataSaveCfg(id) {
  return request({
    url: '/mengniu-api/dataSaveCfg/' + id,
    method: 'delete'
  })
}

// 修改数据保存接口配置状态
export function updateApiStatus(data) {
  return request({
    url: '/mengniu-api/dataSaveCfg/updateApiStatus',
    method: 'put',
    data: data
  })
}
