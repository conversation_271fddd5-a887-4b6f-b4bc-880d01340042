import request from '@/utils/request'

// 查询大区
export function getRegion() {
  return request({
    url: '/business/foundation/organization/getRegion',
    method: 'get',
  })
}

// 查询工厂
export function getFactory(code) {
  return request({
    url: '/business/foundation/organization/getFactory/' + code,
    method: 'get',
  })
}

// 查询组织机构列表
export function listOrganization(query) {
  return request({
    url: '/business/foundation/organization/list',
    method: 'get',
    params: query
  })
}

// 查询组织机构详细
export function getOrganization(id) {
  return request({
    url: '/business/foundation/organization/' + id,
    method: 'get'
  })
}

// 新增组织机构
export function addOrganization(data) {
  return request({
    url: '/business/foundation/organization',
    method: 'post',
    data: data
  })
}

// 修改组织机构
export function updateOrganization(data) {
  return request({
    url: '/business/foundation/organization',
    method: 'put',
    data: data
  })
}

// 删除组织机构
export function delOrganization(id) {
  return request({
    url: '/business/foundation/organization/' + id,
    method: 'delete'
  })
}
