import request from '@/utils/request'

// 查询损失代码列表
export function listLoss(query) {
  return request({
    url: '/business/efficiency/loss/list',
    method: 'get',
    params: query
  })
}

// 查询损失代码详细
export function getLoss(id) {
  return request({
    url: '/business/efficiency/loss/' + id,
    method: 'get'
  })
}

// 新增损失代码
export function addLoss(data) {
  return request({
    url: '/business/efficiency/loss',
    method: 'post',
    data: data
  })
}

// 修改损失代码
export function updateLoss(data) {
  return request({
    url: '/business/efficiency/loss',
    method: 'put',
    data: data
  })
}

// 删除损失代码
export function delLoss(id) {
  return request({
    url: '/business/efficiency/loss/' + id,
    method: 'delete'
  })
}
