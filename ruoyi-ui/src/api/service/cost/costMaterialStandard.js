import request from '@/utils/request'

// 查询材料标准导入列表
export function listCostMaterialStandard(query) {
  return request({
    url: '/business/cost/costMaterialStandard/list',
    method: 'get',
    params: query
  })
}

// 查询材料标准导入详细
export function getCostMaterialStandard(id) {
  return request({
    url: '/business/cost/costMaterialStandard/' + id,
    method: 'get'
  })
}

// 新增材料标准导入
export function addCostMaterialStandard(data) {
  return request({
    url: '/business/cost/costMaterialStandard',
    method: 'post',
    data: data
  })
}

// 修改材料标准导入
export function updateCostMaterialStandard(data) {
  return request({
    url: '/business/cost/costMaterialStandard',
    method: 'put',
    data: data
  })
}

// 删除材料标准导入
export function delCostMaterialStandard(id) {
  return request({
    url: '/business/cost/costMaterialStandard/' + id,
    method: 'delete'
  })
}
