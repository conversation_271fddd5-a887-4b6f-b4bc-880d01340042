import request from '@/utils/request'

export function select() {
  return request({
    url: '/message/emailConfig',
    method: 'get'
  })
}

export function selectDataList() {
  return request({
    url: '/message/emailConfig/selectDataList',
    method: 'get'
  })
}

export function updated(data) {
  return request({
    url: '/message/emailConfig',
    method: 'put',
    data
  })
}

export function created(data) {
  return request({
    url: '/message/emailConfig',
    method: 'post',
    data: data
  })
}

export function deleted(data) {
  return request({
    url: '/message/emailConfig',
    method: 'delete',
    params: data
  })
}

export function selectDing(data) {
  return request({
    url: '/message/dingTalkWebhook/selectDingTalkPage',
    method: 'get',
    params: data
  })
}

export function selectDingList() {
  return request({
    url: '/message/dingTalkWebhook/list',
    method: 'get'
  })
}

export function updatedDing(data) {
  return request({
    url: '/message/dingTalkWebhook',
    method: 'put',
    data
  })
}

export function createdDing(data) {
  return request({
    url: '/message/dingTalkWebhook/addDingConfig',
    method: 'post',
    data: data
  })
}

export function deletedDing(data) {
  return request({
    url: '/message/dingTalkWebhook',
    method: 'delete',
    params: data
  })
}

export function selectPrivateDing(data) {
  return request({
    url: '/message/dingTalkPrivate/selectDingTalkPrivatePage',
    method: 'get',
    params: data
  })
}

export function selectPrivateDingList() {
  return request({
    url: '/message/dingTalkPrivate/list',
    method: 'get'
  })
}

export function updatedPrivateDing(data) {
  return request({
    url: '/message/dingTalkPrivate',
    method: 'put',
    data
  })
}

export function createdPrivateDing(data) {
  return request({
    url: '/message/dingTalkPrivate/addDingPrivateConfig',
    method: 'post',
    data: data
  })
}

export function deletedPrivateDing(data) {
  return request({
    url: '/message/dingTalkPrivate',
    method: 'delete',
    params: data
  })
}
