import request from '@/utils/request'

export function page(data) {
  return request({
    url: '/message/sysMsg/selectSysMsgPage',
    method: 'get',
    params: data
  })
}

export function listMsg() {
  return request({
    url: '/message/sysMsg/listSysMsg',
    method: 'get'
  })
}

export function listNotice() {
  return request({
    url: '/message/sysMsg/findNewMsg',
    method: 'get'
  })
}

export function add(data) {
  return request({
    url: '/message/sysMsg/addSysMsg',
    method: 'post',
    data: data
  })
}

export function update(data) {
  return request({
    url: '/message/sysMsg',
    method: 'put',
    data: data
  })
}

export function deleted(data) {
  return request({
    url: '/message/sysMsg',
    method: 'delete',
    params: data
  })
}

export function oneClickRead() {
  return request({
    url: '/message/sysMsg/oneClickRead',
    method: 'post'
  })
}
