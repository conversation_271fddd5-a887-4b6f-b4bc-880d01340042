import request from '@/utils/request'

export function page(data) {
  return request({
    url: '/message/messageTemplate/selectTemplatePage',
    method: 'get',
    params: data
  })
}

export function listNotice() {
  return request({
    url: '/message/messageTemplate/findNewMsg',
    method: 'get'
  })
}

export function list(data) {
  return request({
    url: '/message/messageTemplate/list',
    method: 'get',
    params: data
  })
}

export function add(data) {
  return request({
    url: '/message/messageTemplate/addTemplate',
    method: 'post',
    data: data
  })
}

export function update(data) {
  return request({
    url: '/message/messageTemplate',
    method: 'put',
    data: data
  })
}

export function deleted(data) {
  return request({
    url: '/message/messageTemplate',
    method: 'delete',
    params: data
  })
}
