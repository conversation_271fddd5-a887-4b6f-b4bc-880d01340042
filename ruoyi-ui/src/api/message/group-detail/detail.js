import request from '@/utils/request'

export function page(data) {
  return request({
    url: '/message/messageNoticeGroupDetail/selectMsgGroupDetailPage',
    method: 'get',
    params: data
  })
}

export function add(data) {
  return request({
    url: '/message/messageNoticeGroupDetail/addMsgGroupDetail',
    method: 'post',
    data: data
  })
}

export function update(data) {
  return request({
    url: '/message/messageNoticeGroupDetail',
    method: 'put',
    data: data
  })
}

export function deleted(data) {
  return request({
    url: '/message/messageNoticeGroupDetail',
    method: 'delete',
    params: data
  })
}

export function toStart(id) {
  return request({
    url: '/message/messageNoticeGroupDetail/toStart?id=' + id,
    method: 'post'
  })
}

export function toStop(id) {
  return request({
    url: '/message/messageNoticeGroupDetail/toStop?id=' + id,
    method: 'post'
  })
}
