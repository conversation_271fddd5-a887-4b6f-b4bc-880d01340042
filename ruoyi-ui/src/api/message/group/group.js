import request from '@/utils/request'

export function page(data) {
  return request({
    url: '/message/messageNoticeGroup/selectMsgGroupPage',
    method: 'get',
    params: data
  })
}

export function add(data) {
  return request({
    url: '/message/messageNoticeGroup/addMsgGroup',
    method: 'post',
    data: data
  })
}

export function update(data) {
  return request({
    url: '/message/messageNoticeGroup',
    method: 'put',
    data: data
  })
}

export function deleted(data) {
  return request({
    url: '/message/messageNoticeGroup',
    method: 'delete',
    params: data
  })
}

export function selectMsgGroup() {
  return request({
    url: '/message/messageNoticeGroup/selectMsgGroup',
    method: 'get'
  })
}
