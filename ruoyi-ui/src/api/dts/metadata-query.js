import request from '@/utils/request'
// 获取mongo表名
export function getMongoTables() {
  return request({
    url: '/data/dts/metadata/getMongoTables',
    method: 'get',
  })

}


// 数据库信息api

// 获取表名
export function getTables(params) {
  return request({
    url: '/data/dts/metadata/getTables',
    method: 'get',
    params
  })
}

// 获取schema
export function getTableSchema(params) {
  return request({
    url: '/data/dts/metadata/getDBSchema',
    method: 'get',
    params
  })
}

// 获取字段
export function getColumns(params) {
  return request({
    url: '/data/dts/metadata/getColumns',
    method: 'get',
    params
  })
}

// 从表单设计器获取字段
export function getColumnsFromFormDesign(params) {
  return request({
    url: '/data/dts/metadata/getColumnsFromFormDesign',
    method: 'get',
    params
  })
}

// 根据sql获取字段
export function getColumnsByQuerySql(params) {
  return request({
    url: '/data/dts/metadata/getColumnsByQuerySql',
    method: 'get',
    params
  })
}

// 根据datasourceID、tablename创建表【目标端】
export function createTable(params) {
  return request({
    url: '/data/dts/metadata/createTable',
    method: 'post',
    params
  })
}
// 判断字段是否存在，存在，即更新值，否则添加字段
export function updateColumnsValue(query) {
  return request({
    url: '/data/dts/metadata/updateColumnsValue',
    method: 'post',
    data: query
  })
}


// 根据datasourceID、tablename创建表【目标端】
export function createTargetTable(params) {
  return request({
    url: '/data/dts/metadata/createTargetTable',
    method: 'post',
    data: params
  })
}

// 根据collection编码查询Mongo数据源中是否存在
export function findCollection(param) {
  return request({
    url: '/codeDev/mongodbData/findCollectionByCode?code=' + param,
    method: 'get'
  })
}
