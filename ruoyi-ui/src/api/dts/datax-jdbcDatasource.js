import request from '@/utils/request'

// datax插件api

export function list(params) {
  return request({
    url: '/data/dts/jobJdbcDatasource',
    method: 'get',
    params
  })
}

export function fetched(params) {
  return request({
    url: '/data/dts/jobJdbcDatasource/' + params,
    method: 'get'
  })
}

export function updated(data) {
  return request({
    url: '/data/dts/jobJdbcDatasource',
    method: 'put',
    data
  })
}

export function created(data) {
  return request({
    url: '/data/dts/jobJdbcDatasource',
    method: 'post',
    data
  })
}

export function deleted(data) {
  return request({
    url: '/data/dts/jobJdbcDatasource',
    method: 'delete',
    params: data
  })
}

export function test(data) {
  return request({
    url: '/data/dts/jobJdbcDatasource/test',
    method: 'post',
    data
  })
}

export function getDataSourceList(params) {
  return request({
    url: '/data/dts/jobJdbcDatasource/all',
    method: 'get',
    params
  })
}

export function getDataSourceListByType(type) {
  return request({
    url: '/data/dts/jobJdbcDatasource/selectDatasourceByType/' + type,
    method: 'get'
  })
}
