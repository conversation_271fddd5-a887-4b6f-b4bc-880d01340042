import request from '@/utils/request'

// 查询homeConfiguration列表
export function listConfiguration(query) {
    return request({
        url: '/system/configuration/list',
        method: 'get',
        params: query
    })
}

// 查询homeConfiguration详细
export function getConfiguration(id) {
    return request({
        url: '/system/configuration/' + id,
        method: 'get'
    })
}

// 新增homeConfiguration
export function addConfiguration(data) {
    return request({
        url: '/system/configuration',
        method: 'post',
        data: data
    })
}

// 修改homeConfiguration
export function updateConfiguration(data) {
    return request({
        url: '/system/configuration',
        method: 'put',
        data: data
    })
}

// 删除homeConfiguration
export function delConfiguration(id) {
    return request({
        url: '/system/configuration/' + id,
        method: 'delete'
    })
}