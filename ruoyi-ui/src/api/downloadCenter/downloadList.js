import request from '@/utils/request'


export function list(params) {
  return request({
    url: '/downloadTask/page',
    method: 'get',
    params
  })
}

export function getByFileName(params) {
  return request({
    url: '/downloadTask/fileName',
    method: 'get',
    params
  })
}

export function getDownloadDataById(id) {
  return request({
    url: '/downloadTask/getDownloadActualFileNameById?id=' + id,
    method: 'post'
  })
}

export function updated(data) {
  return request({
    url: '/downloadTask',
    method: 'put',
    data
  })
}

export function created(data) {
  return request({
    url: '/downloadTask',
    method: 'post',
    data: data
  })
}

export function deleted(data) {
  return request({
    url: '/downloadTask',
    method: 'delete',
    params: data
  })
}
