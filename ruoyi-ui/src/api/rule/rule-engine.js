import request from '@/utils/request'

// 查询流程列表
export function getList(params) {
  return request({
    url: '/ruleEngine/nodeRedFlows',
    method: 'get',
    params
  })
}

// 新增流程
export function addFlow(data) {
  return request({
    url: '/ruleEngine/nodeRedFlows',
    method: 'post',
    data
  })
}

// 更新流程
export function updateFlow(id, data) {
  return request({
    url: `/ruleEngine/nodeRedFlows/${id}`,
    method: 'put',
    data
  })
}

// 删除流程
export function deleteFlows(idList) {
  return request({
    url: '/ruleEngine/nodeRedFlows/' + idList,
    method: 'delete'
  })
}

// 修改流程状态
export function changeFlowStatus(flowId, flowStatus) {
  return request({
    url: `/ruleEngine/nodeRedFlows/${flowId}/status`,
    method: 'put',
    data: flowStatus
  })
}

