import request from '@/utils/request'


/**
 * 下载维度分区模版
 */
export function downloadDimTemplate() {
  return request({
    url: '/document/dataSet/templateDownload',
    method: 'post'
  })
}

/**
 * 上传维度分区模版
 */
export function uploadDimTemplate(data) {
  return request({
    url: '/document/dataSet/importDimension',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
  })
}

/**
 * 下载参数配置模版
 */
export function downloadParamTemplate() {
  return request({
    url: '/document/parameter/templateDownload',
    method: 'post'
  })
}

/**
 * 上传参数配置模版
 */
export function uploadParamTemplate(data) {
  return request({
    url: '/document/parameter/importParameter',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
  })
}


/**
 * 测试模板生成
 */
export function testTemplateGenerate(data) {
  return request({
    url: '/document/parameter/testTemplateGenerate',
    method: 'post',
    data
  })
}


/**
 * 保存参数配置
 */
export function saveConfigure(data) {
  return request({
    url: '/document/configure/saveConfigure',
    method: 'post',
    data
  })
}


/**
 * 获取详情
 */
export function getConfigure(id) {
  return request({
    url: `/document/configure/queryConfigure/${id}`,
    method: 'get',
  })
}

/**
 * 文档生成配置
 * @param {*} data
 * @returns
 */
export function generateDocument(data) {
  return request({
    url: `/document/configure/generateDocument`,
    method: "post",
    data
  })
}
/**
 * 通过配置id查询下面的维度字段
 * @param {Number} configureId
 * @returns
 */
export function queryDimensionData(configureId) {
  return request({
    url: `/document/dataSet/queryDimensionData/${configureId}`,
    method: "get",
  })
}

/**
 * 上传维度分区模版
 */
export function uploadTableConfigure(data) {
  return request({
    url: '/document/table/uploadTableConfigure',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
  })
}
