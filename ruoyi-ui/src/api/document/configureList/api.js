import request from '@/utils/request'

// 查询文档生成配置列表
export function listConfigure(query) {
    return request({
        url: '/document/configure/list',
        method: 'get',
        params: query
    })
}

// 查询文档生成配置详细
export function getConfigure(id) {
    return request({
        url: '/document/configure/' + id,
        method: 'get'
    })
}

// 新增文档生成配置
export function addConfigure(data) {
    return request({
        url: '/document/configure',
        method: 'post',
        data: data
    })
}

// 修改文档生成配置
export function updateConfigure(data) {
    return request({
        url: '/document/configure',
        method: 'put',
        data: data
    })
}

// 删除文档生成配置
export function delConfigure(id) {
    return request({
        url: '/document/configure/deleteConfigure/' + id,
        method: 'delete'
    })
}


// 查询下载记录
export function queryDownloadRecord(configureId){
    return request({
        url:`/document/record/queryDownloadRecord/${configureId}`,
        method:"get"
    })
}
