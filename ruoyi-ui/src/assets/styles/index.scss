@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

.el-popup-parent--hidden {
  padding-right: 0px !important;
}

//main-container全局样式
.app-container {
  padding: 10px 10px 10px 15px;
  height: 100%;
  overflow-y: scroll;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}



.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
    margin-right: 10px;
  }
}

/* 调整table滚动条高度 宽度 start */
// .el-scrollbar__wrap {
//   // overflow: scroll !important;

//   &::-webkit-scrollbar {
//     width: 20px;
//     height: 20px;
//   }
// }

// .el-table__body-wrapper {
//   overflow: scroll !important;

//   &::-webkit-scrollbar {
//     width: 20px;
//     height: 20px;
//   }
// }

/* 调整table滚动条高度 宽度 end */

.no-header-dialog {
  border-radius: 5px;

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 20px;
    // min-height: 300px;
  }

  .el-dialog__footer {
    padding: 0 10px 10px 10px;
  }

  .report-center-header {
    background-image: url("../home-page-images/模块横条.png");
    height: 30px;
    display: flex;
    align-items: center;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    width: 100%;
    margin-bottom: 10px;
    position: relative;

    .header-title {
      margin-left: 5px;
      font-size: 20px;
      font-weight: 500;
      color: rgb(29, 84, 116);
    }


  }

  .report-center-container {
    width: 100%;
    height: 300px;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    align-items: baseline;
    /* 使子节点的高度拉伸，确保和父节点的高度一致 */
  }

  .report-center-content {
    width: calc(25% - 10px);
    margin-right: 10px;
    background-color: rgb(203, 222, 245);
    padding: 10px;
    border: 1px solid rgb(208, 213, 219);
    border-radius: 10px;
    margin-bottom: 10px;
    cursor: pointer;

    .report-center-img {
      width: 100%;
    }

    .report-center-title {
      font-size: 12px;
      color: rgb(2, 10, 118);
    }
  }

  .report-center-content:nth-child(4n) {
    margin-right: 0;
  }
}