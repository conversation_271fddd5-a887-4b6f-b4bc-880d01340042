<template>
  <div class="app-container">
    <div class="content-container">
      <div class="content-left">
        <div class="cockpit">
          <div class="header">
            <img src="../assets/home-page-images/驾驶舱.png" alt="驾驶舱">
            <span class="header-title">驾驶舱</span>
          </div>
          <div class="content">
            <template v-for="(item, index) in cockpitMenuData">
              <div class="content-box" @click="handleClickCockpit(item, index)" v-if="[0, 1, 2, 3].includes(index)">
                <img v-if="item.menuName.includes('中心')" class="content-img" src="../assets/home-page-images2/中心驾驶舱.png"
                  alt="中心驾驶舱">
                <img v-if="item.menuName.includes('实时')" class="content-img" src="../assets/home-page-images2/实时驾驶舱.png"
                  alt="实时驾驶舱">
                <img v-if="item.menuName.includes('大区')" class="content-img" src="../assets/home-page-images2/大区驾驶舱.png"
                  alt="大区驾驶舱">
                <img v-if="item.menuName.includes('职能')" class="content-img" src="../assets/home-page-images2/职能驾驶舱.png"
                  alt="职能驾驶舱">
                <span class="content-title">{{ item.menuName }}</span>
              </div>
            </template>
          </div>
        </div>
        <div class="report-center">
          <div class="header">
            <img src="../assets/home-page-images/报表中心.png" alt="报表中心">
            <span class="header-title">报表中心</span>
          </div>
          <div class="content">
            <div class="content-box" @click="handleClickReportCenter(item)"
              v-for="(item, index) in reportCenterMenuData" :key="item.menuId">
              <!-- 运营 -->
              <img v-if="index === 0" class="content-img" src="../assets/home-page-images/报表中心—运营.png" alt="报表中心—运营">
              <!-- 质量 -->
              <img v-if="index === 1" class="content-img" src="../assets/home-page-images/报表中心—质量.png" alt="报表中心—质量">
              <!-- 技术 -->
              <img v-if="index === 2" class="content-img" src="../assets/home-page-images/报表中心—技术.png" alt="报表中心—技术">
              <!-- 安环 -->
              <img v-if="index === 3" class="content-img" src="../assets/home-page-images/报表中心—安环.png" alt="报表中心—安环">
              <!-- 人力 -->
              <img v-if="index === 4" class="content-img" src="../assets/home-page-images/报表中心—人力.png" alt="报表中心—人力">
              <!-- 工程 -->
              <img v-if="index === 5" class="content-img" src="../assets/home-page-images/报表中心—工程.png" alt="报表中心—工程">
              <div class="content-title">{{ item.menuName }}</div>
            </div>
          </div>
        </div>
        <!-- <div class="process-center">
          <div class="header">
            <img src="../assets/home-page-images/流程中心.png" alt="流程中心">
            <span class="header-title">流程中心</span>
          </div>
          <div class="content" @click="handleClickProcessCenter">
            <table style="width: 100%;table-layout: fixed;">
              <thead>
                <tr>
                  <th>时间</th>
                  <th>任务名称</th>
                  <th>流程状态</th>
                </tr>
              </thead>
              <tbody class="centered-tbody">
                <tr v-for="(item, index) in processCenter" :key="index">
                  <td>{{ parseTime(item.createTime) }}</td>
                  <td>{{ item.flowName }}</td>
                  <td>{{ item.nodeName }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div> -->
        <div class="early-warning-center">
          <div class="header">
            <img src="../assets/home-page-images/预警中心.png" alt="预警中心">
            <span class="header-title">预警中心</span>
          </div>
          <div class="content">
            <table style="width: 100%;table-layout: fixed">
              <thead>
                <tr>
                  <th style="width: 15%;min-width: 150px; border: 1px solid white; ">时间</th>
                  <th style="min-width: 55px; width:5%;border: 1px solid white">类型</th>
                  <th style="width: 15%; border: 1px solid white;">名称</th>
                  <th style="width: 70%;border: 1px solid white;">详情</th>
                </tr>
              </thead>
              <tbody class="centered-tbody">
                <tr v-for="(item, index) in earlyWarningCenter" :key="index">
                  <td @click="routeMessage">{{ item.createTime }}</td>
                  <td @click="routeMessage"><dict-tag :options="dict.type.msg_type" :value="item.msgType" /></td>
                  <td style="text-align: left" @click="routeMessage">{{ item.msgSubject }}</td>
                  <td style="text-align: left" @click="routeMessage" :title="item.msgContent">{{ item.msgContent }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <!-- 通知公告 -->
      <div class="notices-announcements">
        <div class="header">
          <img src="../assets/home-page-images/通知公告.png" alt="通知公告">
          <span class="header-title">通知公告</span>
        </div>
        <div class="content">
          <ul ref="noticeList">
            <li v-for="(item, index) in noticesAnnouncements" :key="index" @click="handleClickNotices(item.noticeId)">
              <img
                :src="item.index === 0 ? require('../assets/home-page-images/红色.png') : item.index === 1 ? require('../assets/home-page-images/橙色.png') : item.index === 2 ? require('../assets/home-page-images/黄色.png') : require('../assets/home-page-images/蓝色.png')"
                alt="框" />
              <span class="index" :style="{ left: item.index + 1 < 10 ? '5px' : '0px' }">{{ item.index + 1 }}</span>
              <span :title="item.content" class="text">{{ item.content }}</span>
              <span class="time">{{ item.time }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <!-- 驾驶舱&报表中心dialog -->
    <el-dialog custom-class="no-header-dialog" :visible.sync="menuDialogVisible" width="75%">
      <div class="report-center-header">
        <img v-if="titleName.includes('报表')" src="../assets/home-page-images/报表中心.png" alt="报表中心">
        <img v-else src="../assets/home-page-images/驾驶舱.png" alt="驾驶舱">
        <span class="header-title">{{ titleName }}</span>
      </div>
      <div class="report-center-container">
        <div v-for="item in dialogChildList" class="report-center-content" @click="handleCliclNode(item)">
          <template v-if="titleName.includes('驾驶舱')">
            <template v-if="!item.menuName.includes('-') & !item.menuName.includes('_')">
              <img v-if="item.menuName.includes('采供')" class="report-center-img"
                src="../assets/home-page-images2/采供驾驶舱.png" :alt="item.menuName">
              <img v-else-if="item.menuName.includes('质量')" class="report-center-img"
                src="../assets/home-page-images2/质量驾驶舱.png" :alt="item.menuName">
              <img v-else-if="item.menuName.includes('检验')" class="report-center-img"
                src="../assets/home-page-images2/检验驾驶舱.png" :alt="item.menuName">
              <img v-else-if="item.menuName.includes('设备')" class="report-center-img"
                src="../assets/home-page-images2/设备管理驾驶舱.png" :alt="item.menuName">
              <img v-else-if="item.menuName.includes('能源')" class="report-center-img"
                src="../assets/home-page-images2/能源驾驶舱.png" :alt="item.menuName">
              <img v-else-if="item.menuName.includes('安环')" class="report-center-img"
                src="../assets/home-page-images2/安环可持续驾驶舱.png" :alt="item.menuName">
              <img v-else class="report-center-img" src="../assets/home-page-images2/报表中心2.png" :alt="item.menuName">
            </template>
            <img v-else class="report-center-img" src="../assets/home-page-images2/报表中心2.png" :alt="item.menuName">
          </template>
          <template v-else>
            <img class="report-center-img" src="../assets/home-page-images2/报表中心2.png" alt="报表中心">
          </template>
          <div style="text-align: center;"><span class="report-center-title">{{ item.menuName }}</span></div>
        </div>
      </div>
      <span slot="footer">
        <el-button size="mini" @click="menuDialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
    <!-- 通知公告dialog -->
    <el-dialog custom-class="no-header-dialog" :visible.sync="noticesAnnouncementsDialogVisible" hight="80%"
      width="75%">
      <div style="height: 700px;" v-if="noticesAnnouncementsDialogVisible" ref="noticesAnnouncementsRender">
      </div>
      <span slot="footer">
        <el-button size="mini" @click="noticesAnnouncementsDialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { listMenu } from "@/api/system/menu";
import { toDoPage } from '@/api/flow/execute'
import { listNotice, getNotice } from "@/api/system/notice";
import { listMsg } from '@/api/message/list/list'
import Quill from "quill";
import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";
import "quill/dist/quill.bubble.css";
import { createSign } from "@/utils/opcener-util"
import { getToken } from '@/utils/auth'
import { getUserProfile } from "@/api/system/user";
export default {
  dicts: ['msg_type'],
  data() {
    return {
      titleName: "",
      cockpitMenuData: [],
      reportCenterMenuData: [],
      dialogChildList: [],
      processCenter: [],
      earlyWarningCenter: [],
      noticesAnnouncements: [],
      menuDialogVisible: false,
      menuTableData: [],
      noticesAnnouncementsDialogVisible: false,
    }
  },
  watch: {
    noticesAnnouncements() {
      this.updateScrollStatus();
    },
  },
  methods: {
    routeMessage() {
      // 前往通知公告管理页面
      this.$router.push("/message/list/");
    },
    updateScrollStatus() {
      this.$nextTick(() => {
        const ul = this.$refs.noticeList;
        if (ul.scrollHeight > ul.clientHeight) {
          ul.classList.add("scroll");
        } else {
          ul.classList.remove("scroll");
        }
      });
    },
    /**
     * 初始化通知公告
     */
    async initNoticesAnnouncements() {
      const result = await listNotice({})
      if (result.code === 200) {
        result.rows.forEach((item, index) => {
          this.noticesAnnouncements.push({
            noticeId: item.noticeId,
            content: item.noticeTitle,
            time: item.createTime,
            index: index
          });
        })
      }
    },
    async handleClickNotices(id) {
      const result = await getNotice(id);
      if (result.code === 200) {
        this.noticesAnnouncementsDialogVisible = true
        this.$nextTick(() => {
          const options = {
            theme: "snow",
            bounds: document.body,
            debug: "warn",
            modules: {
              // 工具栏配置
              toolbar: false
            },
            readOnly: true
          }
          const noticesAnnouncementsRender = this.$refs.noticesAnnouncementsRender;
          this.Quill = new Quill(noticesAnnouncementsRender, options);
          this.Quill.pasteHTML(result.data.noticeContent);
        })
      }
    },
    /**
     * 获取站内信数据
     */
    async getSystemMessageData() {
      const result = await listMsg()
      if (result.code === 200) {
        this.earlyWarningCenter = result.data
      }
    },
    /**
     * 获取流程中心数据
     */
    async getProcessCenterData() {
      const result = await toDoPage({})
      if (result.code === 200) {
        this.processCenter = result.rows
      }
    },
    async handleCliclNode(item) {

      if (item.path.includes("http")) {
        const response = await getUserProfile();
        const userName = response.data.userName
        const sign = createSign("admin")
        const access_token = getToken()
        const userInfo = this.$store.state.user.userInfo
        const dept = userInfo ? userInfo.dept : {}
        const deptCode = dept && dept.deptCode ? dept.deptCode : null
        const path = item.path.replace(/&ac_=[^&]*/g, "");
        const isHasPatams = path.includes("?")
        const cockpitPath = `${path}${isHasPatams ? "&" : "?"}${deptCode ? `dept_code=${deptCode}&` : ""}username=${userName}&access_token=${access_token}&sign=${sign}`
        window.open(cockpitPath, "_blank")
      } else {
        const query = item.query ? JSON.parse(item.query) : {}
        this.$router.push({
          path: item.path,
          query
        })
      }
    },
    /**
     * 点击流程中心
     */
    handleClickProcessCenter() {
      this.$router.push("/Flow/todo")
    },

    /**
     * 点击中心驾驶舱、实时驾驶舱、大区驾驶舱 职能驾驶舱
     */
    async handleClickCockpit(cockpitOption, index) {
      const userInfo = this.$store.state.user.userInfo
      const dept = userInfo ? userInfo.dept : {}
      const deptCode = dept && dept.deptCode ? dept.deptCode : null
      const sign = createSign("admin")
      const access_token = getToken()
      const lastYayerChild = []
      this.titleName = "驾驶舱"
      const findChild = (data) => {
        data.forEach(item => {
          if (item.children && item.children.length > 0) {
            findChild(item.children)
          } else {
            lastYayerChild.push(item)
          }
        })
      }
      const response = await getUserProfile();
      const userName = response.data.userName
      if (cockpitOption.menuName.includes('中心') || cockpitOption.menuName.includes('实时') || cockpitOption.menuName.includes('大区')) {
        // 点击的是前三个
        if (cockpitOption.isFrame === "0") {
          const path = cockpitOption.path.replace(/&ac_=[^&]*/g, "");
          const isHasPatams = path.includes("?")
          const cockpitPath = `${path}${isHasPatams ? "&" : "?"}${deptCode ? `dept_code=${deptCode}&` : ""}username=${userName}&access_token=${access_token}&sign=${sign}`
          window.open(cockpitPath, "_blank")
        } else {
          findChild(cockpitOption.children)
          const firstChild = lastYayerChild[0]
          if (firstChild && firstChild.isFrame === "0") {
            const path = firstChild.path.replace(/&ac_=[^&]*/g, "");
            const isHasPatams = path.includes("?")
            const cockpitPath = `${path}${isHasPatams ? "&" : "?"}${deptCode ? `dept_code=${deptCode}&` : ""}username=${userName}&access_token=${access_token}&sign=${sign}`
            window.open(cockpitPath, "_blank")
          }
        }
      } else {
        // 职能
        // 驾驶舱剩余的菜单
        const remainMenuData = this.menuTableData.find(item => item.menuName === "驾驶舱").children.slice(index)
        findChild(remainMenuData)
        this.dialogChildList = lastYayerChild
        this.menuDialogVisible = true
      }
    },
    /**
     * 点击报表中心
     * @param node
     */
    async handleClickReportCenter(node) {
      this.menuDialogVisible = true
      this.titleName = "报表中心"
      this.dialogChildList = node.children
    },

    handleChildData(data, parentPath) {
      data.forEach(item => {
        if (item.isFrame == "1") {
          item.path = `${parentPath}/${item.path}`
          if (item.path.endsWith("#")) {
            item.path = item.path.substring(0, item.path.length - 1)
          }
        }
        if (item.children && item.children.length > 0) {
          this.handleChildData(item.children, item.path)
        }
      })
    },
    async getMenuTableData() {
      const response = await listMenu({})
      if (response.code == 200) {
        this.menuTableData = this.handleTree(response.data, "menuId");
        this.handleChildData(this.menuTableData, "")
      }
    },

    getLastTwoLevels(data) {
      // 递归函数，提取倒数第二层和倒数第一层数据
      function recursive(node, level) {
        // 如果节点是空的，则返回
        if (!node) return [];

        // 如果当前是倒数第二层或倒数第一层，则保留该节点
        if (level === targetLevel - 1 || level === targetLevel) {
          return [node];
        }

        let result = [];

        // 如果当前节点有子节点，则递归遍历子节点
        if (node.children && node.children.length > 0) {
          node.children.forEach(child => {
            result = result.concat(recursive(child, level + 1));
          });
        }
        return result;
      }

      // 计算目标层级（倒数第二层和倒数第一层）
      const targetLevel = this.getTreeDepth(data); // 获取树的总深度
      console.log("targetLevel", targetLevel);

      return data.reduce((acc, node) => acc.concat(recursive(node, 1)), []);
    },
    // 计算树的深度（即层数）
    getTreeDepth(data) {
      let depth = 0;

      function calcDepth(node, currentDepth) {
        if (!node) return;

        // 更新当前最大深度
        depth = Math.max(depth, currentDepth);

        // 递归遍历子节点
        if (node.children && node.children.length > 0) {
          node.children.forEach(child => calcDepth(child, currentDepth + 1));
        }
      }

      data.forEach(rootNode => calcDepth(rootNode, 1));

      return depth;
    },
    /**
     * 获取驾驶舱的数据
     */
    async getCockpitMenuData() {
      const cockpitMenuOption = this.menuTableData.find(item => item.menuName === "驾驶舱")
      if (cockpitMenuOption && cockpitMenuOption.children) {
        this.cockpitMenuData = cockpitMenuOption.children
      } else {
        this.cockpitMenuData = []
      }
    },

    findSecondLastLevelNodes(treeData) {
      const result = [];

      function traverse(node, depth) {
        if (!node.children || node.children.length === 0) {
          return;
        }

        // 如果当前节点的子节点没有子节点，那么当前节点就是倒数第二级节点
        if (node.children.every(child => !child.children || child.children.length === 0)) {
          result.push(node);
          return;
        }

        // 继续递归遍历子节点
        node.children.forEach(child => traverse(child, depth + 1));
      }

      // 开始遍历
      treeData.forEach(root => traverse(root, 0));

      return result;
    },

    /**
     * 获取报表中心数据
     */
    async getReportCenterMenuData() {
      const reportCenterOriginData = this.menuTableData.filter(item => item.menuName === "报表中心")
      // this.reportCenterMenuData = this.getLastTwoLevels(reportCenterOriginData);
      this.reportCenterMenuData = this.findSecondLastLevelNodes(reportCenterOriginData);
      this.reportCenterMenuData = this.reportCenterMenuData.slice(0, 6);
    },
  },
  async mounted() {
    await this.getMenuTableData()
    this.getCockpitMenuData()
    this.getReportCenterMenuData()
    // this.getProcessCenterData()
    this.initNoticesAnnouncements()
    this.getSystemMessageData()
  }
};
</script>

<style scoped lang="scss">
.app-container {
  background-color: rgb(239, 239, 239);
  min-width: 1700px;
  // overflow-x: scroll;
  // overflow-y: scroll;
  position: relative;
  height: 100%;
  width: 100%;
  padding: 10px 10px 0 10px;

  .content-container {
    display: flex;

    .cockpit,
    .report-center,
    .process-center,
    .early-warning-center,
    .notices-announcements {
      display: flex;
      flex-direction: column;
      position: relative;
      width: calc(50% - 20px);
      margin-right: 20px;
      padding: 20px 20px 30px 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      background-color: white;

      .header {
        background-image: url("../assets/home-page-images/模块横条.png");
        height: 30px;
        display: flex;
        align-items: center;
        background-repeat: no-repeat;
        background-position: center;
        width: 100%;
        margin-bottom: 10px;
        position: relative;

        img {
          height: 20px;
          width: 20px;
        }

        .header-title {
          margin-left: 5px;
          font-size: 20px;
          font-weight: 500;
          color: rgb(29, 84, 116);
        }

        i {
          position: absolute;
          top: 8px;
          right: 25px;
          cursor: pointer;
        }
      }

      .content {
        flex: 1;
        overflow-y: scroll;
      }

    }


    /* 大屏幕：图片覆盖容器 */
    @media (min-width: 1701px) {

      .cockpit,
      .report-center,
      .process-center,
      .early-warning-center,
      .notices-announcements {
        height: 430px;
      }

      .notices-announcements {
        height: 880px;
      }

      .header {
        background-size: cover;
      }
    }

    /* 小屏幕：图片适应容器 */
    @media (max-width: 1700px) {
      .header {
        // color: red;
        // background-size: contain;
        background-size: cover;
      }

      .cockpit,
      .report-center,
      .process-center,
      .early-warning-center,
      .notices-announcements {
        height: 350px;
      }

      .notices-announcements {
        height: 720px;
      }
    }

    .content-left {
      flex: 1;
      display: flex;
      flex-wrap: wrap;

      .cockpit,
      .report-center,
      .process-center,
      .early-warning-center:nth-child(odd) {
        margin-right: 20px;
      }

      // 驾驶舱 & 报表中心公共样式
      .cockpit,
      .report-center {
        .content {
          display: flex;
          flex-wrap: wrap;
          overflow-y: auto;

          .content-box {
            background-color: rgb(235, 245, 250);
            box-sizing: border-box;
            padding: 10px;
            width: calc(33.3% - 10px);
            height: calc(50% - 10px);
            margin-right: 15px;
            margin-bottom: 10px;
            border-radius: 10px;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;

            .content-title {
              font-size: 14px;
            }
          }
        }
      }

      // 驾驶舱私有样式
      .cockpit .content {
        .content-box {
          width: calc(50% - 10px);

          .content-img {
            width: 100%;
            border-radius: 10px;
            height: calc(100% - 20px);
          }

          .content-title {
            color: #1d5474;
            margin-top: 5px;
          }
        }

        .content-box:nth-child(2n) {
          margin-right: 0;
        }
      }

      // 报表中心私有样式
      .report-center .content {
        overflow-y: auto;

        .content-box {
          .content-img {
            height: 80px;
            width: 80px;
          }

          .content-title {
            background-color: rgb(78, 165, 210);
            border-radius: 15px;
            color: #fff;
            margin-top: 10px;
            padding: 2px 10px;
            border: 2px solid rgb(61, 125, 159);
          }
        }

        .content-box:nth-child(3n) {
          margin-right: 0;
        }
      }


      // 流程中心 & 预警中心公共样式
      .process-center,
      .early-warning-center {
        display: flex;
        flex-direction: column;

        .content {
          flex: 1;
          overflow-y: scroll;

          table {
            margin-top: 10px;
            border-spacing: 0;
            border-radius: 5px;
            overflow: hidden;
            table-layout: fixed;
            width: 100%;
            text-align: left;
            font-size: 10px;
          }

          thead {
            margin-bottom: 5px;

            th {
              padding: 5px;
              width: 1fr;
              background-color: #dde6f1;
              text-align: center;
              font-size: 16px;
              color: #1d5474;
            }
          }

          .centered-tbody tr :hover {
            cursor: pointer;
          }

          .centered-tbody td,
          .centered-tbody th {
            text-align: center;
            vertical-align: middle;
            padding: 8px;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .centered-tbody tr:nth-child(odd) {
            background-color: #fff;
          }

          .centered-tbody tr:nth-child(even) {
            background-color: rgb(247, 251, 254)
          }
        }
      }

      .early-warning-center {
        width: 100%;
      }
    }

    // 通知公告私有样式
    .notices-announcements {
      width: 30%;

      @keyframes noticesAnnouncementsScroll {
        0% {
          transform: translateY(0);
        }

        100% {
          transform: translateY(-100%);
        }
      }

      .header {
        background-size: contain;
      }

      .content {
        overflow: hidden;

        ul:hover {
          animation-play-state: paused;
        }

        ul {
          animation: none;
          overflow: hidden;
          list-style-type: none;
          padding: 0;
          margin: 0;
        }

        ul.scroll {
          animation: noticesAnnouncementsScroll 10s linear infinite;
        }

        /* 应用滚动动画 */
        li {
          cursor: pointer;
          font-size: 14px;
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          position: relative;
          top: 1px;

          .index {
            position: absolute;
            color: #f8ebea;
          }

          .text {
            color: #0f0f0f;
            flex: 1;
            white-space: nowrap;
            /* 禁止换行 */
            overflow: hidden;
            /* 隐藏超出的部分 */
            text-overflow: ellipsis;
            /* 显示省略号 */
          }

          .time {
            text-align: right;
            width: 150px;
            color: #adacac
          }

          img {
            margin-right: 5px;
            height: 16px;
            width: 18px;
          }
        }
      }
    }
  }
}
</style>
