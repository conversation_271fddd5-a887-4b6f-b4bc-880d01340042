<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="模版名称" prop="templateName">
        <el-input
          v-model="queryParams.templateName"
          placeholder="请输入模版名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="模版编码" prop="templateCode">
        <el-input
          v-model="queryParams.templateCode"
          placeholder="请输入模版编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['codeDev:formImportTemplate:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['codeDev:formImportTemplate:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['codeDev:formImportTemplate:remove']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['codeDev:formImportTemplate:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="formImportTemplateList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="主键" align="center" prop="id"/>
      <el-table-column label="表单设计id" align="center" prop="formDesignId"/>
      <el-table-column label="模版名称" align="center" prop="templateName"/>
      <el-table-column label="模版编码" align="center" prop="templateCode"/>
      <el-table-column label="创建人" align="center" prop="createBy"/>
      <el-table-column label="创建时间" align="center" prop="createTime"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['codeDev:formImportTemplate:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['codeDev:formImportTemplate:remove']"
          >删除
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="viewImportConfig(scope.row)"
          >查看导入配置
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改formImportTemplate对话框 -->
    <el-dialog :title="title" :visible.sync="open" :modal="false" :modal-append-to-body="false" :append-to-body="true" width="500px" >
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="表单设计id" prop="formDesignId">
          <el-input disabled v-model="form.formDesignId" placeholder="请输入表单设计id"/>
        </el-form-item>
        <el-form-item label="模版名称" prop="templateName">
          <el-input v-model="form.templateName" placeholder="请输入模版名称"/>
        </el-form-item>
        <el-form-item label="模版编码" prop="templateCode">
          <el-input v-model="form.templateCode" placeholder="请输入模版编码"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog :key="dialogKey" title="导入配置信息"  :visible.sync="formImportConfigShow" width="90%"  :append-to-body="true"  class="fullscreen-dialog" >
        <FormImportConfig :templateId="templateId"></FormImportConfig>
    </el-dialog>



  </div>
</template>

<script>
import { addFormImportTemplate, delFormImportTemplate, getFormImportTemplate, listFormImportTemplate, updateFormImportTemplate } from '../../../api/codeDev/formImportTemplate/formImportTemplate'

import FormImportConfig from '@/views/codeDev/formImportConfig/index.vue'
import { string } from '@/components/VForm/VFormDesigner.umd'
export default {
  name: "FormImportTemplate",
  components:{
    FormImportConfig
  },
  props:{
    // 如果你只想传递formDesignId
    formDesignId: {
      type: [Number,String], // 根据你的需求定义类型
      required: true, // 如果这个prop是必须的，可以设置为true
      default: '' // 提供一个默认值，以防没有传递
    },
  },
  data() {
    return {
      dialogKey: 0,
      templateId: null,
      formImportConfigShow: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // formImportTemplate表格数据
      formImportTemplateList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        formDesignId: null,
        templateName: null,
        templateCode: null,
      },
      // 表单参数
      form: {

      },
      // 表单校验
      rules: {
        templateName: [{ required: true, message: '模板名称不能为空', trigger: 'blur' }],
        templateCode: [{ required: true, message: '模板编码不能为空', trigger: 'blur' }],
      }
    };
  },
  created() {
    console.log('ComponentA已挂载，接收到的formDesignId是：', this.formDesignId);
    if(this.formDesignId){
      this.queryParams.formDesignId = this.formDesignId
    }
    this.getList();
  },
  mounted() {
  },
  methods: {
    viewImportConfig(row){
      this.formImportConfigShow = true;
      this.dialogKey++;
      this.templateId = row.id
    },
    /** 查询formImportTemplate列表 */
    getList() {
      this.loading = true;
      listFormImportTemplate(this.queryParams).then(response => {
        this.formImportTemplateList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        formDesignId: null,
        templateName: null,
        templateCode: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.form.formDesignId = this.formDesignId
      this.open = true;
      this.title = "添加导入模板";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getFormImportTemplate(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改导入模板";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateFormImportTemplate(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addFormImportTemplate(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除编号为"' + ids + '"的数据项？').then(function() {
        return delFormImportTemplate(ids);
    }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport()
    {
      this.download('codeDev/formImportTemplate/export', {
        ...this.queryParams
      }, `formImportTemplate_${new Date().getTime()}.xlsx`)
    }
  }
}
;
</script>
