<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">

      <el-form-item label="唯一名称" prop="onlyName">
        <el-input
          v-model="queryParams.onlyName"
          placeholder="请输入唯一名称/字段名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标签" prop="label">
        <el-input
          v-model="queryParams.label"
          placeholder="请输入标签"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="是否校验" prop="isCheck">
        <el-select v-model="queryParams.isCheck" placeholder="请选择是否校验" clearable>
          <el-option
            v-for="dict in dict.type.import_is_check"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否必填" prop="isRequire">
        <el-select v-model="queryParams.isRequire" placeholder="请选择是否必填" clearable>
          <el-option
            v-for="dict in dict.type.import_is_require"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="校验长度" prop="checkLength">
        <el-input
          v-model="queryParams.checkLength"
          placeholder="请输入校验长度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">

      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['codeDev:formImportConfig:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['codeDev:formImportConfig:remove']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['codeDev:formImportConfig:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="formImportConfigList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="表单设计id" align="center" prop="formDesignId"/>
      <el-table-column label="模版id" align="center" prop="templateId"/>
      <el-table-column label="唯一名称" align="center" prop="onlyName"/>
      <el-table-column label="标签" align="center" prop="label"/>
      <el-table-column label="导入的字段名" align="center" prop="importName"/>
      <el-table-column label="是否校验" align="center" prop="isCheck">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.import_is_check" :value="scope.row.isCheck"/>
        </template>
      </el-table-column>
      <el-table-column label="是否必填" align="center" prop="isRequire">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.import_is_require" :value="scope.row.isRequire"/>
        </template>
      </el-table-column>
      <el-table-column label="校验长度" align="center" prop="checkLength"/>
      <el-table-column label="校验类型" align="center" prop="checkType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.check_type" :value="scope.row.checkType"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['codeDev:formImportConfig:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['codeDev:formImportConfig:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改formImportConfig对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="表单设计id" prop="formDesignId">
          <el-input disabled v-model="form.formDesignId" placeholder="请输入表单设计id"/>
        </el-form-item>
        <el-form-item label="唯一名称/字段名" prop="onlyName">
          <el-input disabled v-model="form.onlyName" placeholder="请输入唯一名称"/>
        </el-form-item>
        <el-form-item label="标签" prop="label">
          <el-input disabled v-model="form.label" placeholder="请输入标签"/>
        </el-form-item>
        <el-form-item label="导入的字段名" prop="importName">
          <el-input v-model="form.importName" placeholder="请输入导入的字段名"/>
        </el-form-item>
        <el-form-item label="是否校验" prop="isCheck">
          <el-select v-model="form.isCheck" placeholder="请选择是否校验">
            <el-option
              v-for="dict in dict.type.import_is_check"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否必填" prop="isRequire">
          <el-select v-model="form.isRequire" placeholder="请选择是否必填">
            <el-option
              v-for="dict in dict.type.import_is_require"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="校验长度" prop="checkLength">
          <el-input-number :min="0" v-model="form.checkLength" placeholder="请输入校验长度"/>
        </el-form-item>
        <el-form-item label="校验类型" prop="checkType">
          <el-select v-model="form.checkType" placeholder="请选择检验类型">
            <el-option
                v-for="dict in dict.type.check_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getFormImportConfig, listFormImportConfig, addFormImportConfig, delFormImportConfig, updateFormImportConfig } from '@/api/codeDev/formImportConfig/formImportConfig'

export default {
  name: "FormImportConfig",
  dicts: ['import_is_require', 'import_is_check','check_type'],
  props:{
    // 如果你只想传递formDesignId
    templateId: {
      type: [Number,String], // 根据你的需求定义类型
      required: true, // 如果这个prop是必须的，可以设置为true
      default: '' // 提供一个默认值，以防没有传递
    },
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // formImportConfig表格数据
      formImportConfigList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        formDesignId: null,
        onlyName: null,
        label: null,
        importName: null,
        isCheck: null,
        isRequire: null,
        checkLength: null,
        checkType: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    if(this.templateId){
      this.queryParams.templateId = this.templateId
    }
    this.getList();
  },
  methods: {
    /** 查询formImportConfig列表 */
    getList() {
      this.loading = true;
      listFormImportConfig(this.queryParams).then(response => {
        this.formImportConfigList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        formDesignId: null,
        onlyName: null,
        label: null,
        importName: null,
        isCheck: null,
        isRequire: null,
        checkLength: null,
        checkType: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加formImportConfig";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getFormImportConfig(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改formImportConfig";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateFormImportConfig(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addFormImportConfig(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除编号为"' + ids + '"的数据项？').then(function() {
        return delFormImportConfig(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport()
    {
      this.download('codeDev/formImportConfig/export', {
        ...this.queryParams
      }, `formImportConfig_${new Date().getTime()}.xlsx`)
    }
  }
}
;
</script>
