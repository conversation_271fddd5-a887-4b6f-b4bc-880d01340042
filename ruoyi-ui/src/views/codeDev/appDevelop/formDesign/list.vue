<template>
    
    <div class="formDesign-list">
        <div class="search-container">
            <el-input @blur="handleSearchMenu" placeholder="请输入内容" prefix-icon="el-icon-search" v-model="keyWord">
            </el-input>
        </div>
        <el-menu class="formDesign-content" :unique-opened="true">
            <navItem @collection="handleCollection" :dataList="formDesignList"></navItem>
        </el-menu>
    </div>
</template>
<script>
import { getUserProfile } from "@/api/system/user";
import navItem from "./navItem.vue"
import { appListMenu, addCollect, cancelCollect } from "@/api/system/menu";
export default {
    data() {
        return {
            id: this.$route.query.id,
            formDesignList: [],
            originFormDesignList: [],
            userId: null,
            keyWord: ""
        };
    },
    components: {
        navItem,
    },
    mounted() {
        this.fetchUserName()
        this.getMenulist()
    },
    methods: {
        async fetchUserName() {
            const response = await getUserProfile();
            if (response.code === 200) {
                this.userId = response.data.userId
            }
        },
        async handleCollection(item) {
            if (!item.collection) {
                const data = {
                    icon:item.icon,
                    label:item.label,
                    path:item.path,
                    query:item.query,
                    isFrame:item.isFrame,
                    menuId: item.id,
                    menuName: item.label,
                    id: item.id,
                    userId: this.userId
                }
                const result = await addCollect(data)
                if (result.code === 200) {
                    this.$message.success("收藏成功")
                    this.getMenulist()
                }
            } else {
                // 取消收藏
                const result = await cancelCollect(item.id)
                if (result.code === 200) {
                    this.$message.success("取消收藏")
                    this.getMenulist()
                }
            }
        },
        findDataByTree(treeData) {
            for (let index = 0; index < treeData.length; index++) {
                const treeDataOption = treeData[index];
                if (treeDataOption.id == this.id) {
                    this.formDesignList.push(...treeDataOption.children)
                    this.originFormDesignList.push(...treeDataOption.children)
                }
                if (treeDataOption.children) {
                    this.findDataByTree(treeDataOption.children)
                }
            }
        },
        async getMenulist() {
            const result = await appListMenu()
            if (result.code === 200) {
                const menuTreeData = result.data
                this.formDesignList = []
                this.findDataByTree(menuTreeData)
            }
        },
        handleSearchMenu() {
            if (this.keyWord) {
                this.formDesignList = this.originFormDesignList.filter(item => item.label.includes(this.keyWord))
            } else {
                this.formDesignList = [...this.originFormDesignList]
            }
        }
    }
};
</script>
<style scoped lang="scss">
.formDesign-list {
    display: flex;
    flex-direction: column;
    position: relative;
    height: 100%;
    padding: 10px;
    background-color: rgb(246, 246, 248);

    .search-container {
        margin-top: 15px;
        background-color: #fff;
        padding: 10px ;
        ::v-deep .el-input__inner {
            background-color: rgb(246, 246, 248);
            border-radius: 25px;
        }
    }

    .formDesign-content {
        background-color: #fff;
        flex: 1;
    }
}
</style>