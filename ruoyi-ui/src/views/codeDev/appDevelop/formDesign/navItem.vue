<template>
  <div class="nav-item">
    <template v-for="item in dataList">
      <el-submenu v-if="item.children && item.children.length" :index="String(item.id)" :key="item.id">
        <div slot="title" class="item-title">
          <span>
            <svg-icon :icon-class="item.icon || ''"></svg-icon>
            {{ item.label }}
          </span>
        </div>
        <navItem v-on="$listeners" :dataList="item.children"></navItem>
      </el-submenu>
      <template v-else>
        <el-menu-item @click.native="handleClickMenu(item)">
          <div slot="title" class="item-title">
            <span>
              <svg-icon :icon-class="item.icon || ''"></svg-icon>
              {{ item.label }}
            </span>
            <i @click.stop="handleCollection(item)" class=" el-rate__icon el-icon-star-off"
              :style="rateStyle(item)"></i>
          </div>
        </el-menu-item>
      </template>
    </template>
  </div>
</template>

<script>
import { getUserProfile } from "@/api/system/user";
import { getToken } from '@/utils/auth'
import { createSign } from "@/utils/opcener-util"
export default {
  name: 'NavItem',
  props: {
    dataList: {
      //导航列表
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      userId: null
    }
  },
  methods: {
    rateStyle(item) {
      return {
        color: item.collection ? "rgb(247, 186, 42)" : "rgb(198, 209, 222)"
      }
    },
    async handleClickMenu(menuData) {
      if (menuData.isFrame == "0") {
        // 外链
        const sign = createSign("admin")
        const response = await getUserProfile();
        const userName = response.data.userName
        const access_token = getToken()
        const path = menuData.path.replace(/&ac_=[^&]*/g, "");
        const userInfo = this.$store.state.user.userInfo
        const dept = userInfo ? userInfo.dept : {}
        const deptCode = dept && dept.deptCode ? dept.deptCode : null
        const isHasPatams = path.includes("?")
        const cockpitPath = `${path}${isHasPatams ? "&" : "?"}${deptCode ? `dept_code=${deptCode}&` : ""}username=${userName}&access_token=${access_token}&sign=${sign}`
        // window.open(cockpitPath, "_blank")
        window.location.href = cockpitPath
      } else {
        const query = menuData.query ? JSON.parse(menuData.query) : {}
        this.$router.push({
          path: "/app/formData/list",
          query: query
        })
      }
    },
    async handleCollection(item) {
      this.$emit("collection", item)
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep .el-submenu {
  .el-submenu__title {
    box-sizing: border-box;
    border-bottom: 1px solid #000;
    color: #000;
    font-weight: 600;
    font-size: 16px;
    position: relative;
    padding: 0 20px;

    .item-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }



}

::v-deep .el-menu-item {
  box-sizing: border-box;
  border-bottom: 1px solid #ccc;
  color: #000;
  font-weight: 600;
  font-size: 16px;
  position: relative;
  padding: 0 20px;

  .item-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }


}
</style>
