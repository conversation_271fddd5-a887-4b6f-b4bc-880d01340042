<template>
  <div class="menu-container search">
    <div class="search-container">
      <el-input @blur="handleSearch" placeholder="请输入内容" prefix-icon="el-icon-search" v-model="keyWord">
      </el-input>
    </div>
    <div class="my-collection menu-container-item">
      <div class="item-title">我的收藏</div>
      <div class="menu-container-content">
        <div class="content-item" @click="handleClickCollectionMenu(childrenOption)"
          v-for="childrenOption in collectMenuData" :key="childrenOption.menuId">
          <svg-icon style="height: 1.2rem; width: 1.2rem;" :icon-class="childrenOption.icon || ''"></svg-icon>
          <span class="name">{{ childrenOption.menuName }}</span>
        </div>
      </div>
    </div>
    <div class="menu-container-item" v-for="menuDate in menuTreeData">
      <div class="item-title">{{ menuDate.label }}</div>
      <div class="menu-container-content">
        <div class="content-item1" @click="handleClickMenu(childrenOption)" v-for="childrenOption in menuDate.children"
          :key="childrenOption.label">
          <svg-icon style="height: 2rem; width: 2rem;" :icon-class="childrenOption.icon || ''"></svg-icon>
          <span class="name">{{ childrenOption.label }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getUserProfile } from "@/api/system/user";
import { getToken } from '@/utils/auth'
import { createSign } from "@/utils/opcener-util"
import { appListMenu, collectMenu } from "@/api/system/menu";
export default {
  data() {
    return {
      originCollectMenuData: [],
      collectMenuData: [],
      originCollectMenuData: [],
      menuTreeData: [],
      keyWord: "",
    };
  },
  mounted() {
    this.getMenulist()
    this.getCollectMenu()
  },
  methods: {
    handleSearch() {
      if (this.keyWord) {
        // 收藏过滤
        this.collectMenuData = this.originCollectMenuData.filter((item) => {
          return item.menuName.includes(this.keyWord)
        })
        // const findTreeNode = (treeData) => {
        //   return treeData.filter((item) => {
        //     if (item.children) {
        //       item.children = findTreeNode(item.children)
        //     }else {
        //       console.log("item.label--item.label",item.label);

        //       return item.label.includes(this.keyWord)
        //     }
        //   })
        // }
        // this.menuTreeData = findTreeNode(this.originMenuTreeData)
        // console.log("filter", this.menuTreeData);
        const findTreeNode = (treeData, keyWord) => {
          return treeData
            .map((item) => {
              const newItem = { ...item };

              // 递归过滤子节点
              if (newItem.children) {
                newItem.children = findTreeNode(newItem.children, keyWord);
              }

              if (newItem.label.includes(keyWord) || (newItem.children && newItem.children.length)) {
                return newItem;
              }
              return null;
            })
            .filter(Boolean);
        };
        this.menuTreeData = findTreeNode(this.originMenuTreeData, this.keyWord);

      } else {
        this.collectMenuData = this.originCollectMenuData
        this.menuTreeData = this.originMenuTreeData
      }
    },
    /**
     * 获取收藏菜单数据
     */
    async getCollectMenu() {
      const result = await collectMenu()
      if (result.code === 200) {
        this.collectMenuData = result.rows
        this.originCollectMenuData = result.rows
      }
    },
    async getMenulist() {
      const result = await appListMenu()
      if (result.code === 200) {
        this.menuTreeData = result.data
        // .map(item => ({
        //   ...item,
        //   children: item.children?.map((child) => ({
        //     ...child,
        //     children: []
        //   })),
        // }));
        this.originMenuTreeData = result.data
        // .map(item => ({
        //   ...item,
        //   children: item.children?.map((child) => ({
        //     ...child,
        //     children: []
        //   })),
        // }));
      }
    },
    async handleClickCollectionMenu(menuData) {
      if (menuData.isFrame == "0") {
        // 外链
        const sign = createSign("admin")
        const access_token = getToken()
        const response = await getUserProfile();
        const userName = response.data.userName

        const path = menuData.path.replace(/&ac_=[^&]*/g, "");
        const userInfo = this.$store.state.user.userInfo
        const dept = userInfo ? userInfo.dept : {}
        const deptCode = dept && dept.deptCode ? dept.deptCode : null
        const isHasPatams = path.includes("?")
        const cockpitPath = `${path}${isHasPatams ? "&" : "?"}${deptCode ? `dept_code=${deptCode}&` : ""}username=${userName}&access_token=${access_token}&sign=${sign}`
        // window.open(cockpitPath, "_blank")
        window.location.href = cockpitPath
      } else {
        const query = menuData.query ? JSON.parse(menuData.query) : {}
        this.$router.push({
          path: "/app/formData/list",
          query: query
        })
      }
    },
    /**
     * 
     * @param menuData 点击菜单的数据
     */
    handleClickMenu(menuData) {
      this.$router.push({
        path: "/app/formDesign/list",
        query: {
          id: menuData.id
        }
      })
    },
  }
};
</script>
<style scoped lang="scss">
.menu-container {
  position: relative;
  height: 100%;
  padding: 10px;
  background-color: rgb(246, 246, 248);

  .menu-container-item {
    height: 35%;
    margin: 5px 0;
    background-color: #fff;
    border-radius: 5px;
    // overflow-y: auto;
    display: flex;
    flex-direction: column;

    .item-title {
      height: 46px;
      padding: 0 10px;
      display: flex;
      align-items: center;
      font-weight: 700;
      font-size: 18px;
    }

    .menu-container-content {
      display: flex;
      flex-wrap: wrap;
      padding: 0 10px;
      flex: 1;
      align-items: flex-start;
      overflow-y: scroll;


      .content-item {
        margin: 5px;
        background-color: rgb(246, 246, 248);
        height: 36px;
        display: flex;
        align-items: center;
        font-size: 14px;
        padding: 0 15px;
        border-radius: 10px;

        .name {
          margin-left: 5px;
          // color: rgb(204, 204, 204)
        }
      }

      .content-item1 {
        width: 25%;
        padding: 10px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .name {
          display: inline-block;
          // 超出文本显示省略号
          overflow: hidden;
          // 不允许换行
          white-space: nowrap;
          // 溢出部分使用省略号
          text-overflow: ellipsis;
          margin-top: 10px;
          font-size: 14px;
        }

        &:hover {
          background-color: #f5f5f5;
        }
      }
    }
  }

  .my-collection {
    height: 20%;
  }

  .search-container {
    padding: 0 10px;
    margin: 10px 0;

    ::v-deep .el-input__inner {
      // background-color: rgb(246, 246, 248);
      border-radius: 25px;
    }
  }
}
</style>
