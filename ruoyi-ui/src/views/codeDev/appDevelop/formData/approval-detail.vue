<template>
    <div class="approval-detail-container">
        <el-timeline class="approval-container" v-if="!!warmFlow">
            <el-timeline-item v-for="flowNode in flowNodes" :color="flowNode.color" :key="flowNode.id">
                <el-card class="approval-card">
                    <div slot="header" class="approval-header">
                        {{ flowNode.nodeName }}
                    </div>
                    <div class="approval-info" v-for="item in flowNode.approverInfoList">
                        <div class="approval-name" :style="{ backgroundColor: approvalBackgroundColor(item) }">{{
                            item.userName }} <span v-if="item.skipTypeName">({{ item.skipTypeName }})</span></div>
                        <div class="approval-time">{{ item.approvalTime }}</div>
                    </div>
                </el-card>
            </el-timeline-item>
        </el-timeline>
    </div>
</template>
<script>
import { getInstanceIds } from '@/api/flow/instance'
import { getNodesByDefCode1 } from '@/api/flow/execute'
export default {
    name: 'ApprovalDetail',
    data() {
        return {
            instanceIds: [],
            flowNodes: [],
            approverSkipTypeOptions: [

            ]
        }
    },
    props: {
        warmFlow: {
            type: String,
            required: true
        }
    },
    async mounted() {
        await this.getDicts('approval_status').then(response => {
            if (response.code === 200) {
                this.approverSkipTypeOptions = response.data.map(this.buildOption)
            }
        })
        await this.getInstanceIds()
        await this.handleGetNodesByDefCode()

    },
    methods: {
        buildOption(item) {
            return {
                ...item,
                label: item.dictLabel,
                value: item.dictValue
            }
        },
        async getInstanceIds() {
            const mongoId = this.$route.query.mongoId
            const result = await getInstanceIds(mongoId)
            if (result.code == 200) {
                const instanceIds = result.data || []
                this.instanceIds = instanceIds.split(',')
            }
        },
        async handleGetNodesByDefCode() {
            const lastInstanceId = this.instanceIds[this.instanceIds.length - 1]
            const result = await getNodesByDefCode1(this.warmFlow, lastInstanceId)
            if (result.code == 200) {
                this.flowNodes = result.data.map(item => {
                    const approverInfoList = item.approverInfoList || []
                    const newApproverInfoList = approverInfoList.map(info => {
                        const skipType = this.approverSkipTypeOptions.find(option => option.value === info.skipType)
                        info.skipTypeName = skipType ? skipType.label : ''
                        return info
                    })
                    return {
                        nodeName: item.nodeName,
                        approverInfoList: newApproverInfoList,
                        color: item.nodeStatus === "0" ? "#75fb4c" : item.nodeStatus === "1" ? "#f3b2b1" : "#000000"
                    }
                })
            }
        },
        approvalBackgroundColor(item) {
            return item.skipType === 'PASS' ? '#48a0d0' : item.skipType === 'REJECT' ? "#c72a29" : '#d7d7d7'
        }
    }
}
</script>
<style lang="scss" scoped>
.approval-detail-container {
    position: relative;
    width: 100%;
    // height: 100%;
    background-color: #f5f5f5;

    .approval-container {
        margin-top: 20px;
        padding-left: 20px;
        padding-right: 20px;

        ::v-deep .approval-card {
            .el-card__body {
                padding: 5px 10px;
            }

            .approval-info {
                display: flex;
                // flex-direction: column;
                margin-bottom: 10px;
                justify-content: space-between;
                align-items: center;

                // padding: 10px;
                // border-bottom: 1px solid #e5e5e5;
                .approval-name {
                    padding: 5px;
                    border-radius: 5px;
                    color: #fff;
                    font-size: 12px;
                }

                .approval-time {
                    font-size: 12px;
                }
            }
        }

    }
}
</style>
