<template>
    <div class="formDesign-detail">
        <div class="custom-bar">
            <button class="bar-btn" @click="$router.go(-1)">返回</button>
            <div class="bar-right">
                <button v-if="hasSaveBtn" class="bar-btn" @click="handleClickSaveForm">保存</button>
                <button v-if="hasSaveAndStartApprovalBtn" class="bar-btn"
                    @click="handleClickStartApproval">保存并发起审批</button>
            </div>
        </div>
        <el-tabs v-model="activeName" type="card">
            <el-tab-pane label="数据详情" name="dataDetail"></el-tab-pane>
            <el-tab-pane label="流程详情" name="approvalDetail"></el-tab-pane>
        </el-tabs>

        <v-form-render class="formDesign-render" v-if="hasFormRender" :handle-type="handleType" ref="formRenderRef"
            :form-json="formRender.formJson" :form-data="formRender.formData"></v-form-render>


        <initiate-approval ref="initiateApprovalRef" v-if="hasInitiateApproval"
            :warmFlow="warmFlow"></initiate-approval>

        <approval-detail v-if="hasApprovalDetail" :warmFlow="warmFlow"></approval-detail>
    </div>
</template>
<script>
import InitiateApproval from './initiate-approval.vue'
import ApprovalDetail from './approval-detail.vue'
import { flowStart } from '@/api/flow/instance'
import { getFormDesignList, getSearchConfigList } from '@/api/codeDev/formDesign/formDesign'
import {
    getFormDataList,
    insertFormData,
    updateFormData
} from '@/api/codeDev/formData/formData'

export default {
    components: {
        InitiateApproval,
        ApprovalDetail
    },
    data() {
        return {
            renderFormRender: false,
            formRender: {
                formJson: {},
                formData: {},
            },
            formDesignConfig: {

            },
            tableColumns: [],
            activeName: 'dataDetail',
        };
    },
    computed: {
        handleType() {
            const mongoId = this.$route.query.mongoId
            return mongoId ? "edit" : "add"
        },
        hasFormRender() {
            return this.renderFormRender && this.activeName === 'dataDetail'
        },
        warmFlow() {
            return this.formDesignConfig.warmFlow
        },
        // 未发起
        notInitiated() {
            return this.formRender.formData.approvalStatus === -1
        },
        // 审批中
        approval() {
            return this.formRender.formData.approvalStatus === 1
        },
        // 已处理
        processed() {
            return this.formRender.formData.approvalStatus === 2
        },
        // 撤回
        withdrawn() {
            return this.formRender.formData.approvalStatus === 3
        },
        // 驳回
        rejected() {
            return this.formRender.formData.approvalStatus === 4
        },
        // 是否显示发起审批
        hasInitiateApproval() {
            // 未发起 撤回 驳回显示
            return ((this.notInitiated || this.withdrawn || this.rejected) || this.handleType === 'add') && !!this.warmFlow && this.activeName === 'dataDetail'
        },
        // 是否显示审批详情
        hasApprovalDetail() {
            // 审批中 已处理 显示
            return (this.approval || this.processed || this.withdrawn || this.rejected) && this.activeName === 'approvalDetail' && !!this.warmFlow
        },
        isValid() {
            return this.formRender.formData.is_valid
        },
        // 是否显示保存按钮
        hasSaveBtn() {
            return this.activeName === 'dataDetail' && (this.isValid == 0 || this.handleType === 'add')
        },
        // 是否显示保存并发起审批按钮
        hasSaveAndStartApprovalBtn() {
            return this.activeName === 'dataDetail' && (this.isValid == 0 || this.handleType === 'add') && this.warmFlow
        }
    },
    async mounted() {
        await this.getFormDesignConfig()
        await this.haneleGetSearchConfigList()
        await this.handleGetFormDataDetail()
    },
    methods: {
        async getFormDesignConfig() {
            const tableCode = this.$route.query.tableCode
            const result = await getFormDesignList({ tableCode })
            try {
                this.formDesignConfig = result[0]
                const { widgetList, formConfig } = JSON.parse(this.formDesignConfig.formJsonInfo)
                const newWidgetList = []
                this.processWidgets(widgetList, newWidgetList)
                this.formRender.formJson = {
                    widgetList: newWidgetList,
                    formConfig
                }

            } catch (error) {
                console.error(error)
            }
        },
        async haneleGetSearchConfigList() {
            const result = await getSearchConfigList(this.formDesignConfig.id)
            if (result.code === 200) {
                this.tableColumns = result.data.listConfiguration
            }
        },
        processWidgets(widgetList, newWidgetList) {
            for (let i = 0; i < widgetList.length; i++) {
                const item = widgetList[i]
                if (item.cols) {
                    this.processWidgets(item.cols, newWidgetList)
                } else if (item.widgetList) {
                    this.processWidgets(item.widgetList, newWidgetList)
                } else {
                    newWidgetList.push(item)
                }
            }
        },
        async handleClickSaveForm() {
            const mongoId = this.$route.query.mongoId
            const formDesignId = this.$route.query.formDesignId
            const tableCode = this.$route.query.tableCode
            const formData = await this.$refs.formRenderRef.getFormData()
            const requestParams = {
                mongoId: mongoId ? mongoId : null,
                formDesignId,
                tableCode,
                columnAndDataStr: formData
            }
            if (mongoId) {
                const result = await updateFormData(requestParams)
                if (result.code === 200) {
                    this.$message.success('编辑成功')
                    this.$router.go(-1)
                }
            } else {
                // 新建
                const result = await insertFormData(requestParams)
                if (result.code === 200) {
                    this.$message.success('新建成功')
                    this.$router.go(-1)
                }
            }
        },
        async handleClickStartApproval() {
            const mongoId = this.$route.query.mongoId
            const formDesignId = this.$route.query.formDesignId
            const tableCode = this.$route.query.tableCode
            const formData = await this.$refs.formRenderRef.getFormData()
            const requestParams = {
                mongoId: mongoId ? mongoId : null,
                formDesignId,
                tableCode,
                columnAndDataStr: formData
            }

            const nodePermissionFlag = this.$refs.initiateApprovalRef.flowNodes.filter(item => item.nodeType == "1").map((item, index) => {
                if ((item.approvalMode != '3' || index === 0) && (!item.ids || item.ids.length === 0)) {
                    this.$message.error(`${item.nodeName} 未选择审批人请选择！`)
                    return null
                }
                item.ids = item.ids || []
                return {
                    id: item.ids.join(','),
                    code: item.nodeCode
                }
            })
            if (!nodePermissionFlag.every(_ => _)) {
                return
            }
            if (!!mongoId) {
                // 编辑
                const result = await updateFormData(requestParams)
                if (result.code === 200) {
                    this.$message.success('编辑成功')
                    this.$router.go(-1)
                    // 发起审批
                    const startFlowRequestParams = {
                        dataIds: [mongoId],
                        columnInfoStr:this.formDesignConfig.columnInfo,
                        flowCode: this.warmFlow,
                        tableCode,
                        copyUserIds: [],
                        formDesignId,
                        data: [formData],
                        nodePermissionFlag
                    }
                    const result1 = await flowStart(startFlowRequestParams)
                    if (result1.code === 200) {
                        this.$message.success('发起成功')
                        this.$router.go(-1)
                    }
                }
            } else {
                // 新建
                const result = await insertFormData(requestParams)
                if (result.code === 200) {
                    this.$message.success('新建成功')
                    formData._id = result.data
                    const startFlowRequestParams = {
                        dataIds: [result.data],
                        columnInfoStr:this.formDesignConfig.columnInfo,
                        flowCode: this.warmFlow,
                        tableCode,
                        copyUserIds: [],
                        formDesignId,
                        data: [formData],
                        nodePermissionFlag
                    }
                    const result1 = await flowStart(startFlowRequestParams)
                    if (result1.code === 200) {
                        this.$message.success('发起成功')
                        this.$router.go(-1)
                    }
                }
            }

        },
        handleFileUrl(formData) {
            const fileTypes = ['picture-upload'] // 'file-upload'
            this.tableColumns.forEach(item => {
                if (fileTypes.includes(item.type)) {
                    const fileList = formData[item.name]
                    if (fileList && fileList.length) {
                        fileList.forEach(i => {
                            i.url = `${window.location.origin}${"/opsyndex-file"}${i.relativePath}`
                        })
                    }
                }
            })
            return formData
        },
        /**
         * 获取当前表单数据
         */
        async handleGetFormDataDetail() {
            // 查询数据
            const formDesignId = this.$route.query.formDesignId
            const tableCode = this.$route.query.tableCode
            const mongoId = this.$route.query.mongoId
            const queryParams = {
                formDesignId,
                pageNum: 1,
                pageSize: 10,
                tableCode,
                searchFormParams: {
                    _id: mongoId
                }, // 勿动 后端接口需要
                // mongoId: mongoId
            }
            const res = await getFormDataList(queryParams)
                .finally(() => {
                    this.listLoading = false
                })
            if (res.code === 200) {
                const formData = res.data.dataList.find(item => item._id === mongoId)
                if (formData) {
                    this.formRender.formData = this.handleFileUrl(formData)

                }
                this.renderFormRender = true
            } else {
                this.$message.error('查询失败')
            }
        },
    }
};
</script>
<style scoped lang="scss">
.formDesign-detail {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;

    // 顶部操作栏
    .custom-bar {
        width: 100%;
        height: 56px;
        min-height: 56px;
        background: #0079FE;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 16px;
        box-sizing: border-box;

        .bar-btn {
            background: #005bb5;
            color: #fff;
            border: none;
            border-radius: 16px;
            padding: 3px 12px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.2s;
            height: 28px;
            line-height: 22px;
            display: inline-block;
        }

        .bar-btn:hover {
            background: #003e80;
        }

        .bar-right {
            display: flex;
        }

        .bar-right .bar-btn+.bar-btn {
            margin-left: 10px;
        }

        .bar-right .bar-btn+.bar-btn {
            margin-left: 20px;
        }
    }

    ::v-deep .el-tabs {
        .el-tabs__nav {
            width: 100%;

            .el-tabs__item {
                width: 50%;
                text-align: center;
                background: #fff;
                color: #3477f5;
            }

            .el-tabs__item.is-active {
                background: #3477f5;
                color: #fff
            }
        }
    }

    .formDesign-render {
        margin-top: 10px;
        padding-left: 20px;
        padding-right: 20px;
    }






}
</style>
