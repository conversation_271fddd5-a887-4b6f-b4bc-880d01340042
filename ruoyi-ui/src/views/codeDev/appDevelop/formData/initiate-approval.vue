<template>
    <div class="initiate-approval-container">
        <el-timeline class="approval-container" v-if="!!warmFlow">
            <el-timeline-item v-for="(flowNode, index) in flowNodes" :key="flowNode.id">
                <div style="font-size: 16px; margin-bottom: 10px;">{{ flowNode.nodeName }}</div>
                <div class="user-container" style="margin-bottom: 10px; padding: 5px 0;overflow-y: auto;">
                    <el-tag style="margin-right: 5px;margin-bottom: 5px;" closable
                        v-for="(userName, idx) in flowNode.userName" :key="userName" type="success"
                        @close="handleRemoveUser(flowNode, idx, index)">{{ userName }}</el-tag>
                </div>
                <el-button v-if="hasFlowButton(flowNode)" type="primary" icon="el-icon-plus" circle
                    @click="handleClickFlowNode(flowNode)" />
            </el-timeline-item>
        </el-timeline>
        <el-dialog title="选择人员" :visible.sync="dialogVisible" width="100%" top="8vh" class="user-select-dialog">
            <!-- 搜索框 -->
            <el-select v-model="searchValue" filterable remote clearable placeholder="搜索人员"
                :remote-method="remoteSearchUser" :loading="searchLoading" class="user-select-search"
                style="width: 100%;" @change="handleSelectUser">
                <el-option v-for="item in searchUserList" :key="item.userId" :label="item.nickName"
                    :value="item.userId">
                    <span>{{ item.nickName }}</span>
                    <span style="color: #888; font-size: 12px; margin-left: 8px;">{{ item.username }}</span>
                </el-option>
            </el-select>
            <!-- 面包屑 -->
            <el-breadcrumb separator="/" class="user-select-breadcrumb">
                <el-breadcrumb-item v-for="(item, idx) in breadcrumbList" @click.native="handleSelectDept(item, idx)"
                    :key="idx">{{
                        item.label
                    }}</el-breadcrumb-item>
            </el-breadcrumb>

            <!-- 组织/人员树 -->
            <el-menu class="user-select-menu"
                style="border: none; background: #fff; border-radius: 8px; overflow-y: auto;">
                <template v-for="(item) in deptList">
                    <el-menu-item :key="item.id" @click="handleSelectDept(item)">
                        <el-checkbox v-if="item.type !== 'dept'" v-model="item.checked"></el-checkbox>
                        <i :class="item.type === 'dept' ? 'el-icon-school' : 'el-icon-user'"
                            style="color: #1976d2; font-size: 20px; margin-right: 8px;"></i>
                        <span>{{ item.label }}</span>
                    </el-menu-item>
                </template>
            </el-menu>
            <span slot="footer" class="dialog-footer user-select-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="handleConfirm">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>

import { getDeptTreeById, getUserInfoApp } from "@/api/system/user";
import { getNodesByDefCode } from '@/api/flow/execute'
export default {
    props: {
        warmFlow: {
            type: [String],
            required: true
        }
    },
    data() {
        return {
            dialogVisible: false,
            searchValue: '',
            deptList: [],
            flowNodes: [],
            breadcrumbList: [],
            searchUserList: [],
            searchLoading: false,
            currentFlowNode: null,
        }
    },
    async mounted() {
        await this.getFlowNodes()
    },
    methods: {
        async handleSelectDept(node, idx) {
            if ((!node || !node.type) || node.type == 'dept') {
                if (idx !== undefined) {
                    this.breadcrumbList.splice(idx + 1)
                } else {
                    node && this.breadcrumbList.push({
                        label: node.label,
                        id: node.id
                    })
                }
                const id = node ? node.id : 100
                const userId = node ? node.userId : null
                const result = await getDeptTreeById(id, userId)
                if (result.code === 200) {
                    this.deptList = result.data.map(item => ({
                        ...item,
                        checked: false
                    }))
                }
                this.dialogVisible = true
            }

        },
        async getFlowNodes() {
            const result = await getNodesByDefCode(this.warmFlow)
            if (result.code === 200) {
                this.flowNodes = result.data
            }
        },
        handleClickFlowNode(flowNode) {
            this.currentFlowNode = flowNode
            this.handleSelectDept()
        },
        hasFlowButton(flowNode) {
            return ![0, 2].includes(flowNode.nodeType) || !flowNode.approvalMode === '1'
        },
        async remoteSearchUser(query) {
            if (!query) {
                this.searchUserList = [];
                return;
            }
            this.searchLoading = true;
            // 这里调用你的远程搜索API，示例：
            const res = await getUserInfoApp(query).finally(() => {
                this.searchLoading = false;
            })
            if (res.code === 200) {
                this.searchUserList = res.data
                console.log("this.searchUserList", this.searchUserList);

            }
        },
        handleRemoveUser(flowNode, idx, parentIndex) {
            const originIds = this.flowNodes[parentIndex].ids || []
            const originUserName = this.flowNodes[parentIndex].userName || []
            originIds.splice(idx, 1)
            originUserName.splice(idx, 1)
            this.flowNodes[parentIndex].ids = originIds
            this.flowNodes[parentIndex].userName = originUserName
        },
        handleConfirm() {
            const checkedNodes = this.deptList.filter(item => item.checked)
            const currentFlowNodeIndex = this.flowNodes.findIndex(item => item.id === this.currentFlowNode.id)
            const ids = checkedNodes.map(item => item.id)
            const userNames = checkedNodes.map(item => item.label)
            // 追加数据 不能覆盖
            const originIds = this.flowNodes[currentFlowNodeIndex].ids || []
            const originUserName = this.flowNodes[currentFlowNodeIndex].userName || []
            this.flowNodes[currentFlowNodeIndex].ids = Array.from(new Set([...originIds, ...ids]))
            this.flowNodes[currentFlowNodeIndex].userName = Array.from(new Set([...originUserName, ...userNames]))
            this.dialogVisible = false
        },
        handleSelectUser(val) {
            // 选中后可做后续处理
            const userInfo = this.searchUserList.find(u => u.userId === val);
            if (userInfo) {
                this.handleSelectDept({
                    type: 'dept',
                    userId: userInfo.userId,
                    id: userInfo.deptId,
                })
            } else {
                this.handleSelectDept({
                    type: 'dept',
                    userId: null,
                    id: null
                })
            }
        },
    }
}
</script>
<style lang="scss" scoped>
.initiate-approval-container {
    .approval-container {
        margin-top: 20px;
        padding-left: 20px;
        padding-right: 20px;
    }

    ::v-deep .user-select-dialog {
        border-radius: 14px !important;
        box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.18) !important;

        .el-dialog__header {
            border-bottom: 1px solid #f0f0f0;
            padding: 18px 24px 12px 24px;
            font-size: 20px;
            font-weight: 600;
            color: #222;
        }

        .el-dialog__body {
            padding: 18px 24px 0 24px;
            background: #fafbfc;
        }

        .el-dialog__footer {
            padding: 16px 24px 18px 24px;
            background: #fafbfc;
            border-top: 1px solid #f0f0f0;
        }
    }


    .user-select-search {
        margin-bottom: 12px;
        border-radius: 6px;
        font-size: 13px;
        height: 30px;
    }

    .user-select-breadcrumb {
        background: #f5f5f5;
        padding: 7px 12px 7px 12px;
        margin-bottom: 12px;
        border-radius: 6px;
        font-size: 14px;
        min-height: 14px;
    }

    .user-select-menu {
        background: #fff;
        border-radius: 8px;
        padding: 4px 0;
        min-height: 100px;
        max-height: 280px;
        overflow-y: auto;
        font-size: 14px;
    }

    .user-select-menu .el-menu-item {
        min-height: 30px !important;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
        display: flex;
        align-items: center;
        height: 30px;
    }


    .user-select-footer {
        display: flex;
        justify-content: flex-end;
        gap: 12px;

        .el-button {
            min-width: 64px;
            height: 28px;
            font-size: 13px;
            border-radius: 6px;
            padding: 0 12px;
        }
    }

}
</style>