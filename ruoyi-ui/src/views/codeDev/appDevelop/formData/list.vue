<template>
  <div class="formData-list">
    <div class="custom-bar">
      <button class="bar-btn" @click="$router.go(-1)">返回</button>
      <div class="bar-right">
        <button class="bar-btn" @click="handleOpenSearchDrawer">搜索</button>
        <button class="bar-btn" @click="handleAddFormData">新增</button>
      </div>
    </div>
    <van-pull-refresh class="formData-refresh-container" v-model="refreshing" @refresh="handleOnRefresh">
      <van-list class="formData-list-container" v-model="listLoading" :finished="loadDataFinished"
        @load="handleLoadFormDataList" :offset="20" :immediate-check="false">
        <el-card class="formData-item" v-for="(item, idx) in formDataList" :key="idx"
          @click.native="handleEditFormData(item)">
          <div class="status-bar" v-if="item.approvalStatus !== undefined"
            :style="{ backgroundColor: (approvalStatusMap[item.approvalStatus] && approvalStatusMap[item.approvalStatus].color) || '#D7D7D7' }">
          </div>
          <!-- 操作列 -->
          <div slot="header" class="formData-header" @click.stop>
            <!-- 审批状态 -->
            <div class="approval-status" v-if="item.approvalStatus !== undefined">
              <span class="dot"
                :style="{ backgroundColor: (approvalStatusMap[item.approvalStatus] && approvalStatusMap[item.approvalStatus].color) || '#D7D7D7' }"></span>
              <span class="status-text"
                :style="{ color: (approvalStatusMap[item.approvalStatus] && approvalStatusMap[item.approvalStatus].color) || '#D7D7D7' }">
                {{ (approvalStatusMap[item.approvalStatus] && approvalStatusMap[item.approvalStatus].text) || '未知' }}
              </span>
            </div>
            <el-button type="text" size="mini" v-if="hasDeleteButton(item)" style="color: #D9001B;"
              @click.stop="handleDeleteRow(item)">
              <i class="el-icon-delete"></i>
            </el-button>
          </div>
          <div class="formData-content">
            <el-row class="row-class" v-for="columnInfo in tableColumns" :key="columnInfo.name">
              <el-col :span="9" class="label">{{ columnInfo.label }}</el-col>
              <el-col :span="15" class="data">
                <!-- <span class="ellipsis" :title="item[columnInfo.name]">{{ item[columnInfo.name] }}</span> -->
                <span class="ellipsis" v-html="getTableRenderItem(item, columnInfo)"></span>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </van-list>
    </van-pull-refresh>

    <el-drawer title="搜索" :visible.sync="searchDrawerVisible" direction="btt" size="50%">
      <search-config-form :inline=false ref="searchConfigFormRef" :searchConfig="searchConfiguration"
        :approvalStatuOptions="approvalStatuOptions" :validOptions="validOptions" :delFlagOptions="delFlagOptions"
        @search="handleSearch" @reset="handleReset"></search-config-form>
    </el-drawer>
  </div>
</template>
<script>
import SearchConfigForm from '../../../codeDev/formData/searchConfigForm.vue'
import Vue from "vue"
import {
  getFormDataList, deleteFormDataById
} from '@/api/codeDev/formData/formData'
import { List, Calendar, PullRefresh } from "vant"
import "vant/es/calendar/style";
import "vant/es/list/style";
import "vant/es/pull-refresh/style";
Vue.use(Calendar).use(PullRefresh).use(List);
import { getSearchConfigList, getButtonPermissions, getFormDesignList } from "@/api/codeDev/formDesign/formDesign";
export default {
  data() {
    return {
      searchConfiguration: [],
      tableColumns: [],
      refreshing: false,
      columnInfoList: [],
      formDataList: [],
      searchDrawerVisible: false,
      loadDataFinished: true,
      listLoading: false,
      queryParams: {
        searchFormParams: {},
        pageNum: 1,
        pageSize: 10,
        tableCode: null,
        formDesignId: null,
        // formJsonInfo: null,
        tableName: null,
      },
      approvalStatuOptions: [
        { label: '未发起', value: "-1" },
        { label: '未开始', value: "0" },
        { label: '审批中', value: "1" },
        { label: '审批结束', value: "2" },
        { label: '驳回', value: "3" },
        { label: '撤回', value: "4" }
      ],
      validOptions: [
        {
          label: "无效",
          value: "0"
        }, {
          label: "有效",
          value: "1",
        }
        , {
          label: "锁定",
          value: "2",
        }
      ],
      delFlagOptions: [
        {
          value: "0",
          label: "正常"
        },
        {
          value: "1",
          label: "删除"
        }
      ],
      formDesignDetail: {
        id: null,
        tableName: null,
        tableCode: null
      },
      approvalStatusMap: {
        "-1": { text: "未提交", color: "#D7D7D7" },
        "1": { text: "处理中", color: "#0079FE" },
        "2": { text: "已处理", color: "#00A2D5" },
        "3": { text: "驳回", color: "#D9001B" },
        "4": { text: "撤回", color: "#D9001B" }
      },
      buttonPermissions: []
    };
  },
  components: {
    SearchConfigForm,
  },
  async mounted() {
    await this.getFormDesignDetail()
    await this.handleGetFormDesignFieldConfig();
    this.handleGetFormDataList()
    this.handleGetButtonPermissions()
  },
  methods: {
     /**
     * 渲染table-item的方法
     * @param row
     * @param item
     */
     getTableRenderItem(row, item) {
      let result = ""
      if (item.type == 'file-upload' || item.type == 'picture-upload') {
        // const fileOptions = this.getFileOptions(row[item.name])
        // if (fileOptions && fileOptions.length > 0) {
        //   fileOptions.forEach(fileOption => {
        //     result += `<span style="cursor: pointer;">${fileOption.name}</span>`
        //   })
        // }
        result = row[item.name]
      } else if (item.name === 'approvalStatus') {
        result = this.getLabelMethod(item.name, row[item.name])
      } else if (item.name === 'is_valid') {
        result = this.getLabelMethod(item.name, row[item.name])
      } else if (item.name === 'del_flag') {
        result = this.getLabelMethod(item.name, row[item.name])
      } else if (item.formatScript) {
        const formatScriptFunction = new Function(item.formatScript)
        result = formatScriptFunction.call(row)
      } else {
        result = row[item.name]
      }
      return result
    },
    getLabelMethod(filedName, value) {
      if (filedName === "approvalStatus") {
        const statusOption = this.approvalStatuOptions.find(item => value == item.value)
        if (statusOption) {
          return statusOption.label
        } else {
          return value
        }
      }
      if (filedName === "is_valid") {
        const vaildOption = this.validOptions.find(item => value == item.value)
        if (vaildOption) {
          return vaildOption.label
        } else {
          return value
        }
      }
      if (filedName === "del_flag") {
        const delFlagOption = this.delFlagOptions.find(item => value == item.value)
        if (delFlagOption) {
          return delFlagOption.label
        } else {
          return value
        }
      }
    },
    hasAddButton() {
      return this.buttonPermissions.includes('added') || this.buttonPermissions.includes('all')
    },
    /**
     * 获取当前页面按钮权限
     */
    async handleGetButtonPermissions() {
      const tableCode = this.queryParams.tableCode
      const result = await getButtonPermissions(tableCode)
      if (result.code === 200) {
        this.buttonPermissions = result.data ? result.data.split(',') : []
      }
    },
    hasDeleteButton(row) {
      // http://144.123.43.78:19201/index.php?m=bug&f=view&bugID=2731
      // 删除只有在当前行数据 is_valid = 0 的时候支持删除 is_valid = 0 代表 无效
      return (this.buttonPermissions.includes('delete') && row.is_valid == 0) || this.buttonPermissions.includes('all')
    },
    /**
     * 获取表单设计详情
     */
    async getFormDesignDetail() {
      const tableCode = this.$route.query.tableCode
      await getFormDesignList({ tableCode }).then(
        async (response) => {
          this.queryParams.formDesignId = response[0].id
          this.queryParams.tableCode = response[0].tableCode
          // this.queryParams.formJsonInfo = response[0].formJsonInfo
          this.queryParams.tableName = response[0].tableName

          this.formDesignDetail.tableName = response[0].tableName
          this.formDesignDetail.tableCode = response[0].tableCode
          this.formDesignDetail.id = response[0].id
        }
      )
    },
    handleSearch() {
      this.queryParams.pageNum = 1
      this.queryParams.searchFormParams = this.$refs.searchConfigFormRef.handleBuildRequestParams()
      this.handleGetFormDataList()
      this.searchDrawerVisible = false
    },
    handleReset() {
      this.queryParams.searchFormParams = this.$refs.searchConfigFormRef.handleBuildRequestParams()
      this.handleGetFormDataList()
      this.searchDrawerVisible = false
    },
    beforeHandleCommand(scope, command) {
      return {
        scope,
        command
      }
    },
    handleCommand(command) {
      switch (command.command) {
        case 'delete':
          this.handleDeleteRow(command.scope)
          break;
        default:
          break;
      }

    },
    handleDeleteRow(scope) {
      this.$confirm('确定要进行删除操作吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const tableCode = this.formDesignDetail.tableCode
          const requstParams = {
            tableCode,
            mongoId: scope._id
          }
          const res = await deleteFormDataById(requstParams)
          if (res.code === 200) {
            this.$message.success('删除成功')
          } else {
            this.$message.error('删除失败')
          }
          this.handleGetFormDataList()
        })
    },
    /**
     * 获取表头列
     */
    async handleGetFormDesignFieldConfig() {
      // const formDesignId = this.$route.query.formDesignId
      const result = await getSearchConfigList(this.formDesignDetail.id)
      if (result.code === 200) {
        this.searchConfiguration = result.data.searchConfiguration
        this.tableColumns = result.data.listConfiguration
      }
    },
    /**
     * 上滑加载更多
     */
    handleLoadFormDataList() {
      this.queryParams.pageNum++
      this.handleGetFormDataList(true)
    },
    /**
     * 获取列表数据
     * @param loadData 是否加载
     */
    async handleGetFormDataList(loadData = false) {
      const queryParams = this.queryParams
      this.refreshing = true
      this.listLoading = true
      const res = await getFormDataList(queryParams)
        .finally(() => {
          this.refreshing = false
          this.listLoading = false
        })
      if (res.code === 200) {
        const total = res.data && res.data.total ? res.data.total : 0
        const dataList = res.data && res.data.dataList ? res.data.dataList : []
        if (loadData) {
          this.formDataList.push(...dataList)
        } else {
          this.formDataList = dataList
          this.total = res.data.total
        }
        this.loadDataFinished = this.formDataList.length >= total

      } else {
        this.$message.error('查询失败')
      }
    },

    /**
     * 新增
     */
    handleAddFormData() {
      const tableCode = this.formDesignDetail.tableCode
      const formDesignId = this.formDesignDetail.id
      this.$router.push({
        path: "/app/formData/detail", query: {
          tableCode,
          formDesignId,
          mongoId: null
        }
      })
    },
    handleOpenSearchDrawer() {
      this.searchDrawerVisible = true
      this.$nextTick(() => {
        this.$refs.searchConfigFormRef.handleBuildSearchConfigForm()
      })
    },
    /**
     * 编辑
     * @param row
     */
    handleEditFormData(row) {
      const tableCode = this.formDesignDetail.tableCode
      const formDesignId = this.formDesignDetail.id
      const mongoId = row._id
      this.$router.push({
        path: "/app/formData/detail", query: {
          tableCode,
          formDesignId,
          mongoId
        }
      })
    },
    async handleOnRefresh() {
      this.queryParams.pageNum = 1
      this.handleGetFormDataList()
    }
  }
};
</script>
<style scoped lang="scss">
// ====== 页面整体布局 ======
.formData-list {
  position: relative;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

// ====== 顶部操作栏 ======
.custom-bar {
  width: 100%;
  height: 56px;
  min-height: 56px;
  background: #0079FE;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  box-sizing: border-box;
}

.bar-btn {
  background: #005bb5;
  color: #fff;
  border: none;
  border-radius: 16px;
  padding: 3px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s;
  height: 28px;
  line-height: 22px;
  display: inline-block;
}

.bar-btn:hover {
  background: #003e80;
}

.bar-right {
  display: flex;
}

.bar-right .bar-btn+.bar-btn {
  margin-left: 10px;
}

// ====== 列表容器 ======
.formData-refresh-container {
  background: #dcdfe6;
  flex: 1;
  overflow-y: auto;
}

// ====== 卡片样式 ======
.formData-item {
  margin-bottom: 8px;
  border-radius: 4px;
  background-color: #ffffff;
  min-height: 100px;
  position: relative;

  .formData-header {
    display: flex;
    justify-content: space-between;
  }

  .formData-content {
    width: 90%;
  }
}

// ====== 卡片左侧状态条 ======
.status-bar {
  position: absolute;
  left: 0;
  top: 0;
  width: 8px;
  height: 100%;
  border-radius: 4px 0 0 4px;
}

// ====== 审批状态区块 ======
.approval-status {
  // position: absolute;
  // right: 20px;
  // top: 50%;
  // transform: translateY(-50%);
  display: flex;
  align-items: center;
  font-size: 16px;
  z-index: 10;

  .dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 6px;
  }

  .status-text {
    font-weight: 500;
    margin-right: 2px;
    font-size: 14px;
  }
}

// ====== 表格内容省略号 ======
.ellipsis {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
}

// ====== 行样式 ======
.row-class {
  width: 100%;
  display: flex;
}

// ====== el-drawer样式 ======
::v-deep .el-drawer__body {
  padding: 10px;
}
</style>
