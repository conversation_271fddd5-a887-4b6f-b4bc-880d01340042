<template>
    <div class="page-container">
        <menu-content class="page-content" v-if="isMenuActive"></menu-content>
        <user-content class="page-content" v-if="isUserActive"></user-content>
        <!-- 这个layout布局写的 一言难尽。。。。。 懒得改了 -->
        <div class="footer-wrap">
            <div class="btn-class" @click="menuClickMethod" style="border-right: 1px solid #f7f7f7;">
                <div :class="isMenuActive ? 'color-blue' : ''"><i class="el-icon-menu"></i></div>
                <div :class="isMenuActive ? 'color-blue' : ''">菜单</div>
            </div>
            <div class="btn-class" @click="userClickMethod">
                <div :class="isUserActive ? 'color-blue' : ''"><i class="el-icon-user"></i></div>
                <div :class="isUserActive ? 'color-blue' : ''">我的</div>
            </div>
        </div>
    </div>
</template>
<script>
import menuContent from './menu.vue'
import userContent from './user.vue'
export default {
    components: {
        menuContent,
        userContent
    },
    data() {
        return {
            isMenuActive: true,
            isUserActive: false
        }
    },
    methods: {
        menuClickMethod() {
            this.isMenuActive = true;
            this.isUserActive = false;
            // 打开菜单页面
        },
        userClickMethod() {
            this.isMenuActive = false;
            this.isUserActive = true;
            // 打开用户页面
        }
    }
}
</script>
<style lang="scss" scoped>
.page-container {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;

    .page-content {
        flex: 1;
    }

    .footer-wrap {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 50px;
        background-color: #fff;
        border-top: 1px solid #eee;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .btn-class {
        width: 50%;
        height: 100%;
        text-align: center;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .color-blue {
        color: blue
    }
}
</style>