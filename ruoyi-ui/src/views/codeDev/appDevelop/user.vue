<template>
  <div class="user">
    <div class="bar-container">
      我的
    </div>
    <div style="padding: 10px">
      <el-row class="user-info-panel">
        <el-col :span="18">
          <el-row class="user-nick-name">{{ user.nickName }}</el-row>
          <el-row class="user-dept-name">{{
            user.dept ? user.dept.deptName : "未知部门"
          }}</el-row>
        </el-col>
        <el-col :span="6" class="avatar-col">
          <!-- <el-avatar size="large" :src="user.avatar"></el-avatar> -->
          <!-- <img class="avatar" :src="user.avatar" alt="用户头像"/> -->
          <img class="avatar" src="./img/avatar.png" />
        </el-col>
      </el-row>
      <div>
        <el-card class="box-card">
          <!-- <div class="text item content">
            <div><i class="el-icon-user-solid" style="color:#00678c;padding: 0px 5px"></i> 个人资料</div>
            <i class="el-icon-arrow-right"></i>
          </div>
          <div class="text item content">
            <div><i class="el-icon-lock" style="color:#00678c;padding: 0px 5px"></i> 修改密码</div>
            <i class="el-icon-arrow-right"></i>
          </div> -->
          <div class="text item content" @click="logout">
            <div><i class="el-icon-house" style="color:#00678c;padding: 0px 5px"></i> 退出登录</div>
            <i class="el-icon-arrow-right"></i>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>
<script>
import OAuth2Login from '@/utils/oauth2-login'
import { getUserProfile } from "@/api/system/user";
export default {
  name: "User",
  mixins: [OAuth2Login],
  data() {
    return {
      user: {
        
      },
    };
  },
  mounted(){
    this.getUser();
  },
  methods: {
    logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.handleLogout()
      }).catch(() => {
      });
    },
    getUser() {
      getUserProfile().then(response => {
        this.user = response.data;
        
        // this.roleGroup = response.roleGroup;
        // this.postGroup = response.postGroup;
      });
    }
  },
};
</script>
<style lang="scss" scoped>
.user-info-panel {
  padding: 30px 10px 0 10px;

  .user-nick-name {
    font-size: 28px;
    font-weight: bolder;
  }

  .user-dept-name {
    color: #868686;
    font-size: 14px;
  }

  .avatar-col {
    text-align: right;

    .avatar {
      height: 60px;
      width: 60px;
      border-radius: 30px;
    }
  }
}

.operation-panel {
  margin: 20px 10px;
  border: 1px solid white;
  padding: 5px;
  border-radius: 15px;
  background-color: white;
  box-shadow: 0 0 5px #808080;
}

.text {
  font-size: 14px;
}

.item {
  padding-top: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f5f5f5;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.box-card {
  margin-top: 20px;
}

.bar-container {
  background-color: cadetblue;
  height: 50px;
  line-height: 50px;
  text-align: center;
}
</style>
