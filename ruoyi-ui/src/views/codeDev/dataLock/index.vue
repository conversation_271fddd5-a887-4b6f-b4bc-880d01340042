<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryFormRef" size="mini" inline label-width="auto">
      <el-form-item label="表编码" prop="tableCode">
        <el-select v-model="queryParams.tableCode" placeholder="表编码" clearable filterable>
          <el-option v-for="tableOption in tableOptions" :label="tableOption.label" :key="tableOption.value"
            :value="tableOption.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="启用" prop="enable">
        <el-select v-model="queryParams.enable" placeholder="启用" clearable>
          <el-option label="启用" value="1" />
          <el-option label="关闭" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="范围" prop="scope">
        <el-input v-model="queryParams.scope" placeholder="范围" clearable />
      </el-form-item>
      <el-form-item label="时间" prop="time">
        <el-time-picker v-model="queryParams.time" format="HH:mm" value-format="HH:mm"
          placeholder="时间"></el-time-picker>
      </el-form-item>
      <el-form-item label="影响范围" prop="influenceScope">
        <el-input v-model="queryParams.influenceScope" placeholder="影响范围" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="button-container">
      <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAddDataLockFormData"
        v-hasPermi="['system:dataLock:add']">新增</el-button>
      <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="deleteButtonDisabled"
        @click="handleDelete" v-hasPermi="['system:dataLock:remove']">
        删除
      </el-button>
    </div>
    <el-table ref="fullHeightTableRef" :height="tableHeight" v-loading="tableLoading" :data="dataLockTableData" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="表编码" align="center" prop="tableCode" />
      <el-table-column label="启用" align="center" prop="enable">
        <template slot-scope="scope">
          <el-tag :type="scope.row.enable == 1 ? 'success' : 'info'">{{ scope.row.enable == 1 ? '启用' : "关闭" }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="范围" align="center" prop="scope" />
      <el-table-column label="时间" align="center" prop="time" />
      <el-table-column label="影响范围" align="center" prop="influenceScope" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdateDataLockFormData(scope.row)"
            v-hasPermi="['system:dataLock:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:dataLock:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="text-align: right;">
      <el-pagination v-show="total > 0" @size-change="handlePageSizeChange" @current-change="handlePageNumChange"
        :current-page="queryParams.pageNum" :page-sizes="[10, 20, 30, 50, 100]" :page-size="queryParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>
    </div>

    <!-- 添加或修改dataLock对话框 -->
    <el-dialog :title="title" :visible.sync="dataLockDialogVisible" width="500px" append-to-body>
      <el-form ref="dataLockFormRef" :model="dataLockFormData" size="mini" :rules="dataLockFormRules"
        label-width="80px">
        <el-form-item label="表编码" prop="tableCode">
          <el-select v-model="dataLockFormData.tableCode" style="width: 100%;" placeholder="请选择表编码" filterable
            clearable>
            <el-option v-for="tableOption in tableOptions" :label="tableOption.label" :key="tableOption.value"
              :value="tableOption.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="启用" prop="enable">
          <el-select v-model="dataLockFormData.enable" style="width: 100%;" placeholder="启用" clearable>
            <el-option label="启用" value="1" />
            <el-option label="关闭" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="范围" prop="scope">
          <el-input v-model="dataLockFormData.scope" placeholder="请输入范围" />
        </el-form-item>
        <el-form-item label="时间" prop="time">
          <el-time-picker v-model="dataLockFormData.time" style="width: 100%;" format="HH:mm" value-format="HH:mm"
            placeholder="请选择时间"></el-time-picker>
        </el-form-item>
        <el-form-item label="影响范围" prop="influenceScope">
          <el-input v-model="dataLockFormData.influenceScope" placeholder="请输入影响范围" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="dataLockDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import { listDataLock, getDataLock, delDataLock, addDataLock, updateDataLock } from "@/api/codeDev/dataLock/dataLock";
import { getMongoTables } from '@/api/dts/metadata-query'
export default {
  mixins: [tableFullHeight],
  data() {
    return {
      // 遮罩层
      tableLoading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      deleteButtonDisabled: true,
      // 总条数
      total: 0,
      // dataLock表格数据
      dataLockTableData: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      dataLockDialogVisible: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tableCode: null,
        enable: null,
        scope: null,
        time: null,
        influenceScope: null
      },
      // 表单参数
      dataLockFormData: {
        id: null,
        tableCode: "",
        enable: "0",
        scope: "",
        time: "",
        influenceScope: ""
      },
      // 表单校验
      dataLockFormRules: {
        tableCode: [
          { required: true, message: '请选择表编码', trigger: 'change' },
        ],
        scope: [
          { required: true, message: '请选择范围', trigger: 'blur' },
        ],
        time: [
          { required: true, message: '请选择时间', trigger: 'change' },
        ],
        influenceScope: [
          { required: true, message: '请输入影响范围', trigger: 'blur' }
        ]
      },
      tableOptions: []
    };
  },
  mounted() {
    this.getTableData();
    this.handleGetTableOptions()
  },
  methods: {
    /**
     * 获取tablecode下拉列表
     */
    async handleGetTableOptions() {
      const result = await getMongoTables()
      if (result.code == 0) {
        this.tableOptions = result.data.map(item => {
          return {
            label: item,
            value: item
          }
        })
      }
    },
    handlePageSizeChange(val) {
      this.queryParams.pageSize = val
      this.getTableData()
    },
    handlePageNumChange(val) {
      this.queryParams.pageNum = val
      this.getTableData()
    },
    /** 
     * 查询dataLock列表
     */
    async getTableData() {
      this.tableLoading = true;
      const response = await listDataLock(this.queryParams).finally(() => {
        this.tableLoading = false;
      })
      if (response.code == 200) {
        this.dataLockTableData = response.rows;
        this.total = response.total;
      }
    },
    /** 
     * 搜索按钮操作
     */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getTableData();
    },
    /** 
     * 重置按钮操作
     */
    resetQuery() {
      this.resetForm("queryFormRef");
      this.handleQuery();
    },
    /**
     * 多选框选中数据
     * @param selection 
     */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.deleteButtonDisabled = !selection.length
    },
    /** 
     * 新增按钮操作
     */
    handleAddDataLockFormData() {
      this.dataLockDialogVisible = true;
      this.title = "添加dataLock";
      this.dataLockFormData.id = null
      this.$nextTick(() => {
        this.resetForm("dataLockFormRef");
      })
    },
    /** 
     * 修改按钮操作
     */
    async handleUpdateDataLockFormData(row) {
      const id = row.id || this.ids
      const response = await getDataLock(id)
      if (response.code == 200) {
        this.title = "修改dataLock";
        this.dataLockDialogVisible = true;
        this.$nextTick(() => {
          this.resetForm("dataLockFormRef");
          // this.dataLockFormData = response.data;
          Object.keys(this.dataLockFormData).forEach(key => {
            this.dataLockFormData[key] = response.data[key];
          })
          console.log("this.dataLockFormData", this.dataLockFormData);

        })
      };
    },
    /** 
     * 提交按钮
     */
    async submitForm() {
      const valid = await this.$refs["dataLockFormRef"].validate().catch(() => false);
      if (!valid) return
      if (!!this.dataLockFormData.id) {
        updateDataLock(this.dataLockFormData).then(response => {
          this.$message.success("修改成功");
          this.dataLockDialogVisible = false;
          this.getTableData();
        });
      } else {
        addDataLock(this.dataLockFormData).then(response => {
          this.$message.success("新增成功");
          this.dataLockDialogVisible = false;
          this.getTableData();
        });
      }
    },
    /** 
     * 删除按钮操作
     */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除dataLock编号为"' + ids + '"的数据项？').then(() => {
        return delDataLock(ids);
      }).then(() => {
        this.getTableData();
        this.$message.success("删除成功");
      }).catch(() => {
        this.$message.info("已取消操作");
      });
    },
  }
};
</script>

<style scoped lang="scss">
.app-container {
  position: relative;

  .button-container {
    margin-bottom: 10px;
  }
}
</style>
