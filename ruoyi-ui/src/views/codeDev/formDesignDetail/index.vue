<template>
    <div class="formDesign-detail">
        <el-tabs v-model="formDesignActive">
            <el-tab-pane label="表单设计" name="formDesign">
                <v-form-designer ref="vfDesigner" v-if="formDesignActive === 'formDesign' && hasFormDesigner">
                    <template #customToolButtons>
                        <el-button type="text" @click="downloadTemplate">下载模板</el-button>
                        <el-button type="text" @click="exportExcel">导出Excel</el-button>
                        <el-upload style="padding: 0 10px;" action="#" :http-request="importTemplate"
                            :show-file-list="false">
                            <el-button slot="trigger" type="text">导入Excel</el-button>
                        </el-upload>
                        <el-button type="text" @click="confirSaveFormJson">保存</el-button>
                        <el-button type="text" @click="$router.go(-1)">返回</el-button>
                    </template>
                </v-form-designer>
            </el-tab-pane>
            <el-tab-pane label="列表配置" name="listConfig">
                <form-design-field-config v-if="formDesignActive === 'listConfig'"></form-design-field-config>
            </el-tab-pane>
            <el-tab-pane label="排序" name="tableSort">
                <table-sort v-if="formDesignActive === 'tableSort'"></table-sort>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
import request from '@/utils/request'
import TableSort from './sort-table.vue'
import FormDesignFieldConfig from './formDesignFieldConfig.vue'
import { Loading } from 'element-ui';
import {
    listFormDesign,
    updateFormDesign,
} from "@/api/codeDev/formDesign/formDesign";
import { encodeSqlToEncryptedBase64 } from "@/utils/sqlHandle"
export default {
    name: "FormDesignDetail", // 勿动 缓存组件用
    data() {
        return {
            hasFormDesigner: false,
            selectedRow: null,
            formDesignActive: 'formDesign',
        }
    },
    components: {
        TableSort,
        FormDesignFieldConfig
    },
    methods: {
        // 下载模版
        async downloadTemplate() {
            const fileName = "表单设计导入模板.xlsx"
            const downloadUrl = window.location.protocol + "//" + window.location.host + '/mn_file/' + fileName
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error('网络响应异常');
            }
            const blob = await response.blob();
            // 创建一个<a>标签并设置下载链接
            const link = document.createElement('a');
            link.href = window.URL.createObjectURL(blob);
            // link.href = downloadUrl;
            link.download = fileName
            // 触发下载
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },
        // 导出Excel
        async exportExcel() {
            const formJson = this.$refs.vfDesigner.getFormJson()
            const result = await request.post("/codeDev/toExcel", JSON.stringify(formJson), {
                headers: {
                    'Content-Type': 'application/json',
                },
                responseType: 'blob',
            })
            const blob = new Blob([result], { type: 'text/plain;charset=utf-8' });
            this.downloadBlob(blob, '表单设计.xls');
        },
        downloadBlob(blob, filename) {
            // 创建一个临时的 URL
            const url = window.URL.createObjectURL(blob);
            // 创建一个临时的 <a> 元素
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = filename; // 设置下载文件的文件名

            // 将 <a> 元素添加到 DOM 中并触发点击事件
            document.body.appendChild(a);
            a.click();

            // 移除 <a> 元素并释放 URL
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        },

        // 导入模版
        async importTemplate(params) {
            const formData = new FormData()
            formData.append("file", params.file)
            const result = await request.post("/codeDev/toJSON", formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                }
            })
            if (result.code === 200) {
                this.$refs.vfDesigner.setFormJson(result.data);
            }
        },
        async getFormDesignerDetail() {
            const id = this.$route.query.id
            if (!id) return
            const result = await listFormDesign({ id })
            if (result.code === 200) {
                this.selectedRow = result.rows[0]
                if (!this.selectedRow) return
                const formJsonInfo = JSON.parse(this.selectedRow.formJsonInfo)
                if (formJsonInfo && formJsonInfo.widgetList) {
                    // 清空之前的本地存储
                    localStorage.setItem("widget__list__backup", JSON.stringify(formJsonInfo.widgetList))
                } else {
                    localStorage.setItem("widget__list__backup", JSON.stringify([]))
                }

                if (formJsonInfo && formJsonInfo.formConfig) {
                    localStorage.setItem('form__config__backup', JSON.stringify(formJsonInfo.formConfig))
                } else {
                    localStorage.setItem('form__config__backup', JSON.stringify({}))
                }

                if (formJsonInfo && formJsonInfo.searchConfigList) {
                    localStorage.setItem('search__config__list__backup', JSON.stringify(formJsonInfo.searchConfigList))
                } else {
                    localStorage.setItem('search__config__list__backup', JSON.stringify([]))
                }
                this.hasFormDesigner = true
                this.$nextTick(() => {
                    this.$refs.vfDesigner.setFormJson(formJsonInfo);
                })
            }
        },
        // 保存前 提示
        confirSaveFormJson() {
            this.$confirm("确定要保存此表单信息？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    this.saveFormJson();
                })
                .catch(() => { });
        },
        // 保存表单json
        async saveFormJson() {
            const loading = Loading.service({
                lock: true,
                text: '加载中……',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            const formJson = this.$refs.vfDesigner.getFormJson();
            const formJsonStr = JSON.stringify(formJson);
            this.selectedRow.formJsonInfo = encodeSqlToEncryptedBase64(formJsonStr);
            const res = await updateFormDesign(this.selectedRow).catch(() => {
                loading.close()
            })
            if (res.code === 200) {
                this.$modal.msgSuccess("操作成功");
            } else {
                this.$message.error(res.msg);
            }
            loading.close()
        },

    },
    mounted() {
        this.getFormDesignerDetail();
    },
    watch: {
        // 监听路由的变化
        $route() {
            this.getFormDesignerDetail()
        },
        formDesignActive(){
            if(this.formDesignActive === 'formDesign'){
                this.getFormDesignerDetail()
            }
        }
    },
    // 兼容被keep-alive缓存的组件
    activated() {
        this.getFormDesignerDetail()
    },

}
</script>
<style lang="scss">
.formDesign-detail {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;

    .el-tabs {
        height: 100%;
        display: flex;
        flex-direction: column;

        .el-tabs__content {
            flex: 1;
            overflow: hidden;
            position: relative;
        }

        .el-tab-pane {
            height: 100%;
        }
    }

    .el-tabs__header {
        padding: 0 10px;
    }
}
</style>
