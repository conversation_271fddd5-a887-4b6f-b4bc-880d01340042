<template>
  <el-form :model="tableSortFormData" :rules="tableSortRules" ref="tableSortFormRef" label-width="100px">
    <el-form-item prop="tableData" label-width="0px">
      <el-table :data="tableSortFormData.tableData" style="width: 100%">
        <el-table-column prop="fieldName" label="字段">
          <template slot-scope="scope">
            <el-form-item :prop="'tableData.' + scope.$index + '.fieldName'" :rules="tableSortRules.fieldName">
              <el-select v-model="scope.row.fieldName" clearable placeholder="请选择字段">
                <el-option v-for="item in fieldNameList" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="sortOrder" label="排序方式">
          <template slot-scope="scope">
            <el-form-item :prop="'tableData.' + scope.$index + '.sortOrder'" :rules="tableSortRules.sortOrder">
              <el-select v-model="scope.row.sortOrder" clearable placeholder="请选择排序方式">
                <el-option label="升序" value="ascending"></el-option>
                <el-option label="降序" value="descending"></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <el-button type="text" @click="removeRow(scope.row, scope.$index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="addRow">添加行</el-button>
      <el-button type="primary" @click="submitForm">提交</el-button>
      <el-button @click="resetForm">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { getTableSort, listFormDesign, updateTableSort, deleteTableSort } from "@/api/codeDev/formDesign/formDesign";

export default {
  name: "TableSortForm",
  data() {
    return {
      // 表单数据
      tableSortFormData: {
        tableData: []
      },
      // 表单验证规则
      tableSortRules: {
        fieldName: [
          { required: true, message: '请选择字段', trigger: 'change' }
        ],
        sortOrder: [
          { required: true, message: '请选择排序方式', trigger: 'change' }
        ]
      },
      // 业务字段列表
      businessFields: [],
      // 公共字段列表
      publicFields: []
    }
  },
  computed: {
    // 合并业务字段和公共字段
    fieldNameList() {
      return [...this.businessFields, ...this.publicFields].filter(Boolean);
    }
  },
  mounted() {
    this.initData();
  },
  methods: {
    // 初始化数据
    async initData() {
      await Promise.all([
        this.getFormDesignConfig(),
        this.getPublicFields(),
        this.handleGetTableSort()
      ]);
    },
    // 获取表单设计配置
    async getFormDesignConfig() {
      try {
        const id = this.$route.query.id;
        const result = await listFormDesign({ id });
        if (result.code === 200 && result.rows.length > 0) {
          const formJsonInfo = JSON.parse(result.rows[0].formJsonInfo);
          const newWidgetList = [];
          this.processWidgets(formJsonInfo.widgetList, newWidgetList);
          this.businessFields = newWidgetList.map(item => ({
            label: item.options.label,
            value: item.options.name
          }));
        }
      } catch (error) {
        console.error('获取表单设计配置失败:', error);
      }
    },
    // 处理表单组件
    processWidgets(widgetList, newWidgetList) {
      if (!widgetList) return;
      widgetList.forEach(item => {
        if (item.cols) {
          this.processWidgets(item.cols, newWidgetList);
        } else if (item.widgetList) {
          this.processWidgets(item.widgetList, newWidgetList);
        } else {
          newWidgetList.push(item);
        }
      });
    },
    // 获取公共字段
    async getPublicFields() {
      try {
        const response = await this.getDicts("public_column_list");
        if (response.code === 200) {
          this.publicFields = response.data.map(item => ({
            label: item.dictLabel,
            value: item.dictValue
          }));
        }
      } catch (error) {
        console.error('获取公共字段失败:', error);
      }
    },
    // 获取表格排序数据
    async handleGetTableSort() {
      try {
        const id = this.$route.query.id;
        const result = await getTableSort(id);
        if (result.code === 200) {
          this.tableSortFormData.tableData = result.data;
        }
      } catch (error) {
        console.error('获取表格排序数据失败:', error);
      }
    },
    // 添加行
    addRow() {
      this.tableSortFormData.tableData.push({
        fieldName: '',
        sortOrder: ''
      });
    },
    // 删除行
    async removeRow(row, index) {
      if (row.id) {
        const result = await deleteTableSort(row.id);
        if (result.code === 200) {
          this.$message.success('删除成功');
        } else {
          this.$message.error('删除失败');
        }
      } this.tableSortFormData.tableData.splice(index, 1);
    },
    // 提交表单
    async submitForm() {
      const valid = await this.$refs.tableSortFormRef.validate().catch(() => false);
      if (!valid) {
        return;
      }
      const id = this.$route.query.id;
      const tableData = this.tableSortFormData.tableData.map(item => ({
        id: item.id,
        fieldName: item.fieldName,
        sortOrder: item.sortOrder,
        formId: id
      })
      )
      const result = await updateTableSort(tableData);
      if (result.code === 200) {
        this.$message.success('提交成功');
      } else {
        this.$message.error('提交失败');
      }

    },
    // 重置表单
    resetForm() {
      this.$refs.tableSortFormRef.resetFields();
    }
  }
}
</script>

<style scoped>
.el-table {
  margin-bottom: 20px;
}
</style>