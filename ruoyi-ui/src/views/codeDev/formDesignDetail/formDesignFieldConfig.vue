<template>
    <div class="form-design-field-config">
        <el-form :model="queryParams" ref="queryFormRef" size="small" inline label-width="68px">
            <el-form-item label="字段名称" prop="fieldName">
                <el-input v-model="queryParams.fieldName" placeholder="请输入字段名称" clearable
                    @keyup.enter.native="handleQueryTableData" />
            </el-form-item>
            <el-form-item label="字段编码" prop="fieldCode">
                <el-input v-model="queryParams.fieldCode" placeholder="请输入字段编码" clearable
                    @keyup.enter.native="handleQueryTableData" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQueryTableData">搜索</el-button>
                <el-button size="mini" icon="el-icon-refresh" @click="handleResetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-table border class="draggable-table" row-key="id" v-loading="tableLoading" :data="formDesignFieldConfigList"
            width="100%">
            <el-table-column label="顺序" align="center" type="index" width="50"> </el-table-column>
            <el-table-column label="字段名称" align="center" prop="fieldName" min-width="150" />
            <el-table-column label="字段编码" align="center" prop="fieldCode" min-width="150" />
            <el-table-column label="字段类型" align="center" prop="fieldType" min-width="60" />
            <el-table-column label="自定义宽度" align="center" prop="width" min-width="60">
                <template slot-scope="scope">
                    <el-input v-model.number="scope.row.width" type="number" size="mini" />
                </template>
            </el-table-column>
            <el-table-column label="是否显示" align="center" prop="displayStatus" width="100">
                <template slot-scope="scope">
                    <el-select v-model="scope.row.displayStatus" size="mini">
                        <el-option label="显示" value="1"></el-option>
                        <el-option label="隐藏" value="2"></el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column label="查询条件配置" align="center" prop="allowSearch">
                <template slot-scope="scope" v-if="commonFields.includes(scope.row.fieldCode)">
                    <el-select v-model="scope.row.allowSearch" size="mini">
                        <el-option label="无需查询" value="1"></el-option>
                        <el-option label="精准查询" value="2"></el-option>
                        <el-option :disabled="scope.row.fieldCode === '_id'" label="模糊查询" value="3"></el-option>
                        <el-option label="时间范围" value="4"></el-option>
                    </el-select>
                </template>
            </el-table-column>
            <!-- <el-table-column label="查询默认值" align="center" prop="width" min-width="60">
          <template slot-scope="scope">
            <el-input v-model="scope.row.defaultValue" size="mini" />
          </template>
        </el-table-column>
        <el-table-column label="日期默认值" align="center" prop="defultConfig">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.defaultConfig" active-color="#00A854" active-text="启用" :active-value="0"
              inactive-color="#F04134" inactive-text="停用" :inactive-value="1" size="mini"
              :disabled="!(scope.row.fieldType == 'date' && (scope.row.allowSearch == '4'))" />
          </template>
        </el-table-column>
        <el-table-column label="日期偏移量" align="center" prop="width" min-width="60">
          <template slot-scope="scope">
            <el-input v-model="scope.row.offsetValue" type="number" size="mini" />
          </template>
        </el-table-column> -->
            <el-table-column label="自定义格式化" align="center" prop="width" min-width="60">
                <template slot-scope="scope">
                    <el-button type="text" size="mini" @click="handleOpenFormatDialog(scope.row)">格式化</el-button>
                </template>
            </el-table-column>
            <el-table-column label="冻结列" align="center" prop="freezeColumns">
                <template slot-scope="scope">
                    <el-switch v-model="scope.row.freezeColumns"
                        @change="handleFreezeColumnsChange(scope.row, scope.$index)" active-color="#00A854"
                        active-text="启用" :active-value="1" inactive-color="#F04134" inactive-text="停用"
                        :inactive-value="0" size="mini" />
                </template>
            </el-table-column>
            <el-table-column label="换行" align="center" prop="overflow">
                <template slot-scope="scope">
                    <el-switch v-model="scope.row.overflow" active-color="#00A854" active-text="启用" :active-value="true"
                        inactive-color="#F04134" inactive-text="停用" :inactive-value="false" size="mini" />
                </template>
            </el-table-column>
        </el-table>
        <div style="text-align: right; margin: 10px 0;">
            <el-button size="small" type="primary" @click="submitAndApplyFieldConfig">保存并应用</el-button>
            <el-button size="small" type="primary" @click="submitFieldConfig">保存</el-button>
            <!-- <el-button size="small" @click="handleCancel">取消</el-button> -->
        </div>
        <!-- 自定义格式化 dialog -->
        <el-dialog width="30%" title="自定义格式化" :visible.sync="formatDialogVisible" append-to-body>
            <code-editor ref="formatEditor" v-if="formatDialogVisible" v-model="formatScript">
            </code-editor>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" type="primary" @click="handleSaveFormatConfig">保存</el-button>
                <el-button size="small" @click="formatDialogVisible = false">取消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import Sortable from 'sortablejs'
import { listFormExportConfig, updateFormExportConfigBatch, getFieldSortList } from "@/api/codeDev/formExportConfig/formExportConfig"
import CodeEditor from '@/components/CodeEditor/index.vue'
import { listFormDesignFieldConfig, updateFormDesignFieldConfig } from '@/api/codeDev/formDesignFieldConfig/formDesignFieldConfig'
const COMMONFIELDS = ["_id", "create_time", "update_time", "create_by", "update_by", "is_valid"] // "del_flag" 字段不参与搜索
export default {
    name: "FormDesignFieldConfig",
    components: {
        CodeEditor
    },
    data() {
        return {
            tableLoading: false,
            total: 0,
            formDesignFieldConfigList: [],
            queryParams: {
                pageNum: 1,
                pageSize: 1000,
                formDesignId: this.$route.query.id,
            },
            formatDialogVisible: false,
            formatConfigId: null,
            formatScript: "",
            commonFields: COMMONFIELDS
        };
    },
    mounted() {
        this.getFieldConfigTableData();
        this.$nextTick(() => {
            this.rowDrag();
        });
    },
    methods: {
        rowDrag() {
            const el = document.querySelectorAll(".draggable-table .el-table__body-wrapper > table > tbody")[0];
            this.sortableInstance = Sortable.create(el, {
                disabled: false,
                ghostClass: "sortable-ghost",
                animation: 150,
                onEnd: (e) => {
                    const sourceIndex = e.oldIndex;
                    const targetIndex = e.newIndex;
                    if (this.isValidMove(sourceIndex, targetIndex)) {
                        const formDesignFieldConfigList = this.formDesignFieldConfigList.slice();
                        const movedItem = formDesignFieldConfigList.splice(e.oldIndex, 1)[0];
                        formDesignFieldConfigList.splice(e.newIndex, 0, movedItem);
                        this.formDesignFieldConfigList = formDesignFieldConfigList.slice();
                    } else {
                        this.formDesignFieldConfigList = this.formDesignFieldConfigList.slice();
                        const el = document.querySelectorAll(".draggable-table .el-table__body-wrapper > table > tbody")[0];
                        if (targetIndex > sourceIndex) {
                            el.insertBefore(e.item, el.children[sourceIndex]);
                        } else {
                            el.insertBefore(e.item, el.children[sourceIndex + 1]);
                        }
                    }
                }
            });
        },
        isValidMove(sourceIndex, targetIndex) {
            const sourceRow = this.formDesignFieldConfigList[sourceIndex];
            if (targetIndex === 0) {
                const firstRow = this.formDesignFieldConfigList[0];
                return sourceRow.freezeColumns === firstRow.freezeColumns;
            } else if (targetIndex === this.formDesignFieldConfigList.length - 1) {
                const lastRow = this.formDesignFieldConfigList[this.formDesignFieldConfigList.length - 1];
                return sourceRow.freezeColumns === lastRow.freezeColumns;
            } else {
                const prevRow = this.formDesignFieldConfigList[targetIndex];
                const nextRow = this.formDesignFieldConfigList[targetIndex + 1];
                return sourceRow.freezeColumns === prevRow.freezeColumns || sourceRow.freezeColumns === nextRow.freezeColumns;
            }
        },
        handleFreezeColumnsChange({ freezeColumns }, index) {
            if (freezeColumns) {
                const [currentRow] = this.formDesignFieldConfigList.splice(index, 1)
                this.formDesignFieldConfigList.unshift(currentRow)
            } else {
                const [currentRow] = this.formDesignFieldConfigList.splice(index, 1)
                this.formDesignFieldConfigList.push(currentRow)
            }
        },
        handleOpenFormatDialog(row) {
            this.formatConfigId = row.id
            this.formatScript = row.formatScript || ""
            this.formatDialogVisible = true
        },
        handleSaveFormatConfig() {
            const index = this.formDesignFieldConfigList.findIndex(item => item.id == this.formatConfigId)
            const currentConfig = this.formDesignFieldConfigList[index]
            this.$set(currentConfig, 'formatScript', this.formatScript)
            this.$set(this.formDesignFieldConfigList, index, currentConfig)
            this.formatDialogVisible = false
        },
        submitFieldConfig() {
            const formDesignFieldConfigList = this.formDesignFieldConfigList.map((item, index) => {
                return {
                    ...item,
                    sort: index + 1,
                    offsetValue: item.offsetValue === "" ? null : item.offsetValue,
                }
            })
            this.tableLoading = true
            updateFormDesignFieldConfig(formDesignFieldConfigList).then((res) => {
                if (res.code === 200) {
                    this.$message.success("保存成功");
                }
            }).finally(() => {
                this.tableLoading = false
            })
        },
        handleCancel() {
            this.$router.go(-1)
        },
        async submitAndApplyFieldConfig() {
            this.submitFieldConfig()
            const queryParam = {
                formDesignId: this.$route.query.id,
                pageSize: 1000,
                pageNum: 1,
            };
            let formExportConfigList = []
            await listFormExportConfig(queryParam).then((response) => {
                if (response.code === 200) {
                    formExportConfigList = response.rows;
                }
            });
            await getFieldSortList(this.$route.query.id).then((res) => {
                const fieldSortMap = res.data;
                formExportConfigList.forEach((e) => {
                    if (fieldSortMap[e.onlyName] != undefined) {
                        e.sort = fieldSortMap[e.onlyName];
                    }
                });
                formExportConfigList.sort((a, b) => {
                    return a.sort - b.sort;
                });
            });
            await updateFormExportConfigBatch(formExportConfigList)
            this.$message.success("保存成功");
        },
        /**
         * 获取列表配置数据
         */
        getFieldConfigTableData() {
            this.tableLoading = true;
            listFormDesignFieldConfig(this.queryParams).then(response => {
                if (response.code === 200) {
                    this.formDesignFieldConfigList = response.rows.map(item => {
                        return {
                            ...item,
                            freezeColumns: item.freezeColumns ? 1 : 0,
                            defaultValue: item.defaultValue !== undefined ? item.defaultValue : "",
                            offsetValue: item.offsetValue !== undefined ? item.offsetValue : null,
                        }
                    });
                    this.total = response.total;
                    this.tableLoading = false;
                }
            });
        },
        handleQueryTableData() {
            this.queryParams.pageNum = 1;
            this.getFieldConfigTableData();
        },
        handleResetQuery() {
            this.resetForm("queryFormRef");
            this.handleQueryTableData();
        },
    }
}
</script>
<style lang="scss" scoped>
.form-design-field-config {
    height: 100%;
    overflow-y: auto;
    padding: 10px;
    background-color: #fff;
}
</style>
