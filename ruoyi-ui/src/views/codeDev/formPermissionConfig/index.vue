<template>
  <div class="permission-container">
    <el-dialog title="数据权限配置" :append-to-body="true" :visible.sync="permissionConfigDialogShow" width="95%"
      class="fullscreen-dialog" @close="cancelConfig()">
      <div style="margin-bottom: 10px">
        <el-button type="primary" @click="openModal('add')">新增</el-button>
        <el-button @click="openModal('edit')" :disabled="!permissionRecordSelectedData">修改</el-button>
        <el-button @click="openModal('delete')" :disabled="!permissionRecordSelectedData">删除</el-button>
        <el-button @click="handleExport">导出</el-button>
        <el-button @click="handleImport">导入</el-button>
      </div>
      <el-form :model="queryFormData" inline label-width="80px">
        <el-form-item label="角色名称">
          <el-select v-model="queryFormData.roleCode" remote reserve-keyword placeholder="请输入角色名称"
            :remote-method="getRoleList" filterable>
            <el-option v-for="option in roleRecord" :key="option.roleKey" :label="option.roleName"
              :value="option.roleKey">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="queryPermissionList">查询</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="permissionRecordLoading" :data="permissionRecord" highlight-current-row
        @current-change="handleCurrentChange">
        <el-table-column label="角色" align="center" prop="roleCode"></el-table-column>
        <el-table-column label="角色名称" align="center" prop="roleCodeName">
          <template slot-scope="scope">
            {{ setRoleName(scope.row.roleCode) }}
          </template>
        </el-table-column>
        <el-table-column label="权限" align="center" prop="permissionTypeName"></el-table-column>
        <el-table-column label="按钮权限" align="center" prop="buttonPermissions">
          <template slot-scope="scope">
            {{ getButtonLabel(scope.row.buttonPermissions) }}
          </template>
        </el-table-column>
      </el-table>
      <el-pagination @size-change="handlePageSizeChange" @current-change="handlePageNumChange" :current-page="pageNum"
        :page-sizes="[10, 20, 30, 40]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
      <!-- 新增/修改 弹窗 -->
      <el-dialog :title="modalType == 'add' ? '新增权限' : '修改权限'" :append-to-body="true" :visible.sync="modalShow"
        width="500px" class="fullscreen-dialog" @close="cancelModal()">
        <el-form :model="modalForm" ref="modalForm" size="small" :inline="true" :rules="rules" label-width="85px">
          <el-form-item label="角色" prop="roleCode">
            <el-select v-model="modalForm.roleCode" filterable>
              <el-option v-for="option in roleRecord" :key="option.roleKey" :label="option.roleName"
                :value="option.roleKey">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="权限范围" prop="permissionType">
            <el-select v-model="modalForm.permissionType" style="width: 100%">
              <el-option v-for="option in dataScopeOptions" :key="option.value" :label="option.label"
                :value="option.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="数据权限" v-show="modalForm.permissionType == 2">
            <el-checkbox v-model="deptExpand" @change="handleCheckedTreeExpand($event, 'dept')">展开/折叠</el-checkbox>
            <el-checkbox v-model="deptNodeAll" @change="handleCheckedTreeNodeAll($event, 'dept')">全选/全不选</el-checkbox>
            <el-checkbox v-model="dataScopeForm.deptCheckStrictly"
              @change="handleCheckedTreeConnect($event, 'dept')">父子联动</el-checkbox>
            <el-tree :key="modalShow" class="tree-border" :data="deptOptions" show-checkbox default-expand-all
              ref="dept" node-key="id" :check-strictly="!dataScopeForm.deptCheckStrictly" empty-text="加载中，请稍后"
              :props="defaultProps" :default-checked-keys="modalForm.dept != '' ? modalForm.dept.split(',') : []
                "></el-tree>
          </el-form-item>
          <el-form-item label="按钮权限" prop="permissionButtons" style="display: flex;flex-wrap: nowrap;">
            <el-checkbox-group v-model="permissionButtons" style="width:350px">
              <el-checkbox v-for="(item, index) in buttonArray" :key="index" :label="item.label">
                {{ item.label }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitModal">确 定</el-button>
          <el-button @click="cancelModal">取 消</el-button>
        </div>
      </el-dialog>
    </el-dialog>
    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px">
      <el-upload v-loading="upload.isUploading" ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          <!--          <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据-->
          <!--        <el-link type="info" style="font-size:12px" @click="importTemplate">下载模板</el-link>-->
        </div>
        <div class="el-upload__tip" style="color:red" slot="tip">提示：仅允许导入“xls”或“xlsx”格式文件！</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as Api from "@/api/codeDev/formPermissionConfig/formPermissionConfig.js";
import { listRole } from "@/api/system/role.js";
import * as userApi from "@/api/system/user";
import { getToken } from "@/utils/auth";
export default {
  name: "FormDesignFieldConfig",
  dicts: ["field_display_or_hide"],
  props: {
    formDesignId: {
      type: [Number, String],
      required: true,
      default: "",
    },
    formDesignData: {
      type: Object,
      required: true,
      default: "",
    },
    permissionConfigDialogShow: {
      type: Boolean,
      required: false,
      default: "",
    },
  },
  data() {

    return {
      pageSize: 10,
      pageNum: 1,
      total: 0,
      permissionRecord: [],
      permissionRecordLoading: false,
      permissionRecordSelectedData: null,
      // 弹窗
      modalShow: false,
      modalType: "add",
      modalForm: {
        roleCode: "",
        permissionType: "",
        dept: "",
      },
      // 角色列表
      roleRecord: [],
      permissionButtons: [],
      buttonArray: [
        { label: '新增', value: 'added' },
        { label: '批量删除', value: 'batchDelete' },
        { label: '导出', value: 'export' },
        { label: '导入', value: 'import' },
        { label: '复制', value: 'copy' },
        { label: '编辑', value: 'edit' },
        { label: '删除', value: 'delete' }
      ],
      // 数据范围选项
      dataScopeOptions: [
        {
          value: "1",
          label: "全部数据权限",
        },
        {
          value: "2",
          label: "自定数据权限",
        },
        {
          value: "3",
          label: "本部门数据权限",
        },
        {
          value: "4",
          label: "本部门及以下数据权限",
        },
        {
          value: "5",
          label: "仅本人数据权限",
        },
      ],
      // 部门下拉树
      deptExpand: true,
      deptNodeAll: false,
      deptOptions: [],
      dataScopeForm: {
        tableName: "",
        tableCode: "",
        dataScope: undefined,
      },
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 其他数据属性
      rules: {
        roleCode: [
          { required: true, message: '请选择角色', trigger: 'change' }
        ],
        permissionType: [
          { required: true, message: '请选择权限范围', trigger: 'change' }
        ],
        // 其他字段的验证规则
      },
      // 导入参数
      upload: {
        // 是否显示弹出层（导入）
        open: false,
        // 弹出层标题（导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/codeDev/permissionConfig/importData"
      },
      queryFormData: {
        roleCode: null
      }
    };
  },
  mounted() {
    this.getPermissionList();
    this.getRoleList();
    this.getDeptTree();
  },
  methods: {

    getButtonLabel(permissions) {
      if (!permissions) return '';
      const permissionList = permissions.split(',');
      const labels = permissionList.map(permission => {
        const button = this.buttonArray.find(item => item.value === permission);
        return button ? button.label : permission;
      });
      return labels.join(', ');
    },
    handlePageNumChange(val) {
      this.pageNum = val
      this.getPermissionList()
    },
    handlePageSizeChange(val) {
      this.pageNum = 1
      this.pageSize = val
      this.getPermissionList()
    },
    queryPermissionList() {
      this.pageNum = 1
      this.getPermissionList()
    },
    /** 权限列表 ********************************************************************************* */
    getPermissionList() {
      const params = {
        roleCode: this.queryFormData.roleCode,
        tableCode: this.formDesignData.tableCode,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      };
      this.permissionRecordLoading = true;
      Api.listPermissionConfig(params)
        .then((res) => {
          this.permissionRecordLoading = false;
          if (res.code == 200) {
            this.permissionRecord = res.rows.map((cur) => {
              cur.permissionTypeName = this.dataScopeOptions.find((item) => {
                return item.value == cur.permissionType;
              }).label;
              return cur;
            });
            this.total = res.total;
          }
        })
        .catch((error) => {
          this.permissionRecordLoading = false;
        });
    },

    // 行选中
    handleCurrentChange(e) {
      console.log("行选中数据", e);
      this.permissionRecordSelectedData = e;
    },

    /** 弹窗 ********************************************************************************* */
    openModal(type) {
      this.modalType = type;
      switch (type) {
        case "add":
          this.modalShow = true;
          this.permissionButtons = []
          break;
        case "edit":
          this.modalShow = true;
          this.getPermissionDetail();
          break;
        case "delete":
          Api.delPermissionConfig(this.permissionRecordSelectedData.id).then(
            (res) => {
              if (res.code == 200) {
                this.getPermissionList();
              }
            }
          );
          break;
      }
    },
    cancelModal() {
      this.modalForm.dept = "";
      for (let key in this.modalForm) {
        this.modalForm[key] = "";
      }
      this.modalShow = false;
    },

    /** 部门下拉树 ********************************************************************************* */
    // 树权限（展开/折叠）
    handleCheckedTreeExpand(value, type) {
      if (type === "menu") {
        let treeList = this.menuOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.menu.store.nodesMap[treeList[i].id].expanded = value;
        }
      } else if (type === "dept") {
        let treeList = this.deptOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.dept.store.nodesMap[treeList[i].id].expanded = value;
        }
      }
    },
    // 树权限（全选/全不选）
    handleCheckedTreeNodeAll(value, type) {
      if (type === "menu") {
        this.$refs.menu.setCheckedNodes(value ? this.menuOptions : []);
      } else if (type === "dept") {
        this.$refs.dept.setCheckedNodes(value ? this.deptOptions : []);
      }
    },
    // 树权限（父子联动）
    handleCheckedTreeConnect(value, type) {
      if (type == "menu") {
        this.dataScopeForm.menuCheckStrictly = value ? true : false;
      } else if (type == "dept") {
        this.dataScopeForm.deptCheckStrictly = value ? true : false;
      }
    },
    // 所有部门节点数据
    getDeptAllCheckedKeys() {
      // 目前被选中的部门节点
      let checkedKeys = this.$refs.dept.getCheckedKeys();
      // 半选中的部门节点
      let halfCheckedKeys = this.$refs.dept.getHalfCheckedKeys();
      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
      return checkedKeys;
    },
    getPermissionDetail() {
      Api.getPermissionConfig(this.permissionRecordSelectedData.id).then(
        (res) => {
          if (res.code == 200) {
            this.permissionButtons = []
            let arr = res.data.buttonPermissions.split(',')
            if (arr.includes('all')) {
              this.permissionButtons = this.buttonArray.map(item => item.label)
            } else {
              this.buttonArray.forEach(item => {
                arr.forEach(ite => {
                  if (item.value === ite) {
                    this.permissionButtons.push(item.label)
                  }
                })
              })
            }

            for (let key in res.data) {
              if (key == "dept") {
                this.modalForm[key] = res.data[key];
              } else {
                this.modalForm[key] = res.data[key];
              }
            }
          }
        }
      );
    },

    submitModal() {
      this.$refs.modalForm.validate((valid) => {
        if (valid) {
          let params;
          let arr = [];
          this.permissionButtons.forEach(item => {
            const button = this.buttonArray.find(ite => ite.label === item);
            if (button) {
              arr.push(button.value);
            }
          });

          switch (this.modalType) {
            case "add":
              params = {
                tableCode: this.formDesignData.tableCode,
                permissionType: this.modalForm.permissionType,
                roleCode: this.modalForm.roleCode,
                dept: this.getDeptAllCheckedKeys().join(","),
                buttonPermissions: arr.join(',')
              };
              Api.addPermissionConfig(params).then((res) => {
                if (res.code == 200) {
                  this.cancelModal();
                  this.getPermissionList();
                }
              });
              break;
            case "edit":
              this.modalForm.dept = this.getDeptAllCheckedKeys().join(",");
              params = this.modalForm;
              params.buttonPermissions = arr.join(',');
              Api.updatePermissionConfig(params).then((res) => {
                if (res.code == 200) {
                  this.cancelModal();
                  this.getPermissionList();
                }
              });
              break;
          }
        } else {
          console.log('表单验证失败');
          return false;
        }
      });
    },

    /** 获取角色列表 */
    getRoleList(roleName) {
      const params = {
        roleName: roleName,
        pageNum: 1,
        pageSize: 2000
      }
      listRole(params).then((res) => {
        if (res.code == 200) {
          this.roleRecord = res.rows;
        }
      });
    },
    setRoleName(code) {
      let text = "";
      this.roleRecord.forEach((item) => {
        if (item.roleKey == code) {
          text = item.roleName;
        }
      });
      return text;
    },

    /** 查询部门下拉树结构 */
    getDeptTree() {
      userApi.deptTreeSelect().then((response) => {
        this.deptOptions = response.data;
      });
    },

    /** 关闭弹窗 */
    cancelConfig() {
      this.$emit("update:permissionConfigDialogShow", false);
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('/codeDev/permissionConfig/export/', {
        tableCode: this.formDesignData.tableCode
      }, `权限配置.xlsx`)
    },
    handleImport() {
      this.upload.title = "配置导入";
      this.upload.open = true;
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getPermissionList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
  },
};
</script>

<style scoped lang="scss">
.permission-container {
  position: relative;
}
</style>
