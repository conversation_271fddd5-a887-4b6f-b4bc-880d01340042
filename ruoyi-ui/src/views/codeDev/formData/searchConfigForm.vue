<template>
    <!-- formData 搜索头 -->
    <el-form :model="searchFormParams" ref="queryFormRef" @submit.native.prevent size="small" :inline="inline"
        label-width="auto">
        <el-form-item v-for="(item) in searchConfiguration" :key="item.name" :label="item.label" :prop="`${item.name}`">
            <!-- 输入框 -->
            <el-input v-if="hasInputWidget(item)" :style="widgetStyle(item)" v-model="searchFormParams[item.name]"
                :placeholder="widgetPlaceholder(item)" clearable />

            <!-- 下拉框 -->
            <el-select :ref="`select-filter-${item.name}`" v-if="hasSelectWidget(item)" :style="widgetStyle(item)"
                :filterable="item.filterable" :multiple="selectMultiple(item)" @change="handleSelectChange(item)"
                v-model="searchFormParams[item.name]" :placeholder="widgetPlaceholder(item)" clearable filterable>
                <el-option v-for="option in item.optionItems" :key="option.value" :label="option.label"
                    :value="option.value">
                </el-option>
            </el-select>
            <!-- 时间 -->
            <el-time-picker v-if="hasTimePickerWidget(item)" :popper-class="isMobile ? 'mobile-time-picker' : ''"
                :style="widgetStyle(item)" v-model="searchFormParams[item.name]" :placeholder="widgetPlaceholder(item)">
            </el-time-picker>
            <!-- pc端时间范围 -->
            <el-time-picker v-if="hasTimePickerRangeWidget(item)"
                :popper-class="isMobile ? 'mobile-time-range-picker' : ''" is-range :style="widgetStyle(item)"
                v-model="searchFormParams[item.name]" :placeholder="widgetPlaceholder(item)">
            </el-time-picker>
            <!-- 日期 月 范围 -->
            <el-date-picker v-if="hasDatePickerWidget(item)" :popper-class="isMobile ? 'mobile-date-picker' : ''
                " :picker-options="pickerOptions(item)" :style="widgetStyle(item)"
                v-model="searchFormParams[item.name]" :type="datePickerType(item)" value-format="timestamp"
                :placeholder="widgetPlaceholder(item)">
            </el-date-picker>
            <!-- 年范围 -->
            <el-date-picker1 v-if="hasDatePickerRangeWidget(item)"
                :popper-class="isMobile ? 'mobile-date-range-picker' : ''" :picker-options="pickerOptions(item)"
                :style="widgetStyle(item)" v-model="searchFormParams[item.name]" :type="dateRangePickerType(item)"
                value-format="timestamp" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker1>

        </el-form-item>

        <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleSubmitQuery">
                搜索
            </el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="handleResetQuery">
                重置
            </el-button>
        </el-form-item>

    </el-form>
</template>
<script>
import dayjs from "dayjs"
const TIMETYPES = ["time"]
const INPUTTYPES = ["input", "textarea", "number", "location"]
const SELECTTYPES = ["radio", "checkbox", "select"]
const DATETYPES = ["date"]
const DATERANGETYPES = ["date"]
import ElDatePicker1 from "element-ui/packages/date-picker"
import Vue from 'vue';
Vue.use(ElDatePicker1);
export default {
    data() {
        return {

            /* 时间组件 start */
            // mobileTimePickerVisible: false,
            // mobileTimePickerValue: null,
            // selectedMobileTimeOption: null,
            /* 时间组件 end */

            /* 日期组件 start */
            // mobileDatePickerVisible: false,
            // mobileDatePickerValue: null,
            // selectedMobileDateOption: null,
            /* 日期组件 end */

            // dateRangePickerVisible: false,
            // mobileDateRangePickerValue: null,
            // selectedDateRangeOption: null,

            searchConfiguration: [],
            searchFormParams: {}
        }
    },
    props: {
        inline: {
            type: Boolean,
            default: () => true
        },
        searchConfig: {
            type: Array,
            default: () => []
        },
        validOptions: {
            type: Array,
            default: () => []
        },
        approvalStatuOptions: {
            type: Array,
            default: () => []
        },
        delFlagOptions: {
            type: Array,
            default: () => []
        },
    },
    watch: {
        searchConfig: {
            immediate: true,
            deep: true,
            handler() {
                if (this.searchConfig.length === 0 || !this.isIos) return
                setTimeout(() => {
                    this.searchConfig.forEach(item => {
                        if (item.type === "select") {
                            const refName = `select-filter-${item.name}`
                            const components = this.$refs[refName]
                            components.forEach(component => {
                                if (component.$el) {
                                    component.$el.addEventListener(
                                        'pointerdown',
                                        (e) => {
                                            e.preventDefault()
                                            e.stopPropagation()
                                            setTimeout(() => {
                                                component.handleFocus(e)
                                            }, 100)
                                        }
                                    )
                                }
                            })
                        }
                    })
                }, 2000)
            },
        },
    },
    computed: {
        pickerOptions() {
            return function (option) {
                return {
                    disabledDate: (time) => {
                        const earliestAvailableTimeSwitch = option.earliestAvailableTimeSwitch
                        const earliestAvailableTimeType = option.earliestAvailableTimeType // 只有一种情况 就是动态值
                        const earliestAvailableTime = option.earliestAvailableTime
                        const minDayCount = earliestAvailableTimeSwitch === "true" ? earliestAvailableTime : -10000
                        // 获取当前日期范围
                        const minDay = dayjs().subtract(Math.abs(minDayCount), 'day').startOf('day') // 最小日期
                        const latestAvailableTimeSwitch = option.latestAvailableTimeSwitch
                        const latestAvailableTimeType = option.latestAvailableTimeType // 只有一种情况 就是动态值
                        const latestAvailableTime = option.latestAvailableTime
                        const maxDayCount = latestAvailableTimeSwitch === "true" ? latestAvailableTime : 10000
                        const maxDay = dayjs().add(maxDayCount, 'day').endOf('day') // 最大日期
                        const currentTime = dayjs(time) // 转换 time 为 dayjs 对象
                        // 禁用范围外的日期
                        return (currentTime.isBefore(minDay) || currentTime.isAfter(maxDay))
                    }
                }
            }
        },
        authorization() {
            return localStorage.getItem('BearToken')
        },
        isMobile() {
            // 获取当前用户是移动端还是pc端
            const ua = navigator.userAgent.toLowerCase();
            const isMobile = ua.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)
            return isMobile
        },
        isIos() {
            const isIos = /iPhone|iPad|iPod/i.test(
                navigator.userAgent
            )
            return isIos
        }
    },
    methods: {
        /**
         * 构建请求参数
         * @param requestParams 
         */
        buildRequestParams(requestParams = "") {
            const reg = /\$\{(\w+)\}/g;
            requestParams = requestParams.replace(reg, (_match, p1) => {
                const value = this.searchFormParams[p1]
                return value ? value : null
            });
            return Object.fromEntries(new URLSearchParams(requestParams).entries());
        },
        /**
         * 获取数据市场详情
         * @param options
         */
        async getDataMarketDetail(options) {
            const dataMarketRequestId = options.dataMarketRequestId
            if (!dataMarketRequestId) return
            const VUE_APP_BASE_API = process.env.VUE_APP_BASE_API
            const getRequestDetailUrl = `${VUE_APP_BASE_API}/data/market/dataApis/detail/${dataMarketRequestId}`
            const result = await fetch(
                getRequestDetailUrl,
                {
                    method: 'get',
                    headers: {
                        Authorization: `Bearer ${this.authorization}`
                    }
                }
            )
                .then((res) => res.json())
                .then((result) => {
                    const requestDetail = {
                        apiVersion: '',
                        apiHeader: {},
                        apiUrl: '',
                        reqMethod: ''
                    }
                    if (result.code == 200) {
                        requestDetail.apiHeader = result.data.header
                        requestDetail.apiVersion = result.data.data.apiVersion
                        requestDetail.apiUrl = result.data.data.apiUrl
                        requestDetail.reqMethod = result.data.data.reqMethod
                    }
                    return requestDetail
                })
            return result
        },
        /**
         * 获取接口市场下拉选项
         * @param options 
         */
        async getDataMarketItemOptions(options) {
            let optionItems = []
            try {
                const { apiHeader, apiVersion, apiUrl, reqMethod } = await this.getDataMarketDetail(options)
                if (!apiVersion || !apiUrl) {
                    this.$message.warning("apiVersion 或者 apiUrl 不存在")
                    return optionItems
                }
                const VUE_APP_BASE_API = process.env.VUE_APP_BASE_API
                const requestUrl = `${VUE_APP_BASE_API}/data/api/services/${apiVersion}${apiUrl}`
                const dataMarketRequestParams = options.dataMarketRequestParams
                const requestParams = this.buildRequestParams(dataMarketRequestParams)
                let dataMarketResult = null
                const requestHeaders = {}
                Object.keys(apiHeader).forEach((key) => {
                    const value = apiHeader[key]
                    const newKey = key.replace('K', '-k')
                    requestHeaders[newKey] = value
                })
                if (reqMethod.toLowerCase() == 'get') {
                    const finalRequestUrl = `${requestUrl}?${new URLSearchParams(
                        requestParams
                    ).toString()}`
                    dataMarketResult = await fetch(finalRequestUrl, {
                        method: 'get',
                        headers: {
                            ...requestHeaders,
                            Authorization: `Bearer ${this.authorization}`
                        }
                    }).then((res) => res.json())
                } else {
                    dataMarketResult = await fetch(requestUrl, {
                        method: 'post',
                        body: JSON.stringify(requestParams),
                        headers: {
                            ...requestHeaders,
                            'Content-Type': 'application/json',
                            Authorization: `Bearer ${this.authorization}`
                        }
                    }).then((res) => res.json())
                }
                if (dataMarketResult.code == 200) {
                    const dataMarketResultData = dataMarketResult.data
                    if (!dataMarketResultData || !Array.isArray(dataMarketResultData)) {
                        this.$message.error('数据不符合要求')
                        return
                    }
                    optionItems = dataMarketResultData.map((item) => {
                        const dataMarketResponseLabelKey = options.dataMarketResponseLabelKey
                        if (dataMarketResponseLabelKey) {
                            item.label = item[dataMarketResponseLabelKey]
                        }
                        const dataMarketResponseValueKey = options.dataMarketResponseValueKey
                        if (dataMarketResponseValueKey) {
                            item.value = item[dataMarketResponseValueKey]
                        }
                        return item
                    })
                }
                return optionItems
            } catch (error) {
                return optionItems
            }
        },
        /**
         * 获取接口地址下拉选项
         * @param options 
         */
        async getInterfaceAddressItemOptions(options) {
            let optionItems = []
            try {
                const url = options.interfaceAddressRequestUrl
                if (!url) {
                    this.$message.warning("没有配置接口地址")
                    return optionItems
                }
                const method = options.interfaceAddressRequestMethod
                const interfaceAddressRequestParams = options.interfaceAddressRequestParams
                const requestParams = this.buildRequestParams(interfaceAddressRequestParams)
                let interfaceAddressResult = null
                if (method == 'get') {
                    const requestUrl = `${url}${requestParams ? '?' : ''}${new URLSearchParams(
                        requestParams
                    ).toString()}`
                    interfaceAddressResult = await fetch(requestUrl, {
                        method: 'get',
                        headers: {
                            Authorization: `Bearer ${this.authorization}`
                        }
                    }).then((res) => res.json())
                } else {
                    interfaceAddressResult = await fetch(url, {
                        method: 'post',
                        body: JSON.stringify(requestParams),
                        headers: {
                            Authorization: `Bearer ${this.authorization}`,
                            'Content-Type': 'application/json'
                        }
                    }).then((res) => res.json())
                }
                if (interfaceAddressResult.code == 200) {
                    const interfaceAddressResultData = interfaceAddressResult.data
                    if (
                        !interfaceAddressResultData ||
                        !Array.isArray(interfaceAddressResultData)
                    ) {
                        this.$message.error('数据不符合要求')
                        return
                    }
                    optionItems = interfaceAddressResultData.map((item) => {
                        const interfaceAddressResponseLabelKey = options.interfaceAddressResponseLabelKey
                        if (interfaceAddressResponseLabelKey) {
                            item.label = item[interfaceAddressResponseLabelKey]
                        }
                        const interfaceAddressResponseValueKey = options.interfaceAddressResponseValueKey
                        if (interfaceAddressResponseValueKey) {
                            item.value = item[interfaceAddressResponseValueKey]
                        }
                        return item
                    })
                }
                return optionItems
            } catch (err) {
                return optionItems
            }
        },
        /**
         * 校验默认值是否为数组
         * @param option 搜索配置
         */
        checkDefaultValueIsArray(option) {
            const isCheckbox = option.type === "checkbox"
            const isMultipleSelect = option.type === "select" && option.multiple == true
            const isTimeRange = option.type === "time" && option.matchType == "4"
            const isDateRange = option.type === "date" && option.matchType == "4"
            return isCheckbox || isMultipleSelect || isTimeRange || isDateRange
        },
        /**
         * 构建具有双向绑定的searchFormParams
         * @param option 搜索配置
         */
        buildSearchFormParams(option) {
            if (option.type === "time") {
                // 只有精准匹配的时候才有默认值
                if (option.matchType == "2") {
                    const currentDate = dayjs().format("YYYY-MM-DD")
                    this.$set(this.searchFormParams, option.name, dayjs(currentDate + " " + option.defaultValue).valueOf())
                } else {
                    // 范围匹配没有默认值 或者默认值为空数组
                    this.$set(this.searchFormParams, option.name, null)
                }
            } else if (option.type === "date") {
                if (option.matchType == "2") {
                    // 精准查询
                    if (!!option.defaultValueSettingtTabName) {
                        // 表单配置的精准查询
                        if (option.defaultValueSettingtTabName === "fixedValue") {
                            // 精准匹配 固定值
                            this.$set(this.searchFormParams, option.name, dayjs(option.defaultValue).valueOf())
                        } else {
                            // 精准匹配 动态值
                            this.$set(this.searchFormParams, option.name, dayjs().add(option.dynamicValue, 'day').format("YYYY-MM-DD HH:mm:ss").valueOf())
                        }
                    } else {
                        // 列表配置的精准查询
                        this.$set(this.searchFormParams, option.name, "")
                    }
                } else {
                    // const defaultValue = option.defaultValue ? Array.isArray(option.defaultValue) ? option.defaultValue : JSON.parse(option.defaultValue) : []
                    // 时间范围
                    this.$set(this.searchFormParams, option.name, [])
                }
            } else if (option.type === "select") {
                if (option.matchType == "2") {
                    //  == true 的判断条件不要去掉  后端可能返回 "true"
                    if (option.multiple == true) {
                        // 精准匹配多选
                        const defaultValue = option.defaultValue ? Array.isArray(option.defaultValue) ? option.defaultValue : JSON.parse(option.defaultValue) : []
                        this.$set(this.searchFormParams, option.name, defaultValue)
                    } else {
                        const defaultValue = option.defaultValue ? option.defaultValue : null
                        // 精准匹配单选
                        this.$set(this.searchFormParams, option.name, defaultValue)
                    }
                } else {
                    // 暂时没有
                }
            } else if (option.type === "checkbox") {
                if (option.matchType == "2") {
                    const defaultValue = option.defaultValue ? Array.isArray(option.defaultValue) ? option.defaultValue : JSON.parse(option.defaultValue) : []
                    this.$set(this.searchFormParams, option.name, defaultValue)
                } else {
                    // 暂时没有
                }
            } else {
                // 剩下的就是 input number textarea radio 配置的默认值
                const defaultValue = option.defaultValue ? option.defaultValue : null
                this.$set(this.searchFormParams, option.name, defaultValue)
            }
        },
        /**
         * 构建搜索表单
         */
        handleBuildSearchConfigForm() {
            return new Promise((resolve) => {
                this.$nextTick(async () => {
                    this.searchConfiguration = []
                    const searchConfiguration = this.searchConfig
                    for (let index = 0, len = searchConfiguration.length; index < len; index++) {
                        const item = searchConfiguration[index];
                        this.buildSearchFormParams(item)
                        if (!!item.associatedParent) {
                            item.associatedParent = Array.isArray(item.associatedParent) ? item.associatedParent : JSON.parse(item.associatedParent)
                        }
                        this.searchConfiguration.push(item)
                    }
                    resolve()
                    // 异步构建select的下拉选 不用阻塞 发送数据请求
                    for (let index = 0, len = searchConfiguration.length; index < len; index++) {
                        const item = searchConfiguration[index];
                        item.optionItems = await this.getOptionItems(item)
                        this.$set(this.searchConfiguration, index, { ...item, optionItems: item.optionItems })
                    }
                })
            })
        },
        /**
         * 组件样式
         */
        widgetStyle(option) {
            return {
                width: option.columnWidth
            }
        },
        datePickerType(option) {
            return option.displayType
        },
        dateRangePickerType(option) {
            if (option.displayType == "datetimerange") {
                return option.displayType + "range"
            } else if (option.displayType == "month") {
                return option.displayType + "range"
            } else if (option.displayType == "date") {
                return option.displayType + "range"
            } else if (option.displayType == "year") {
                return "yearrange"
            } else {
                return "daterange"
            }
        },
        widgetPlaceholder(option) {
            return option.label
        },
        /**
         * 是否显示input组件
         * @param option 搜索配置
         */
        hasInputWidget(option) {
            const matchTypes = ["2", "3"]
            const widgetType = option.type
            return INPUTTYPES.includes(widgetType) && matchTypes.includes(option.matchType)
        },
        /**
         * 是否显示日期组件
         * @param option 搜索配置
         */
        hasDatePickerWidget(option) {
            return DATETYPES.includes(option.type) && option.matchType === '2'
        },
        /**
         * 是否显示日期范围组件
         * @param option 搜索配置
         */
        hasDatePickerRangeWidget(option) {
            return DATERANGETYPES.includes(option.type) && option.matchType === '4'
        },
        /**
         * 是否显示时间组件
         * @param option 搜索配置
         */
        hasTimePickerWidget(option) {
            return TIMETYPES.includes(option.type) && option.matchType === '2'
        },
        /**
         * 是否显示时间范围组件
         * @param option 搜索配置
         */
        hasTimePickerRangeWidget(option) {
            return TIMETYPES.includes(option.type) && option.matchType === '4'
        },
        /**
         * 是否显示select组件
         * @param option 搜索配置
         */
        hasSelectWidget(option) {
            const widgetType = option.type
            return SELECTTYPES.includes(widgetType)
        },
        /**
         * 获取选项
         * @param option 搜索配置
         */
        async getOptionItems(option) {
            if (option.type === "radio" || option.type === "checkbox") {
                // 单选 多选 下拉选项是固定的
                const optionItems = option.optionItems ? Array.isArray(option.optionItems) ? option.optionItems : JSON.parse(option.optionItems) : []
                return optionItems.map(item => {
                    return {
                        ...item,
                        value: String(item.value) // 临时处理 不这么做的话 下拉框的选项匹配不上
                    }
                })
            } else if (option.type === "select") {
                if (option.optionSettingType === "interfaceAddress") {
                    // 接口地址
                    return await this.getInterfaceAddressItemOptions(option)
                } else if (option.optionSettingType === "dataMarket") {
                    // 数据市场
                    return await this.getDataMarketItemOptions(option)
                } else if (option.optionSettingType === "fixedValue") {
                    // 固定值
                    const optionItems = option.optionItems ? Array.isArray(option.optionItems) ? option.optionItems : JSON.parse(option.optionItems) : []
                    return optionItems.map(item => {
                        return {
                            ...item,
                            value: String(item.value) // 临时处理 不这么做的话 下拉框的选项匹配不上
                        }
                    })
                } else {
                    // 2025年02月28日16:55:04 xuchongyu 是否删除标志不再参数搜索
                    // if (option.name === "del_flag") {
                    //     // 单独处理 "del_flag" 下拉项 0是未删除 1是删除
                    //     return this.delFlagOptions
                    // }  
                    if (option.name === "is_valid") {
                        return this.validOptions
                    }
                    if (option.name === "approvalStatus") {
                        return this.approvalStatuOptions
                    }
                }
            } else {
                return null
            }

        },
        /**
         * 下拉框组件发生变更
         * @param option 
         */
        async handleSelectChange(option) {
            if (option.type !== "select") return
            const name = option.name
            const searchConfiguration = this.searchConfiguration
            for (let index = 0, len = searchConfiguration.length; index < len; index++) {
                const item = searchConfiguration[index]
                let associatedParent = []
                if (item.associatedParent) {
                    if (Array.isArray(item.associatedParent)) {
                        associatedParent = item.associatedParent
                    } else {
                        try {
                            associatedParent = JSON.parse(item.associatedParent)
                        } catch (error) {
                            console.error("associatedParent 解析错误", item.associatedParent)
                        }
                    }
                }
                if (associatedParent && associatedParent.length) {
                    if (associatedParent.includes(name)) {
                        const optionItems = await this.getOptionItems(item)
                        this.$set(this.searchConfiguration, index, { ...item, optionItems })
                    }
                }
            }
        },
        selectMultiple(option) {
            return this.checkDefaultValueIsArray(option)
        },
        /**
         * 处理请求参数
         */
        handleBuildRequestParams() {
            // 给后端处理数据 将日期数据处理成时间戳
            const searchFormParams = JSON.parse(JSON.stringify(this.searchFormParams))
            this.searchConfiguration.forEach(item => {
                if (item.type === "date" || item.type === "time") {
                    const currenValue = searchFormParams[item.name]
                    if (Array.isArray(currenValue)) {
                        if (currenValue.length > 0) {
                            searchFormParams[item.name] = currenValue.map((ite, index) => {
                                if (index === 0) {
                                    return dayjs(ite).startOf("day").valueOf()
                                } else if (index === 1) {
                                    // 因为组件 时间范围 选择同一个时间的话 这个数据都是每天的 00：00：00 需要特殊处理
                                    if (item.displayType === "date") {
                                        // 将日期组件的时间范围 处理成 00:00:00 23:59:59
                                        const endTime = dayjs(ite).format("YYYY-MM-DD 23:59:59")
                                        return dayjs(endTime).valueOf()
                                    } else if (item.displayType === "month") {
                                        return dayjs(ite).endOf('month').valueOf()
                                    } else if (item.displayType === "year") {
                                        return dayjs(ite).endOf('year').valueOf()
                                    } else {
                                        return dayjs(ite).endOf('day').valueOf()
                                    }
                                }
                            })
                            searchFormParams[item.name] = searchFormParams[item.name].join("~")
                        } else {
                            searchFormParams[item.name] = searchFormParams[item.name].join("~")
                        }
                    } else {
                        searchFormParams[item.name] = !!currenValue ? dayjs(currenValue).valueOf() : null
                    }
                } else if (item.type === "number") {
                    // 将number组件类型的数据转成number
                    const currenValue = searchFormParams[item.name]
                    searchFormParams[item.name] = !!currenValue ? Number(searchFormParams[item.name]) : null
                }
            })
            console.log("构建之后的请求数据", searchFormParams);
            return searchFormParams
        },
        /**
         * 点击搜索按钮
         */
        handleSubmitQuery() {
            this.$emit("search")
        },
        /**
         * 点击重置按钮
         */
        handleResetQuery() {
            this.$refs.queryFormRef.resetFields()
            this.$emit("reset")
        },
        /**
         * 日期组件点击
         * @param option 
         */
        handleClickDatePicker(option) {
            this.mobileDatePickerVisible = true
            this.selectedMobileDateOption = option
        },
        /**
         * 日期组件点击确认
         * @param date 
         */
        // handleDatePickerConfirm(date) {
        //     this.searchFormParams[this.selectedMobileDateOption.name] = dayjs(date).valueOf()
        //     this.selectedMobileDateOption = null
        //     this.mobileDatePickerVisible = false
        // },

        // handleClickDateRangePicker(option) {
        //     this.dateRangePickerVisible = true
        //     this.selectedDateRangeOption = option
        // },

        /**
         * 日期范围组件点击确认
         * @param date 
         */
        // handleDateRangePickerConfirm(date) {
        //     const [starTime, endTime] = date;
        //     this.searchFormParams[this.selectedDateRangeOption.name] = [new Date(starTime).getTime(), new Date(endTime).getTime()]
        //     this.selectedDateRangeOption = null
        //     this.dateRangePickerVisible = false
        // }
    }
}
</script>
<style lang="scss">
.mobile-date-range-picker {
    transform: scale(0.9) !important;
    width: 300px !important;
    transform-origin: top center !important;

    .el-picker-panel__body-wrapper {
        .el-picker-panel__body {
            min-width: 300px !important;

            .el-picker-panel__content {
                width: 100%;
                float: none;
            }
        }
    }
}

.mobile-time-range-picker {
    width: 300px !important;
    transform: scale(0.9) !important;
    transform-origin: top center !important;
}

.mobile-time-picker,
.mobile-date-picker {
    transform: scale(0.9) !important;
    transform-origin: top center !important;
}
</style>