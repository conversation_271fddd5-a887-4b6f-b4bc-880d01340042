<template>
  <el-table v-loading="tableLoading" :height="tableHeight" :data="tableData" element-loading-text="数据加载中..."
    ref="tableRef" :header-cell-style="headerCellStyle" stripe border fit :max-height="tableHeight"
    :header-cell-class-name="handleHeaderClass" @sort-change="handleSortChange" highlight-current-row
    @selection-change="handleSelectionChange">
    <slot name="selection-column"></slot>
    <el-table-column :fixed="item.freezeColumns === 1 ? true : false" sortable="custom" v-for="item in tableColumns"
      :key="item.name" :label="item.label" :prop="item.name" show-overflow-tooltip :min-width="columnWidth(item)"
      :sort-orders="['descending', 'ascending', null]" align="center">
      <template slot-scope="{ row }">
        <div @click="handleClickCell(row, item)" class="table-column-content">
          <span v-html="getTableRenderItem(row, item)" :style="getTableRenderStyle(item)" class="ellipsis-text"></span>
        </div>
      </template>
    </el-table-column>
    <slot name="handler-column"></slot>
  </el-table>
</template>
<script>
import { listAttachment } from "@/api/file/attachment"
export default {
  props: {
    tableLoading: {
      type: Boolean,
      default: false
    },
    tableData: {
      type: Array,
      default: () => []
    },
    tableColumns: {
      type: Array,
      default: () => []
    },
    hasHanlerColumn: {
      type: Boolean,
      default: false
    },
    expandCollapseColumn: {
      type: Boolean,
      default: false
    },
    tableHeight: {
      type: Number,
      default: () => 330
    },
    validOptions: {
      type: Array,
      default: () => []
    },
    approvalStatuOptions: {
      type: Array,
      default: () => []
    },
    delFlagOptions: {
      type: Array,
      default: () => []
    },
    sortable: {
      type: String | Boolean,
      default: () => false
    },
    formDesignDetail: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      orderBys: []
    }
  },
  computed: {
    headerCellStyle() {
      return { background: '#f5f7fa', color: '#666' }
    }
  },
  methods: {
    // 设置列的排序状态
    handleHeaderClass({ column }) {
      if (column.sortable === 'custom') {
        const tableColumn = this.tableColumns.find(col => col.name === column.property);
        if (tableColumn) {
          // 设置列的排序状态
          column.multiOrder = tableColumn.sortOrder;
          column.order = tableColumn.sortOrder;
        }
      }
    },
    columnWidth(option) {
      const columnWidth = option.columnWidth
      if (!columnWidth) {
        return "150"
      }
      if (columnWidth.endsWith('px')) {
        // 去掉后面 px
        const width = columnWidth.substring(0, columnWidth.length - 2)
        return width
      }
      return columnWidth
    },
    async handleClickCell(row, column) {
      return
      console.log(row, column);
      if (column.type === "file-upload") {
        const businessId = row._id
        const businessType = this.formDesignDetail.id
        const fileList = row[column.name]
        if (fileList && fileList.length) {
          const fileId = fileList[0].fileId
          const result = await listAttachment({
            businessId,
            businessType,
            fileId
          })
          if (result.code == 200) {
            const rows = result.rows
            if (rows && rows.length > 0) {
              const fileOption = rows[0]
              console.log("fileOption--fileOption", fileOption);
              const relativePath = fileOption.relativePath
              const fileName = fileOption.fileName
              // const downloadUrl = `${process.env.VUE_APP_BASE_API}${relativePath}`
              const downloadUrl = `${window.location.origin}${"/opsyndex-file"}${relativePath}`
              console.log("downloadUrl--downloadUrl", downloadUrl);
              this.downloadFile(downloadUrl, fileName)
            }
          }
        }

      }
    },

    downloadFile(fileUrl, fileName) {
      const link = document.createElement("a");
      link.href = fileUrl;
      link.download = fileName; // 可选，浏览器会自动解析文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    handleBuildSortParams() {
      return new Promise((resolve) => {
        this.$nextTick(() => {
          const orderBys = []
          this.tableColumns.forEach(column => {
            if (column.sortOrder) {
              orderBys.push({
                column: column.name,
                sortOrder: column.sortOrder
              })
            }
          })
          this.orderBys = orderBys
          resolve(this.orderBys)
        })
      })
    },
    /**
     * 渲染table-item的方法
     * @param row
     * @param item
     */
    getTableRenderItem(row, item) {
      let result = ""
      if (item.type == 'file-upload' || item.type == 'picture-upload') {
        const fileOptions = this.getFileOptions(row[item.name])
        if (fileOptions && fileOptions.length > 0) {
          fileOptions.forEach(fileOption => {
            result += `<span style="cursor: pointer;">${fileOption.name}</span>`
          })
        }
      } else if (item.name === 'approvalStatus') {
        result = this.getLabelMethod(item.name, row[item.name])
      } else if (item.name === 'is_valid') {
        result = this.getLabelMethod(item.name, row[item.name])
      } else if (item.name === 'del_flag') {
        result = this.getLabelMethod(item.name, row[item.name])
      } else if (item.formatScript) {
        const formatScriptFunction = new Function(item.formatScript)
        result = formatScriptFunction.call(row)
      } else {
        result = row[item.name]
      }
      return result
    },
    getTableRenderStyle(item) {
      const overflow = item.overflow
      // overflow 为 true的，换行显示
      if (overflow) {
        return { overflow: 'hidden', whiteSpace: 'normal' }
      }
      return {}
    },
    getFileOptions(fileOptions) {
      if (Array.isArray(fileOptions) && fileOptions.length > 0) {
        return fileOptions.filter(_ => _)
      }
      return []
    },
    getLabelMethod(filedName, value) {
      if (filedName === "approvalStatus") {
        const statusOption = this.approvalStatuOptions.find(item => value == item.value)
        if (statusOption) {
          return statusOption.label
        } else {
          return value
        }
      }
      if (filedName === "is_valid") {
        const vaildOption = this.validOptions.find(item => value == item.value)
        if (vaildOption) {
          return vaildOption.label
        } else {
          return value
        }
      }
      if (filedName === "del_flag") {
        const delFlagOption = this.delFlagOptions.find(item => value == item.value)
        if (delFlagOption) {
          return delFlagOption.label
        } else {
          return value
        }
      }
    },
    handleSelectionChange(value) {
      // 直接做转发
      this.$emit("selection-change", value)
    },
    handleSortChange({ column, prop, order }) {
      if (column.sortable !== 'custom') return;

      column.multiOrder = order;

      // 更新表格列数据中的排序状态
      const tableColumn = this.tableColumns.find(col => col.name === prop);
      if (tableColumn) {
        tableColumn.sortOrder = column.multiOrder;
      }

      // 更新排序参数
      const existingOrder = this.orderBys.find(item => item.column === prop)

      if (existingOrder) {
        existingOrder.sortOrder = column.multiOrder
      } else {
        this.orderBys.push({
          column: prop,
          sortOrder: column.multiOrder
        })
      }

      this.$emit("sort-change", this.orderBys);
    },
  }
}
</script>
<style lang="scss" scoped>
.table-column-content {
  display: flex;
  flex-direction: column;
}

.ellipsis-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}
</style>