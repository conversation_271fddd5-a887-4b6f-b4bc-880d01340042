<template>
  <div class="app-container" v-loading="pageLoading">
    <search-config-form ref="searchConfigFormRef" :searchConfig="searchConfiguration"
      :approvalStatuOptions="approvalStatuOptions" :validOptions="validOptions" :delFlagOptions="delFlagOptions"
      @search="handleSearch" @reset="handleReset"></search-config-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAddFormData"
          v-if="hasAddButton()">
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" @click="handleBatchDeleteFormData"
          v-if="hasBatchDeleteButton()">
          删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-dropdown style="font-size: large" v-if="hasExportButton()">
          <el-button type="warning" size="mini">
            导出<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item @click.native="handleExportExcel(formDesignDetail.tableName)">Excel
            </el-dropdown-item>
            <el-dropdown-item @click.native="handleExportPdf">Pdf
            </el-dropdown-item>
            <el-dropdown-item v-if="hasWarmFlowButton()" @click.native="exportApproval">审批
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport" v-if="hasImportButton()">
          导入
        </el-button>
      </el-col>
      <el-col :span="1.5" :offset="22.5">
        <el-button type="success" plain icon="el-icon-document" size="mini" @click="handleOpenWorkflow"
          v-if="hasWarmFlowButton()">发起流程</el-button>
      </el-col>
      <!--      <el-col :span="1.5" :offset="22.5">-->
      <!--        <el-button type="warning" plain icon="el-icon-document" size="mini" @click="exportApproval"-->
      <!--                   v-if="hasWarmFlowButton()">审批导出</el-button>-->
      <!--      </el-col>-->
      <el-col :span="1.5">
        <el-button type="primary" plain size="mini" @click="expandCollapseColumn = !expandCollapseColumn">{{
          `${expandCollapseColumn ? "收起" : "展开"}操作` }}
        </el-button>
      </el-col>
    </el-row>
    <!--  -->
    <form-data-table ref="fullHeightTableRef" :tableHeight="tableHeight" :formDesignDetail="formDesignDetail"
      :tableLoading="tableLoading" :tableData="tableData" :tableColumns="tableColumns"
      :approvalStatuOptions="approvalStatuOptions" :validOptions="validOptions" :delFlagOptions="delFlagOptions"
      @selection-change="handleSelectionChange" @sort-change="handleSortTableData" sortable="custom">
      <el-table-column slot="selection-column" type="selection" width="55" align="center" fixed="left"
        :selectable="hasDeleteButton" />
      <el-table-column slot="handler-column" v-if="expandCollapseColumn" label="操作" align="center" fixed="right"
        width="220">
        <template slot-scope="{ row }">
          <el-button size="mini" type="text" @click="handleViewApproval(row)" v-if="hasViewApprovalButton(row)">
            审批查看
          </el-button>
          <el-button size="mini" type="text" @click="handleCopyFormData(row)" v-if="hasCopyButton(row)">
            复制
          </el-button>
          <el-button size="mini" type="text" @click="handleViewFormData(row)" v-if="hasViewButton(row)">
            查看
          </el-button>
          <el-button size="mini" type="text" @click="handleUpdateFormData(row)" v-if="hasEditButton(row)">
            编辑
          </el-button>
          <el-button size="mini" type="text" @click="handleDeleteFormData(row)" v-if="hasDeleteButton(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </form-data-table>

    <el-pagination style="float: right;" v-show="total > 0" @size-change="handlePageSizeChange"
      @current-change="handlePageNumChange" :current-page="queryParams.pageNum"
      :page-sizes="[10, 20, 30, 50, 100, 1000]" :page-size="queryParams.pageSize"
      layout="total, sizes, prev, pager, next, jumper" :total="total">
    </el-pagination>

    <!-- 新建编辑查看复制 -->
    <el-dialog :title="formDesignDetail.tableName" :visible.sync="formRenderOption.formRenderShow" width="80%">
      <v-form-render :handle-type="handleType" v-if="formRenderOption.formRenderShow"
        :downUploadFileCallback="downLoadUploadFile" :form-json="formRenderOption.formJson"
        :form-data="formRenderOption.formData" ref="vFormRef">
      </v-form-render>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="formRenderOption.loading" @click="saveFormData">{{ formRenderButtonName }}</el-button>
        <el-button type="primary" v-if="savaAndStartButton()" @click="savaAndStart()">保存并发起流程</el-button>
      </div>
    </el-dialog>

    <el-dialog title="流程信息" :visible.sync="flowNodeDialogVisible" width="80%">
      <el-alert title="审批用户" style="margin: 10px 0;" type="info" :closable="false"></el-alert>

      <el-form label-width="auto">
        <el-form-item :label="processNode.nodeName" v-for="(processNode, index) in processNodeList" :key="index">
          <el-tag type="success" style="margin: 0 5px;" v-for="(userName, userIndex) in processNode.userName"
                  @close="handleDeleteSelectedUser(index, userIndex)" :key="userName"
                  :closable="processNode.approvalMode === '1'">
            {{ userName }}
          </el-tag>
          <el-button type="primary" @click="initUser(processNode)" size="mini"
                     v-if="hasSelectUserButton(processNode, index)">选择用户</el-button>
        </el-form-item>
      </el-form>
      <el-alert v-if="hasCCButton()" title="抄送用户" style="margin: 10px 0;" type="info" :closable="false"></el-alert>

      <el-form v-if="hasCCButton()" label-width="auto">
        <el-form-item>
          <el-tag type="success" style="margin: 0 5px;" v-for="(userName, userIndex) in copyUserNames"
                  @close="handleDeleteSelectedCopyUser(userIndex)" :key="userName" :closable="true">
            {{ userName }}
          </el-tag>
          <el-button type="primary" @click="initCopyUser" size="mini">选择用户</el-button>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary"  @click="startFlow()">确认发起</el-button>
      </div>
    </el-dialog>

    <!-- 流程发起表单-->
    <el-dialog title="审批流程" :visible.sync="flowRenderShow" width="80%">
      <el-alert title="已选数据" style="margin-bottom: 10px;" type="info" :closable="false"></el-alert>
      <form-data-table :tableData="selectedTabledata" :tableColumns="tableColumns"
        :approvalStatuOptions="approvalStatuOptions" :validOptions="validOptions"
        :delFlagOptions="delFlagOptions"></form-data-table>

      <el-alert title="审批用户" style="margin: 10px 0;" type="info" :closable="false"></el-alert>

      <el-form label-width="auto">
        <el-form-item :label="processNode.nodeName" v-for="(processNode, index) in processNodeList" :key="index">
          <el-tag type="success" style="margin: 0 5px;" v-for="(userName, userIndex) in processNode.userName"
            @close="handleDeleteSelectedUser(index, userIndex)" :key="userName"
            :closable="processNode.approvalMode === '1'">
            {{ userName }}
          </el-tag>
          <el-button type="primary" @click="initUser(processNode)" size="mini"
            v-if="hasSelectUserButton(processNode, index)">选择用户</el-button>
        </el-form-item>
      </el-form>
      <el-alert v-if="hasCCButton()" title="抄送用户" style="margin: 10px 0;" type="info" :closable="false"></el-alert>

      <el-form v-if="hasCCButton()" label-width="auto">
        <el-form-item>
          <el-tag type="success" style="margin: 0 5px;" v-for="(userName, userIndex) in copyUserNames"
            @close="handleDeleteSelectedCopyUser(userIndex)" :key="userName" :closable="true">
            {{ userName }}
          </el-tag>
          <el-button type="primary" @click="initCopyUser" size="mini">选择用户</el-button>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="warmFlowFire">
          发起流程
        </el-button>
      </div>
    </el-dialog>


    <!-- 权限标识：会签票签选择用户 -->
    <el-dialog title="用户选择" :visible.sync="selectUserDialogVisible" width="80%" append-to-body>
      <select-user v-if="selectUserDialogVisible" ref="selectUserRef" :userIds="userIds"
        :userVisible.sync="selectUserDialogVisible" @handleUserSelect="handleUserSelect">
      </select-user>
    </el-dialog>

    <!-- 权限标识：抄送选择用户 -->
    <el-dialog title="用户选择" :visible.sync="selectCopyUserDialogVisible" width="80%" append-to-body>
      <select-user v-if="selectCopyUserDialogVisible" ref="selectCopyUserRef" :userIds="userCopyIds"
        :userVisible.sync="selectCopyUserDialogVisible" @handleUserSelect="handleCopyUserSelect">
      </select-user>
    </el-dialog>

    <!-- 表单数据导入对话框 -->
    <el-dialog title="表单数据导入" :visible.sync="uploadConfig.visible" width="400px" append-to-body>
      <div v-loading="importLoading">
        <el-select style="margin-bottom: 2vh" filterable v-model="queryParams.templateId" placeholder="请选择导入模版">
          <el-option v-for="item in templateList" :key="item.id" :label="item.templateName"
            :value="item.id"></el-option>
        </el-select>
        <el-button type="success" plain icon="el-icon-download" size="mini"
          @click="downloadImportTemplate(formDesignDetail.tableName)">下载导入模版
        </el-button>
        <el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="uploadConfig.headers" :action="uploadConfig.url +
          '?updateSupport=' +
          uploadConfig.updateSupport +
          '&collectionName=' +
          formDesignDetail.tableCode +
          '&templateId=' +
          queryParams.templateId" :disabled="uploadConfig.isUploading" :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess" :auto-upload="false" drag>
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip text-center" slot="tip">
            <span>仅允许导入xls、xlsx格式文件。</span>
          </div>
        </el-upload>
        <div style="overflow: hidden" slot="footer" class="dialog-footer">
          <el-button style="float: right" type="primary" @click="submitFileForm">确 定
          </el-button>
          <el-button style="float: right" @click="uploadConfig.visible = false">取 消
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAttachment } from "@/api/file/attachment"
import { batchUpdateAttachment } from "@/api/file/attachment"
import dayjs from "dayjs"
import { getSearchConfigList, getButtonPermissions } from "@/api/codeDev/formDesign/formDesign";
import SearchConfigForm from './searchConfigForm.vue'
import FormDataTable from "./formDataTable.vue";
import { listFormImportTemplate } from '@/api/codeDev/formImportTemplate/formImportTemplate'
import { getFormDesignList } from '@/api/codeDev/formDesign/formDesign'
import {
  deleteFormDataBatch,
  deleteFormDataById,
  downloadFileData, exportFormDataApproval,
  getFormDataList,
  insertFormData,
  updateFormData,
} from '@/api/codeDev/formData/formData'
import { getInstanceIds } from '@/api/flow/instance'
import { getToken } from '@/utils/auth'
import tableFullHeight from "@/utils/tableFullHeight.js"
import SelectUser from '@/views/components/selectUser.vue'
import { flowStart } from '@/api/flow/instance'
import {getApproverInfo, getNodesByDefCode} from '@/api/flow/execute'
import { Loading } from "element-ui";
import {getDefinitionByCode} from "@/api/flow/definition";
export default {
  name: "FormData", // 勿动 缓存组件用
  mixins: [tableFullHeight],
  components: { SelectUser, SearchConfigForm, FormDataTable },
  data() {
    return {
      // 初始化页面的loading
      pageLoading: false,
      searchConfiguration: [],
      // formRender的操作类型
      handleType: 'add',
      // tableHeight: 330,
      expandCollapseColumn: true,
      buttonPermissions: [],
      userIds: '',
      userCopyIds: '',
      copyUserIds: [],
      copyUserNames: [],
      processNodeList: [],// 审批流程图
      clickItem: {}, // 点击框框数据
      importLoading: false,
      templateList: [],
      approvalStatuOptions: [
        { label: '未发起', value: "-1" },
        { label: '未开始', value: "0" },
        { label: '审批中', value: "1" },
        { label: '审批结束', value: "2" },
        { label: '驳回', value: "3" },
        { label: '撤回', value: "4" }
      ],
      validOptions: [
        {
          label: "无效",
          value: "0"
        }, {
          label: "有效",
          value: "1",
        }
        , {
          label: "锁定",
          value: "2",
        }
      ],
      delFlagOptions: [
        {
          value: "0",
          label: "正常"
        },
        {
          value: "1",
          label: "删除"
        }
      ],
      // 数据导入参数
      uploadConfig: {
        visible: false,
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/codeDev/formData/importFormData'
      },
      // 选中数组
      selectedMongoIds: [],
      // 正在操作的数据id
      selectedMongoId: null,
      selectedRowId: null,
      total: 0,
      // formRenderShow: false,
      flowRenderShow: false,
      selectUserDialogVisible: false,
      selectCopyUserDialogVisible: false,
      // 查询参数
      queryParams: {
        searchFormParams: null,
        pageNum: 1,
        pageSize: 10,
        tableCode: null,
        formDesignId: null,
        // formJsonInfo: null,
        tableName: null,
        sortBys: []
      },
      formRenderOption: {
        loading: false,
        formRenderShow: false,
        formJson: {},
        formData: {},
        copyFormData: {}
      },
      formDesignDetail: {
        tableName: '',
        tableCode: '',
        id: '',
        formJsonInfo: '',
        columnInfo: '',
        columnInfoStr:'',
      },
      tableLoading: false,
      tableData: [],
      originTableData: [],
      tableColumns: [],
      selectedTabledata: [],
      warmFlowCode: '',
      isInvalidView: '',
      formDataList :[],
      isCc:'',
      flowNodeDialogVisible: false,
      formRenderButtonName: "",
      // lastTimeSearchFormParams: null
    }
  },
  async mounted() {
    await this.getFormDesignDetail()
    await this.initPage()
    // this.getTableMaxHeight()
    this.getTemplateList()
    this.handleGetButtonPermissions()
  },
  methods: {
    async downLoadUploadFile(fileList, fileName) {
      if (this.handleType === "add") {
        this.$message.warning("新建不可下载文件")
        return
      }
      let currentFile = null
      fileList.forEach(fileOption => {
        if ("response" in fileOption) {
          if (fileName === fileOption.response.data.name) { }
          currentFile = fileOption.response.data
        } else {
          if (fileOption.name === fileName) {
            currentFile = fileOption
          }
        }
      })
      if (!currentFile) return
      const businessId = this.selectedRowId
      const businessType = this.formDesignDetail.id
      const fileId = currentFile.fileId
      const result = await listAttachment({
        businessId,
        businessType,
        fileId
      })
      if (result.code == 200) {
        const rows = result.rows
        if (rows && rows.length > 0) {
          const fileOption = rows[0]
          const relativePath = fileOption.relativePath
          const fileName = fileOption.fileName
          // const downloadUrl = `${process.env.VUE_APP_BASE_API}${relativePath}`
          const downloadUrl = `${window.location.origin}${"/opsyndex-file"}${relativePath}`
          this.downloadFile(downloadUrl, fileName)
        }
      }
    },
    downloadFile(fileUrl, fileName) {
      const link = document.createElement("a");
      link.href = fileUrl;
      link.download = fileName; // 可选，浏览器会自动解析文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    /**
     * 获取模版列表
     */
    async getTemplateList() {
      const formDesignId = this.formDesignDetail.id
      const queryParams = {
        formDesignId,
        pageNum: 1,
        pageSize: 200
      }
      const response = await listFormImportTemplate(queryParams)
      if (response.code === 200) {
        this.templateList = response.rows
      }
    },
    /**
     * 获取当前页面按钮权限
     */
    async handleGetButtonPermissions() {
      const tableCode = this.formDesignDetail.tableCode
      const result = await getButtonPermissions(tableCode)
      if (result.code === 200) {
        this.buttonPermissions = result.data ? result.data.split(',') : []
      }
    },
    /**
     * 获取表单设计详情
     */
    async getFormDesignDetail() {
      const tableCode = this.$route.query.tableCode
      await getFormDesignList({ tableCode }).then(
        async (response) => {
          if (response[0].warmFlow) {
            await getDefinitionByCode(response[0].warmFlow).then(
              async (res) => {
                this.isCc = res.data.isCc
              }
            )
          } else {
            this.isCc = 'N'
          }
          this.warmFlowCode = response[0].warmFlow
          this.isInvalidView = response[0].isInvalidView
          this.formDesignDetail.tableName = response[0].tableName
          this.formDesignDetail.tableCode = response[0].tableCode
          this.formDesignDetail.formJsonInfo = response[0].formJsonInfo
          this.formDesignDetail.id = response[0].id
          this.formDesignDetail.columnInfoStr = response[0].columnInfo
          // this.formDesignDetail.columnInfo = JSON.parse(response[0].columnInfo)
        }
      )
    },
    async initPage() {
      this.queryParams = {
        searchFormParams: null,
        pageNum: 1,
        pageSize: 10,
        tableCode: null,
        formDesignId: null,
        tableName: null,
        // formJsonInfo: null,
        sortBys: []
      }
      this.pageLoading = true
      const id = this.formDesignDetail.id
      if (!id) {
        this.$message.warning("路径中的id不存在")
        return
      }
      const result = await getSearchConfigList(id)
      if (result.code === 200) {
        this.searchConfiguration = []
        const searchConfiguration = result.data.searchConfiguration
        this.tableColumns = result.data.listConfiguration
        this.tableColumns.forEach(item => {
          const currentItem = searchConfiguration.find((i) => {
            return i.name === item.name
          })
          if (currentItem) {
            this.searchConfiguration.push(currentItem)
          }
        })
      }

      // 构建搜索头
      await this.$refs.searchConfigFormRef.handleBuildSearchConfigForm()
      await this.$nextTick()
      await this.handleGetFormDataList()
      setTimeout(() => {
        this.initAutoHeight();
      }, 1000);
      this.pageLoading = false
    },
    /**
     * 搜索头点击搜索
     */
    handleSearch() {
      this.queryParams.pageNum = 1
      this.handleGetFormDataList()
    },
    handleReset() {
      this.handleGetFormDataList()
    },
    getTableMaxHeight() {
      // 获取要监听的 DOM 元素
      const targetElement = document.getElementsByClassName('app-container')[0]
      // 创建 ResizeObserver 实例，并传入回调函数
      const resizeObserver = new ResizeObserver((entries) => {
        entries.forEach(entry => {
          // 获取元素的高度
          const height = entry.contentRect.height;
          this.tableHeight = height - 120
        });
      });
      // 开始监听目标元素的大小变化
      resizeObserver.observe(targetElement);
    },
    hasViewApprovalButton(row) {
      // 审批中 审批结束 驳回 撤回
      // { label: '审批中', value: 1 },
      // { label: '审批结束', value: 2 },
      // { label: '驳回', value: 3 },
      // { label: '撤回', value: 4 }
      const viewApprovalStatus = [1, 2, 3, 4]
      return viewApprovalStatus.includes(row.approvalStatus)
    },
    hasAddButton() {
      return this.buttonPermissions.includes('added') || this.buttonPermissions.includes('all')
    },
    hasBatchDeleteButton() {
      return this.buttonPermissions.includes('batchDelete') || this.buttonPermissions.includes('all')
    },
    hasExportButton() {
      return this.buttonPermissions.includes('export') || this.buttonPermissions.includes('all')
    },
    hasImportButton() {
      return this.buttonPermissions.includes('import') || this.buttonPermissions.includes('all')
    },
    hasWarmFlowButton() {
      return !!this.warmFlowCode
    },
    savaAndStartButton() {
      return !!this.warmFlowCode && (this.handleType === "add" || this.handleType === "edit")
    },
    hasCCButton() {
      return this.isCc === 1;
    },
    hasCopyButton(_row) {
      // 暂时不考虑其他因素
      return this.buttonPermissions.includes('copy') || this.buttonPermissions.includes('all')
    },
    hasViewButton(row) {
      // 查看按钮没有权限字段暂时不用考虑
      // 【查看】功能，仅在【有效】【锁定】展示。1有效 2锁定
      return (row.is_valid == 1 || row.is_valid == 2) || this.isInvalidView == 1 || this.buttonPermissions.includes('all')
    },
    hasEditButton(row) {
      // 删除按钮只在无效的时候展示 row.is_valid == 0 代表无效
      return (this.buttonPermissions.includes('edit') && (row.is_valid == 0 || row.is_valid === null || row.is_valid === undefined || row.is_valid == "")) || this.buttonPermissions.includes('all')
    },
    hasDeleteButton(row) {
      // http://144.123.43.78:19201/index.php?m=bug&f=view&bugID=2731
      // 删除只有在当前行数据 is_valid = 0 的时候支持删除 is_valid = 0 代表 无效
      return (this.buttonPermissions.includes('delete') && row.is_valid == 0) || this.buttonPermissions.includes('all')
    },
    checkStartFlow() {
      for (let i = 0; i < this.processNodeList.length; i++) {
        if ((this.processNodeList[i].approvalMode !== '3' || i === 0) && (!this.processNodeList[i].ids || this.processNodeList[i].ids.length === 0)) {
          this.$message.error(`${this.processNodeList[i].nodeName} 未选择审批人请选择！`)
          return false
        }
      }
      return true
    },
    handlePageSizeChange(val) {
      this.queryParams.pageSize = val
      this.handleGetFormDataList()
    },
    handlePageNumChange(val) {
      this.queryParams.pageNum = val
      this.handleGetFormDataList()
    },
    /**
     * 前端排序
     * @param param0
     */
    handleSortTableData({ prop, order }) {
      // const originTableData = this.originTableData;  // 无需深拷贝
      // if (order === 'descending') {
      //   originTableData.sort((item1, item2) => {
      //     if (typeof item1[prop] === 'number' && typeof item2[prop] === 'number') {
      //       return item2[prop] - item1[prop];
      //     }
      //     return item2[prop].localeCompare(item1[prop]);
      //   });
      // } else if (order === 'ascending') {
      //   originTableData.sort((item1, item2) => {
      //     if (typeof item1[prop] === 'number' && typeof item2[prop] === 'number') {
      //       return item1[prop] - item2[prop];
      //     }
      //     return item1[prop].localeCompare(item2[prop]);
      //   });
      // }
      // this.tableData = [...originTableData];
      this.handleGetFormDataList()
    },
    /**
     * 多选框选中更改数据
     * @param selectedTableData
     */
    handleSelectionChange(selectedTableData) {
      this.selectedMongoIds = selectedTableData.map((item) => item._id)
      this.selectedTabledata = selectedTableData
    },
    /**
     * 点击发起流程
     */
    async handleOpenWorkflow() {
      if (this.selectedMongoIds.length === 0) {
        this.$message.warning('请选择数据')
        return
      }
      if (this.selectedMongoIds.length !== 1) {
        this.$message.warning('只能选择一条数据发起流程')
        return
      }
      // 根据流程定义查询中间节点
      const res = await getNodesByDefCode(this.warmFlowCode)
      if (res.code === 200) {
        this.processNodeList = res.data.filter(item => item.nodeType === 1)
        await this.processingApproval(this.selectedTabledata[0])
        this.flowRenderShow = true
      }

    },
    handleDeleteFormData(row) {
      this.$confirm('确定要进行删除操作吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const delQuery = {
            tableCode: this.formDesignDetail.tableCode,
            mongoId: row._id
          }
          deleteFormDataById(delQuery).then((res) => {
            if (res.code === 200) {
              this.$message.success('删除成功')
            } else {
              this.$message.error('删除失败')
            }
            if (this.tableData.length === 1) {
              this.queryParams.pageNum--
            }
            this.handleGetFormDataList()
          })
        })
    },
    /**
     * 批量删除
     */
    handleBatchDeleteFormData() {
      if (this.selectedMongoIds.length === 0) {
        this.$message.warning('请选择数据')
        return
      }
      this.$confirm('确定要进行删除操作吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const query = {
            tableCode: this.formDesignDetail.tableCode,
            mongoIds: this.selectedMongoIds
          }
          const res = await deleteFormDataBatch(query)
          if (res.code === 200) {
            this.$message.success(res.msg)
            this.handleGetFormDataList()
          } else {
            this.$message.error(res.msg)
          }

        })
    },

    /**
     * 点击导入按钮
     */
    handleImport() {
      this.queryParams.templateId = this.templateList[0].id
      this.uploadConfig.visible = true
    },
    /**
     * 文件上传中处理
     */
    handleFileUploadProgress() {
      this.importLoading = true
      this.uploadConfig.isUploading = true
    },
    /**
     * 下载导入模版
     * @param tableName
     */
    downloadImportTemplate(tableName) {
      if (this.queryParams.templateId == null) {
        this.$message.error('请先选择导入模板')
        return
      }
      const query = {
        formDesignId: this.formDesignDetail.id,
        templateId: this.queryParams.templateId
      }
      this.queryParams.formDesignId = this.formDesignDetail.id
      this.download(
        'codeDev/formData/downloadImportTemplate',
        query,
        `${tableName}_导入模版.xlsx`
      )
    },
    /**
     * 导出按钮
     * @param tableName
     */
    handleExportExcel(tableName) {
      const downloadRequstParams = {
        formDesignId: this.formDesignDetail.id,
        formJsonInfo: this.formDesignDetail.formJsonInfo,
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        searchFormParams: this.$refs.searchConfigFormRef.handleBuildRequestParams(),
        tableCode: this.formDesignDetail.tableCode,
        tableName
      }
      downloadFileData(downloadRequstParams).then((res) => {
        const resData = res.data
        if (resData == 'DownloadCenter') {
          this.$message.warning(res.msg)
        } else if (resData == 'Browser') {
          const currentTime = dayjs(`${new Date()}`).format('YYYY-MM-DD HH:mm')
          this.downloadFormData('codeDev/formData/exportFormDataDirectly', downloadRequstParams, `${tableName}${currentTime}.xlsx`)
        }
      })
    },
    /**
     * 导出 pdf
     */
    handleExportPdf() {
      const downloadRequstParams = {
        formDesignId: this.formDesignDetail.id,
        formJsonInfo: this.formDesignDetail.formJsonInfo,
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        searchFormParams: this.$refs.searchConfigFormRef.handleBuildRequestParams(),
        tableCode: this.formDesignDetail.tableCode,
        tableName: this.formDesignDetail.tableName
      }
      const currentTime = dayjs(`${new Date()}`).format('YYYY-MM-DD HH:mm')
      this.downloadFormData('codeDev/formData/exportPdf', downloadRequstParams, `${this.formDesignDetail.tableName}${currentTime}.pdf`)
    },
    /**
     * 文件上传成功处理
     * @param response
     */
    handleFileSuccess(response) {
      this.uploadConfig.visible = false
      this.importLoading = false
      this.uploadConfig.isUploading = false
      this.$refs.uploadRef.clearFiles()
      this.$alert(
        '<div style=\'overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;\'>' +
        response.msg.replace(/\n/g, '<br/>') +
        '</div>',
        '导入结果',
        { dangerouslyUseHTMLString: true }
      )
      this.handleGetFormDataList()
    },
    /**
     * 提交上传文件
     */
    submitFileForm() {
      if (!this.queryParams.templateId) {
        this.$message.error('请先选择模板')
        return
      }
      this.$refs.uploadRef.submit()
    },
    // 批量绑定关联关系
    async handleBindRelation(handleType, businessId, businessType, fileStatus) {
      const fileTypes = ['picture-upload', 'file-upload']
      if (handleType === "add") {
        const uploadResult = this.tableData.find(item => item._id === businessId)
        this.tableColumns.forEach(item => {
          if (fileTypes.includes(item.type)) {
            const uploadList = uploadResult[item.name]
            if (uploadList && uploadList.length > 0) {
              const bindingParams = uploadList.map(item => {
                const fileId = item.fileId
                if (fileId) {
                  const bindingParams = {
                    businessId,
                    businessType,
                    fileStatus,
                    fileId
                  }
                  return bindingParams
                }
              }).filter(_ => _)
              batchUpdateAttachment(bindingParams).then(res => {
                console.log("新建批量关联", res);
              })
            }
          }
        })
      } else {
        this.tableColumns.forEach(item => {
          if (fileTypes.includes(item.type)) {
            const newUploadList = this.formRenderOption.formData[item.name]
            const oldUploadList = this.formRenderOption.copyFormData[item.name]
            if (JSON.stringify(oldUploadList) !== JSON.stringify(newUploadList)) {
              if (oldUploadList && oldUploadList.length > 0) {
                // 取消关联
                const bindingParams = oldUploadList.map(item => {
                  const fileId = item.fileId
                  if (fileId) {
                    const bindingParams = {
                      businessId,
                      businessType,
                      fileStatus: "UNBOUND",
                      fileId
                    }
                    return bindingParams
                  }
                }).filter(_ => _)
                batchUpdateAttachment(bindingParams).then(res => {
                  console.log("编辑批量取消关联", res);
                })
              }
              if (newUploadList && newUploadList.length > 0) {
                const bindingParams = newUploadList.map(fileOption => {
                  const bindingParams = {
                    businessId,
                    businessType,
                    fileStatus: "BOUND",
                    fileId: fileOption.fileId
                  }
                  if ("response" in fileOption) {
                    bindingParams.fileId = fileOption.response.data.fileId
                  }
                  return bindingParams
                }).filter(_ => _)
                batchUpdateAttachment(bindingParams).then(res => {
                  console.log("编辑批量关联", res);
                })
              }
            }
          }
        })
      }
    },
    async saveFormData() {
      if (this.handleType == "view") {
        this.formRenderOption.formRenderShow = false
      } else {
        const formData = await this.$refs.vFormRef.getFormData()
        this.formRenderOption.loading = true
        const insertOrUpdateQuery = {
          tableCode: this.formDesignDetail.tableCode,
          columnAndDataStr: Object.assign({}, formData),
          mongoId: this.selectedMongoId,
          formDesignId: this.formDesignDetail.id
        }
        if (this.selectedMongoId) {
          // selectedMongoId 不等于空 走修改
          const res = await updateFormData(insertOrUpdateQuery)
          if (res.code === 200) {
            this.$message.success('操作成功')
            await this.handleGetFormDataList()
            const businessId = this.selectedMongoId
            const businessType = this.formDesignDetail.id
            this.formRenderOption.formData = JSON.parse(JSON.stringify(formData))
            this.handleBindRelation("edit", businessId, businessType)
            this.formRenderOption.loading = false
          }
        } else {
          // selectedMongoId 等于null 走新增
          const res = await insertFormData(insertOrUpdateQuery)
          if (res.code === 200) {
            this.$message.success('操作成功')
            await this.handleGetFormDataList()
            const businessId = res.data
            const businessType = this.formDesignDetail.id
            const fileStatus = "BOUND" // 关联状态
            this.handleBindRelation("add", businessId, businessType, fileStatus)
            this.formRenderOption.loading = false
          }
        }
        this.formRenderOption.formRenderShow = false
      }
    },
    // 保存并发起流程按钮
    async savaAndStart() {
      // 先保存数据然后拿到返回的 mongoID
      const formData = Object.assign({}, await this.$refs.vFormRef.getFormData())

      // 根据流程定义查询中间节点
      const res = await getNodesByDefCode(this.warmFlowCode)
        if (res.code === 200) {
          this.processNodeList = res.data.filter(item => item.nodeType === 1)
          await this.processingApproval(formData)
          this.flowNodeDialogVisible = true
        }

    },
    // 处理根据字段自动匹配审批人
    processingApproval(formData) {
      return new Promise((resolve, reject) => {
        const targetNodes = this.processNodeList.filter(
          item => item.approvalMode === '4' && item.approvalField
        )

        // 存储匹配的字段和值
        const matchedFields = {};
        // 遍历目标节点数组
        targetNodes.forEach(node => {
          // 获取当前节点的approvalField值（作为字段名）
          const fieldName = node.approvalField;

          // 检查formData中是否存在该字段
          if (formData.hasOwnProperty(fieldName)) {
            // 存在则存入结果对象
            matchedFields[fieldName] = formData[fieldName];

          }
        });

        // 存储提取结果的对象
        const result = {};
        // 用于匹配括号内内容的正则表达式
        const regex = /\(([^)]+)\)/;
        // 遍历原对象
        for (const key in matchedFields) {
          if (matchedFields.hasOwnProperty(key)) {
            const value = matchedFields[key];
            // 执行匹配
            const match = value.match(regex);
            // 如果匹配成功，提取括号内的内容
            if (match && match[1]) {
              result[key] = match[1];
            }
          }
        }
        getApproverInfo(result).then((res) => {
          console.log('审批人',res.data)
          this.processNodeList.forEach(node => {
            // 检查节点是否有approvalField
            if (node.approvalField) {
              // 以approvalField的值作为key，从res.data中获取对应值
              const key = node.approvalField;
              const value = res.data[key];

              // 如果找到对应值，按":"分割处理
              if (value) {
                const [id, name] = value.split(':');

                // 替换或初始化ids数组（确保是数组类型）
                node.ids = id ? [id] : [];

                // 替换或初始化username数组（确保是数组类型）
                node.userName = name ? [name] : [];
              } else {
                // 未找到对应值时清空数组
                node.ids = [];
                node.userName = [];
              }
            } else {
              // 没有approvalField时清空数组
              node.ids = [];
              node.userName = [];
            }
          });
          resolve();
        })

      })

    },
    // 确认发起
    async startFlow() {

      // 校验审批人
      if (!this.checkStartFlow()) {
        return; // 验证失败时终止后续操作
      }
      // 先保存数据然后拿到返回的 mongoID
      const formData = await this.$refs.vFormRef.getFormData()
      this.formRenderOption.loading = true
      const insertOrUpdateQuery = {
        tableCode: this.formDesignDetail.tableCode,
        columnAndDataStr: Object.assign({}, formData),
        mongoId: this.selectedMongoId,
        formDesignId: this.formDesignDetail.id
      }


      if (this.selectedMongoId) {
        // selectedMongoId 不等于空 走修改
        updateFormData(insertOrUpdateQuery).then(async (res) =>{
          if (res.code === 200) {
            this.formDataList[0] = Object.assign({}, this.formDataList[0], Object.assign({}, formData));
            const param = {
              dataIds: [this.formDataList[0]._id],
              data: this.formDataList,
              flowCode: this.warmFlowCode,
              tableCode: this.formDesignDetail.tableCode,
              formDesignId: this.formDesignDetail.id,
              copyUserIds: this.copyUserIds,
              columnInfoStr: this.formDesignDetail.columnInfoStr,
              nodePermissionFlag: this.processNodeList.map(item => {
                return {
                  id: item.ids?.join(",") || "", // 使用可选链操作符和空值合并运算符
                  code: item.nodeCode
                }
              })
            }
            flowStart(param).then((res) => {
              if (res.code === 200) {
                this.$message.success('操作成功')
              } else {
                this.$message.error(res.msg)
              }
            })
            this.flowNodeDialogVisible = false
            this.formRenderOption.formRenderShow = false
            this.formRenderOption.loading = false
            await this.handleGetFormDataList();
            const businessId = this.selectedMongoId
            const businessType = this.formDesignDetail.id
            this.formRenderOption.formData = JSON.parse(JSON.stringify(formData))
            this.handleBindRelation("edit", businessId, businessType)
          }
        })

      } else {
        // selectedMongoId 等于null 走新增
        insertFormData(insertOrUpdateQuery).then(async (res) => {
          if (res.code === 200) {
            const param = {
              dataIds: [res.data],
              data: [Object.assign({}, formData, { _id: res.data })],
              flowCode: this.warmFlowCode,
              tableCode: this.formDesignDetail.tableCode,
              formDesignId: this.formDesignDetail.id,
              copyUserIds: this.copyUserIds,
              columnInfoStr: this.formDesignDetail.columnInfoStr,
              nodePermissionFlag: this.processNodeList.map(item => {
                return {
                  id: item.ids?.join(",") || "", // 使用可选链操作符和空值合并运算符
                  code: item.nodeCode
                }
              })
            }
            flowStart(param).then((res) => {
              if (res.code === 200) {
                this.$message.success('操作成功')
              } else {
                this.$message.error(res.msg)
              }
            })
            this.flowNodeDialogVisible = false
            this.formRenderOption.formRenderShow = false
            this.formRenderOption.loading = false
            await this.handleGetFormDataList();
            const businessId = res.data
            const businessType = this.formDesignDetail.id
            const fileStatus = "BOUND" // 关联状态
            this.handleBindRelation("add", businessId, businessType, fileStatus)
          }
        })
      }

    },
    handleDeleteSelectedUser(index, userIndex) {
      this.processNodeList[index].userName.splice(userIndex, 1)
      this.processNodeList[index].ids.splice(userIndex, 1)
      this.processNodeList = JSON.parse(JSON.stringify(this.processNodeList))
    },
    handleDeleteSelectedCopyUser(userIndex) {
      this.copyUserIds.splice(userIndex, 1)
      this.copyUserNames.splice(userIndex, 1)
    },
    warmFlowFire() {
      // 校验审批人
      if (!this.checkStartFlow()) {
        return; // 验证失败时终止后续操作
      }

      // 发起流程 进入待办
      const param = {
        dataIds: this.selectedTabledata.map((item) => item._id),
        data: this.selectedTabledata,
        flowCode: this.warmFlowCode,
        tableCode: this.formDesignDetail.tableCode,
        formDesignId: this.formDesignDetail.id,
        copyUserIds: this.copyUserIds,
        columnInfoStr: this.formDesignDetail.columnInfoStr,
        nodePermissionFlag: this.processNodeList.map(item => {
          return {
            id: item.ids?.join(",") || "",
            code: item.nodeCode
          }
        })
      }
      this.flowStartApi(param)
      this.flowRenderShow = false
    },
    // 发起流程接口
    flowStartApi(params) {
      flowStart(params).then((res) => {
        if (res.code === 200) {
          this.$message.success('操作成功')
          this.handleGetFormDataList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    /**
     * 获取列表数据
     */
    async handleGetFormDataList() {
      this.tableLoading = true
      // this.queryParams.formJsonInfo = this.formDesignDetail.formJsonInfo
      this.queryParams.tableName = this.formDesignDetail.tableName
      this.queryParams.tableCode = this.formDesignDetail.tableCode
      this.queryParams.formDesignId = this.formDesignDetail.id
      this.queryParams.searchFormParams = this.$refs.searchConfigFormRef.handleBuildRequestParams()
      this.queryParams.sortBys = await this.$refs.fullHeightTableRef.handleBuildSortParams();
      const res = await getFormDataList(this.queryParams).finally(() => { this.tableLoading = false })
      if (res.code === 200) {
        this.tableData = res.data.dataList
        this.originTableData = res.data.dataList
        this.total = res.data.total
      } else {
        this.$message.error('查询失败')
      }
    },
    /**
     * 新增表单
     */
    handleAddFormData() {
      this.selectedMongoId = null
      this.selectedRowId = null
      this.formRenderButtonName = "保存"
      this.handleType = "add"
      this.formRenderOption.formJson = JSON.parse(this.formDesignDetail.formJsonInfo)
      this.formRenderOption.formData = {}
      this.formRenderOption.formRenderShow = true
    },
    handleFileUrl(formData) {
      const fileTypes = ['picture-upload'] // 'file-upload'
      this.tableColumns.forEach(item => {
        if (fileTypes.includes(item.type)) {
          const fileList = formData[item.name]
          if (fileList && fileList.length) {
            fileList.forEach(i => {
              i.url = `${window.location.origin}${"/opsyndex-file"}${i.relativePath}`
            })
          }
        }
      })
      return formData
    },
    /**
     * 查看表单
     * @param row
     */
    handleViewFormData(row) {
      this.handleType = "view"
      this.formRenderButtonName = "关闭"
      this.formRenderOption.formJson = JSON.parse(this.formDesignDetail.formJsonInfo)
      this.formRenderOption.formData = this.handleFileUrl(JSON.parse(JSON.stringify(row)))
      this.formRenderOption.formRenderShow = true
      this.$nextTick(() => {
        this.$refs.vFormRef.disableForm()
      })
    },
    /**
     * 编辑表单
     * @param row
     */
    handleUpdateFormData(row) {
      let rowList = []
      rowList.push(row)
      this.formDataList = rowList
      this.selectedMongoId = row._id
      this.selectedRowId = row._id
      this.handleType = "edit"
      this.formRenderButtonName = "保存"
      this.formRenderOption.formJson = JSON.parse(this.formDesignDetail.formJsonInfo)
      this.formRenderOption.formData = this.handleFileUrl(JSON.parse(JSON.stringify(row)))
      this.formRenderOption.copyFormData = this.handleFileUrl(JSON.parse(JSON.stringify(row)))
      this.formRenderOption.formRenderShow = true
    },
    /**
     * 审批查看
     * @param row
     */
    async handleViewApproval(row) {
      const result = await getInstanceIds(row._id)
      if (result.code == 200) {
        const instanceIds = result.data
        // const params = { disabled: false, pageNum: 1 };
        // this.$tab.openPage(`${row.tableName}（流程历史记录）`, `/done/doneList/index/${instanceIds}`, params);
        this.$router.push({
          path: `/done/doneList/index/${instanceIds}`,
          query: {
            tableCode: this.formDesignDetail.tableCode,
            formDesignId: this.formDesignDetail.id,
            tableName: this.formDesignDetail.tableName
          }
        })
        // setTimeout(() => {
        //   const routeOption = {
        //     path: `/done/doneList/index/${instanceIds}`,
        //     fullPath: `/done/doneList/index/${instanceIds}`,
        //     meta: {
        //       title: `${row.tableName}（流程历史记录）`
        //     }
        //   }
        //   this.$store.dispatch('tagsView/addView', routeOption)
        // })
      }
    },
    /**
     * 复制表单
     * @param row
     */
    handleCopyFormData(row) {
      this.selectedMongoId = null
      this.selectedRowId = row._id
      this.formRenderButtonName = "复制"
      this.handleType = "copy"
      this.formRenderOption.formJson = JSON.parse(this.formDesignDetail.formJsonInfo)
      // this.formRenderOption.formData = JSON.parse(JSON.stringify(row))
      this.formRenderOption.formData = this.handleFileUrl(JSON.parse(JSON.stringify(row)))
      // this.formRenderOption.copyFormData = this.handleFileUrl(JSON.parse(JSON.stringify(row)))
      this.formRenderOption.formRenderShow = true
    },
    /**
     * 打开用户选择弹窗
     * @param processNode
     */
    initUser(processNode) {
      // 发起流程 反显用
      this.clickItem = processNode
      processNode.ids = processNode.ids ? processNode.ids : []
      this.selectUserDialogVisible = true
      if (!processNode.ids || !processNode.ids.length) {
        this.$nextTick(() => {
          this.$refs.selectUserRef.$refs.tableRef.clearSelection()
        })
      }
      this.userIds = processNode.ids.join(",")
    },
    initCopyUser() {
      this.userCopyIds = this.copyUserIds.toString()
      this.selectCopyUserDialogVisible = true
    },
    /**
     * 获取选中用户数据
     * @param checkedItemList
     */
    handleUserSelect(checkedItemList) {

      // this.permissionFlag = checkedItemList.map(e => {
      //   return e.userId
      // }).join(',')

      const userName = checkedItemList.map(e => {
        return e.nickName
      })//.join(',')

      const ids = checkedItemList.map(e => {
        return e.userId
      })//.join(',')

      this.processNodeList.forEach(item => {
        if (item.id === this.clickItem.id) {
          item.userName = userName
          item.ids = ids
        }
      })
    },
    handleCopyUserSelect(checkedItemList) {
      const userName = checkedItemList.map(e => {
        return e.nickName
      })//.join(',')

      const ids = checkedItemList.map(e => {
        return e.userId
      })//.join(',')

      this.copyUserIds = ids
      this.copyUserNames = userName
    },
    hasSelectUserButton(processNode, index) {
      if (processNode.approvalMode) {
        if (processNode.approvalMode === '1') {
          return true;
        } else return processNode.approvalMode === '3' && index === 0;
      } else {
        return false;
      }
    },
    // 审批导出
    async exportApproval() {
      // if (this.selectedMongoIds.length === 0) {
      //   this.$message.warning('请选择数据')
      //   return
      // }
      // if (this.selectedMongoIds.length >  1000) {
      //   this.$message.warning('导出数据不能超过1000条')
      //   return
      // }
      console.log('zsq', this.selectedMongoIds)
      let downloadLoadingInstance = Loading.service({ text: "正在下载数据，请稍候", spinner: "el-icon-loading", background: "rgba(0, 0, 0, 0.7)", })
      const exportFormDataVo = {
        bizFormData: {
          formDesignId: this.formDesignDetail.id,
          tableCode: this.formDesignDetail.tableCode,
          searchFormParams: this.$refs.searchConfigFormRef.handleBuildRequestParams(),
        },
        dataIds: this.selectedMongoIds,
      }
      const tableName = this.formDesignDetail.tableName
      // this.download('/codeDev/formData/exportFormDataApproval', exportFormDataVo, `${tableName}${Date.now()}.xlsx`)
      const result = await exportFormDataApproval(exportFormDataVo)
      const blob = new Blob([result])
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      const currentTime = dayjs(`${new Date()}`).format('YYYY-MM-DD HH:mm')
      a.download = `${tableName}${currentTime}.xlsx`
      a.click()
      URL.revokeObjectURL(url)
      downloadLoadingInstance.close();
    },
  }
}
</script>
<style lang="scss" scoped>
.app-container {
  position: relative;

  .person-class {
    height: 40px;
    border: 1px solid #ccc;
    text-align: center;
    line-height: 40px;
    margin-top: 5px;
    width: 150px;
    overflow: hidden;
    /* 横向超出隐藏 */
    white-space: nowrap;
    /* 防止文本换行 */
  }

  .arrow-right {
    position: absolute;
    left: 85%;
    top: 50%;
    display: inline-block;
    width: 0;
    height: 0;
    border-right: 10px solid transparent;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-left: 10px solid black;
  }

  .arrow-right::after {
    content: '';
    position: absolute;
    top: -10px;
    left: 10px;
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid white;
  }
}
</style>
