<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryFormRef" size="small" inline v-show="showSearch" label-width="68px">
      <el-form-item label="分类" prop="classify">
        <el-select v-model="queryParams.classify" placeholder="分类" class="filter-item" clearable>
          <el-option v-for="item in categoryList" :key="item.dictLabel" :label="item.dictLabel"
            :value="item.dictLabel" />
        </el-select>
      </el-form-item>
      <el-form-item label="表名" prop="tableName">
        <el-input v-model="queryParams.tableName" placeholder="请输入表名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="表编码" prop="tableCode">
        <el-input v-model="queryParams.tableCode" placeholder="请输入表编码" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['codeDev:formDesign:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['codeDev:formDesign:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['codeDev:formDesign:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['codeDev:formDesign:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table ref="fullHeightTableRef" :height="tableHeight" stripe border v-loading="loading" :data="formDesignList"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="排序" align="center" prop="sort" width="60px" />
      <el-table-column label="分类" align="center" prop="classify" width="100px" />
      <el-table-column label="表名" align="center" prop="tableName" />
      <el-table-column label="表编码" align="center" prop="tableCode" />
      <el-table-column label="创建时间" width="160px" align="center" prop="createTime" />
      <el-table-column label="操作" align="center" width="330px" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="gotoFormData(scope.row)">表单数据</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit"
            @click="gotoFormDesignDetail(scope.row)">设计表单</el-button>
          <!-- <el-button size="mini" type="text" icon="el-icon-open" @click="columnConfig(scope.row)">列表配置</el-button> -->
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleDataScope(scope.row)">数据权限</el-button>
          <el-button size="mini" type="text" icon="el-icon-receiving" @click="importConfig(scope.row)">导入配置</el-button>
          <el-button size="mini" type="text" icon="el-icon-files" @click="exportConfig(scope.row)">导出配置</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['codeDev:formDesign:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['codeDev:formDesign:remove']">删除</el-button>
          <el-button type="text" icon="el-icon-plus" size="mini" @click="handleCreateMenu(scope.row)"
            v-hasPermi="['system:menu:add']">创建菜单</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div style="text-align: right;">
      <el-pagination v-show="total > 0" @size-change="handlePageSizeChange" @current-change="handlePageNumChange"
        :current-page="queryParams.pageNum" :page-sizes="[10, 20, 30, 50, 100, 1000]" :page-size="queryParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>
    </div>
    <!-- 添加或修改信息对话框-->
    <el-dialog v-loading="editLoading" :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="分类" prop="classify">
          <el-select v-model="form.classify" placeholder="分类" class="filter-item" filterable clearable>
            <el-option v-for="item in categoryList" :key="item.dictLabel" :label="item.dictLabel"
              :value="item.dictLabel" />
          </el-select>
        </el-form-item>
        <el-form-item label="表名" prop="tableName">
          <el-input v-model="form.tableName" placeholder="请输入表名" />
        </el-form-item>
        <el-form-item label="表编码" prop="tableCode">
          <el-input v-model="form.tableCode" placeholder="请输入表编码" />
        </el-form-item>
        <el-form-item label="无效可查看" prop="isInvalidView">
          <el-select v-model="form.isInvalidView" placeholder="无效可查看">
            <el-option
              v-for="item in isInvalidViewList"
              :key="item.key"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审批流" prop="warmFlow">
          <el-select v-model="form.warmFlow" placeholder="审批流" class="filter-item" clearable filterable>
            <el-option v-for="item in warmFlowList" :key="item.flowCode" :label="item.flowName"
              :value="item.flowCode" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" placeholder="请输入排序" />
        </el-form-item>
        <el-table :data="form.publicFieldConfigList" style="width: 100%">
          <el-table-column label="公共字段名称" align="center" prop="fieldName" />
          <el-table-column label="公共字段编码" align="center" prop="fieldCode" />
          <el-table-column label="是否可配" align="center" prop="status">
            <template slot-scope="scope">
              <el-select v-model="scope.row.status" clearable>
                <el-option v-for="dict in dict.type.field_display_or_hide" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!--    表单设计的模态框-->
    <el-dialog v-loading="vFormLoading" title="表单设计" @open="onOpen" :visible.sync="formDesignerShow" width="100%"
      class="fullscreen-dialog">
      <v-form-designer v-if="formDesignerShow" ref="vfDesigner" :banned-widgets="testBanned" :form-json="formJson"
        :form-data="formData" :option-data="optionData" :designer-config="designerConfig">
      </v-form-designer>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirSaveFormJson"> 保存 </el-button>
      </div>
    </el-dialog>

    <el-dialog title="导出配置" :visible.sync="exportConfigDialogShow" width="100%" class="fullscreen-dialog">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-download" size="mini"
            @click="exportConfigHandleImport">导入</el-button>
        </el-col>

        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-download" size="mini"
            @click="exportConfigHandleExport">导出</el-button>
        </el-col>

        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-refresh" size="mini"
            @click="getFieldsListSort">应用列表配置顺序</el-button>
        </el-col>
      </el-row>

      <el-table class="draggable-table" v-loading="exportConfigLoading" row-key="id" :data="formExportConfigList">
        <el-table-column label="唯一名称" align="center" prop="onlyName"></el-table-column>
        <el-table-column label="标签" align="center" prop="label"></el-table-column>
        <el-table-column label="导出的字段名" align="center" prop="exportName">
          <template slot-scope="scope">
            <el-input v-model="scope.row.exportName"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="是否导出" align="center" prop="isExport">
          <template slot-scope="scope">
            <el-select v-model="scope.row.isExport" clearable>
              <el-option v-for="dict in dict.type.is_export" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="导出顺序" align="center" prop="sort">
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirExportConfig"> 保存 </el-button>
      </div>
    </el-dialog>

    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px">
      <dev v-loading="importLoading">
        <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
          :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            将文件拖到此处，或
            <em>点击上传</em>
          </div>
          <div class="el-upload__tip" style="color: red" slot="tip">
            提示：仅允许导入“xls”或“xlsx”格式文件！
          </div>
        </el-upload>
      </dev>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :key="importDialogKey" title="导入配置" :append-to-body="true" :visible.sync="importConfigTemplateDialogShow"
      width="95%" class="fullscreen-dialog">
      <FormImportTemplate refs="formImportTemplate" :formDesignId="formDesignId"></FormImportTemplate>
    </el-dialog>
    <!-- <FormDesignFieldConfig refs="formImportTemplate" :formDesignId="formDesignId"
      :formDesignFieldConfigDialogShow.sync="formDesignFieldConfigDialogShow" v-if="formDesignFieldConfigDialogShow">
    </FormDesignFieldConfig> -->
    <!-- 数据权限 -->
    <FormPermissionConfig :formDesignId="formDesignId" :permissionConfigDialogShow.sync="permissionConfigDialogShow"
      :formDesignData="formDesignData" v-if="permissionConfigDialogShow" />
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import {
  listFormDesign,
  getFormDesign,
  delFormDesign,
  addFormDesign,
  updateFormDesign,
  listWarmFlow,
} from "@/api/codeDev/formDesign/formDesign";
import {
  listFormExportConfig,
  updateFormExportConfigBatch,
  getFieldSortList,
} from "@/api/codeDev/formExportConfig/formExportConfig";
import { deleteMongodbCollection } from "@/api/codeDev/formData/formData";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import FormImportTemplate from "@/views/codeDev/formImportTemplate/index.vue";
import Sortable from "sortablejs";
// import FormDesignFieldConfig from "@/views/codeDev/formDesignFieldConfig/index.vue";
import FormPermissionConfig from "@/views/codeDev/formPermissionConfig/index.vue";
import { getDicts as getCategoryDictData } from "@/api/system/dict/data";
import { getToken } from "@/utils/auth";
import { encodeSqlToEncryptedBase64 } from "@/utils/sqlHandle"
export default {
  mixins: [tableFullHeight],
  name: "FormDesign",
  dicts: ["has_approve_button", "is_export", "field_display_or_hide"],
  components: {
    FormImportTemplate,
    // FormDesignFieldConfig,
    FormPermissionConfig,
  },
  data() {
    return {
      fieldSortMap: {},
      importDialogKey: 0,
      // fieldConfigKey: 0,
      importTemplateQueryParams: {},
      formDesignId: null,
      formDesignData: null,
      formExportConfigList: [],
      formImportTemplateList: [],
      exportConfigDialogShow: false,
      importConfigTemplateDialogShow: false,
      permissionConfigDialogShow: false,
      // formDesignFieldConfigDialogShow: false,
      addImportTemplateShow: false,
      formDesignerShow: false,
      tableColumns: [],
      testBanned: [], //设计器左侧组件库中组价类型的显示
      formJson: "",
      formData: [],
      optionData: [],
      designerConfig: {
        //是否显示语言切换菜单
        languageMenu: false,
        //是否显示GitHub、文档等外部链接
        externalLink: false,
      },
      selectedRow: null,
      // 导入遮罩层
      importLoading: false,
      // 遮罩层
      exportConfigLoading: false,
      importConfigTemplateLoading: false,
      loading: true,
      editLoading: false,
      vFormLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表单设计表格数据
      formDesignList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tableName: null,
        tableCode: null,
        formJsonInfo: null,
        classify: null,
      },
      // 表单参数
      form: {
        publicColumnShowInfo: [],
      },
      publicColumnDictList: [],
      importTemplateForm: {},
      // 表单校验
      rules: {
        tableName: [
          { required: true, message: "表名不能为空", trigger: "blur" },
        ],
        tableCode: [
          { required: true, message: "表编码不能为空", trigger: "blur" },
          {
            validator: (_rule, value, callback) => {
              if (value.length >= 50) {
                callback(new Error("表编码长度不能超过50"));
              }
              callback();
            }, trigger: "blur"
          },
        ],
        classify: [
          { required: true, message: "分类不能为空", trigger: "blur" },
        ],
      },
      categoryList: [],
      warmFlowList: [],
      // 导入参数
      upload: {
        // 是否显示弹出层（导入）
        open: false,
        // 弹出层标题（导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url:
          process.env.VUE_APP_BASE_API + "codeDev/formExportConfig/importData",
      },
      isInvalidViewList: [
        { key: 1, value: 1, label: '是' },
        { key: 0, value: 0, label: '否' }
      ]
    };
  },

  created() {
    this.getList();
    this.getDicts("public_column_list").then((response) => {
      if (response.code === 200) {
        this.publicColumnDictList = response.data;
      }
    });
    this.getCategoryDict();
    this.getWarmFlows();
  },

  methods: {
    handlePageSizeChange(val) {
      this.queryParams.pageSize = val
      this.getList()
    },
    handlePageNumChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    },
    // columnConfig(row) {
    //   this.formDesignId = row.id;
    //   this.formDesignFieldConfigDialogShow = true;
    // },
    //点击 导入配置 按钮
    importConfig(row) {
      this.formDesignId = row.id;
      this.importConfigTemplateDialogShow = true;
      this.importDialogKey++; // 更新key以强制重新渲染
    },
    // 表格行拖拽
    rowDrag() {
      const el = document.querySelectorAll(
        ".draggable-table .el-table__body-wrapper > table > tbody"
      )[0];
      Sortable.create(el, {
        disabled: false, // 拖拽是否可用
        ghostClass: "sortable-ghost", //拖拽样式
        animation: 150, // 拖拽延时
        onEnd: (e) => {
          const formExportConfigList = this.formExportConfigList.slice();
          const movedItem = formExportConfigList.splice(e.oldIndex, 1)[0];
          formExportConfigList.splice(e.newIndex, 0, movedItem);
          // // 更新原数组
          this.formExportConfigList = formExportConfigList.slice();
        },
      });
    },
    //点击 导出配置 按钮
    exportConfig(row) {
      this.exportConfigDialogShow = true;
      this.exportConfigLoading = true;
      const queryParam = {
        formDesignId: row.id,
        pageSize: 1000,
        pageNum: 1,
      };
      this.formDesignId = row.id;
      listFormExportConfig(queryParam).then((response) => {
        this.formExportConfigList = response.rows;
        this.exportConfigLoading = false;
      });
      this.$nextTick(() => {
        this.rowDrag();
      });
    },
    //点击 数据权限 按钮
    handleDataScope(row) {
      this.formDesignId = row.id;
      this.formDesignData = row;
      this.permissionConfigDialogShow = true;
    },

    //导出配置的保存按钮
    confirExportConfig() {
      this.$confirm("确定要保存此列表的导出配置信息？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.saveFormExportConfig();
        })
        .catch(() => { });
    },
    saveFormExportConfig() {
      const formExportConfigList = this.formExportConfigList.map(
        (item, index) => {
          return {
            ...item,
            sort: index + 1,
          };
        }
      );
      updateFormExportConfigBatch(formExportConfigList).then((res) => {
        if (res.code === 200) {
          this.$message.success("操作成功");
          this.exportConfigDialogShow = false;
        }
      });
    },
    // 表单设计的模态框 打开时调用
    onOpen() {
      this.$nextTick(() => {
        if (this.$refs && this.$refs.vfDesigner) {
          this.$refs.vfDesigner.clearDesigner();
          //打开模态框时 将formJson的值 通过设计器的setFormJson渲染出来
          this.$refs.vfDesigner.setFormJson(this.formJson);
        }
      });
    },
    //保存前 提示
    confirSaveFormJson() {
      this.$confirm("确定要保存此表单信息？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.vFormLoading = true;
          this.saveFormJson();
        })
        .catch(() => {
          this.formDesignerShow = false;
        });
    },
    // 保存表单json
    saveFormJson() {
      const formJson = this.$refs.vfDesigner.getFormJson();
      const formJsonStr = JSON.stringify(formJson);
      this.selectedRow.formJsonInfo = encodeSqlToEncryptedBase64(formJsonStr);
      updateFormDesign(this.selectedRow).then((res) => {
        if (res.code === 200) {
          this.$modal.msgSuccess("操作成功");
        } else {
          this.$modal.msgSuccess(res.msg);
        }
        this.formJson = "";
        this.getList();
        this.formDesignerShow = false;
        this.vFormLoading = false;
      });
    },
    /**
     * 点击设计表单
     */
    gotoFormDesignDetail(row) {
      const path = "/codeDev/formDesignDetail"
      this.$router.push({
        path,
        query: {
          id: row.id
        }
      })
      setTimeout(() => {
        const routeOption = {
          path: `${path}?id=${row.id}`,
          fullPath: `${path}?id=${row.id}`,
          meta: {
            title: `${row.tableName}（表单设计）`
          }
        }
        this.$store.dispatch('tagsView/addView', routeOption)
      })
    },
    /**
     * 点击表单数据按钮
     */
    gotoFormData(row) {
      this.$router.push({
        path: "/codeDev/formData",
        query: {
          tableCode: row.tableCode
        }
      })
      setTimeout(() => {
        const routeOption = {
          path: `/codeDev/formData?tableCode=${row.tableCode}`,
          fullPath: `/codeDev/formData?tableCode=${row.tableCode}`,
          meta: {
            title: `${row.tableName}（表单数据）`
          }
        }
        this.$store.dispatch('tagsView/addView', routeOption)
      })
    },
    /**
     * 查询表单设计列表
     */
    getList() {
      this.loading = true;
      listFormDesign(this.queryParams).then((response) => {
        this.formDesignList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    // 获取分类字典对应数据
    getCategoryDict() {
      getCategoryDictData("form_category").then((res) => {
        this.categoryList = res.data;
      });
    },
    //获取流程定义
    getWarmFlows() {
      listWarmFlow().then((res) => {
        this.warmFlowList = res.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        tableName: null,
        tableCode: null,
        formJsonInfo: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        delFlag: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryFormRef");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      // 创建一个新数组，用于存储转换后的对象
      this.form.publicFieldConfigList = this.publicColumnDictList.map(
        (item) => ({
          fieldName: item.dictLabel,
          fieldCode: item.dictValue,
          status: "1", // 新增的value属性，默认值为1
        })
      );
      this.title = "添加表单设计";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getFormDesign(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改表单设计";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.editLoading = true;
          if (this.form.id != null) {
            // formJsonInfo 加密
            if (this.form.formJsonInfo) {
              this.form.formJsonInfo = encodeSqlToEncryptedBase64(this.form.formJsonInfo)
            }
            updateFormDesign(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.editLoading = false;
            });
          } else {
            addFormDesign(this.form).then((response) => {
              if (response.code === 200) {
                this.$modal.msgSuccess("新增成功");
              } else {
                this.$modal.msgSuccess(response.msg);
              }
              this.open = false;
              this.getList();
            }).finally(() => {
              this.editLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除表单设计编号为"' + ids + '"的数据项？')
        .then(function () {
          delFormDesign(ids);
          const delParams = {
            tableCode: row.tableCode,
          };
          return deleteMongodbCollection(delParams); // 确保这里返回Promise，以便链式调用
        })
        .then(() => {
          this.$modal.msgSuccess("删除成功");
        })
        .catch((error) => {
          // 处理可能的错误，例如显示错误信息
          // this.$modal.msgError("删除失败：" + error.message);
        })
        .finally(() => {
          // 无论成功或失败，都执行这里的代码
          this.getList();
        });
    },
    handleCreateMenu(row) {
      this.$router.push({
        path: "/System/menu",
        query: {
          tableCode: row.tableCode,
          tableName: row.tableName
        }
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "codeDev/formDesign/export",
        {
          ...this.queryParams,
        },
        `formDesign_${new Date().getTime()}.xlsx`
      );
    },

    /** 导出配置导出按钮 */
    exportConfigHandleExport() {
      const queryParam = {
        formDesignId: this.formDesignId,
        pageSize: 1000,
        pageNum: 1,
      };
      this.download(
        "codeDev/formExportConfig/export",
        {
          ...queryParam,
        },
        `formDesign_${new Date().getTime()}.xlsx`
      );
    },

    /** 导出配置导入按钮 */
    exportConfigHandleImport() {
      this.upload.title = "配置导入";
      this.upload.open = true;
    },
    /** 应用列表配置顺序按钮 */
    getFieldsListSort() {
      getFieldSortList(this.formDesignId).then((res) => {
        this.fieldSortMap = res.data;
        this.formExportConfigList.forEach((e) => {
          if (this.fieldSortMap[e.onlyName] != undefined) {
            e.sort = this.fieldSortMap[e.onlyName];
          }
        });
        this.formExportConfigList.sort((a, b) => {
          return a.sort - b.sort;
        });
      });
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download(
        "codeDev/formExportConfig/importTemplate",
        {
          ...this.queryParams,
        },
        `导出配置-模板.xlsx`
      );
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.importLoading = true;
      this.upload.isUploading = true;
      this.exportConfigLoading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.importLoading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果123", {
        dangerouslyUseHTMLString: true,
      });
      const queryParam = {
        formDesignId: this.formDesignId,
        pageSize: 1000,
        pageNum: 1,
      };
      listFormExportConfig(queryParam).then((response) => {
        this.formExportConfigList = response.rows;
        this.exportConfigLoading = false;
      });
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
  },
};
</script>
