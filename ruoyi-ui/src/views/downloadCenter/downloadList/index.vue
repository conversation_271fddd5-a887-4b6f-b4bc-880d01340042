<template>
  <div class="app-container">
    <div ref="queryFormRef" class="filter-container">
      <el-input
        v-model="listQuery.fileName"
        clearable
        placeholder="任务名称"
        style="width: 200px;"
        class="filter-item"
      />
      <el-select v-model="listQuery.status" placeholder="状态"  class="filter-item" clearable>
        <el-option
          v-for="item in download_status"
          :key="item.dictCode"
          :label="item.dictLabel"
          :value="item.dictValue"
        />
      </el-select>

      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="fetchData">
        搜索
      </el-button>
    </div>
    <el-table
      ref="fullHeightTableRef"
      :height="tableHeight"
      v-loading="listLoading"
      :data="formDataList"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="任务名称" width="300px">
        <template slot-scope="scope">{{ scope.row.taskName }}</template>
      </el-table-column>
      <el-table-column align="center" label="文件名称" width="300px">
        <template slot-scope="scope">{{ scope.row.fileName }}</template>
      </el-table-column>
<!--      <el-table-column label="文件大小(byte)" align="center" width="150px">-->
<!--        <template slot-scope="scope">{{ scope.row.fileSize }}-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="下载日期" align="center">
        <template slot-scope="scope">{{ scope.row.downloadTime }}</template>
      </el-table-column>
      <el-table-column label="下载人" align="center">
        <template slot-scope="scope">{{ scope.row.downloadUser }}</template>
      </el-table-column>
      <el-table-column label="内容查询条件" width="600" align="center">
        <template slot-scope="scope">{{ scope.row.downloadCondition }}</template>
      </el-table-column>
      <el-table-column label="下载状态" width="100" align="center">
        <template slot-scope="scope">
          <span :class="getStatusClass(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="文件状态" width="100" align="center">
        <template slot-scope="scope">
          <span :class="getStatusClassForFile(scope.row.taskValid)">
            {{ getStatusTextForFile(scope.row.taskValid) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="任务耗时(秒)" align="center">
        <template slot-scope="scope">{{ scope.row.taskConsume }}</template>
      </el-table-column>
      <el-table-column label="存储文件" width="200" align="center">
        <template slot-scope="scope">{{ scope.row.actualFileName }}</template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200px">
        <template slot-scope="{row}">
          <el-button v-if="row.status == '3' && row.taskValid == '0'" size="mini" type="info" style="width: 60px!important;" @click="downloadTaskFile(row.actualFileName, row.fileName)">
            <a>下载</a>
          </el-button>
          <el-button v-if="row.status!=='deleted'" size="mini" type="danger" style="width: 60px!important;" @click="confirmDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNum"
      :limit.sync="listQuery.pageSize"
      @pagination="fetchData"
    />
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import * as downloadApi from '@/api/downloadCenter/downloadList'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import { getDicts as getDictData } from '@/api/system/dict/data'
import { getDownloadDataById } from '@/api/downloadCenter/downloadList'

export default {
  mixins: [tableFullHeight],
  name: 'JobProject',
  components: { Pagination },
  directives: { waves },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'gray',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      baseURL: window.location.protocol + "//" + window.location.host + '/mn_file/',
      download_status: [],
      file_status: [],
      workflow_status: [],
      diffStatusDict: [],
      downloadFile: '',
      list: null,
      listLoading: true,
      total: 0,
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        fileName: '',
        status: ''
      },
      formDataList: [],
      dialogPluginVisible: false,
      pluginData: [],
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: 'Edit',
        create: 'Create',
        clear: 'Clear'
      },
      rules: {
      },
      temp: {
        id: undefined,
        name: '',
        description: ''
      },
      visible: true
    }
  },
  created() {
    this.getDictDetailInfo()
    this.getDictDetailInfoForFile()
    this.fetchData()
  },
  methods: {
    getStatusClass(status) {
      const found = this.download_status.filter(item => item.dictValue == status)
      const executeResult = found[0]
      switch (executeResult.dictValue) {
        case '1':
          return 'todo-class'
        case '2':
          return 'doing-class'
        case '3':
          return 'done-class'
        case '4':
          return 'fail-class'
      }
      return ""
    },
    getStatusClassForFile(status) {
      const found = this.file_status.filter(item => item.dictValue == status)
      const executeResult = found[0]
      switch (executeResult.dictValue) {
        case '0':
          return 'effective-class'
        case '1':
          return 'lose-effective-class'
      }
    },
    getStatusText(status) {
      const found = this.download_status.filter(item => item.dictValue == status)
      const executeResult = found[0]
      return executeResult.dictLabel
    },
    getStatusTextForFile(status) {
      const found = this.file_status.filter(item => item.dictValue == status)
      const executeResult = found[0]
      return executeResult.dictLabel
    },
    getDictDetailInfo() {
      getDictData("download_status").then(res => {
        this.download_status = res.data
      })
    },
    getDictDetailInfoForFile() {
      getDictData("file_effective").then(res => {
        this.file_status = res.data
      })
    },
    fetchData() {
      this.listLoading = true
      downloadApi.list(this.listQuery).then(response => {
        this.formDataList = response.rows
        this.total = response.total
        this.listLoading = false
      })
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        name: '',
        description: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          downloadApi.created(this.temp).then(() => {
            this.fetchData()
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Created Successfully',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row) // copy obj
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          downloadApi.updated(tempData).then(() => {
            this.fetchData()
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    async downloadTaskFile(actualFileName, aliasName) {
      try {
        // const downloadUrl = 'http://192.168.0.9:8765/mn_file/' + actualFileName
        const downloadUrl = this.baseURL + actualFileName
        // 创建一个<a>标签并设置下载链接
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = aliasName
        // 触发下载
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } catch (error) {
        console.error('下载文件失败:', error);
      }
    },
    //删除按钮提示模态框
    confirmDelete(row) {
      this.$confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 用户点击确定按钮时执行删除操作
        this.handleDelete(row);
        //   handleDelete 这个是原来的方法要设置
      }).catch(() => {
        // 用户点击取消按钮时的操作，可以不做任何处理或关闭模态框
      });
    },
    handleDelete(row) {
      const idList = []
      idList.push(row.id)
      downloadApi.deleted({ idList: row.id }).then(response => {
        this.fetchData()
        this.$notify({
          title: 'Success',
          message: 'Delete Successfully',
          type: 'success',
          duration: 2000
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-popover-content {
  max-height: 300px;
  max-width: 600px;
  overflow-y: auto;
  color: #1459ad;
}
.todo-class {
  color: grey;
}
.doing-class {
  color: blue;
}
.done-class {
  color: green;
}
.fail-class {
  color: red;
}
.effective-class {
  color: green;
}
.lose-effective-class {
  color: red;
}
</style>
