<template>
  <div class="login" :style="{ backgroundImage: `url(${backgroundImage})` }">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
<!--      <h3 class="title">常温供应链管理中心运营指挥平台</h3>-->
      <h3 class="title">{{ loginPageName }}</h3>
      <el-form-item prop="username">
        <el-input v-model="loginForm.username" type="text" auto-complete="off" placeholder="账号">
          <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input v-model="loginForm.password" type="password" auto-complete="off" placeholder="密码"
          @keyup.enter.native="handleLogin">
          <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="code" v-if="captchaEnabled">
        <el-input v-model="loginForm.code" auto-complete="off" placeholder="验证码" style="width: 63%"
          @keyup.enter.native="handleLogin">
          <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" class="login-code-img" />
        </div>
      </el-form-item>
      <el-form-item>
        <div style="display: flex; justify-content: space-between;">
          <el-checkbox v-model="loginForm.rememberMe" style="margin: 0 0 0 0;">记住密码</el-checkbox>
<!--          <span style="margin: 0 0 0 0; cursor: pointer" @click="forgetPwd">忘记密码？</span>-->
        </div>
      </el-form-item>
      <el-form-item style="width:100%;">
        <el-button :loading="loading" size="medium" type="primary" style="width:100%;"
          @click.native.prevent="handleLogin">
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
        <div style="float: right;" v-if="register">
          <router-link class="link-type" :to="'/register'">立即注册</router-link>
        </div>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-login-footer">
      <span>{{ copyright }}</span>
    </div>
  </div>
</template>

<script>
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from '@/utils/jsencrypt'
import { checkLogin } from '@/api/login'
import { getBaseConfig } from '@/api/system/security/base-config/base-config'
export default {
  name: "Login",
  data() {
    return {
      baseUrl: window.location.origin + "/opsyndex-file",
      codeUrl: "",
      loginPageName: '',
      copyright: '',
      backgroundImage: '',
      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
        code: "",
        uuid: "",
        forcedLogin: false
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号" }
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" }
        ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }]
      },
      loading: false,
      // 验证码开关
      captchaEnabled: false,
      // 注册开关
      register: false,
      // redirect: undefined
    };
  },
  created() {
    this.getCode();
    this.getCookie();
  },
  mounted() {
    this.fetchBaseConfig()
  },
  methods: {
    async fetchBaseConfig() {
      try {
        const res = await getBaseConfig(); // 处理异步请求
        if (res.code === 200) {
          this.loginPageName = res.data.loginPageName;

          this.copyright = res.data.copyright

          this.backgroundImage = this.baseUrl + res.data.loginPageBG

          document.title = res.data.systemName

          var link= document.querySelector("link[rel*='icon']") || document.createElement('link');
          link.type = 'image/x-icon';
          link.rel = 'shortcut icon';
          link.href = this.baseUrl + res.data.favicon
          document.getElementsByTagName('head')[0].appendChild(link);
        }
      } catch (error) {
        console.error("Error fetching base config:", error);
      }
    },
    getCode() {
      getCodeImg().then(res => {
        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled;
        if (this.captchaEnabled) {
          this.codeUrl = "data:image/gif;base64," + res.img;
          this.loginForm.uuid = res.uuid;
        }
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      };
    },
    // 打开自定义确认框 element-ui现有的确认框已经不满足业务需要
    openMessageBox() {
      return new Promise((resolve) => {
        const h = this.$createElement
        this.$msgbox({
          title: '',
          message: h('div', null, [
            h('i', {
              class: 'el-icon-warning',
              style: 'color: #FCBE2D;font-size: 18px !important;margin-right: 10px;'
            }),
            h('span', null, `当前用户【${this.loginForm.username}】已登录，是否强制登录?`),
            h(
              'div',
              {
                style: 'display:flex;justify-content: flex-end;justify-content: flex-end;margin-top: 10px;margin-bottom: -10px;'
              },
              [
                // 取消
                h(
                  'el-button',
                  {
                    on: {
                      click: () => {
                        this.$msgbox.close()
                        resolve('0')
                      }
                    }
                  },
                  '取消'
                ),
                // 强制
                h(
                  'el-button',
                  {
                    on: {
                      click: () => {
                        this.$msgbox.close()
                        resolve('1')
                      }
                    }
                  },
                  '强制'
                ),
                // 继续
                h(
                  'el-button',
                  {
                    class: 'el-button--primary',
                    on: {
                      click: () => {
                        this.$msgbox.close()
                        resolve('2')
                      }
                    }
                  },
                  '继续'
                )
              ]
            )
          ]),
          showCancelButton: false,
          showConfirmButton: false,
        })
      })
    },
    // 校验用户是否登录
    async checkCurrentUserLogin() {
      const result = await checkLogin(this.loginForm.username)
      if (result.code === 200) {
        if (result.data) {
          const choose = await this.openMessageBox()
          if (choose == "0") {
            throw new Error('已取消强制登录')
          } else if (choose == "1") {
            this.loginForm.forcedLogin = true
          } else {
            this.loginForm.forcedLogin = false
          }
        }
      } else {
        this.$message.error(result.msg)
        return
      }
    },
    handleRememberMe() {
      if (this.loginForm.rememberMe) {
        Cookies.set("username", this.loginForm.username, { expires: 30 });
        Cookies.set("password", encrypt(this.loginForm.password), { expires: 30 });
        Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });
      } else {
        Cookies.remove("username");
        Cookies.remove("password");
        Cookies.remove('rememberMe');
      }
    },
    async handleLogin() {
      const valid = await this.$refs.loginForm.validate().catch(() => false);
      if (!valid) return
      await this.checkCurrentUserLogin()
      this.loading = true;
      this.handleRememberMe()
      this.$store.dispatch("Login", this.loginForm).then(() => {
        const redirect = this.$route.query.redirect
        // 获取当前用户是移动端还是pc端
        // const ua = navigator.userAgent.toLowerCase();
        const isMobile =  /Mobi|Android|iPhone/i.test(navigator.userAgent)//ua.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)
        console.log("isMobile----isMobile",isMobile);
        if (isMobile) {
          this.$router.replace({ path: "/app/menu" })
        } else {
          this.$router.push({ path: redirect || "/" })
        }
      }).catch((error) => {
        console.error(error)
        this.loading = false;
        if (this.captchaEnabled) {
          this.getCode();
        }
      });
    },
    forgetPwd() {
      console.log('跳转忘记密码页面')
      this.$router.push({ path: '/forget-pwd' }); // 跳转到修改密码页面
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  //background-image: url("../assets/images/img.png");
  background-size: cover;
}

.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;

  .el-input {
    height: 38px;

    input {
      height: 38px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.login-code-img {
  height: 38px;
}
</style>
