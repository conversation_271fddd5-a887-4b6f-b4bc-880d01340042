<template>
  <div class="app-container">
    <div ref="queryFormRef" class="filter-container">
      <el-button class="filter-item" style="margin-left: 10px" type="primary" icon="el-icon-edit" @click="handleCreate">
        添加
      </el-button>
    </div>
    <el-table :height="tableHeight" ref="fullHeightTableRef" v-loading="listLoading" :data="list"
      element-loading-text="Loading" border fit highlight-current-row>
      <el-table-column label="排序" width="50" align="center">
        <template slot-scope="scope">{{ scope.row.order }}</template>
      </el-table-column>
      <el-table-column label="名称" width="120" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope">{{ scope.row.title }}</template>
      </el-table-column>
      <el-table-column label="分组标识" width="200" align="center">
        <template slot-scope="scope">{{ scope.row.appName }}</template>
      </el-table-column>
      <el-table-column label="注册方式" width="110" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          {{
            addressTypes.find((t) => t.value === scope.row.addressType).label
          }}</template>
      </el-table-column>
      <el-table-column label="在线机器" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope">{{ scope.row.addressList }}</template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="230" class-name="small-padding fixed-width">
        <template slot-scope="{ row }">
          <el-button type="primary" size="mini" style="width: 60px!important;" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button v-if="row.status != 'deleted'" size="mini" type="danger" @click="confirmDelete(row)"
            style="width: 60px!important;">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size"
      @pagination="fetchData" />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="80px"
        style="width: 400px; margin-left: 50px">
        <el-form-item label="名称" prop="title">
          <el-input v-model="temp.title" placeholder="请输入执行器名称" />
        </el-form-item>
        <el-form-item label="分组标识" prop="appName">
          <el-input v-model="temp.appName" placeholder="分组标识" />
        </el-form-item>

        <el-form-item label="排序" prop="prop">
          <el-input v-model="temp.order" placeholder="执行器序号" />
        </el-form-item>
        <el-form-item label="注册方式" prop="addressType">
          <el-radio-group v-model="temp.addressType">
            <el-radio :label="0">自动注册</el-radio>
            <el-radio :label="1">手动录入</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="机器地址" prop="addressList">
          <el-input v-model="temp.addressList" :disabled="dialogStatus !== 'create'" placeholder="多个以逗号分隔" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false"> 取消 </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import * as executor from '@/api/dts/datax-executor'
import waves from '@/directive/waves' // waves directive
import Pagination from '@/components/Pagination' // secondary package based on el-pagination

export default {
  mixins: [tableFullHeight],
  name: 'Executor',
  components: { Pagination },
  directives: { waves },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'gray',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      deleteDialogVisible: false,
      list: null,
      listLoading: true,
      total: 0,
      listQuery: {
        current: 1,
        size: 10,
        name: undefined,
        jobGroup: undefined
      },
      editJsonVisible: false,
      pluginData: [],
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: 'Edit',
        create: 'Create'
      },
      rules: {
        appName: [
          { required: true, message: 'appName is required', trigger: 'blur' }
        ],
        title: [
          { required: true, message: 'title is required', trigger: 'blur' }
        ],
        order: [
          { required: true, message: 'title is required', trigger: 'blur' }
        ],
        addressType: [
          { required: true, message: 'title is required', trigger: 'change' }
        ]
      },
      temp: {
        id: undefined,
        appName: undefined,
        title: undefined,
        order: undefined,
        addressType: undefined,
        addressList: undefined
      },
      addressTypes: [
        { value: 0, label: '自动注册' },
        { value: 1, label: '手动录入' }
      ]
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      this.listLoading = true
      executor.getList().then((response) => {
        const { content } = response
        this.list = content
        this.listLoading = false
      })
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        appName: undefined,
        title: undefined,
        order: undefined,
        addressType: undefined,
        addressList: undefined
      }
    },
    // 删除按钮提示模态框
    confirmDelete(row) {
      this.$confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 用户点击确定按钮时执行删除操作
          this.handleDelete(row)
        })
        .catch(() => {
          // 用户点击取消按钮时的操作，可以不做任何处理或关闭模态框
        })
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          executor.created(this.temp).then((res) => {
            if (res.code === 200) {
              this.fetchData()
              this.dialogFormVisible = false
              this.$notify({
                title: 'Success',
                message: 'Created Successfully',
                type: 'success',
                duration: 2000
              })
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row) // copy obj
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          tempData.configJson = this.configJson
          executor.updated(tempData).then(() => {
            this.fetchData()
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handleDelete(row) {
      executor.deleted(row.id).then((response) => {
        if (response.code === 500) {
          this.$notify({
            title: 'error',
            message: response.msg,
            type: 'error',
            duration: 2000
          })
        } else {
          this.$notify({
            title: 'Success',
            message: 'Delete Successfully',
            type: 'success',
            duration: 2000
          })
        }
        this.fetchData()
      })
      // const index = this.list.indexOf(row)
    },
    handleFetchPv(id) {
      executor.fetch(id).then((response) => {
        this.pluginData = response
        this.dialogPvVisible = true
      })
    }
  }
}
</script>
