<template>
    <div class='oauth2-redirect'>
        <div class="loading-container">
            <div class="loading-spinner">
                <div class="spinner-circle"></div>
                <div class="spinner-circle"></div>
                <div class="spinner-circle"></div>
            </div>
            <div class="loading-text">正在处理登录请求，请稍候...</div>
        </div>
    </div>
</template>

<script>
import OAuth2Login from '@/utils/oauth2-login'
export default {
    name: 'oauth2-redirect',
    mixins: [OAuth2Login],
    created() {
        this.handleParseUrl()
    },
}
</script>

<style lang='scss' scoped>
.oauth2-redirect {
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f5f7fa;
}

.loading-container {
    text-align: center;
}

.loading-spinner {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
}

.spinner-circle {
    position: absolute;
    width: 13px;
    height: 13px;
    border-radius: 50%;
    background: #1890ff;
    animation-timing-function: cubic-bezier(0, 1, 1, 0);

    &:nth-child(1) {
        left: 8px;
        animation: spinner1 0.6s infinite;
    }

    &:nth-child(2) {
        left: 8px;
        animation: spinner2 0.6s infinite;
    }

    &:nth-child(3) {
        left: 32px;
        animation: spinner2 0.6s infinite;
    }

    &:nth-child(4) {
        left: 56px;
        animation: spinner3 0.6s infinite;
    }
}

.loading-text {
    color: #606266;
    font-size: 14px;
    margin-top: 10px;
}

@keyframes spinner1 {
    0% {
        transform: scale(0);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes spinner2 {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(24px, 0);
    }
}

@keyframes spinner3 {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(0);
    }
}
</style>