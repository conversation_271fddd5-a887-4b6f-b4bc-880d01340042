<template>
  <div class="app-container">
    <div ref="queryFormRef" class="filter-container">
      <el-input
        v-model="listQuery.templateName"
        clearable
        placeholder="模板名称"
        style="width: 200px;"
        class="filter-item"
      />
      <el-input
        v-model="listQuery.templateTitle"
        clearable
        placeholder="消息标题"
        style="width: 200px;"
        class="filter-item"
      />
      <el-input
        v-model="listQuery.templateContent"
        clearable
        placeholder="消息内容"
        style="width: 200px;"
        class="filter-item"
      />
      <el-select v-model="listQuery.templateChannel" placeholder="消息渠道" class="filter-item" clearable>
        <el-option
          v-for="dict in dict.type.message_channel"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>

      <el-select v-model="listQuery.msgType" placeholder="消息类型" class="filter-item" clearable>
        <el-option
          v-for="dict in dict.type.msg_type"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>

      <el-select v-model="listQuery.enable" placeholder="启用状态" class="filter-item" clearable>
        <el-option
          v-for="dict in dict.type.template_enable"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>

      <el-button v-waves class="filter-item" size="small" type="primary" icon="el-icon-search" @click="fetchData">
        搜索
      </el-button>
      <el-button v-waves class="filter-item" size="small" type="primary" icon="el-icon-plus" @click="addShowDialog">
        新增
      </el-button>
      <el-button v-waves class="filter-item" size="small" type="primary" icon="el-icon-refresh" @click="resetQuery">
        重置
      </el-button>
    </div>

    <el-table
      ref="fullHeightTableRef"
      :height="tableHeight"
      v-loading="listLoading"
      :data="formDataList"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="模板名称" prop="templateName"></el-table-column>
      <el-table-column align="center" label="消息标题" prop="templateTitle"></el-table-column>
<!--      <el-table-column align="center" label="消息内容" prop="templateContent">-->
<!--        <template slot-scope="scope">-->
<!--          <div class="table-content">-->
<!--            {{ scope.row.templateContent }}-->
<!--          </div>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column align="center" label="可用消息渠道" prop="templateChannel">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.message_channel" :value="scope.row.templateChannel"/>
        </template>
      </el-table-column>
      <el-table-column align="center" label="启用状态" prop="enable">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.template_enable" :value="scope.row.enable"/>
        </template>
      </el-table-column>
      <el-table-column align="center" label="消息类型">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.msg_type" :value="scope.row.msgType"/>
        </template>
      </el-table-column>
      <el-table-column align="center" label="备注" prop="remark"></el-table-column>
      <el-table-column label="操作" align="center" fixed="right">
        <template slot-scope="{ row }">
          <el-button size="mini" type="text" @click="updateShowDialog(row)">
            修改
          </el-button>
          <el-button size="mini" type="text" @click="deleteMsg(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNum"
      :limit.sync="listQuery.pageSize"
      @pagination="fetchData"
      class="pagination"
    />

    <!-- 新增的 Dialog -->
    <el-dialog :visible.sync="dialogVisible" title="消息详情">
      <el-form label-width="100px" ref="dialogForm" :model="dialogData" :rules="rules" status-icon>
        <template v-if="dialogData.id != undefined || dialogData.id != null">
          <el-row>
            <el-col :span="12">
              <el-form-item label="模板名称" prop="templateName">
                <el-input v-model="dialogData.templateName" placeholder="请输入模板名称"></el-input>
              </el-form-item>
              <el-form-item label="是否启用" prop="enable">
                <el-select v-model="dialogData.enable" placeholder="是否启用" style="width: 100%" class="filter-item" clearable>
                  <el-option
                    v-for="dict in dict.type.template_enable"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="通知类型" prop="msgType">
                <el-select v-model="dialogData.msgType" placeholder="请选择通知类型" style="width: 100%" class="filter-item" clearable>
                  <el-option
                    v-for="dict in dict.type.msg_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item v-if="showTemplateType" label="模板类型" prop="templateMsgType">
                <el-select v-model="dialogData.templateMsgType" placeholder="请选择模板类型" style="width: 100%" class="filter-item" clearable @change="handleTemplateTypeChange">
                  <el-option
                    v-for="dict in dict.type.ding_msg_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="消息标题" prop="templateTitle">
                <el-input v-model="dialogData.templateTitle" placeholder="请输入消息标题"></el-input>
              </el-form-item>
              <el-form-item label="消息渠道" prop="templateChannel">
                <el-select v-model="dialogData.templateChannel" placeholder="请选择消息渠道" style="width: 100%" class="filter-item" clearable multiple @change="handleChannelChange">
                  <el-option
                    v-for="dict in dict.type.message_channel"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="模板备注">
                <el-input v-model="dialogData.remark"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item v-if="!showEditor" label="内容" prop="templateContent">
            <el-input
              type="textarea"
              v-model="dialogData.templateContent"
              rows="12"
            ></el-input>
          </el-form-item>
          <el-form-item v-if="showTemplateType && showEditor" label="内容" prop="markdownContent">
            <mavon-editor v-model="dialogData.markdownContent" :xssOptions="xssOptions"  ref=md @imgAdd="handleImgAdd"/>
          </el-form-item>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-button
                @click="handleViewJobLog(dialogData.msgGroupList.toString())"
              >引用此模板的提醒组</el-button>
            </el-col>
          </el-row>
        </template>

        <!-- 新建 -->
        <template v-if="dialogData.id === undefined || dialogData.id === null">
          <el-row>
            <el-col :span="12">
              <el-form-item label="模板名称" prop="templateName">
                <el-input v-model="dialogData.templateName" placeholder="请输入模板名称"></el-input>
              </el-form-item>
              <el-form-item label="是否启用" prop="enable">
                <el-select v-model="dialogData.enable" placeholder="是否启用" style="width: 100%" class="filter-item" clearable>
                  <el-option
                    v-for="dict in dict.type.template_enable"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="通知类型" prop="msgType">
                <el-select v-model="dialogData.msgType" placeholder="请选择通知类型" style="width: 100%" class="filter-item" clearable>
                  <el-option
                    v-for="dict in dict.type.msg_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item v-if="showTemplateType" label="模板类型" prop="templateMsgType">
                <el-select v-model="dialogData.templateMsgType" placeholder="请选择模板类型" style="width: 100%" class="filter-item" clearable @change="handleTemplateTypeChange">
                  <el-option
                    v-for="dict in dict.type.ding_msg_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="消息标题" prop="templateTitle">
                <el-input v-model="dialogData.templateTitle" placeholder="请输入标题"></el-input>
              </el-form-item>
              <el-form-item label="消息渠道" prop="templateChannel">
                <el-select v-model="dialogData.templateChannel" placeholder="请选择消息渠道" style="width: 100%" class="filter-item" clearable multiple @change="handleChannelChange">
                  <el-option
                    v-for="dict in dict.type.message_channel"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="模板备注">
                <el-input v-model="dialogData.remark"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item v-if="!showEditor" label="内容" prop="templateContent">
            <el-input
              type="textarea"
              v-model="dialogData.templateContent"
              rows="12"
            ></el-input>
          </el-form-item>
          <el-form-item v-if="showTemplateType && showEditor" label="内容" prop="markdownContent">
            <mavon-editor v-model="dialogData.markdownContent" :xssOptions="xssOptions" ref=md @imgAdd="handleImgAdd"/>
          </el-form-item>
        </template>
      </el-form>

      <div slot="footer" type="dialog-footer">
        <el-button type="danger" @click="closeDialog()">
          关闭
        </el-button>
        <el-button type="primary" @click="confirmData()">
          确定
        </el-button>
      </div>
    </el-dialog>

    <el-dialog title="消息提醒组名称" :visible.sync="logShow" width="35%">
      <div class="log-container">
        <pre :loading="logLoading" v-text="logContent" />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="logShow = false"> 关闭 </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination/index.vue'
import * as template from "@/api/message/template/template";
import 'mavon-editor/dist/css/index.css'
import { deepClone } from '@/utils'
import axios from 'axios'
import { getToken } from '@/utils/auth'
import * as attach from '@/api/file/attachment'

export default {
  mixins: [tableFullHeight],
  name: 'JobProject',
  components: { Pagination },
  directives: { waves },
  dicts: ['msg_type', 'view_status', 'msg_status', 'template_enable', 'message_channel', 'ding_msg_type'],
  data() {
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + '/file/attachments/upload',
      baseUrl: window.location.origin + "/opsyndex-file",
      dialogStatus: '',
      dialogVisible: false,
      formDataList: [],
      listLoading: true,
      total: 0,
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        templateName: '',
        templateTitle: '',
        templateContent: '',
        templateChannel: [],
        enable: '',
        msgType: ''
      },
      dialogData: {
        id: undefined,
        templateName: '',
        templateTitle: '',
        templateContent: '',
        templateChannel: [],
        remark: '',
        enable: '',
        msgType: '',
        templateMsgType: '',
        markdownContent: '',
        msgGroupList: []
      },
      xssOptions: {},
      fileIds: [],
      // 日志内容
      logContent: '',
      // 显示日志
      logShow: false,
      // 日志显示加载中效果
      logLoading: false,
      rules: {
        templateName: [
          { required: true, message: '请填写模板名称', trigger: 'blur' }
        ],
        templateTitle: [
          { required: true, message: '请填写标题', trigger: 'blur' }
        ],
        templateContent: [
          { required: true, message: '请填写内容', trigger: 'blur' }
        ],
        markdownContent: [
          { required: true, message: '请填写内容', trigger: 'blur' }
        ],
        templateChannel: [
          { required: true, message: '请选择消息渠道', trigger: 'change' }
        ],
        enable: [
          { required: true, message: '请选择是否启用', trigger: 'change' }
        ],
        msgType: [
          { required: true, message: '请选择消息类型', trigger: 'change' }
        ],
        templateMsgType: [
          { required: true, message: '请选择模板类型', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
  },
  computed: {
    showTemplateType() {
      // 仅当templateChannel中仅包含 'dingding' 时为 true
      return this.dialogData.templateChannel.length === 1 &&
        this.dialogData.templateChannel.includes('dingding');
    },
    showEditor() {
      // 检查选择的模板类型是否为 "simpleMarkdown"
      return this.dialogData.templateMsgType === 'sampleMarkdown';
    },
    token() {
      return {
        Authorization: 'Bearer ' + getToken()
      }
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    resetTemp() {
      this.dialogData = {
        id: undefined,
        templateName: '',
        templateTitle: '',
        templateContent: '',
        templateChannel: [],
        remark: '',
        enable: '',
        msgType: ''
      }
      this.fileIds = []
    },
    resetQuery(){
      this.listQuery.templateName = ''
      this.listQuery.templateTitle = ''
      this.listQuery.templateContent = ''
      this.listQuery.templateChannel = []
      this.listQuery.msgType = ''
      this.listQuery.enable = ''
      this.fetchData();

    },
    addShowDialog() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogVisible = true
    },
    fetchData() {
      this.listLoading = true
      template.page(this.listQuery).then(response => {
        this.formDataList = response.rows
        this.total = response.total
        this.listLoading = false
      }).catch(error => {
        console.error(error)
        this.listLoading = false
      })
    },
    // 展示
    updateShowDialog(row) {
      this.dialogVisible = true
      this.dialogStatus = 'update'
      this.dialogData = JSON.parse(JSON.stringify(row))
      this.dialogData.enable = row.enable.toString()
      this.dialogData.msgType = row.msgType.toString()
      this.dialogData.templateChannel = row.templateChannel.split(',')
    },
    closeDialog() {
      this.dialogVisible = false
    },
    deleteMsg(row) {
      const idList = []
      idList.push(row.id)

      this.$modal.confirm('是否确认删除编号为"' + idList + '"的数据项？').then(function () {
        return template.deleted({ idList: row.id });
      }).then(() => {
        this.fetchData()
        this.$notify({
          title: 'Success',
          message: 'Delete Successfully',
          type: 'success',
          duration: 2000
        })
      }).catch(() => {
        this.$notify({
          title: 'error',
          message: 'Delete Failed',
          type: 'error',
          duration: 2000
        })
      });
    },
    confirmData() {
      this.$refs['dialogForm'].validate(async valid => {
        if (valid) {
          const cloneData = deepClone(this.dialogData)
          cloneData.templateChannel = String(cloneData.templateChannel)

          if (Array.isArray(this.fileIds) && this.fileIds.length > 0) {
            const bindParam = {
              fileIdList: this.fileIds,
              // businessId: this.dialogData.id,
              // fileStatus: 'BOUND',
              fileStatus: '',
              businessType: 'message'
            }

            await attach.batchBindAttachment(bindParam)
          }

          const param = JSON.parse(JSON.stringify(cloneData))
          if (this.dialogStatus == 'create') {
            template.add(param).then(response => {
              this.fetchData()
              this.$notify({
                title: 'Success',
                message: 'Add Successfully',
                type: 'success',
                duration: 2000
              })
              this.dialogVisible = false
            })
          } else {
            template.update(param).then(response => {
              this.fetchData()
              this.$notify({
                title: 'Success',
                message: 'Update Successfully',
                type: 'success',
                duration: 2000
              })
              this.dialogVisible = false
            })
          }
        }
      })
    },
    handleViewJobLog(logContent) {
      console.log(logContent)
      this.logDialogVisible = true
      if (this.logShow === false) {
        this.logShow = true
      }
      this.loadLog(logContent)
    },
    loadLog(logContent) {
      this.logLoading = true
      this.logContent = logContent
      this.logLoading = false
    },
    handleChannelChange() {
      // 当消息渠道改变时，可以在此处进行其它逻辑
      if (!this.showTemplateType) {
        // 如果不是，仅清空模板类型的选择
        this.dialogData.templateMsgType = 'sampleText';
      }
    },
    handleTemplateTypeChange() {
      // 当模板类型改变时，可以在此处进行其它逻辑
    },
    handleImgAdd(pos, $file) {
      console.log("开始上传文件")
      let formData = new FormData();
      formData.append('file', $file);
      // formData.append('businessId', this.dialogData.id)
      formData.append('businessType', 'message')
      axios.post(this.uploadUrl, formData, {
        headers: {
          ...this.token,
          'Content-Type': 'multipart/form-data'
        }
      })
        .then(response => {
          // 假设后端返回格式 { code: 200, url: '图片地址' }
          if (response.data.code === 200) {
            // this.fileIds.push(response.data.fileId)
            const imageUrl = this.baseUrl + response.data.data.relativePath
            // 插入图片
            this.$refs.md.$img2Url(pos, imageUrl)
          } else {
            this.$message.error('图片上传失败')
          }
        })
        .catch(error => {
          console.error('图片上传异常:', error)
          this.$message.error('图片上传异常')
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.table-content {
  max-height: 50px; /* 设置最大高度 */
  overflow: hidden; /* 溢出隐藏 */
  text-overflow: ellipsis; /* 显示省略号 */
  white-space: nowrap; /* 不换行 */
}
</style>

<style lang="scss">

.v-note-help-wrapper {
  z-index: 9999 !important; /* mavon-editor 语法帮助弹窗置为上层 */
}

</style>
