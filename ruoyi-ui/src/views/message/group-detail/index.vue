<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.name"
        clearable
        placeholder="提醒组明细名称"
        style="width: 200px;"
        class="filter-item"
      />
      <el-input
        v-model="listQuery.number"
        clearable
        placeholder="提醒组明细编码"
        style="width: 200px;"
        class="filter-item"
      />
      <el-select v-model="listQuery.msgType" placeholder="推送方式" class="filter-item" clearable>
        <el-option
          v-for="dict in dict.type.message_channel"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>

      <el-button v-waves class="filter-item" size="small" type="primary" icon="el-icon-search" @click="fetchData">
        搜索
      </el-button>
      <el-button v-waves class="filter-item" size="small" type="primary" icon="el-icon-search" @click="addShowDialog">
        新增
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="formDataList"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="名称" prop="name"></el-table-column>
      <el-table-column align="center" label="编码" prop="number"></el-table-column>
      <el-table-column align="center" label="消息模板" prop="templateId">
        <template slot-scope="scope">
          {{ findTemplateNameById(scope.row.templateId) }}
        </template>
      </el-table-column>
      <el-table-column label="是否启用" align="center" width="150">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.enable" active-color="#00A854" active-text="启用" :active-value="1"
                     inactive-color="#F04134" inactive-text="禁用" :inactive-value="0" @change="changeSwitch(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="发送方式" prop="msgType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.message_channel" :value="scope.row.msgType"/>
        </template>
      </el-table-column>
      <el-table-column align="center" label="钉钉(群聊|单聊)" prop="chatWay">
        <template slot-scope="scope">
          {{ getChatWayLabel(scope.row.chatWay) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right">
        <template slot-scope="{ row }">
          <el-button size="mini" type="text" @click="updateShowDialog(row)">
            修改
          </el-button>
          <el-button size="mini" type="text" @click="deleteMsg(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNum"
      :limit.sync="listQuery.pageSize"
      @pagination="fetchData"
      class="pagination"
    />

    <!-- 新增的 Dialog -->
    <el-dialog :visible.sync="dialogVisible" title="消息提醒组明细">
      <el-form label-width="100px" ref="dialogForm" :model="dialogData" :rules="rules" status-icon>
        <template v-if="dialogData.id != undefined || dialogData.id != null">
          <el-row>
            <el-col :span="12">
              <el-form-item label="名称" prop="name">
                <el-input v-model="dialogData.name"></el-input>
              </el-form-item>
              <el-form-item label="推送方式" prop="msgType">
                <el-select v-model="dialogData.msgType" placeholder="请选择推送方式" style="width: 100%" class="filter-item" @change="handleMessageChannelChange" clearable>
                  <el-option
                    v-for="dict in dict.type.message_channel"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="群组|单聊" v-if="dialogData.msgType == 'dingding'" prop="chatWay">
                <el-select v-model="dialogData.chatWay" placeholder="请选择群组|单聊" style="width: 100%" class="filter-item" @change="handleMsgTypeChange" clearable>
                  <el-option
                    v-for="item in this.dingChatWay"
                    :key="item.key"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="发送配置" v-if="dialogData.msgType == 'dingding'" prop="configId">
                <el-select v-model="dialogData.configId" placeholder="请选择推送方式" style="width: 100%" class="filter-item" clearable>
                  <el-option
                    v-for="item in this.configList"
                    :key="item.id"
                    :label="item.configName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="编码" prop="number">
                <el-input v-model="dialogData.number"></el-input>
              </el-form-item>
              <el-form-item label="消息模板" prop="templateId">
                <el-select v-model="dialogData.templateId" placeholder="请选择消息模板" style="width: 100%" class="filter-item" clearable filterable>
                  <el-option
                    v-for="item in this.templateArr"
                    :key="item.id"
                    :label="item.templateName"
                    :value="item.id"
                  />
                </el-select>

              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="消息接收人">
                <el-select
                  v-model="dialogData.userList"
                  filterable
                  multiple
                  @change="$forceUpdate()"
                  placeholder="请选择消息接收人"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in this.sysUserList"
                    :key="item.userId"
                    :label="item.nickName"
                    :value="item.userId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <template v-if="dialogData.id === undefined || dialogData.id === null">
          <el-row>
            <el-col :span="12">
              <el-form-item label="名称" prop="name">
                <el-input v-model="dialogData.name"></el-input>
              </el-form-item>
              <el-form-item label="推送方式" prop="msgType">
                <el-select v-model="dialogData.msgType" placeholder="请选择推送方式" style="width: 100%" class="filter-item" @change="handleMessageChannelChange" clearable>
                  <el-option
                    v-for="dict in dict.type.message_channel"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="群组|单聊" v-if="dialogData.msgType == 'dingding'" prop="chatWay">
                <el-select v-model="dialogData.chatWay" placeholder="请选择群组|单聊" class="filter-item" style="width: 100%" @change="handleMsgTypeChange" clearable>
                  <el-option
                    v-for="item in this.dingChatWay"
                    :key="item.key"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="发送配置" v-if="dialogData.msgType == 'dingding'" prop="configId">
                <el-select v-model="dialogData.configId" placeholder="请选择发送配置" style="width: 100%" class="filter-item" clearable>
                  <el-option
                    v-for="item in this.configList"
                    :key="item.id"
                    :label="item.configName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="编码" prop="number">
                <el-input v-model="dialogData.number"></el-input>
              </el-form-item>
              <el-form-item label="消息模板" prop="templateId">
                <el-select v-model="dialogData.templateId" placeholder="请选择消息模板" style="width: 100%" class="filter-item" clearable filterable>
                  <el-option
                    v-for="item in this.templateArr"
                    :key="item.id"
                    :label="item.templateName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="消息接收人">
                <el-select
                  v-model="dialogData.userList"
                  filterable
                  multiple
                  @change="$forceUpdate()"
                  placeholder="请选择消息接收人"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in this.sysUserList"
                    :key="item.userId"
                    :label="item.nickName"
                    :value="item.userId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
      </el-form>

      <div slot="footer" type="dialog-footer">
        <el-button type="danger" @click="closeDialog()">
          关闭
        </el-button>
        <el-button type="primary" @click="confirmData()">
          确定
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination/index.vue'
import * as group from "@/api/message/group-detail/detail";
import { allUserList } from '@/api/system/user'
import { list } from '@/api/message/template/template'
import { selectDingList, selectPrivateDingList } from '@/api/message/config/config'

export default {
  name: 'JobProject',
  components: { Pagination },
  directives: { waves },
  dicts: ['message_channel'],
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      formDataList: [],
      listLoading: true,
      total: 0,
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        groupId: null,
        name: '',
        number: '',
        msgType: ''
      },
      dialogData: {
        id: undefined,
        name: '',
        number: '',
        templateId: null,
        msgType: '',
        chatWay: '',
        configId: null,
        userList: [],
        groupId: null,
        groupName: ''
      },
      dingChatWay: [
        { key: 'single', value: 'single', label: '单聊' },
        { key: 'group', value: 'group', label: '群聊' }
      ],
      sysUserList: [],
      configList: [],
      templateArr: [],
      rules: {
        name: [
          { required: true, message: '请填写名称', trigger: 'blur' }
        ],
        number: [
          { required: true, message: '请填写编码', trigger: 'blur' }
        ],
        templateId: [
          { required: true, message: '请选择消息模板', trigger: 'blur' }
        ],
        msgType: [
          { required: true, message: '请选择推送方式', trigger: 'blur' }
        ],
        chatWay: [
          { required: true, message: '请选择聊天类型', trigger: 'blur' }
        ],
        configId: [
          { required: true, message: '请选择发送配置', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.fetchData()
    this.getAllUserList()
    this.getTemplateByChannel('null')
  },
  methods: {
    findTemplateNameById(targetId) {
      // 寻找对应 id 的元素
      const foundItem = this.templateArr.find(item => item.id === targetId);
      // 提取 templateName
      return foundItem ? foundItem.templateName : null;
    },
    resetTemp() {
      this.dialogData = {
        id: undefined,
        templateName: '',
        templateTitle: '',
        templateContent: '',
        templateChannel: '',
        remark: '',
        enable: '',
        msgType: ''
      }
      this.templateArr = []
    },
    addShowDialog() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogVisible = true
    },
    fetchData() {
      this.listLoading = true
      this.listQuery.groupId = this.$route.query.groupId
      group.page(this.listQuery).then(response => {
        this.formDataList = response.rows
        this.total = response.total
        this.listLoading = false
      }).catch(error => {
        console.error(error)
        this.listLoading = false
      })
    },
    // 展示
    updateShowDialog(row) {
      this.dialogVisible = true
      this.dialogStatus = 'update'
      this.dialogData = JSON.parse(JSON.stringify(row))
      this.getTemplateByChannel(this.dialogData.msgType)
      console.log(this.dialogData.msgType)
      this.handleMsgTypeChange(this.dialogData.chatWay)
    },
    closeDialog() {
      this.dialogVisible = false
    },
    deleteMsg(row) {
      const idList = []
      idList.push(row.id)
      group.deleted({ idList: row.id }).then(response => {
        this.fetchData()
        this.$notify({
          title: 'Success',
          message: 'Delete Successfully',
          type: 'success',
          duration: 2000
        })
      })
    },
    confirmData() {
      this.$refs['dialogForm'].validate(valid => {
        if (valid) {
          this.dialogData.groupId = this.$route.query.groupId;
          this.dialogData.groupName = this.$route.query.groupName;
          const param = JSON.parse(JSON.stringify(this.dialogData))
          if (this.dialogStatus == 'create') {
            group.add(param).then(response => {
              this.fetchData()
              this.$notify({
                title: 'Success',
                message: 'Add Successfully',
                type: 'success',
                duration: 2000
              })
              this.dialogVisible = false
            })
          } else {
            group.update(param).then(response => {
              this.fetchData()
              this.$notify({
                title: 'Success',
                message: 'Update Successfully',
                type: 'success',
                duration: 2000
              })
              this.dialogVisible = false
            })
          }
        }
      })
    },
    getChatWayLabel(key) {
      const found = this.dingChatWay.find(item => item.key === key);
      return found ? found.label : key;
    },
    getAllUserList() {
      const params = {}
      allUserList(params).then(result => {
        this.sysUserList = result.data
      })
    },
    getTemplateByChannel(channel) {
      const params = { channel }
      list(params).then(result => {
        this.templateArr = result.data
      })
    },
    handleMessageChannelChange(value) {
      // 如果推送方式为空，锁定消息模板
      if (!value) {
        this.dialogData.templateId = null; // 清空消息模板
        return;
      }
      // 调用接口，根据推送方式获取消息模板
      this.getTemplateByChannel(value);
    },
    handleMsgTypeChange(value) {
      if ('single' == value) {
        this.getDingPrivateConfig()
      } else if ('group' == value) {
        this.getDingGroupConfig()
      } else {
        this.configList = []
      }
    },
    // 获取钉钉群聊配置
    getDingGroupConfig() {
      selectDingList().then(result => {
        this.configList = result.data
      })
    },
    // 获取钉钉单聊配置
    getDingPrivateConfig() {
      selectPrivateDingList().then(result => {
        this.configList = result.data
      })
    },
    changeSwitch(row) {
      row.enable === 1 ? this.toStart(row) : this.toStop(row)
    },
    toStart(row) {
      group.toStart(row.id).then(response => {
        this.$notify({
          title: 'Success',
          message: '启用成功',
          type: 'success',
          duration: 2000
        })
      })
    },
    toStop(row) {
      group.toStop(row.id).then(response => {
        this.$notify({
          title: 'Success',
          message: '禁用成功',
          type: 'success',
          duration: 2000
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.table-content {
  max-height: 50px; /* 设置最大高度 */
  overflow: hidden; /* 溢出隐藏 */
  text-overflow: ellipsis; /* 显示省略号 */
  white-space: nowrap; /* 不换行 */
}
</style>
