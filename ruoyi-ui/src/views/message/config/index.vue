<template>
  <el-container>
    <el-main>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="邮件发送配置" name="email">
          <el-form :model="emailForm" label-width="150px">
            <el-form-item label="配置名称">
              <el-input v-model="emailForm.configName" placeholder="请填写配置名称"></el-input>
            </el-form-item>

            <el-form-item label="SMTP 服务器地址">
              <el-input v-model="emailForm.host" placeholder="如：smtp.qq.com"></el-input>
            </el-form-item>

            <el-form-item label="SMTP 端口">
              <el-select
                v-model="emailForm.smtpPort"
                placeholder="选择端口"
                @change="updateSSLAndTLS">
                <el-option
                  v-for="option in portSelect"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="发件人邮箱">
              <el-input v-model="emailForm.username" placeholder="请填写邮箱"></el-input>
            </el-form-item>

            <el-form-item label="授权码">
              <el-input type="password" v-model="emailForm.password" placeholder="请输入授权码"></el-input>
            </el-form-item>

            <el-form-item label="邮件发送协议">
              <el-input v-model="emailForm.protocol" placeholder="请输入邮件发送协议"></el-input>
            </el-form-item>

            <el-form-item label="邮件发送显示名称">
              <el-input v-model="emailForm.displaySendname" placeholder="邮件发送显示名称"></el-input>
            </el-form-item>

            <el-form-item label="是否开启TLS">
              <el-select
                v-model="emailForm.starttlsEnable"
                :disabled="emailForm.smtpPort !== 587"
                placeholder="请选择是否开启TLS">
                <el-option
                  v-for="option in optionSelect"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="是否开启SSL">
              <el-select
                v-model="emailForm.sslEnable"
                :disabled="emailForm.smtpPort !== 465"
                placeholder="选择是否开启SSL">
                <el-option
                  v-for="option in optionSelect"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="saveEmailConfig">保存</el-button>
              <el-button type="primary" @click="refreshEmailConfig">刷新</el-button>
            </el-form-item>
          </el-form>

          <el-table :data="emailRecipients" style="width: 100%" v-if="emailRecipients.length > 0">
            <el-table-column prop="email" label="收件人邮箱" />
            <el-table-column prop="status" label="发送状态" />
            <el-table-column prop="timestamp" label="发送时间" />
          </el-table>
        </el-tab-pane>

        <el-tab-pane label="钉钉自定义机器人(群聊)" name="dingding">
          <el-form ref="queryForm" :model="listQuery" :inline="true">
            <el-form-item label="配置名称" prop="configName">
              <el-input
                v-model="listQuery.configName"
                placeholder="请输入配置名称"
                clearable
                size="small"
              />
            </el-form-item>
            <el-form-item label="群组名称" prop="groupName">
              <el-input
                v-model="listQuery.groupName"
                placeholder="请输入群组名称"
                clearable
                size="small"
              />
            </el-form-item>
            <el-form-item label="机器人名称" prop="robotName">
              <el-input
                v-model="listQuery.robotName"
                placeholder="请输入机器人名称"
                clearable
                size="small"
              />
            </el-form-item>
            <el-form-item label="是否启用加签" prop="secretEnable">
              <el-select v-model="listQuery.secretEnable" placeholder="是否启用加签" class="filter-item" clearable>
                <el-option
                  v-for="dict in dict.type.secret_enable"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button type="primary" size="mini" @click="addShowDialog">新增</el-button>
            </el-form-item>
          </el-form>
          <el-table :data="tableDataList" style="width: 100%">
            <el-table-column prop="configName" label="配置名称"/>
            <el-table-column prop="groupName" label="群聊名称"/>
            <el-table-column prop="robotName" label="机器人名称"/>
            <el-table-column prop="webhookUrl" label="Webhook URL"/>
            <el-table-column label="操作" width="100px">
              <template v-slot="scope">
                <el-button type="text" @click="updateShowDialog(scope.row)">修改</el-button>
                <el-button type="text" @click="deleteConfig(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.pageNum"
            :limit.sync="listQuery.pageSize"
            @pagination="getDingConfigList"
          />
          <el-dialog :visible.sync="dialogVisible" title="查看配置">
            <el-form label-width="120px" ref="dialogGroupForm" :model="dialogData" :rules="rulesGroup"  status-icon>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="群组名称" prop="groupName">
                    <el-input v-model="dialogData.groupName"></el-input>
                  </el-form-item>
                  <el-form-item label="是否启用加签" prop="secretEnable">
                    <el-select v-model="dialogData.secretEnable" placeholder="是否启用加签" style="width: 100%" class="filter-item" clearable>
                      <el-option
                        v-for="dict in dict.type.secret_enable"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="机器人名称" prop="robotName">
                    <el-input v-model="dialogData.robotName"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="配置名称" prop="configName">
                    <el-input v-model="dialogData.configName"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-form-item label="Webhook Url" prop="webhookUrl">
                  <el-input v-model="dialogData.webhookUrl"></el-input>
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item label="Access Token" prop="accessToken">
                  <el-input v-model="dialogData.accessToken"></el-input>
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item label="Secret Token" prop="secretToken">
                  <el-input v-model="dialogData.secretToken"></el-input>
                </el-form-item>
              </el-row>
            </el-form>
            <div slot="footer" type="dialog-footer">
              <el-button type="danger" @click="closeDialog()">
                关闭
              </el-button>
              <el-button type="primary" @click="confirmData()">
                确定
              </el-button>
            </div>
          </el-dialog>
        </el-tab-pane>
        <el-tab-pane label="企业内部机器人(单聊)" name="dingPrivate">
          <el-form ref="queryFormPrivate" :model="privateListQuery" :inline="true">
            <el-form-item label="配置名称" prop="configName">
              <el-input
                v-model="privateListQuery.configName"
                placeholder="请输入配置名称"
                clearable
                size="small"
              />
            </el-form-item>
            <el-form-item label="配置编码" prop="configCode">
              <el-input
                v-model="privateListQuery.configCode"
                placeholder="请输入配置编码"
                clearable
                size="small"
              />
            </el-form-item>
            <el-form-item label="机器人编码" prop="robotCode">
              <el-input
                v-model="privateListQuery.robotCode"
                placeholder="请输入机器人编码"
                clearable
                size="small"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handlePrivateQuery">搜索</el-button>
              <el-button type="primary" size="mini" @click="addShowPrivateDialog">新增</el-button>
            </el-form-item>
          </el-form>
          <el-table :data="privateTableDataList" style="width: 100%">
            <el-table-column prop="configName" label="配置名称"/>
            <el-table-column prop="configCode" label="配置编码"/>
            <el-table-column prop="robotName" label="机器人名称"/>
            <el-table-column prop="robotCode" label="机器人编码"/>
<!--            <el-table-column prop="msgKey" label="单聊消息类型">-->
<!--              <template slot-scope="scope">-->
<!--                <dict-tag :options="dict.type.ding_msg_type" :value="scope.row.msgKey"/>-->
<!--              </template>-->
<!--            </el-table-column>-->
            <el-table-column label="操作" width="100px">
              <template v-slot="scope">
                <el-button type="text" @click="updatePrivateShowDialog(scope.row)">修改</el-button>
                <el-button type="text" @click="deletePrivateConfig(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="privateTotal > 0"
            :total="privateTotal"
            :page.sync="privateListQuery.pageNum"
            :limit.sync="privateListQuery.pageSize"
            @pagination="getDingPrivateConfigList"
          />
          <el-dialog :visible.sync="privateDialogVisible" title="查看配置">
            <el-form label-width="120px" ref="dialogSingleFrom" :model="privateDialogData" :rules="rulesSingle" status-icon>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="配置名称" prop="configName">
                    <el-input v-model="privateDialogData.configName"></el-input>
                  </el-form-item>
                  <el-form-item label="机器人名称" prop="robotName">
                    <el-input v-model="privateDialogData.robotName"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="配置编码" prop="configCode">
                    <el-input v-model="privateDialogData.configCode"></el-input>
                  </el-form-item>
                  <el-form-item label="机器人编码" prop="robotCode">
                    <el-input v-model="privateDialogData.robotCode"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
<!--              <el-row>-->
<!--                <el-col :span="12">-->
<!--                  <el-form-item label="消息类型" prop="msgKey">-->
<!--                    <el-select v-model="privateDialogData.msgKey" placeholder="消息类型" style="width: 100%" class="filter-item" clearable>-->
<!--                      <el-option-->
<!--                        v-for="dict in dict.type.ding_msg_type"-->
<!--                        :key="dict.value"-->
<!--                        :label="dict.label"-->
<!--                        :value="dict.value"-->
<!--                      />-->
<!--                    </el-select>-->
<!--                  </el-form-item>-->
<!--                </el-col>-->
<!--              </el-row>-->
            </el-form>
            <div slot="footer" type="dialog-footer">
              <el-button type="danger" @click="closePirvateDialog()">
                关闭
              </el-button>
              <el-button type="primary" @click="confirmPrivateData()">
                确定
              </el-button>
            </div>
          </el-dialog>
        </el-tab-pane>
      </el-tabs>
    </el-main>
  </el-container>
</template>

<script>
import { select, created as createConfig, updated as updateConfig, deleted as deleteConfig, selectDing, createdDing, updatedDing, deletedDing, selectPrivateDing, createdPrivateDing, updatedPrivateDing, deletedPrivateDing } from '@/api/message/config/config';
import Pagination from '@/components/Pagination/index.vue'
import {delMonitorconfig} from "@/api/alert/monitorconfig/api";


export default {
  dicts: ['secret_enable', 'ding_msg_type'],
  components: { Pagination },
  data() {
    return {
      activeTab: 'email',
      emailForm: {
        configName: '',
        host: 'smtp.qq.com',
        smtpPort: 465,
        username: '',
        password: '',
        protocol: 'smtp',
        displaySendmail: '',
        displaySendname: '',
        starttlsEnable: 0,
        sslEnable: null
      },
      dialogData: {
        id: undefined,
        groupName: '',
        robotName: '',
        configName: '',
        webhookUrl: 'https://oapi.dingtalk.com/robot/send',
        accessToken: '',
        secretEnable: '',
        secretToken: ''
      },
      dialogStatus: '',
      dialogVisible: false,
      privateDialogData: {
        id: undefined,
        configName: '',
        configCode: '',
        robotName: '',
        robotCode: '',
        msgKey: ''
      },
      privateDialogStatus: '',
      privateDialogVisible: false,
      total: 0,
      tableDataList: [],
      listQuery: {
        pageNum: 1,
        pageSize: 20,
        id: undefined,
        configName: '',
        groupName: '',
        robotName: '',
        webhookUrl: '',
        accessToken: '',
        secretEnable: '',
        secretToken: ''
      },
      privateTotal: 0,
      privateTableDataList: [],
      privateListQuery: {
        pageNum: 1,
        pageSize: 20,
        id: undefined,
        configName: '',
        configCode: '',
        robotName: '',
        robotCode: '',
        msgKey: ''
      },
      emailRecipients: [],
      optionSelect: [
        { label: '是', value: 0 },
        { label: '否', value: 1 }
      ],
      portSelect: [
        { label: '465 (SSL)', value: 465 },
        { label: '587 (StartTLS)', value: 587 }
      ],
      rulesGroup: {
        groupName: [
          { required: true, message: '请填写群组名称', trigger: 'blur' }
        ],
        robotName: [
          { required: true, message: '请填写机器人名称', trigger: 'blur' }
        ],
        configName: [
          { required: true, message: '请填写配置名称', trigger: 'blur' }
        ],
        webhookUrl: [
          { required: true, message: '请填写webhookUrl', trigger: 'blur' }
        ],
        accessToken: [
          { required: true, message: '请填写accessToken', trigger: 'blur' }
        ],
        secretEnable: [
          { required: true, message: '请选择是否启用加签', trigger: 'change' }
        ],
        secretToken: [
          { required: true, message: '请填写secretToken', trigger: 'blur' }
        ]
      },
      rulesSingle: {
        configName: [
          { required: true, message: '请填写配置名称', trigger: 'blur' }
        ],
        configCode: [
          { required: true, message: '请填写配置编码', trigger: 'blur' }
        ],
        robotName: [
          { required: true, message: '请填写机器人名称', trigger: 'blur' }
        ],
        robotCode: [
          { required: true, message: '请填写机器人编码', trigger: 'blur' }
        ],
        msgKey: [
          { required: true, message: '请选择消息类型', trigger: 'change' }
        ]
      }
    };
  },
  created() {
    this.getEmailConfig();
    this.getDingConfigList();
    this.getDingPrivateConfigList()
  },
  methods: {
    saveEmailConfig() {
      createConfig(this.emailForm).then(response => {
        if (response.code === 200) {
          this.$message.success('保存成功');
        } else {
          this.$message.error('保存失败');
        }
      });
    },
    refreshEmailConfig() {
      this.getEmailConfig();
    },
    getEmailConfig() {
      select().then(response => {
        if (response.code == 200) {
          this.emailForm = response.data;
        }
      });
    },
    updateSSLAndTLS(value) {
      if (value === 465) {
        this.emailForm.sslEnable = 0; // 开启SSL
      } else if (value === 587) {
        this.emailForm.sslEnable = 1; // 关闭SSL
        this.emailForm.starttlsEnable = 0; // 开启TLS
      }
    },
    // 获取获取钉钉单聊配置列表
    getDingPrivateConfigList() {
      selectPrivateDing(this.privateListQuery).then(result => {
        this.privateTableDataList = result.rows
        this.privateTotal = result.total
      })
    },
    // 获取钉钉配置列表
    getDingConfigList() {
      selectDing(this.listQuery).then(result => {
        this.tableDataList = result.rows
        this.total = result.total
      })
    },
    handleQuery() {
      this.getDingConfigList()
    },
    // 搜索单聊配置
    handlePrivateQuery() {
      this.getDingPrivateConfigList()
    },
    resetQuery() {
      this.listQuery = {
        pageNum: 1,
        pageSize: 20,
        id: undefined,
        groupName: '',
        robotName: '',
        webhookUrl: '',
        accessToken: '',
        secretEnable: '',
        secretToken: ''
      }
      this.getDingConfigList()
    },
    resetDialogData() {
      this.dialogData = {
        id: undefined,
        groupName: '',
        robotName: '',
        webhookUrl: 'https://oapi.dingtalk.com/robot/send',
        accessToken: '',
        secretEnable: '',
        secretToken: ''
      }
    },
    // 重置单聊配置查询
    resetPrivateQuery() {
      this.privateListQuery = {
        pageNum: 1,
        pageSize: 20,
        id: undefined,
        configName: '',
        configCode: '',
        robotName: '',
        robotCode: '',
        msgKey: ''
      }
      this.getDingPrivateConfigList()
    },
    // 重置单聊配置查询
    resetPrivateDialogData() {
      this.privateDialogData = {
        id: undefined,
        configName: '',
        configCode: '',
        robotName: '',
        robotCode: '',
        msgKey: ''
      }
    },
    // 展示
    addShowDialog() {
      this.resetDialogData()
      this.dialogStatus = 'create'
      this.dialogVisible = true
    },
    updateShowDialog(row) {
      this.dialogStatus = 'update'
      this.dialogVisible = true
      this.dialogData = JSON.parse(JSON.stringify(row))
    },
    // 展示单聊配置
    addShowPrivateDialog() {
      this.resetPrivateDialogData()
      this.privateDialogStatus = 'create'
      this.privateDialogVisible = true
    },
    updatePrivateShowDialog(row) {
      this.privateDialogStatus = 'update'
      this.privateDialogVisible = true
      this.privateDialogData = JSON.parse(JSON.stringify(row))
    },
    closeDialog() {
      this.dialogVisible = false
    },
    closePirvateDialog() {
      this.privateDialogVisible = false
    },
    confirmData() {
      this.$refs['dialogGroupForm'].validate(valid => {
        if (valid) {
          const param = JSON.parse(JSON.stringify(this.dialogData))
          if (this.dialogStatus == 'create') {
            createdDing(param).then(response => {
              this.getDingConfigList()
              this.$notify({
                title: 'Success',
                message: 'Add Successfully',
                type: 'success',
                duration: 2000
              })
              this.dialogVisible = false
            })
          } else {
            updatedDing(param).then(response => {
              this.getDingConfigList()
              this.$notify({
                title: 'Success',
                message: 'Update Successfully',
                type: 'success',
                duration: 2000
              })
              this.dialogVisible = false
            })
          }
        }
      })
    },
    confirmPrivateData() {
      this.$refs['dialogSingleFrom'].validate(valid => {
        if (valid) {
          const param = JSON.parse(JSON.stringify(this.privateDialogData))
          if (this.privateDialogStatus == 'create') {
            createdPrivateDing(param).then(response => {
              this.getDingPrivateConfigList()
              this.$notify({
                title: 'Success',
                message: 'Add Successfully',
                type: 'success',
                duration: 2000
              })
              this.privateDialogVisible = false
            })
          } else {
            updatedPrivateDing(param).then(response => {
              this.getDingPrivateConfigList()
              this.$notify({
                title: 'Success',
                message: 'Update Successfully',
                type: 'success',
                duration: 2000
              })
              this.privateDialogVisible = false
            })
          }
        }
      })
    },
    deleteConfig(row) {
      const idList = []
      idList.push(row.id)
      deletedDing({ idList: row.id }).then(response => {
        this.getDingConfigList()
        this.$notify({
          title: 'Success',
          message: 'Delete Successfully',
          type: 'success',
          duration: 2000
        })
      })
    },
    // 删除单聊配置
    deletePrivateConfig(row) {
      const idList = []
      idList.push(row.id)
      this.$modal.confirm('是否确认删除编号为"' + idList + '"的数据项？').then(function() {
        return deletedPrivateDing({ idList: row.id });
      }).then(() => {
        this.getDingPrivateConfigList()
        this.$notify({
          title: 'Success',
          message: 'Delete Successfully',
          type: 'success',
          duration: 2000
        })
      }).catch(() => {});
    },
  }
};
</script>

<style scoped>
/* 可以在这里添加CSS样式 */
</style>
