<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.sendFrom"
        clearable
        placeholder="发送人"
        style="width: 200px;"
        class="filter-item"
      />
      <el-select v-model="listQuery.viewStatus" placeholder="消息查看状态" class="filter-item" clearable>
        <el-option
          v-for="dict in dict.type.view_status"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>
      <el-input
        v-model="listQuery.msgSubject"
        clearable
        placeholder="消息主题"
        style="width: 200px;"
        class="filter-item"
      />
      <el-input
        v-model="listQuery.msgContent"
        clearable
        placeholder="消息内容"
        style="width: 200px;"
        class="filter-item"
      />

      <el-select v-model="listQuery.msgType" placeholder="消息类型" class="filter-item" clearable>
        <el-option
          v-for="dict in dict.type.msg_type"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>

      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="fetchData">
        搜索
      </el-button>
      <el-button v-waves class="filter-item" type="success" icon="el-icon-brush" @click="oneClickRead">
        一键已读
      </el-button>
    </div>

    <el-table ref="myTable"
      v-loading="listLoading"
      :data="formDataList"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
      stripe
      :height="tableHeight"
    >
      <el-table-column align="center" label="消息标题" prop="msgSubject" min-width="300px"></el-table-column>
      <el-table-column align="center" label="消息内容" prop="msgContent" min-width="700px"></el-table-column>
      <el-table-column align="center" label="消息来源" prop="msgSource" width="120px"></el-table-column>
<!--      <el-table-column align="center" label="发送人" prop="sendFrom"></el-table-column>-->
<!--      <el-table-column align="center" label="发送人" prop="sendFromName"></el-table-column>-->
      <el-table-column align="center" label="发送日期" prop="createTime" min-width="160px"></el-table-column>
      <el-table-column align="center" label="查看状态" prop="viewStatus" fixed="right" width="75px">
        <template slot-scope="scope">
          <!--     查看状态[view_status] 0未读、1已读     -->
          <dict-tag :options="dict.type.view_status" :value="scope.row.viewStatus"/>
        </template>
      </el-table-column>
      <el-table-column align="center" label="消息类型" fixed="right" width="75px">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.msg_type" :value="scope.row.msgType"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right" min-width="150px">
        <template slot-scope="{ row }">
          <el-button size="mini" type="text" @click="showDialog(row)">
            查看
          </el-button>
          <el-button size="mini" type="text" @click="confirmDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNum"
      :limit.sync="listQuery.pageSize"
      @pagination="fetchData"
      class="pagination"
    />

    <!-- 新增的 Dialog -->
    <el-dialog :visible.sync="dialogVisible" title="消息详情">
      <el-form label-width="100px" :model="dialogData" status-icon>
        <el-row>
          <el-col :span="12">
            <el-form-item label="消息来源">
              <el-input v-model="dialogData.msgSource" readonly></el-input>
            </el-form-item>
            <el-form-item label="消息主题">
              <el-input v-model="dialogData.msgSubject" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发送日期">
              <el-input v-model="dialogData.createTime" readonly></el-input>
            </el-form-item>
            <el-form-item label="消息类型">
              <dict-tag :options="dict.type.msg_type" :value="dialogData.msgType"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="消息内容">
          <el-input
            type="textarea"
            v-model="dialogData.msgContent"
            readonly
            rows="4"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" type="dialog-footer">
        <el-button type="primary" @click="closeDialog()">
          关闭
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import waves from '@/directive/waves'
export default {
  name: 'JobProject',
  components: { Pagination },
  directives: { waves },
  dicts: ['msg_type', 'view_status', 'msg_status'],
  data() {
    return {
      tableHeight: '330px',
      dialogVisible: false,
      formDataList: [],
      listLoading: true,
      total: 0,
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        msgSubject: '',
        msgContent: '',
        sendFrom: '',
        msgType: null,
        viewStatus: '0'
      },
      dialogData: {
        id: undefined,
        msgSubject: '',
        msgContent: '',
        msgSource: '',
        msgType: null,
        viewStatus: null,
        createTime: null
      }
    }
  },
  watch: {
    dialogVisible(newVal) {
      if (!newVal) {
        this.fetchData()
      }
    }
  },
  created() {
    this.fetchData()

  },
  mounted() {
    // 初始化动态计算高度 TODO：搜索条件收缩时需要监听和计算。
    this.dynamicTableHeight();
  },
  methods: {
    fetchData() {
      this.listLoading = true
      list.page(this.listQuery).then(response => {
        this.formDataList = response.rows
        this.total = response.total
        this.listLoading = false
      }).catch(error => {
        console.error(error)
        this.listLoading = false
      })
    },
    // 展示
    showDialog(row) {
      this.dialogVisible = true
      this.dialogData = row
      list.update(row)
    },
    closeDialog() {
      this.dialogVisible = false
    },
    confirmDelete(row) {
      this.$confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 用户点击确定按钮时执行删除操作
        this.deleteMsg(row);
      }).catch(() => {
        // 用户点击取消按钮时的操作，可以不做任何处理或关闭模态框
      });
    },
    deleteMsg(row) {
      const idList = []
      idList.push(row.id)
      list.deleted({ idList: row.id }).then(response => {
        this.fetchData()
        this.$notify({
          title: 'Success',
          message: 'Delete Successfully',
          type: 'success',
          duration: 2000
        })
      })
    },

    dynamicTableHeight() {
      //挂载window.onresize事件(动态设置table高度)
      let _this = this
      window.onresize = () => {
        if (_this.resizeFlag) {
          clearTimeout(_this.resizeFlag)
        }
        _this.resizeFlag = setTimeout(() => {
          _this.getTableHeight()
          _this.resizeFlag = null
        }, 100)
      }
      // 监听滚动条的位置
      this.$refs.myTable.bodyWrapper.addEventListener(
          'scroll',
          (res) => {
            this.scrollTop = res.target.scrollTop
          },
          false
      )
      this.$nextTick(() => {
        // this.getList()

        this.getTableHeight()
      })
    },
    getTableHeight() {
      // https://blog.csdn.net/qq_40603125/article/details/145773702
      const tableElement = this.$refs.myTable.$el // 获取el-table的根DOM元素
      const tableTop = tableElement.getBoundingClientRect().top // 获取距离视窗顶部的距离
      this.tableHeight = window.innerHeight - tableTop - 75
    },
    oneClickRead() {
      oneClickRead().then(response => {
        if (response.code === 200) {
          this.fetchData()
          this.$notify({
            title: 'Success',
            message: response.msg,
            type: 'success',
            duration: 2000
          })
        }
      })
    }
  }
}
import Pagination from '@/components/Pagination/index.vue'

import * as list from "@/api/message/list/list";
import { oneClickRead } from '@/api/message/list/list'
</script>

<style lang="scss" scoped>
</style>
