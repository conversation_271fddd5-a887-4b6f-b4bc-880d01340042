<template>
  <div class="app-container">
    <div ref="queryFormRef" class="filter-container">
      <el-input
        v-model="listQuery.sendFromEmail"
        clearable
        placeholder="发件人邮箱"
        style="width: 200px;"
        class="filter-item"
      />
      <el-input
        v-model="listQuery.sendToEmail"
        clearable
        placeholder="收件人邮箱"
        style="width: 200px;"
        class="filter-item"
      />
      <el-input
        v-model="listQuery.subject"
        clearable
        placeholder="邮件主题"
        style="width: 200px;"
        class="filter-item"
      />
      <el-input
        v-model="listQuery.content"
        clearable
        placeholder="邮件内容"
        style="width: 200px;"
        class="filter-item"
      />

      <el-select v-model="listQuery.type" placeholder="消息类型" class="filter-item" clearable>
        <el-option
          v-for="dict in dict.type.msg_type"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>

      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="fetchData">
        搜索
      </el-button>
    </div>

    <el-table
      ref="fullHeightTableRef"
      :height="tableHeight"
      v-loading="listLoading"
      :data="formDataList"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="邮件主题" prop="subject"></el-table-column>
      <el-table-column align="center" label="邮件内容" prop="content"></el-table-column>
      <el-table-column align="center" label="收件人邮箱" prop="sendToEmail"></el-table-column>
      <el-table-column align="center" label="发件人邮箱" prop="sendFromEmail"></el-table-column>
      <el-table-column align="center" label="发送日期" prop="createTime"></el-table-column>
      <el-table-column align="center" label="消息类型" fixed="right">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.msg_type" :value="scope.row.type"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right">
        <template slot-scope="{ row }">
          <el-button size="mini" type="text" @click="deleteMsg(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNum"
      :limit.sync="listQuery.pageSize"
      @pagination="fetchData"
      class="pagination"
    />
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination/index.vue'
import * as list from "@/api/message/email/email";

export default {
  mixins: [tableFullHeight],
  name: 'JobProject',
  components: { Pagination },
  directives: { waves },
  dicts: ['msg_type', 'view_status', 'msg_status'],
  data() {
    return {
      formDataList: [],
      msgTypeDict: [],
      viewStatusDict: [],
      listLoading: true,
      total: 0,
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        sendFromEmail: '',
        sendToEmail: '',
        subject: '',
        content: '',
        type: null
      }
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      this.listLoading = true
      console.log(this.listQuery)
      list.page(this.listQuery).then(response => {
        this.formDataList = response.rows
        this.total = response.total
        this.listLoading = false
      }).catch(error => {
        console.error(error)
        this.listLoading = false
      })
    },
    deleteMsg(row) {
      const idList = []
      idList.push(row.id)
      list.deleted({ idList: row.id }).then(response => {
        this.fetchData()
        this.$notify({
          title: 'Success',
          message: 'Delete Successfully',
          type: 'success',
          duration: 2000
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
