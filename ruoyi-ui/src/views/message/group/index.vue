<template>
  <div class="app-container">
    <div ref="queryFormRef" class="filter-container">
      <el-select v-model="listQuery.type" placeholder="提醒组类型" style="width: 200px;" class="filter-item" filterable clearable>
        <el-option
          v-for="dict in options.messageGroupTypeOptions"
          :key="dict.dictValue"
          :label="dict.dictLabel"
          :value="dict.dictValue"
        />
      </el-select>
      <el-input
        v-model="listQuery.name"
        clearable
        placeholder="提醒组名称"
        style="width: 200px;"
        class="filter-item"
      />
      <el-input
        v-model="listQuery.number"
        clearable
        placeholder="提醒组编码"
        style="width: 200px;"
        class="filter-item"
      />

      <el-button v-waves class="filter-item" size="small" type="primary" icon="el-icon-search" @click="fetchData">
        搜索
      </el-button>
      <el-button v-waves class="filter-item" size="small" type="primary" icon="el-icon-search" @click="addShowDialog">
        新增
      </el-button>
    </div>

    <el-table
      ref="fullHeightTableRef"
      :height="tableHeight"
      v-loading="listLoading"
      :data="formDataList"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="提醒组类型" prop="type">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.message_group_type" :value="scope.row.type"/>
        </template>
      </el-table-column>
      <el-table-column align="center" label="提醒组名称" prop="name"></el-table-column>
      <el-table-column align="center" label="提醒组编码" prop="number"></el-table-column>
      <el-table-column align="center" label="创建时间" prop="createTime"></el-table-column>
      <el-table-column align="center" label="创建人" prop="createBy"></el-table-column>
      <el-table-column label="操作" align="center" fixed="right">
        <template slot-scope="{ row }">
          <el-button size="mini" type="text" @click="updateShowDialog(row)">
            修改
          </el-button>
          <el-button size="mini" type="text" @click="handleDetail(row)">
            配置
          </el-button>
          <el-button size="mini" type="text" @click="deleteMsg(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNum"
      :limit.sync="listQuery.pageSize"
      @pagination="fetchData"
      class="pagination"
    />

    <!-- 新增的 Dialog -->
    <el-dialog :visible.sync="dialogVisible" title="消息提醒组">
      <el-form label-width="100px" ref="noticeForm" :model="dialogData" :rules="rules" status-icon>
        <el-row>
          <el-col :span="12">
            <el-form-item label="指标类型" prop="targetType">
              <el-select v-model="dialogData.type" style="width: 100%" clearable>
                <el-option
                  v-for="dict in options.messageGroupTypeOptions"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="提醒组名称" prop="name">
              <el-input v-model="dialogData.name"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="提醒组编码" prop="number">
              <el-input v-model="dialogData.number"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" type="dialog-footer">
        <el-button type="danger" @click="closeDialog()">
          关闭
        </el-button>
        <el-button type="primary" @click="confirmData()">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination/index.vue'
import * as group from "@/api/message/group/group";
import { allUserList } from '@/api/system/user'
import { selectDataList, selectPrivateDingList } from '@/api/message/config/config'

export default {
  mixins: [tableFullHeight],
  name: 'JobProject',
  components: { Pagination },
  directives: { waves },
  dicts: ['message_group_type', 'msg_type', 'view_status', 'msg_status', 'template_enable', 'message_channel'],
  data() {
    return {
      dialogStatus: '',
      dialogVisible: false,
      formDataList: [],
      listLoading: true,
      total: 0,
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        name: '',
        number: ''
      },
      dialogData: {
        id: undefined,
        name: '',
        number: '',
        type: null,
      },
      dingChatWay: [
        { key: 'single', value: 'single', label: '单聊' },
        { key: 'group', value: 'group', label: '群聊' }
      ],
      sysUserList: [],
      configList: [],
      templateList: [],
      isTemplateLocked: false,
      rules: {
        name: [
          { required: true, message: '请填写提醒组名称', trigger: 'blur' }
        ],
        number: [
          { required: true, message: '请填写提醒组编码', trigger: 'blur' }
        ]
      },
      options: {
        messageGroupTypeOptions: []
      }
    }
  },
  created() {
    this.fetchData()
    this.getAllUserList()
    this.getDicts("message_group_type").then(response => {
      this.options.messageGroupTypeOptions = response.data;
    });
  },
  methods: {
    resetTemp() {
      this.dialogData = {
        id: undefined,
        templateName: '',
        templateTitle: '',
        templateContent: '',
        templateChannel: '',
        remark: '',
        enable: '',
        msgType: ''
      }
    },
    addShowDialog() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogVisible = true
      this.isTemplateLocked = true
    },
    fetchData() {
      this.listLoading = true
      group.page(this.listQuery).then(response => {
        this.formDataList = response.rows
        this.total = response.total
        this.listLoading = false
      }).catch(error => {
        console.error(error)
        this.listLoading = false
      })
    },
    // 展示
    updateShowDialog(row) {
      this.dialogVisible = true
      this.dialogStatus = 'update'
      this.dialogData = JSON.parse(JSON.stringify(row))
    },
    closeDialog() {
      this.dialogVisible = false
    },
    deleteMsg(row) {
      const idList = []
      idList.push(row.id)
      this.$modal.confirm('是否确认删除编号为"' + idList + '"的数据项？').then(function () {
        return group.deleted({idList: row.id});
      }).then(() => {
        this.fetchData()
        this.$notify({
          title: 'Success',
          message: 'Delete Successfully',
          type: 'success',
          duration: 2000
        })
      }).catch(() => {
      });
    },
    confirmData() {
      this.$refs['noticeForm'].validate(valid => {
        if (valid) {
          const param = JSON.parse(JSON.stringify(this.dialogData))
          if (this.dialogStatus == 'create') {
            group.add(param).then(response => {
              if (response.code === 200) {
                this.fetchData()
                this.$notify({
                  title: 'Success',
                  message: 'Add Successfully',
                  type: 'success',
                  duration: 2000
                })
                this.dialogVisible = false
              } else {
                this.$notify({
                  title: 'Error',
                  message: 'Add Failed ' + response.message,
                  type: 'error',
                  duration: 4000
                })
              }
            })
          } else {
            group.update(param).then(response => {
              if (response.code === 200) {
                this.fetchData()
                this.$notify({
                  title: 'Success',
                  message: 'Add Successfully',
                  type: 'success',
                  duration: 2000
                })
                this.dialogVisible = false
              } else {
                this.$notify({
                  title: 'Error',
                  message: 'Add Failed ' + response.message,
                  type: 'error',
                  duration: 4000
                })
              }
            })
          }
        }
      })
    },
    getAllUserList() {
      const params = {}
      allUserList(params).then(result => {
        this.sysUserList = result.data
      })
    },
    // 获取邮件配置
    getEmailConfig() {
      selectDataList().then(result => {
        this.configList = result.data
      })
    },
    // 获取钉钉单聊配置
    getDingPrivateConfig() {
      selectPrivateDingList().then(result => {
        this.configList = result.data
      })
    },
    handleDetail(row) {
      this.$router.push({ path: '/message/group-detail', query: { groupId: row.id, groupName: row.name }})
    },
  }
}
</script>

<style lang="scss" scoped>
.table-content {
  max-height: 50px; /* 设置最大高度 */
  overflow: hidden; /* 溢出隐藏 */
  text-overflow: ellipsis; /* 显示省略号 */
  white-space: nowrap; /* 不换行 */
}
</style>
