<template>
  <el-dialog title="生成报告" :visible="dialogVisible" width="600px" @close="handleClose">
    <div style="font-size: 18px; margin-bottom: 24px;">您即将生成的报告模板名称为：<b>{{ templateName }}</b></div>
    <el-form ref="downloadConfigRef" :model="downloadConfigFormData" :rules="downloadConfigFormRules" label-width="100px" >
      <el-form-item label="维度字段" v-if="dimensionalityStatus" prop="dimensionField">
        <el-select v-model="downloadConfigFormData.dimensionField" placeholder="请选择数据来源" style="width: 260px;">
          <el-option v-for="item in dimensionOptions" :label="item.label" :value="item.value"
            :key="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="时间" v-if="dateTimeColumnStatus" prop="dateRange">
        <el-date-picker v-model="downloadConfigFormData.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" style="width: 260px;" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { generateDocument, queryDimensionData } from "@/api/document/configure/api"
export default {
  name: 'DownloadTemplateDialog',
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    templateName: {
      type: String,
      default: ''
    },
    templateId: {
      type: Number,
      default: null
    },
    dateTimeColumnStatus: {
      type: Boolean,
      default: false
    },
    dimensionalityStatus: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      downloadConfigFormData: {
        dimensionField: null,
        dateRange: []
      },
      downloadConfigFormRules: {
        dimensionField: [
          { required: true, message: '请选择维度字段', trigger: 'change' }
        ],
        dateRange: [
          { required: true, message: '请选择时间范围', trigger: 'change' }
        ]
      },
      dimensionOptions: []
    }
  },
  watch: {
    visible: {
      immediate: true,
      handler(val) {
        this.dialogVisible = val
        if (val) {
          this.handleQueryDimensionData()
          this.resetForm()
        }
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
    },
    resetForm() {
      if (this.$refs.downloadConfigRef) {
        this.$refs.downloadConfigRef.resetFields()
      } else {
        this.downloadConfigFormData = {
          dimensionField: null,
          dateRange: []
        }
      }
    },
    async handleQueryDimensionData() {
      const result = await queryDimensionData(this.templateId)
      if (result.code == 200) {
        this.dimensionOptions = result.data.map(item => {
          return {
            ...item,
            label: item.dataName,
            value: item.dataCode
          }
        })
      }
    },
    async handleConfirm() {
      this.$refs.downloadConfigRef.validate(async valid => {
        if (valid) {
          const startTime = this.downloadConfigFormData.dateRange[0] ? this.downloadConfigFormData.dateRange[0] : null
          const endTime = this.downloadConfigFormData.dateRange[1] ? this.downloadConfigFormData.dateRange[1] : null
          const requestParams = {
            configureId: this.templateId,
            dimensionField: this.downloadConfigFormData.dimensionField,
            startTime: startTime,
            endTime: endTime,
          }
          this.download("/document/configure/generateDocument", requestParams, `${this.templateName}.docx`)
          this.handleClose()
        } else {
          return false
        }
      })
    }
  }
}
</script>
