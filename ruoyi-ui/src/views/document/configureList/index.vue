<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryFormRef" label-width="auto" inline>
      <el-form-item label="模板名称">
        <el-input v-model="queryParams.templateName" placeholder="模板名称" clearable />
      </el-form-item>
      <el-form-item label="模板类别">
        <el-input v-model="queryParams.templateType" placeholder="模板类别" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="small" icon="el-icon-search" @click="handleQuery">查询</el-button>
        <el-button type="info" size="small" icon="el-icon-refresh" @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="table-operations">
      <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">新建模板</el-button>
    </div>
    <el-table ref="fullHeightTableRef" :height="tableHeight" v-loading="loading" :data="configureList" stripe border>
      <el-table-column label="编号" width="80px" align="center" prop="id" />
      <el-table-column label="模板名称" align="center" prop="templateName" />
      <el-table-column label="模板类别" align="center" prop="templateType" />
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column label="更新时间" align="center" prop="updateTime" />
      <el-table-column label="操作" align="center" width="350">
        <template slot-scope="scope">
          <el-button @click="handleClickEdit(scope.row)" type="primary" size="mini">编辑</el-button>
          <el-button type="success" size="mini" @click="handleDownloadReport(scope.row)">下载报告</el-button>
          <el-button type="info" size="mini" @click="handleExportRecords(scope.row)">导出记录</el-button>
          <el-button type="danger" size="mini" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination style="text-align: right;" background layout="total, prev, pager, next, sizes, jumper" :total="total"
      :page-size="queryParams.pageSize" :current-page="queryParams.pageNum" @current-change="handlePageChange"
      @size-change="handleSizeChange" />
    <download-template-dialog :visible.sync="showDownloadDialog" :date-time-column-status="dateTimeColumnStatus" :dimensionality-status="dimensionalityStatus" :template-id="currentTemplateId" :template-name="currentTemplateName" />
    <export-records-dialog :visible.sync="showExportRecordsDialog" :template-id="currentTemplateId"
      :template-name="currentTemplateName" />
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import { listConfigure, delConfigure } from "@/api/document/configureList/api";
import DownloadTemplateDialog from './DownloadTemplateDialog.vue';
import ExportRecordsDialog from './ExportRecordsDialog.vue';

export default {
  name: "ConfigureList",
  components: { DownloadTemplateDialog, ExportRecordsDialog },
  mixins: [tableFullHeight],
  data() {
    return {
      loading: false,
      configureList: [],
      total: 0,
      queryParams: {
        id: this.$route.query.id ? Number(this.$route.query.id) : null,
        templateName: '',
        templateType: '',
        pageNum: 1,
        pageSize: 10
      },
      showDownloadDialog: false,
      showExportRecordsDialog: false,
      currentTemplateName: '',
      currentTemplateId: null,
      dateTimeColumnStatus:false,
      dimensionalityStatus:false
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      listConfigure(this.queryParams)
        .then(response => {
          this.configureList = response.rows;
          this.total = response.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 重置
    handleReset() {
      this.queryParams = {
        templateName: '',
        templateType: '',
        pageNum: 1,
        pageSize: 10
      };
      this.getList();
    },
    handleAdd() {
      this.$router.push("/document/configure")
    },
    handleClickEdit(row) {
      this.$router.push({
        path: "/document/configure",
        query: {
          id: row.id
        }
      })
    },
    handleDownloadReport(row) {
      this.dateTimeColumnStatus = row.dateTimeColumnStatus;
      this.dimensionalityStatus = row.dimensionalityStatus;
      this.currentTemplateId = row.id;
      this.currentTemplateName = row.templateName;
      this.showDownloadDialog = true;
    },
    handleExportRecords(row) {
      this.currentTemplateId = row.id;
      this.currentTemplateName = row.templateName;
      this.showExportRecordsDialog = true;
    },

    handleDelete(row) {
      this.$confirm(`确认删除【${row.templateName}】该模板吗?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delConfigure(row.id).then(response => {
          if (response.code === 200) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
            this.getList();
          }
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    handlePageChange(page) {
      this.queryParams.pageNum = page;
      this.getList();
    },
    handleSizeChange(size) {
      this.queryParams.pageSize = size;
      this.queryParams.pageNum = 1;
      this.getList();
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  position: relative;

  .el-button+.el-button {
    margin-left: 8px;
  }
}
</style>
