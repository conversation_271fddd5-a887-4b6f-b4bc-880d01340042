<template>
  <el-dialog title="导出记录" :visible="dialogVisible" width="900px" append-to-body @close="handleClose">
    <div class="export-records-container">
      <el-table :data="recordsList" stripe border v-loading="loading">
        <el-table-column label="文件名称" align="center" prop="fileName" />
        <el-table-column label="生成状态" align="center" prop="downloadStatus" >
          <template slot-scope="scope">
            <span>{{ downloadStatusOptions.find(item => item.value === scope.row.downloadStatus).label || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="下载时间" align="center" prop="downloadTime" />
        <el-table-column label="下载人" align="center" prop="downloadUser" />
        <!-- <el-table-column label="下载参数" align="center" prop="downloadParams" /> -->
        <el-table-column label="操作" align="center" width="120">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" @click="handleDownload(scope.row)">下载</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- <el-pagination style="text-align: right; margin-top: 15px" background
        layout="total, prev, pager, next, sizes, jumper" :total="total" :page-size="queryParams.pageSize"
        :current-page="queryParams.pageNum" @current-change="handlePageChange" @size-change="handleSizeChange" /> -->
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleClose">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { queryDownloadRecord } from "@/api/document/configureList/api";

export default {
  name: "ExportRecordsDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    templateId: {
      type: [String, Number],
      default: ""
    },
    templateName: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      recordsList: [],
      total: 0,
      queryParams: {
        templateId: undefined,
        pageNum: 1,
        pageSize: 10
      }
    };
  },
  watch: {
    visible: {
      immediate: true,
      handler(val) {
        this.dialogVisible = val;
        if (val) {
          this.queryParams.templateId = this.templateId;
          this.getRecordsList();
        }
      }
    }
  },
  mounted() {
    this.getDicts('document_download_status').then(response => {
      if (response.code === 200) {
        this.downloadStatusOptions = response.data.map(this.buildOption)
      }
    })
  },
  methods: {
    buildOption(item) {
      return {
        ...item,
        label: item.dictLabel,
        value: item.dictValue
      }
    },
    handleClose() {
      this.$emit('update:visible', false);
    },
    async getRecordsList() {
      this.loading = true;
      const result = await queryDownloadRecord(this.queryParams.templateId)
      if (result.code === 200) {
        this.recordsList = result.data;
        this.total = result.total;
      }
      this.loading = false;
    },
    handlePageChange(page) {
      this.queryParams.pageNum = page;
      this.getRecordsList();
    },
    handleSizeChange(size) {
      this.queryParams.pageSize = size;
      this.queryParams.pageNum = 1;
      this.getRecordsList();
    },
    async handleDownload(row) {
      this.download("/document/record/downloadFile", row, row.fileName)
    }
  }
};
</script>

<style lang="scss" scoped>
.export-records-container {
  .el-table {
    margin-bottom: 15px;
  }
}
</style>
