<template>
  <div>
    <el-dialog title="表格配置" :visible.sync="dialogVisible" width="90%">
      <el-form ref="tableConfigFormRef" :model="formData">
        <el-form-item>
          <div class="table-operations" style="margin-bottom: 10px;">
            <el-button type="success" size="mini" @click="addRow">新增</el-button>
            <el-button type="primary" size="mini" @click="importData">导入</el-button>
            <el-button type="danger" size="mini" @click="exportData">导出</el-button>
          </div>
          <el-table class="table-draggable-table" row-key="id" :data="formData.visibleRows" border style="margin-bottom: 32px;">
            <el-table-column label="字段编码" align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.fieldCode" placeholder="字段编码" clearable />
              </template>
            </el-table-column>
            <el-table-column label="字段名称" align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.fieldName" placeholder="字段名称" clearable />
              </template>
            </el-table-column>
            <el-table-column label="宽度" align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.width" placeholder="宽度" clearable />
              </template>
            </el-table-column>
            <el-table-column label="字体" align="center">
              <template slot-scope="scope">
                <el-select v-model="scope.row.font" placeholder="请选择字体" clearable>
                  <el-option v-for="item in fontTypeOptions" :key="item.value" :label="item.label"
                    :value="item.value" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="字号" align="center">
              <template slot-scope="scope">
                <el-select v-model="scope.row.wordSize" placeholder="请选择字号" clearable>
                  <el-option v-for="item in wordSizeOptions" :key="item.value" :label="item.label"
                    :value="item.value" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="120">
              <template slot-scope="scope">
                <el-button type="danger" size="mini" @click="removeRow(scope.row, scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="importCorrelation.dialogVisible" width="600px" :close-on-click-modal="false"
      :show-close="true" append-to-body :destroy-on-close="true" :modal-append-to-body="false" :center="false"
      title="文件上传" :loading="importCorrelation.uploading">
      <div style="margin-bottom: 30px;">
        <el-upload class="upload-demo" action="#" drag :auto-upload="false" :show-file-list="true" :limit="1"
          :on-change="handleFileChange" :on-remove="handleFileRemove" :file-list="importCorrelation.fileList">
          <i class="el-icon-upload" style="font-size: 48px;"></i>
          <div class="el-upload__text" style="font-size: 18px; margin-top: 10px;">
            将文件拖到此处，或 <em>点击上传</em>
          </div>
        </el-upload>
        <div style="margin-top: 8px; color: #888; font-size: 14px;">
          仅允许导入xls、xlsx格式文件。
          <el-link type="primary" @click.prevent="downloadTemplate">下载模板</el-link>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="importCancel">取 消</el-button>
        <el-button type="primary" @click="importConfirm" :loading="importCorrelation.uploading">确 定</el-button>
      </span>
    </el-dialog>
  </div>


</template>
<script>
import Sortable from 'sortablejs'
// import DbmlSelector from './DbmlSelector.vue'
import { uploadTableConfigure } from '@/api/document/configure/api'
export default {
  name: 'TableConfigDialog',
  // components: { DbmlSelector },
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    tableConfig: {
      type: Object,
      // required: true
      default: () => ({})
    }
  },
  data() {
    return {
      importCorrelation: {
        dialogVisible: false,
        updateExist: false,
        file: null,
        fileList: [],
        uploading: false
      },
      dialogVisible: false,
      wordSizeOptions: [],
      fontTypeOptions: [],
      formData: {
        selectedDatasource: '',
        visibleRows: [],
        deletedRows: []
      },
    }
  },
  computed: {
    sortedTableConfig() {
      const deletedRows = this.formData.deletedRows.map((item) => ({
        ...item,
        tableOrder: 0
      }))
      const visibleRows = this.formData.visibleRows.map((item, index) => ({
        ...item,
        tableOrder: index + 1
      }))
      return [...deletedRows, ...visibleRows];
    }
  },
  watch: {
    visible: {
      immediate: true,
      handler(val) {
        this.dialogVisible = val;
        if (val) {
          this.$nextTick(() => {
            this.rowDrag();
          });
        }
      }
    },
    dialogVisible(val) {
      // 当内部dialogVisible变化时，通知父组件更新visible
      if (val !== this.visible) {
        this.$emit('update:visible', val);
      }
    },
    tableConfig: {
      handler(newVal) {
        if (newVal && newVal.tableConfigure && newVal.tableConfigure.length) {
          this.formData.visibleRows = newVal.tableConfigure.filter(item => item.delFlag != 1);
          this.formData.deletedRows = newVal.tableConfigure.filter(item => item.delFlag == 1);
        } else {
          this.formData.visibleRows = [
            { fieldCode: '', fieldName: '', width: '', font: '', wordSize: '' }
          ];
          this.formData.deletedRows = [];
        }
      },
      deep: true
    }
  },
  mounted() {
    this.getDicts('document_word_size').then(response => {
      if (response.code === 200) {
        this.wordSizeOptions = response.data.map(this.buildOption)
      }
    })
    this.getDicts('document_font_type').then(response => {
      if (response.code === 200) {
        this.fontTypeOptions = response.data.map(this.buildOption)
      }
    })
  },
  methods: {
    rowDrag() {
      const el = document.querySelectorAll(
        ".table-draggable-table .el-table__body-wrapper > table > tbody"
      )[0];
      Sortable.create(el, {
        disabled: false,
        ghostClass: "sortable-ghost",
        animation: 150,
        onEnd: (e) => {
          const visibleRows = this.formData.visibleRows;
          const movedItem = visibleRows[e.oldIndex];
          visibleRows.splice(e.oldIndex, 1);
          visibleRows.splice(e.newIndex, 0, movedItem);
          this.formData.visibleRows = visibleRows;
        },
      });
    },
    buildOption(item) {
      return {
        ...item,
        label: item.dictLabel,
        value: item.dictValue
      }
    },
    addRow() {
      const id = Date.now()
      this.formData.visibleRows.splice(0, 0, { id, fieldCode: '', fieldName: '', width: '', font: '', wordSize: '' });
    },
    removeRow(row, idx) {
      if (row.id) {
        row.delFlag = 1;
        this.formData.deletedRows.push(row);
        this.formData.visibleRows = this.formData.visibleRows.filter(item => item.id !== row.id);
      } else {
        this.formData.visibleRows.splice(idx, 1);
      }
    },
    closeDialog() {
      this.dialogVisible = false;
    },
    handleConfirm() {
      this.dialogVisible = false;
      this.$emit('set-table-config', this.sortedTableConfig);
    },
    importConfirm() {
      if (!this.importCorrelation.file) {
        this.$message.warning('请先选择要导入的文件')
        return
      }
      this.importCorrelation.uploading = true
      const formData = new FormData()
      formData.append('file', this.importCorrelation.file.raw)
      uploadTableConfigure(formData).then(res => {
        if (res.code === 200) {
          res.data.forEach(item => {
            this.formData.visibleRows.push(item)
          })
        }
      }).finally(() => {
        this.importCancel()
      })
    },
    importCancel() {
      this.importCorrelation.dialogVisible = false
      this.importCorrelation.file = null
      this.importCorrelation.fileList = []
      this.importCorrelation.updateExist = false
      this.importCorrelation.uploading = false
    },
    importData() {
      this.importCorrelation.dialogVisible = true;
    },
    exportData() {
      this.download('/document/table/export', { "parameterId": this.tableConfig.id }, `表格配置导入模板.xlsx`)
    },
    downloadTemplate() {
      this.download('/document/table/templateDownload', {}, `表格配置导入模板.xlsx`)
    },
    handleFileChange(file) {
      this.importCorrelation.file = file
      this.importCorrelation.fileList = [file]
    },
    handleFileRemove(file) {
      this.importCorrelation.file = null
      this.importCorrelation.fileList = []
    },
  }
}
</script>

<style lang="scss" scoped>
.custom-select {
  display: inline-flex;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 0 12px;
  height: 36px;
  min-width: 220px;
  cursor: pointer;
  background: #fff;
  position: relative;
  transition: border-color .2s;

  &:hover {
    border-color: #409EFF;
  }
}

.custom-select__selected {
  flex: 1;
  color: #606266;
  font-size: 14px;

  &.is-placeholder {
    color: #c0c4cc;
  }
}

.custom-select__icon {
  font-size: 16px;
  color: #c0c4cc;
  margin-left: 8px;
}
</style>
