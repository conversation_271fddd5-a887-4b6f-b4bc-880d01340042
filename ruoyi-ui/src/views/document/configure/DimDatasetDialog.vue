<template>
  <el-dialog title="维度分区配置" :visible.sync="dialogVisible" width="700px">
    <el-button type="primary" @click="addRow">新建</el-button>
    <el-button type="primary" @click="showImportDialog = true">导入</el-button>
    <el-table :data="tableData.filter(item => item.delFlag !== 1)" border class="custom-table">
      <el-table-column label="中文名" prop="name" align="center">
        <template slot-scope="scope">
          <el-input v-model="scope.row.dataName" placeholder="请输入中文名" size="small" clearable />
        </template>
      </el-table-column>
      <el-table-column label="编码" prop="code" align="center">
        <template slot-scope="scope">
          <el-input v-model="scope.row.dataCode" placeholder="请输入编码" size="small" clearable />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="copyRow(scope.$index)">复制</el-button>
          <el-button type="danger" size="small" @click="removeRow(scope.row, scope.$index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </span>
    <!-- 导入模态框 -->
    <import-dialog :type="'dim'" :visible.sync="showImportDialog" @set-table-data="setTableData" />
  </el-dialog>
</template>

<script>
import ImportDialog from './components/ImportDialog.vue'

export default {
  name: 'DimDatasetDialog',
  components: {
    ImportDialog
  },
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    dimensionData: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      tableData: [
        { name: '', code: '' }
      ],
      showImportDialog: false,
      updateExist: false,
      importFile: null,
      dialogVisible: this.visible
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val;
      },
      immediate: true
    },
    dimensionData: {
      handler(newVal) {
        this.tableData = newVal
      },
      immediate: true
    }
  },
  methods: {
    addRow() {
      this.tableData.push({
        id: '',
        dataName: '',
        dataCode: '',
        configureId: '',
        tenantId: '',
        revision: '',
        delFlag: 0
      });
    },
    copyRow(index) {
      const row = { ...this.tableData[index] };
      this.tableData.splice(index + 1, 0, row);
    },
    removeRow(row, index) {
      if (row.id) {
        this.tableData = this.tableData.filter(item => item.delFlag !== 1)
        this.tableData[index].delFlag = 1;
      } else {
        this.tableData.splice(index, 1);
      }
    },
    setTableData(data) {
      // 将后端返回的 DocumentDimensionData 数组转换为前端需要的格式
      this.tableData = data.map(item => {
        return {
          id: item.id,
          delFlag: item.delFlag,
          dataName: item.dataName,
          dataCode: item.dataCode,
          configureId: item.configureId,
          tenantId: item.tenantId,
          revision: item.revision
        }
      });

      // 如果导入的数据为空，至少保留一行空数据
      if (this.tableData.length === 0) {
        this.tableData = [{ name: '', code: '' }];
      }
    },
    closeDialog() {
      this.$emit('update:visible', false);
    },
    handleConfirm() {
      this.$emit("set-dimTable-data", this.tableData)
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-table {
  margin-bottom: 10px;
}

.el-table th,
.el-table td {
  text-align: center;
}

.import-title {
  background: #ffe066;
  color: #333;
  font-size: 18px;
  font-weight: bold;
  padding: 10px 20px;
  border-radius: 4px;
  box-shadow: 2px 4px 8px #eee;
  display: inline-block;
}
</style>