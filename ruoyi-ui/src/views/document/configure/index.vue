<template>
    <div class="app-container">
        <el-form :model="configureFormData" :rules="configureFormRules" ref="configureFormRef" label-width="auto">
            <!-- 基础配置 -->
            <el-card class="box-card" style="margin-bottom: 20px;">
                <div slot="header" class="clearfix">
                    <span>基础配置</span>
                </div>

                <el-form-item label="模板名称" prop="templateName">
                    <el-input v-model="configureFormData.templateName" placeholder="请输入内容" clearable />
                </el-form-item>

                <el-form-item label="模板类别" prop="templateType">
                    <el-input v-model="configureFormData.templateType" placeholder="请输入内容" clearable />
                </el-form-item>

                <el-form-item label="word模板附件">
                    <div v-if="configureFormData.fileName" class="file-list">
                        <div class="file-item">
                            <i class="el-icon-document"></i>
                            <span class="file-name" @click="handleDownloadFile">{{ configureFormData.fileName }}</span>
                            <i class="el-icon-delete" @click="handleRemoveFile"></i>
                        </div>
                    </div>
                    <el-upload v-else class="upload-demo" :headers="headers" :action="uploadUrl" :show-file-list="false"
                        :on-success="handleUploadSuccess" :on-error="handleUploadError"
                        :before-upload="handleBeforeUpload" drag>
                        <i class="el-icon-upload"></i>
                        <div class="el-upload__text">
                            将文件拖到此处，或 <em>点击上传</em>
                        </div>
                        <div class="el-upload__tip" slot="tip">只能上传word文件（.doc/.docx）</div>
                    </el-upload>
                </el-form-item>

            </el-card>

            <!-- 生成配置 -->
            <el-card class="box-card" style="margin-bottom: 20px;">
                <div slot="header" class="clearfix">
                    <span>生成配置</span>
                </div>
                <el-form-item label="维度区分">
                    <el-switch v-model="configureFormData.dimension" />
                    <el-input v-if="configureFormData.dimension" v-model="configureFormData.dimensionField"
                        placeholder="字段编码（列）" style="width: 120px; margin-left: 10px;" clearable />
                    <el-input v-if="configureFormData.dimension" v-model="configureFormData.dimensionName"
                        placeholder="维度名称" style="width: 120px; margin-left: 10px;" clearable />
                    <el-button v-if="configureFormData.dimension" type="primary" size="mini" style="margin-left: 10px;"
                        @click="handleShowDimDialog">设置维度数据集</el-button>
                </el-form-item>
                <el-form-item label="时间">
                    <el-switch v-model="configureFormData.time" />
                    <el-input v-if="configureFormData.time" v-model="configureFormData.timeField" placeholder="字段名称"
                        style="width: 120px; margin-left: 10px;" clearable />
                </el-form-item>

            </el-card>

            <!-- 参数配置 -->
            <el-card class="box-card">
                <div slot="header" class="clearfix">
                    <span>参数配置</span>
                  <el-button type="primary" size="mini" style="float: right;"
                             @click="parameterExport">导出</el-button>

                    <el-button type="success" size="mini" style="float: right;"
                        @click="showImportDialog = true">导入</el-button>
                </div>
                <el-form-item label-width="0">
                    <div class="table-operations" style="margin-bottom: 10px;">
                        <el-button type="success" size="mini" @click="addRow">新增</el-button>
                    </div>
                    <el-table class="draggable-table" row-key="id" :data="configureFormData.parameter" border
                        style="width: 100%; margin-bottom: 10px;">
                        <el-table-column label="类型" width="120">
                            <template slot-scope="scope">
                                <el-form-item :prop="'parameter.' + scope.$index + '.configureType'"
                                    :rules="configureFormRules.configureType">
                                    <el-select v-model="scope.row.configureType" placeholder="请选择" clearable>
                                        <el-option v-for="item in parameterTypeOptions" :key="item.value"
                                            :label="item.label" :value="item.value" />
                                    </el-select>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column label="模板变量编码">
                            <template slot-scope="scope">
                                <el-form-item :prop="'parameter.' + scope.$index + '.templateVariableCode'"
                                    :rules="configureFormRules.templateVariableCode">
                                    <el-input v-model="scope.row.templateVariableCode" placeholder="模板变量编码" clearable />
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column label="word模板变量名称">
                            <template slot-scope="scope">
                                <el-form-item :prop="'parameter.' + scope.$index + '.templateVariableName'"
                                    :rules="configureFormRules.templateVariableName">
                                    <el-input v-model.trim="scope.row.templateVariableName" placeholder="word模板变量名称"
                                        clearable />
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column label="数据源" width="240">
                            <template slot-scope="scope">
                                <template v-if="scope.row.configureType !== 'chart'">
                                    <el-form-item :prop="'parameter.' + scope.$index + '.dataSource'"
                                        :rules="configureFormRules.dataSource">
                                        <DbmlSelector :model-value="scope.row.dataSource"
                                            :data-source-name.sync="scope.row.dataSourceName"
                                            @update:modelValue="(val) => handleParameterChange(scope.$index, 'dataSource', val)" />
                                    </el-form-item>
                                </template>
                            </template>
                        </el-table-column>
                        <el-table-column label="变量名">
                            <template slot-scope="scope">
                                <template
                                    v-if="scope.row.configureType !== 'table' && scope.row.configureType !== 'chart'">
                                    <el-form-item :prop="'parameter.' + scope.$index + '.enterVariableName'"
                                        :rules="configureFormRules.enterVariableName">
                                        <el-input v-model="scope.row.enterVariableName" placeholder="变量名" clearable />
                                    </el-form-item>
                                </template>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="380">
                            <template slot-scope="scope">
                                <el-button type="primary" size="mini" @click="copyRow(scope.$index)">复制</el-button>
                                <el-button type="danger" size="mini"
                                    @click="removeRow(scope.row, scope.$index)">删除</el-button>
                                <el-button v-if="scope.row.configureType === 'table'" type="primary" size="mini"
                                    style="margin-left: 10px;"
                                    @click="handleShowTableConfigDialog(scope.row, scope.$index)">表格配置</el-button>
                                <el-button v-if="scope.row.configureType === 'chart'" type="primary" size="mini"
                                    style="margin-left: 10px;"
                                    @click="handleShowChartConfigDialog(scope.row, scope.$index)">图表配置</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-form-item>
            </el-card>
        </el-form>

        <!-- 新增底部操作按钮 -->
        <div class="footer-btns">
            <el-button type="primary" @click="handleTestGenerate">测试模板生成</el-button>
            <el-button type="primary" style="margin-left: 60px;" @click="handleSave">保存</el-button>
        </div>

        <!-- 维度数据集弹窗 -->
        <DimDatasetDialog :visible.sync="showDimDialog" :dimension-data="currentDimData"
            @set-dimTable-data="handleSetDimTableData" />
        <!-- 图表内容弹窗 -->
        <ChartConfigDialog :visible.sync="showChartConfigDialog" :chart-config="currentChartConfig"
            @set-chart-config="handleSetChartConfig" />
        <!-- 表格内容弹窗 -->
        <TableConfigDialog :visible.sync="showTableConfigDialog" :table-config="currentTableConfig"
            @set-table-config="handleSetTableConfig" />
        <!-- 导入弹窗 -->
        <import-dialog :visible.sync="showImportDialog" :type="'param'" @set-table-data="setTableData" />

    </div>
</template>

<script>
import DimDatasetDialog from './DimDatasetDialog.vue'
import TableConfigDialog from './TableConfigDialog.vue'
import ChartConfigDialog from './ChartConfigDialog.vue'
import DbmlSelector from './DbmlSelector.vue'
import ImportDialog from './components/ImportDialog.vue'
import { getToken } from '@/utils/auth'
import { saveConfigure, getConfigure } from '@/api/document/configure/api'
import { updateAttachment } from "@/api/file/attachment"
import Sortable from "sortablejs";
import {deepClone} from "@/utils";
// 声明枚举
const FileStatus = {
    BOUND: 'BOUND',
    UNBOUND: 'UNBOUND'
}
export default {
    name: 'Configure',
    components: { DimDatasetDialog, TableConfigDialog, ChartConfigDialog, DbmlSelector, ImportDialog },
    data() {
        return {
            parameterTypeOptions: [],
            uploadUrl: process.env.VUE_APP_BASE_API + '/file/attachments/upload',
            configureFormData: {
                templateName: '',
                templateType: '',
                templateId: null,
                // 文件名称
                fileName: null,
                templateUrl: null,
                dimension: false,
                dimensionField: '',
                dimensionName: '',
                time: false,
                timeField: '',
                dimensionData: [],
                parameter: [
                    {
                        delFlag: 0,
                        configureType: 'text',
                        templateVariableCode: '',
                        templateVariableName: '',
                        dataSource: '',
                        enterVariableName: '',
                        chartConfigure: null,
                        tableConfigure: null
                    }
                ],
                deletedParameter: []
            },
            configureFormRules: {
                'templateName': [
                    { required: true, message: '请输入模板名称', trigger: 'blur' }
                ],
                'templateType': [
                    { required: true, message: '请输入模板类别', trigger: 'blur' }
                ],
                'configureType': [
                    {
                        required: true, validator: (rule, value, callback) => {
                            const currentValue = this.getValueByRule(rule)
                            if (!currentValue) {
                                callback(new Error('请选择类型'))
                            } else {
                                callback()
                            }
                        }, trigger: 'change'
                    }
                ],
                'templateVariableCode': [
                    // 自定义表单验证
                    {
                        required: true, validator: (rule, value, callback) => {
                            const currentValue = this.getValueByRule(rule)
                            if (!currentValue) {
                                callback(new Error('模板变量编码不能为空'))
                            } else {
                                callback()
                            }
                        }, trigger: 'blur'
                    }
                ],
                'templateVariableName': [
                    {
                        required: true, validator: (rule, value, callback) => {
                            const currentValue = this.getValueByRule(rule)
                            if (!currentValue) {
                                callback(new Error('word模板变量名称不能为空'))
                            } else {
                                callback()
                            }
                        }, trigger: 'blur'
                    }
                ],
                'dataSource': [
                    {
                        required: true, validator: (rule, value, callback) => {
                            const currentValue = this.getValueByRule(rule)
                            if (!currentValue) {
                                callback(new Error('数据源不能为空'))
                            } else {
                                callback()
                            }
                        }, trigger: 'change'
                    }
                ],
                'enterVariableName': [
                    {
                        required: true, validator: (rule, value, callback) => {
                            const currentValue = this.getValueByRule(rule)
                            if (!currentValue) {
                                callback(new Error('变量名不能为空'))
                            } else {
                                callback()
                            }
                        }, trigger: 'blur'
                    }
                ]
            },
            showDimDialog: false,
            currentDimData: [],
            showChartConfigDialog: false,
            currentChartConfig: null,
            currentChartIndex: null,
            showTableConfigDialog: false,
            currentTableConfig: null,
            currentTableIndex: null,
            showImportDialog: false,
            oldFileId: null
        }
    },
    computed: {
        headers() {
            return {
                Authorization: 'Bearer ' + getToken()
            }
        }
    },
    mounted() {
        this.handleGetConfigureDetail()
        this.rowDrag()
        this.getParameterTypeOptions()
    },
    methods: {
        getParameterTypeOptions() {
            this.getDicts('document_parameter_type').then(response => {
                if (response.code === 200) {
                    this.parameterTypeOptions = response.data.map(this.buildOption)
                }
            })
        },
        getValueByRule(rule) {
            const index = rule.field.split('.')[1]
            const fileName = rule.field.split('.')[2]
            const currentValue = this.configureFormData.parameter[index][fileName]
            return currentValue
        },
        buildOption(item) {
            return {
                ...item,
                label: item.dictLabel,
                value: item.dictValue
            }
        },
        handleShowDimDialog() {
            this.showDimDialog = true
            this.currentDimData = this.configureFormData.dimensionData
        },
        handleUploadSuccess(res) {
            this.configureFormData.fileName = res.data.name
            this.configureFormData.fileId = res.data.fileId
            this.configureFormData.templateId = res.data.id
            this.configureFormData.templateUrl = res.data.relativePath
        },
        handleUploadError(err) {
            this.$message.error('上传失败')
        },
        handleBeforeUpload(file) {
            // 修改文件格式校验，限制只能上传word文件
            const extension = file.name.split('.').pop().toLowerCase();
            const isWord = ['doc', 'docx'].includes(extension);
            if (!isWord) {
                this.$message.error('只能上传Word文件(.doc/.docx)');
                return false;
            }

            const isLt20M = file.size / 1024 / 1024 < 20;
            if (!isLt20M) {
                this.$message.error('上传文件大小不能超过20M!');
                return false;
            }
            return true;
        },
        addRow() {
            this.configureFormData.parameter.push({
                id: Date.now(),
                configureType: 'text',
                templateVariableCode: '',
                templateVariableName: '',
                dataSource: '',
                dataSourceName: '',
                enterVariableName: '',
                chartConfigure: null,
                tableConfigure: null
            });
        },
        copyRow(index) {
            const row = JSON.parse(JSON.stringify(this.configureFormData.parameter[index]))
            // const row = { ...this.configureFormData.parameter[index] };
            row.id = Date.now();
            if (row.chartConfigure) {
              row.chartConfigure.id = null;
              row.chartConfigure.parameterId = null;
            }
            if (row.tableConfigure) {
              for (let tableConfigureKey in row.tableConfigure) {
                row.tableConfigure[tableConfigureKey].id = null;
                row.tableConfigure[tableConfigureKey].parameterId = null;
              }
            }
            if (row.configureType === '文字') row.configureType = 'text';
            if (row.configureType === '表格') row.configureType = 'table';
            if (row.configureType === '图表') row.configureType = 'chart';
            this.configureFormData.parameter.splice(index + 1, 0, row);
        },
        removeRow(row, index) {
            if (String(row.id).length === 13) {
                this.configureFormData.parameter.splice(index, 1);
            } else {
                row.delFlag = 1;
                this.configureFormData.parameter = this.configureFormData.parameter.filter(item => item.delFlag !== 1)
                this.configureFormData.deletedParameter.push(row)
            }
        },
        setTableData(data) {
            // 将后端返回的 DocumentParameterDto 数组转换为前端需要的格式
            this.configureFormData.parameter = data.map(item => {
                return {
                    configureType: item.configureType,
                    templateVariableCode: item.templateVariableCode,
                    templateVariableName: item.templateVariableName,
                    dataSource: item.dataSource,
                    enterVariableName: item.enterVariableName,
                    chartConfigure: null,
                    tableConfigure: null
                }
            });

            // 如果导入的数据为空，至少保留一行空数据
            if (this.configureFormData.parameter.length === 0) {
                this.configureFormData.parameter = [{
                    configureType: 'text',
                    templateVariableCode: '',
                    templateVariableName: '',
                    dataSource: '',
                    enterVariableName: '',
                    chartConfigure: null,
                    tableConfigure: null
                }];
            }
        },
        handleShowTableConfigDialog(row, index) {
            this.showTableConfigDialog = true;
            this.currentTableConfig = row;
            this.currentTableIndex = index;
        },
        handleSetTableConfig(data) {
            // 将本地id转换为null
            data = data.map(item => {
                return {
                    ...item,
                    id:String(item.id).length === 13 ? null :item.id
                }
            })
            this.configureFormData.parameter[this.currentTableIndex].tableConfigure = data;
        },
        handleShowChartConfigDialog(row, index) {
            this.showChartConfigDialog = true;
            this.currentChartConfig = row.chartConfigure;
            this.currentChartIndex = index;
        },
        handleSetChartConfig(data) {
            this.configureFormData.parameter[this.currentChartIndex].chartConfigure = data;
        },
        handleSetDimTableData(data) {
            // 将维度数据集数据保存到 configureFormData 中
            this.configureFormData.dimensionData = data.map(item => {
                return {
                    delFlag: item.delFlag,
                    id: item.id,
                    configureId: item.configureId,
                    dataName: item.dataName,
                    dataCode: item.dataCode,
                    tenantId: item.tenantId,
                    revision: item.revision
                }
            });
        },
        async handleTestGenerate() {
            // const valid = await this.$refs.configureFormRef.validate().catch(() => false);
            // if (!valid) return
            // this.$message.success('校验通过，测试模板生成');
            this.$message.info('敬请期待');
        },
        async handleSave() {
            const valid = await this.$refs.configureFormRef.validate().catch(() => false);
            if (!valid) return
            // 显示加载遮罩层
            this.$loading({
                lock: true,
                text: '加载中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });

            const base = {
                id: this.configureFormData.id,
                templateName: this.configureFormData.templateName,
                templateType: this.configureFormData.templateType,
                // fileName
                fileName: this.configureFormData.fileName,
                // 文件id
                fileId: this.configureFormData.fileId,
                // 模版id
                templateId: this.configureFormData.templateId,
                // 模版相对地址
                templateUrl: this.configureFormData.templateUrl,
                // 维度名称
                dimensionalityName: this.configureFormData.dimensionName,
                // 维度区分列
                dimensionalityColumn: this.configureFormData.dimensionField,
                // 时间列
                dateTimeColumn: this.configureFormData.timeField,
                // 维度区分状态
                dimensionalityStatus: this.configureFormData.dimension,
                // 时间状态
                dateTimeColumnStatus: this.configureFormData.time
            }

            const parameter = this.configureFormData.parameter.map((item, index) => {
                const isLocalItem = String(item.id).length === 13
                return {
                    sort: index,
                    id: isLocalItem ? null : item.id,
                    delFlag: isLocalItem ? null : item.delFlag, //其实都是0
                    configureType: item.configureType,
                    templateVariableCode: item.templateVariableCode,
                    templateVariableName: item.templateVariableName,
                    dataSource: item.dataSource,
                    dataSourceName: item.dataSourceName,
                    enterVariableName: item.enterVariableName,
                    chartConfigure: item.configureType === 'chart' ? item.chartConfigure : null,
                    tableConfigure: item.configureType === 'table' ? item.tableConfigure : null
                }
            })
            const delParameter = this.configureFormData.deletedParameter.map((item, index) => {
                return {
                    sort: parameter.length + index,
                    id: item.id,
                    delFlag: item.delFlag, //其实都是1
                    configureType: item.configureType,
                    templateVariableCode: item.templateVariableCode,
                    templateVariableName: item.templateVariableName,
                    dataSource: item.dataSource,
                    dataSourceName: item.dataSourceName,
                    enterVariableName: item.enterVariableName,
                    chartConfigure: item.configureType === 'chart' ? item.chartConfigure : null,
                    tableConfigure: item.configureType === 'table' ? item.tableConfigure : null
                }
            })
            const dimensionData = this.configureFormData.dimensionData.map(item => {
                return {
                    delFlag: item.delFlag,
                    id: item.id,
                    configureId: item.configureId,
                    dataName: item.dataName,
                    dataCode: item.dataCode,
                    tenantId: item.tenantId,
                    revision: item.revision
                }
            })
            const submitFormData = {
                base, parameter: [...parameter, ...delParameter], dimensionData
            }
            console.log("submitFormData--submitFormData", submitFormData);

            saveConfigure(submitFormData).then(res => {
                if (res.code === 200) {
                    this.$message.success('保存成功')
                    const visitedViews = this.$store.state.tagsView.visitedViews
                    const configPage = visitedViews.find(item => item.path === '/document/configure')
                    // 去掉configPage
                    visitedViews.splice(visitedViews.indexOf(configPage), 1)
                    this.$router.push({ path: '/document/list' })
                    // 更新附件状态
                    if (this.oldFileId === null) {
                        // 绑定
                        this.handleUpdateAttachment(res.data, this.configureFormData.fileId, FileStatus.BOUND)
                    } else {
                        if (this.oldFileId !== this.configureFormData.fileId) {
                            // 解绑旧的
                            this.handleUpdateAttachment(res.data, this.oldFileId, FileStatus.UNBOUND)
                            // 绑定新的
                            this.handleUpdateAttachment(res.data, this.configureFormData.fileId, FileStatus.BOUND)
                        }
                    }
                }
            }).finally(() => {
                // 关闭加载遮罩层
                this.$loading().close();
            })
        },
        // 表格行拖拽
        rowDrag() {
            const el = document.querySelectorAll(
                ".draggable-table .el-table__body-wrapper > table > tbody"
            )[0];
            Sortable.create(el, {
                disabled: false, // 拖拽是否可用
                ghostClass: "sortable-ghost", //拖拽样式
                animation: 150, // 拖拽延时
                onEnd: (e) => {
                    const parameter = this.configureFormData.parameter.slice()
                    const movedItem = parameter.splice(e.oldIndex, 1)[0];
                    parameter.splice(e.newIndex, 0, movedItem);
                    this.configureFormData.parameter = JSON.parse(JSON.stringify(parameter));
                },
            });
        },
        /**
         * 获取配置详情
         */
        async handleGetConfigureDetail() {
            const id = this.$route.query.id
            if (!id) return
            // 显示加载遮罩层
            this.$loading({
                lock: true,
                text: '加载中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });

            const res = await getConfigure(id).finally(() => {
                // 关闭加载遮罩层
                this.$loading().close();
            })
            if (res.code === 200) {
                const base = res.data.base
                const parameter = res.data.parameter
                const dimensionData = res.data.dimensionData
                this.configureFormData.id = base.id
                this.configureFormData.templateName = base.templateName
                this.configureFormData.templateType = base.templateType
                this.configureFormData.templateId = base.templateId
                this.configureFormData.templateUrl = base.templateUrl
                // 文件名回显
                this.configureFormData.fileName = base.fileName
                this.configureFormData.fileId = base.fileId
                this.configureFormData.dimension = base.dimensionalityStatus
                this.configureFormData.dimensionField = base.dimensionalityColumn
                this.configureFormData.dimensionName = base.dimensionalityName
                this.configureFormData.time = base.dateTimeColumnStatus
                this.configureFormData.timeField = base.dateTimeColumn
                this.configureFormData.dimensionData = dimensionData
                this.configureFormData.parameter = parameter.filter(item => item.delFlag !== 1)
                this.configureFormData.deletedParameter = parameter.filter(item => item.delFlag === 1)

                // 记录文件名称
                this.oldFileId = base.fileId
            }

        },
        handleParameterChange(index, field, value) {
            this.$set(this.configureFormData.parameter[index], field, value);
        },
        /**
         * 更新附件
         * @param id 业务id
         * @param fileId 文件id
         * @param fileStatus 文件状态
         */
        handleUpdateAttachment(id, fileId, fileStatus) {
            const data = {
                businessId: id,
                businessType: id,
                fileStatus: fileStatus,
                fileId: fileId
            }
            updateAttachment(data).then(res => {
                if (res.code === 200) {
                    this.$message.success('更新成功')
                }
            })
        },
        handleRemoveFile() {
            this.configureFormData.fileName = null;
            this.configureFormData.templateId = null;
            this.configureFormData.templateUrl = null;
        },
        handleDownloadFile() {
            if (!this.configureFormData.templateUrl) {
                this.$message.warning('文件路径不存在，无法下载');
                return;
            }
            // 创建下载链接
            const downloadUrl = `${window.location.origin}${"/opsyndex-file"}${this.configureFormData.templateUrl}`
            // 创建a标签并模拟点击下载
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.setAttribute('download', this.configureFormData.fileName);
            link.setAttribute('target', '_blank');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },
        /** 参数导出*/
        parameterExport() {
          if (this.configureFormData.id) {
            this.download('/document/parameter/exportParameter', { "configureId": this.configureFormData.id }, `数据配置参数.xlsx`)
          }else {
            this.$message({
              message: '请先保存后再进行导出',
              type: 'warning'
            });
          }
        }
    }
}
</script>

<style lang="scss" scoped>
.app-container {
    position: relative;

    .box-card {
        margin-bottom: 20px;
    }
}

.footer-btns {
    width: 100%;
    display: flex;
    justify-content: center;
    margin: 40px 0 20px 0;
}

.upload-demo {
    .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;

        &:hover {
            border-color: #409EFF;
        }
    }

    .el-upload__text {
        color: #606266;
        font-size: 14px;
        text-align: center;
        margin: 10px 0;

        em {
            color: #409EFF;
            font-style: normal;
        }
    }
}

.file-list {
    margin-bottom: 10px;

    .file-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        border: 1px solid #e1e1e1;
        border-radius: 4px;
        background-color: #f8f8f8;

        .el-icon-document {
            color: #909399;
            font-size: 18px;
            margin-right: 10px;
        }

        .file-name {
            flex: 1;
            font-size: 14px;
            color: #606266;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: pointer;

            &:hover {
                color: #409EFF;
                text-decoration: underline;
            }
        }

        .el-icon-delete {
            color: #c0c4cc;
            font-size: 16px;
            cursor: pointer;

            &:hover {
                color: #f56c6c;
            }
        }
    }
}
</style>
