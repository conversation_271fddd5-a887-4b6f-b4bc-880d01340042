<template>
  <el-dialog title="图表配置" :visible.sync="dialogVisible" width="700px">
    <el-form ref="chartConfigFormRef" :model="chartConfigFormData" :rules="chartConfigFormRules" label-width="auto"
      class="chart-config-form">
      <el-form-item label="图表标题" prop="bindingType">
        <el-select v-model="chartConfigFormData.bindingType" placeholder="请选择绑定类型" style="width: 220px;" clearable>
          <el-option v-for="item in titleBindingTypeOptions" :key="item.value" :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="标题绑定数据源" prop="bindingDataSource" v-if="chartConfigFormData.bindingType === 'dbms'">
        <DbmlSelector :model-value="chartConfigFormData.bindingDataSource"
          :data-source-name.sync="chartConfigFormData.bindingDataSourceName"
          @update:modelValue="(val) => handleBindingDataSourceChange(val)" placeholder="请选择数据源" />
      </el-form-item>
      <el-form-item label="图表名称" prop="title">
        <el-input v-model="chartConfigFormData.title" placeholder="图表名称" style="width: 220px;" clearable />
      </el-form-item>
      <el-form-item label="图表类型" prop="chartType">
        <el-select v-model="chartConfigFormData.chartType" placeholder="请选择图表类型" style="width: 220px;" clearable>
          <el-option v-for="item in chartTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-tabs v-model="activeTab" class="chart-tabs">
        <el-tab-pane label="数据绑定" name="bind">
          <el-form-item label="数据源" prop="dataSource">
            <DbmlSelector :model-value="chartConfigFormData.dataSource"
            :data-source-name.sync="chartConfigFormData.dataSourceName"
              @update:modelValue="(val) => handleDatasetSourceChange(val)" placeholder="请选择数据源" />
          </el-form-item>
          <el-form-item label="类别" prop="categoryCode">
            <el-input v-model="chartConfigFormData.categoryCode" placeholder="类别列" style="width: 220px;" clearable />
          </el-form-item>
          <el-form-item label="系列" prop="seriesNameList">
            <div v-for="(item, idx) in chartConfigFormData.seriesNameList" :key="idx" class="series-row">
              <el-form-item :prop="'seriesNameList.' + idx + '.category'" :rules="chartConfigFormRules.seriesNameItem">
                <el-input v-model="item.category" placeholder="类别列" style="width: 220px; margin-right: 16px;" clearable />
              </el-form-item>
              <el-form-item v-if="chartConfigFormData.chartType === 'columnLine'" :prop="'seriesNameList.' + idx + '.type'" :rules="chartConfigFormRules.seriesNameItem">
                <el-select v-model="item.type" placeholder="类型" style="width: 100px; margin-right: 16px;" clearable>
                  <el-option label="柱" value="column"></el-option>
                  <el-option label="线" value="line"></el-option>
                </el-select>
              </el-form-item>
              <el-button v-if="chartConfigFormData.chartType === 'columnLine'" type="success" size="mini" @click="addSeries(idx)">新增</el-button>
              <el-button v-if="idx > 0" type="danger" size="mini" @click="removeSeries(idx)">删除</el-button>
            </div>
          </el-form-item>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">取 消</el-button>
      <el-button type="warning" @click="resetForm">清空</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import DbmlSelector from './DbmlSelector.vue'
export default {
  name: 'ChartConfigDialog',
  components: { DbmlSelector },
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    chartConfig: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      activeTab: 'bind',
      chartConfigFormRules: {
        bindingType: [
          { required: true, message: '请选择绑定类型', trigger: 'change' }
        ],
        bindingDataSource: [
          // 自定义验证
          {
            required: true, validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error('请选择标题绑定数据源'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        title: [
          { required: true, message: '请输入图表名称', trigger: 'blur' }
        ],
        chartType: [
          { required: true, message: '请选择图表类型', trigger: 'change' }
        ],
        dataSource: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error('请选择数据源'));
              } else {
                callback();
              }
            }, trigger: 'change'
          }
        ],
        categoryCode: [
          { required: true, message: '请输入类别列', trigger: 'blur' }
        ],
        seriesNameList: [
          { required: true, type: 'array', message: '请输入系列值', trigger: 'blur' }
        ],
        seriesNameItem: [
          { required: true, message: '请输入系列值', trigger: 'blur' }
        ]
      },
      chartConfigFormData: {
        bindingType: '',
        bindingDataSource: '',
        bindingDataSourceName: '',
        title: '',
        chartType: '',
        dataSource: '',
        dataSourceName: '',
        categoryCode: '',
        seriesNameList: [{ category: '' ,type: ''}]
      },
      titleBindingTypeOptions: [],
      chartTypeOptions: [],
    }
  },
  watch: {
    visible: {
      immediate: true,
      handler(val) {
        this.dialogVisible = val;
      }
    },
    dialogVisible(val) {
      if (val !== this.visible) {
        this.$emit('update:visible', val);
      }
    },
    chartConfig: {
      handler(newVal) {
        if (newVal) {
          if (newVal.seriesNameList && Array.isArray(newVal.seriesNameList)) {
            this.chartConfigFormData = {
              ...newVal,
              seriesNameList: newVal.seriesNameList
            };
          } else {
            this.chartConfigFormData = {
              ...newVal,
              seriesNameList: [{ category: '' ,type: ''}]
            };
          }
        } else {
          this.chartConfigFormData = this.getInitialFormData();
        }
      },
      deep: true
    }
  },
  mounted() {
    this.getDicts('document_title_binding_type').then(response => {
      if (response.code === 200) {
        this.titleBindingTypeOptions = response.data.map(this.buildOption)
      }
    })
    this.getDicts('document_chart_type').then(response => {
      if (response.code === 200) {
        this.chartTypeOptions = response.data.map(this.buildOption)
      }
    })
  },
  methods: {
    getInitialFormData() {
      return {
        bindingType: '',
        bindingDataSource: '',
        title: '',
        chartType: '',
        dataSource: '',
        categoryCode: '',
        seriesNameList: [{ value: '' }]
      };
    },
    buildOption(item) {
      return {
        ...item,
        label: item.dictLabel,
        value: item.dictValue
      }
    },
    addSeries(idx) {
      this.chartConfigFormData.seriesNameList.splice(idx + 1, 0, { category: '' ,type: ''});
    },
    removeSeries(idx) {
      if (this.chartConfigFormData.seriesNameList.length > 1) {
        this.chartConfigFormData.seriesNameList.splice(idx, 1);
      }
    },
    handleBindingDataSourceChange(val) {
      this.$set(this.chartConfigFormData, 'bindingDataSource', val);
    },
    handleDatasetSourceChange(val) {
      this.$set(this.chartConfigFormData, 'dataSource', val);
    },
    closeDialog() {
      this.dialogVisible = false;
    },
    resetForm() {
      this.chartConfigFormData = this.getInitialFormData();
      this.$nextTick(() => {
        this.$refs.chartConfigFormRef && this.$refs.chartConfigFormRef.clearValidate();
      });
    },
    async handleConfirm() {
      const valid = await this.$refs.chartConfigFormRef.validate().catch(() => false);
      if (!valid) return;
      // seriesNameList
      //this.chartConfigFormData.seriesNameList = this.chartConfigFormData.seriesNameList.map(item => item.value);
      this.dialogVisible = false;
      this.$emit('set-chart-config', this.chartConfigFormData);
    }
  }
}
</script>

<style lang="scss" scoped>
.chart-config-form {
  :deep(.el-form-item__error) {
    position: absolute;
    top: 100%;
    left: 0;
    padding-top: 4px;
  }

  .series-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: 10px;

    .el-form-item {
      margin-bottom: 0;
      margin-right: 16px;
    }
  }
}
</style>
