<template>
    <div class="dbmlselector">
        <div class="custom-select" :class="{ 'is-disabled': disabled }">
            <div class="select-content" @click="showDialog">
                <span class="custom-select__selected" :class="{ 'is-placeholder': !modelValue }">
                    {{ displayValue || '请选择数据源' }}
                </span>
                <i class="el-icon-arrow-down custom-select__icon"></i>
            </div>
            <i v-if="modelValue && !disabled" class="el-icon-circle-close custom-select__clear"
                @click.stop="clearSelection"></i>
        </div>
        <el-dialog title="选择数据源" :visible.sync="dialogVisible" width="80%" :close-on-click-modal="false" append-to-body>
            <el-form inline :model="listQuery" ref="queryFormRef" class="filter-container">
                <el-form-item label="任务分组">
                    <el-select v-model="listQuery.groupId" placeholder="任务分组" style="width: 200px;" class="filter-item"
                        clearable filterable>
                        <el-option v-for="item in sqlGroupList" :key="item.id" :label="item.jobDesc" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="任务名称">
                    <el-input v-model="listQuery.jobDesc" placeholder="任务名称" style="width: 200px;"
                        class="filter-item" />
                </el-form-item>
                <el-form-item label="所属项目">
                    <el-select v-model="listQuery.projectIds" multiple placeholder="所属项目" class="filter-item">
                        <el-option v-for="item in jobProjectList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="fetchData">
                        搜索
                    </el-button>
                    <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-refresh"
                        @click="handleReset">
                        重置
                    </el-button>
                </el-form-item>
            </el-form>
            <el-input style="width: 220px; margin-bottom: 10px;" v-model="displayValue" readonly></el-input>
            <el-table v-loading="listLoading" :data="list" element-loading-text="Loading" border fit
                highlight-current-row style="width: 100%" size="medium" ref="singleTable" @row-click="handleRowClick">
                <el-table-column width="55" align="center">
                    <template slot-scope="scope">
                        <el-checkbox v-model="scope.row.selected" :disabled="disabled"
                            @change="handleCheckboxChange(scope.row)"></el-checkbox>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="ID" width="120" prop="id">
                </el-table-column>
                <el-table-column align="center" label="数据源" width="120">
                    <template slot-scope="scope">{{ getDatasourceNameById(scope.row.datasourceId) }}</template>
                </el-table-column>
                <el-table-column label="所属分组" align="center" prop="groupName">
                </el-table-column>
                <el-table-column label="任务名称" align="center" prop="jobDesc">
                </el-table-column>
                <el-table-column label="所属项目" align="center" prop="projectName">
                </el-table-column>
            </el-table>
            <div class="pagination-container">
                <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page="listQuery.current" :page-sizes="[10, 20, 30, 50]" :page-size="listQuery.size"
                    layout="total, sizes, prev, pager, next, jumper" :total="total">
                </el-pagination>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="handleSelect" :disabled="disabled">确定</el-button>
                <el-button @click="dialogVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>

</template>

<script>
import * as sqlJob from '@/api/dbms/dbms-job-info'
import * as sqlGroup from '@/api/dbms/dbms-job-group'
import waves from '@/directive/waves'
import * as jobProjectApi from '@/api/datax/datax-job-project'
import { list as jdbcDsList } from '@/api/datax/datax-jdbcDatasource'
export default {
    name: 'DbmlSelector',
    directives: { waves },
    props: {
        modelValue: {
            type: [String, Number, null],
            default: null
        },
        disabled: {
            type: Boolean,
            default: false
        },
        dataSourceName: {
            type: String,
            default: ''
        }
    },
    emits: ['update:modelValue'],
    watch: {
        modelValue: {
            handler(val) {
                if (val) {
                    this.updateDisplayValue();
                }
            },
            immediate: true
        }
    },
    data() {
        return {
            jdbcDsQuery: {
                current: 1,
                size: 200,
                datasource: '',
                ascs: 'datasource_name'
            },
            datasourceList: [],
            list: null,
            listLoading: true,
            total: 0,
            listQuery: {
                category: null,
                current: 1,
                size: 10,
                jobGroup: 0,
                projectIds: '',
                triggerStatus: -1,
                jobDesc: '',
                glueType: 'BEAN',
                datasourceType: '',
            },
            jobProjectList: '',
            sqlGroupList: [],
            dialogVisible: false,
            displayValue: '',
        }
    },
    created() {
        this.displayValue = this.dataSourceName
    },
    methods: {
        getGroupList() {
            sqlGroup.getDataList().then(result => {
                this.sqlGroupList = result.data
            })
        },
        getJobProject() {
            jobProjectApi.getJobProjectList().then(response => {
                this.jobProjectList = response.data
            })
        },
        fetchData() {
            this.listLoading = true
            sqlJob.getList(this.listQuery).then(response => {
                const { content } = response
                this.total = content.recordsTotal
                this.list = content.data.map(item => {
                    return {
                        ...item,
                        selected: item.id === this.modelValue
                    }
                })
                this.updateDisplayValue()
                this.listLoading = false
            })
        },
        handleReset() {
            this.$refs.queryFormRef.resetFields()
            this.fetchData()
        },

        // 处理分页大小变更
        handleSizeChange(val) {
            this.listQuery.size = val
            this.fetchData()
        },

        // 处理当前页变更
        handleCurrentChange(val) {
            this.listQuery.current = val
            this.fetchData()
        },

        // 获取可用数据源
        getJdbcDs() {
            this.loading = true
            jdbcDsList(this.jdbcDsQuery).then(response => {
                const { records } = response.data
                this.datasourceList = records
                this.loading = false
            })
        },
        getDatasourceNameById(id) {
            const found = this.datasourceList.find(item => item.id === id);
            return found ? found.datasourceName : null;
        },

        /**
         * 更新显示值
         */
        updateDisplayValue() {
            if (this.modelValue) {
                const selectedRow = this.list?.find(item => item.id === this.modelValue);
                if (selectedRow) {
                    this.displayValue = selectedRow.jobDesc
                } else {
                    this.displayValue = '';
                }
            } else {
                this.displayValue = '';
            }
        },

        showDialog() {
            if (this.disabled) return
            this.dialogVisible = true;
            this.fetchData()
            this.getGroupList()
            this.getJobProject()
            this.getJdbcDs()
        },

        handleSelect() {
            const selectedRow = this.list.find(item => item.selected);
            if (selectedRow) {
                this.$emit('update:modelValue', selectedRow.id);
                this.$emit('update:dataSourceName', selectedRow.jobDesc);
                this.updateDisplayValue();
                this.dialogVisible = false;
            } else {
                this.$message({
                    message: '请选择一条记录',
                    type: 'warning'
                });
            }
        },

        handleCheckboxChange(row) {
            // 确保只能选中一个
            this.list.forEach(item => {
                if (item !== row) {
                    item.selected = false;
                }
            });
            // 设置当前行为表格的当前行
            this.$refs.singleTable.setCurrentRow(row);
        },

        // 处理行点击事件
        handleRowClick(row) {
            if (this.disabled) return;

            // 切换当前行选中状态
            row.selected = true;

            // 取消其他行的选中状态
            this.list.forEach(item => {
                if (item !== row) {
                    item.selected = false;
                }
            });

            // 设置表格当前行高亮
            this.$refs.singleTable.setCurrentRow(row);

            this.displayValue = row.jobDesc
        },

        /**
         * 清除选择
         */
        clearSelection() {
            this.$emit('update:modelValue', null);
            this.displayValue = '';

            // 如果列表已加载，清除选中状态
            if (this.list) {
                this.list.forEach(item => {
                    item.selected = false;
                });
                if (this.$refs.singleTable) {
                    this.$refs.singleTable.setCurrentRow(null);
                }
            }
        },
    }
}
</script>

<style scoped lang="scss">
.dbmlselector {
    .filter-container {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 10px;
        padding: 16px;
        background: #f5f7fa;
        border-radius: 4px;
        margin-bottom: 16px;

        .filter-item {
            margin-bottom: 0;
        }

        .el-form-item {
            margin-bottom: 0;
        }
    }

    .pagination-container {
        padding: 16px;
        text-align: right;
        background: #fff;
        border-top: 1px solid #ebeef5;
    }

    // 表格样式
    .el-table {
        margin: 16px 0;
        border-radius: 4px;
        overflow: hidden;

        ::v-deep th {
            background-color: #f5f7fa;
            color: #606266;
            font-weight: 600;
            height: 44px;
        }

        ::v-deep td {
            padding: 8px 0;
        }

        ::v-deep .el-radio {
            .el-radio__label {
                display: none;
            }

        }

        ::v-deep tr {
            cursor: pointer;
        }
    }

    // 弹窗样式
    ::v-deep .el-dialog {
        border-radius: 8px;
        overflow: hidden;
        margin-top: 8vh !important;

        .el-dialog__header {
            padding: 20px;
            border-bottom: 1px solid #ebeef5;
            background: #f5f7fa;

            .el-dialog__title {
                font-size: 16px;
                font-weight: 600;
                color: #303133;
            }
        }

        .el-dialog__body {
            padding: 20px;
        }

        .el-dialog__footer {
            padding: 16px 20px;
            border-top: 1px solid #ebeef5;
            background: #f5f7fa;
        }
    }

    // 选择器样式
    .custom-select {
        display: inline-flex;
        align-items: center;
        border: 1px solid #dcdfe6;
        border-radius: 6px;
        padding: 0 12px;
        height: 40px;
        min-width: 220px;
        background: #fff;
        position: relative;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);

        .select-content {
            display: flex;
            align-items: center;
            flex: 1;
            cursor: pointer;
            height: 100%;
            padding: 0 4px;
        }

        &:hover {
            border-color: #409EFF;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
        }

        &:focus-within {
            border-color: #409EFF;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
        }

        &__selected {
            flex: 1;
            color: #303133;
            font-size: 14px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            transition: color 0.3s ease;

            &.is-placeholder {
                color: #909399;
            }
        }

        &__icon {
            font-size: 16px;
            color: #909399;
            margin-left: 8px;
            transition: all 0.3s ease;
        }

        &__clear {
            font-size: 16px;
            color: #909399;
            cursor: pointer;
            margin-left: 8px;
            display: none;
            transition: all 0.3s ease;
            padding: 4px;
            border-radius: 50%;

            &:hover {
                color: #f56c6c;
                background-color: rgba(245, 108, 108, 0.1);
            }
        }

        &:hover {
            .custom-select__clear {
                display: inline-flex;
                align-items: center;
                justify-content: center;
            }

            .custom-select__icon {
                color: #409EFF;
            }
        }

        &.is-disabled {
            background-color: #f5f7fa;
            border-color: #e4e7ed;
            cursor: not-allowed;
            box-shadow: none;

            &:hover {
                border-color: #e4e7ed;
                box-shadow: none;
            }

            .custom-select__icon {
                color: #c0c4cc;
            }

            .custom-select__selected {
                color: #c0c4cc;
            }

            .select-content {
                cursor: not-allowed;
            }
        }
    }

    // 按钮样式
    .el-button {
        &--primary {
            background-color: #409EFF;
            border-color: #409EFF;

            &:hover {
                background-color: #66b1ff;
                border-color: #66b1ff;
            }
        }
    }
}
</style>