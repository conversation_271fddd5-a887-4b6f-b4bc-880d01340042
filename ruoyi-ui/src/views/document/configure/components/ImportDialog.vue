<template>
  <el-dialog :visible.sync="dialogVisible" width="600px" :close-on-click-modal="false" :show-close="true" append-to-body
    :destroy-on-close="true" :modal-append-to-body="false" :center="false" title="文件上传" :before-close="handleCancel">
    <div style="margin-bottom: 30px;">
      <el-upload class="upload-demo" action="#" drag :auto-upload="false" :show-file-list="true" :limit="1"
        :on-change="handleFileChange" :on-remove="handleFileRemove" :file-list="fileList">
        <i class="el-icon-upload" style="font-size: 48px;"></i>
        <div class="el-upload__text" style="font-size: 18px; margin-top: 10px;">
          将文件拖到此处，或 <em>点击上传</em>
        </div>
      </el-upload>
      <div style="margin-top: 16px;">
        <el-checkbox v-model="updateExist">是否更新已经存在的用户数据</el-checkbox>
      </div>
      <div style="margin-top: 8px; color: #888; font-size: 14px;">
        仅允许导入xls、xlsx格式文件。
        <el-link type="primary" @click.prevent="downloadTemplate">下载模板</el-link>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="uploading">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { uploadDimTemplate, uploadParamTemplate } from '@/api/document/configure/api'
import dayjs from 'dayjs'
export default {
  name: 'ImportDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'param'
    }
  },
  data() {
    return {
      updateExist: false,
      file: null,
      fileList: [],
      uploading: false
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    handleFileChange(file) {
      this.file = file
      this.fileList = [file]
    },
    handleFileRemove(file) {
      this.file = null
      this.fileList = []
    },
    handleCancel() {
      this.dialogVisible = false
      this.file = null
      this.fileList = []
      this.updateExist = false
      this.uploading = false
    },
    handleConfirm() {
      if (!this.file) {
        this.$message.warning('请先选择要导入的文件')
        return
      }
      this.uploading = true
      const formData = new FormData()
      formData.append('file', this.file.raw)
      // formData.append('updateExist', this.updateExist)
      // 这里添加导入处理逻辑
      if (this.type === 'param') {
        uploadParamTemplate(formData).then(res => {
          if (res.code === 200) {
            this.handleCancel()
            this.$emit('set-table-data', res.data)
          }
        }).finally(() => {
          this.uploading = false
        })
      } else if (this.type === 'dim') {
        uploadDimTemplate(formData).then(res => {
          if (res.code === 200) {
            this.handleCancel()
            this.$emit('set-table-data', res.data)
          }
        }).finally(() => {
          this.uploading = false
        })
      }

      this.handleCancel()
    },
    downloadTemplate() {
      const downloadUrl = this.type === 'param' ? 'document/parameter/templateDownload' : 'document/dataSet/templateDownload'
      const currentTime = dayjs(`${new Date()}`).format('YYYY-MM-DD HH:mm')
      const fileName = this.type === 'param' ? '参数配置模版' : '维度分区模版'
      this.download(downloadUrl, {}, `${fileName}${currentTime}.xlsx`)
    }
  }
}
</script>