<template>
  <div id="app">
    <div id="custom-loader-wrapper">
      <div class="custom-loader">
        <span class="dot dot1"></span>
        <span class="dot dot2"></span>
        <span class="dot dot3"></span>
        <span class="dot dot4"></span>
      </div>
      <div class="custom-title">{{ sysName }}</div>
    </div>
  </div>
</template>

<script>
import { dingLogin } from "@/api/login";
import * as dd from 'dingtalk-jsapi';
export default {
  data() {
    return {
      temporaryCode: '',
      sysName: 'ALP OPSYNDEX 平台'
    }
  },
  async mounted() {
    this.handleGetSysInfo()
    try {
      // 判断是否是钉钉环境 并且 有 corpId参数
      if (window.navigator.userAgent.includes("DingTalk") && this.$route.query.corpId) {
        await this.getDingCode();
        await this.dingLogin();
      } else {
        await this.$router.push({ path: "/internal-login" })
      }
    } catch (error) {
      console.log("初始化失败")
      console.error('初始化失败:', error);
    }
  },
  methods: {
    handleGetSysInfo() {
      const baseUrl = process.env.VUE_APP_BASE_API
      fetch(baseUrl + "/system/baseConfig/queryBaseConfig").then(async (res) => {
        const data = await res.json()
        const loaderWrapper = document.getElementById("custom-loader-wrapper")
        if (loaderWrapper) {
          const { loginPageName } = data.data
          this.sysName = loginPageName
          loaderWrapper.style.display = "none"
        }
      })
    },
    getDingCode() {
      return new Promise((resolve, reject) => {
        dd.runtime.permission.requestAuthCode({
          corpId: this.$route.query.corpId,
          onSuccess: (result) => {
            this.temporaryCode = result.code;
            resolve(result.code);
          },
          onFail: (err) => {
            console.log("获取临时授权码失败")
            reject(err);
          }
        });
      });
    },
    dingLogin() {
      dingLogin(this.temporaryCode).then(response => {
        console.log("免密登录接口调用")
        if (response.code === 200) {
          this.$store.dispatch('dingLogin', response.data).then(resp => {
            const ua = navigator.userAgent.toLowerCase();
            const isMobile = ua.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)
            if (isMobile) {
              this.$router.push({ path: "/app/menu" })
            } else {
              this.$router.push({ path: "/" })
            }
          })
        } else {
          // 跳转到登录页面
          console.log("免密登录失败")
          this.$router.push({ path: "/internal-login" })
        }
      }).catch(error => {
        console.log("免密登录失败")
        this.$router.push({ path: "/internal-login" })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
#app {
  height: 100%;
  margin: 0px;
  padding: 0px;
}

#custom-loader-wrapper {
  width: 100vw;
  height: 100vh;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.custom-loader {
  width: 48px;
  height: 48px;
  position: relative;
  margin-bottom: 24px;
  animation: rotate 1s linear infinite;
}

.custom-loader .dot {
  width: 12px;
  height: 12px;
  background: #6fc08c;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -6px 0 0 -6px;
  opacity: 0.8;
}

.custom-loader .dot1 {
  transform: rotate(0deg) translate(20px);
}

.custom-loader .dot2 {
  transform: rotate(90deg) translate(20px);
}

.custom-loader .dot3 {
  transform: rotate(180deg) translate(20px);
}

.custom-loader .dot4 {
  transform: rotate(270deg) translate(20px);
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

.custom-title {
  font-size: 22px;
  color: #222;
  letter-spacing: 2px;
  font-weight: 500;
  text-align: center;
}
</style>
