<template>
  <div class="app-container">
    <div ref="queryFormRef" class="filter-container">
      <el-select v-model="listQuery.workflowName" placeholder="工作流名称"  class="filter-item" clearable filterable>
        <el-option
          v-for="item in workflow_list"
          :key="item.workflowName"
          :label="item.workflowName"
          :value="item.workflowName"
        />
      </el-select>
<!--      <el-input-->
<!--        v-model="listQuery.workflowName"-->
<!--        clearable-->
<!--        placeholder="工作流名称"-->
<!--        style="width: 200px;"-->
<!--        class="filter-item"-->
<!--      />-->
      <el-select v-model="listQuery.status" placeholder="执行状态"  class="filter-item" clearable>
        <el-option
          v-for="item in workflow_exec_status"
          :key="item.dictCode"
          :label="item.dictLabel"
          :value="item.dictValue"
        />
      </el-select>
      <el-input v-model="listQuery.executeDurationTime" placeholder=">=执行时长" style="width: 200px;" class="filter-item" />
      <el-select v-show="false" v-model="listQuery.diffStatus" placeholder="任务结果" clearable>
        <el-option
          v-for="item in diffStatusDict"
          :key="item.id"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-date-picker
        v-model="dateRange"
        style="width: 400px"
        value-format="yyyy-MM-dd HH:mm:ss"
        type="datetimerange"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        class="filter-item"
      ></el-date-picker>

      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="fetchData">
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-edit"
        @click="handlerDelete"
      >
        清除
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-refresh"
                 @click="handleReset">
        重置
      </el-button>
<!--      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-edit" @click="handleCreate">-->
<!--        添加-->
<!--      </el-button>-->
    </div>
    <el-table
      ref="fullHeightTableRef"
      :height="tableHeight"
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="日志ID" width="100px">
        <template slot-scope="scope">{{ scope.row.id }}</template>
      </el-table-column>
      <el-table-column label="工作流名称" align="center" width="350px">
        <template slot-scope="scope">{{ scope.row.workflowName }}
        </template>
      </el-table-column>
<!--      <el-table-column label="任务状态" width="150" align="center">-->
<!--        <template slot-scope="scope">-->
<!--          {{ getStatusText2(scope.row.status) }}-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="工作流执行结果" align="center">
        <template slot-scope="scope">
          <span :class="getStatusClass(scope.row.executeResult)">
            {{ getStatusText1(scope.row.executeResult) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="工作流执行详情" align="center">
        <template slot-scope="scope">
          <el-popover
            placement="bottom"
            width="600"
            trigger="click"
          >
            <div class="custom-popover-content">
              <h5 v-html="scope.row.handleMsg"/>
            </div>
            <el-button slot="reference">查看</el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="执行开始时间" align="center">
        <template slot-scope="scope">{{ scope.row.executeTime }}</template>
      </el-table-column>
      <el-table-column label="执行结束时间" align="center">
        <template slot-scope="scope">{{ scope.row.executeEndTime }}</template>
      </el-table-column>
      <el-table-column label="执行时长(s)" align="center">
        <template slot-scope="scope">{{ scope.row.executeDurationTime }}</template>
      </el-table-column>
<!--      <el-table-column label="结束时间" width="200" align="center">-->
<!--        <template slot-scope="scope">{{ scope.row.endTime }}</template>-->
<!--      </el-table-column>-->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300px">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" style="width: 60px!important;" @click="handleDetail(row)">
            查看
          </el-button>
          <el-button v-if="row.status!=='deleted'" size="mini" type="danger" style="width: 60px!important;" @click="confirmDelete(row)">
            删除
          </el-button>
          <el-button v-if="row.executeResult===1" size="mini" type="warning" style="width: 60px!important;" @click="handleStop(row)">
            终止
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="fetchData"
    />

    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="center"
        label-width="100px"
      >
        <el-row>
          <el-col :span="14" :offset="5">
            <el-form-item label="清除范围">
              <el-select
                v-model="temp.deleteType"
                placeholder="请选择清理范围"
                style="width: 230px"
              >
                <el-option
                  v-for="item in deleteTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false"> 取消 </el-button>
        <el-button type="primary" @click="deleteLog"> 确定 </el-button>
      </div>
    </el-dialog>

<!--    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="800px">-->
<!--      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="100px">-->
<!--        <el-form-item label="项目名称" prop="name">-->
<!--          <el-input v-model="temp.name" placeholder="项目名称" style="width: 40%" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="项目描述" prop="description">-->
<!--          <el-input v-model="temp.description" placeholder="项目描述" style="width: 40%" />-->
<!--        </el-form-item>-->
<!--      </el-form>-->
<!--      <div slot="footer" class="dialog-footer">-->
<!--        <el-button @click="dialogFormVisible = false">-->
<!--          取消-->
<!--        </el-button>-->
<!--        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">-->
<!--          确认-->
<!--        </el-button>-->
<!--      </div>-->
<!--    </el-dialog>-->
<!--    <el-dialog :visible.sync="dialogPluginVisible" title="Reading statistics">-->
<!--      <el-table :data="pluginData" border fit highlight-current-row style="width: 100%">-->
<!--        <el-table-column prop="key" label="Channel" />-->
<!--        <el-table-column prop="pv" label="Pv" />-->
<!--      </el-table>-->
<!--      <span slot="footer" class="dialog-footer">-->
<!--        <el-button type="primary" @click="dialogPvVisible = false">Confirm</el-button>-->
<!--      </span>-->
<!--    </el-dialog>-->
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import * as workflowLogApi from '@/api/workflow/workflowLog'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import { getDicts as getDictData, getData as getDictDetail } from '@/api/system/dict/data'
import * as log from '@/api/workflow/workflowLog'
import { getAllList } from '@/api/workflow/workflow-info'
import { updateFlowStatus } from '@/api/workflow/workflowLog'

export default {
  mixins: [tableFullHeight],
  name: 'WorkflowLog',
  components: { Pagination },
  directives: { waves },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'gray',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },
  mounted() {
    console.log(this.$route.query.workflowName)
    if (this.$route.query.workflowName) {
      this.listQuery.workflowName = this.$route.query.workflowName
    }
    this.fetchData()
  },
  data() {
    return {
      workflow_exec_status: [],
      workflow_status: [],
      workflow_list: [],
      diffStatusDict: [],
      list: null,
      listLoading: true,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        workflowName: '',
        status: '',
        executeDurationTime: ''
      },
      deleteTypeList: [
        { value: 1, label: '清理一周之前日志数据' },
        { value: 2, label: '清理一个月之前日志数据' },
        { value: 3, label: '清理三个月之前日志数据' },
        { value: 4, label: '清理一年之前日志数据' },
        { value: 5, label: '清理一千条以前日志数据' },
        { value: 6, label: '清理一万条以前日志数据' },
        { value: 7, label: '清理三万条以前日志数据' },
        { value: 8, label: '清理十万条以前日志数据' },
        { value: 9, label: '清理所有日志数据' }
      ],
      dialogPluginVisible: false,
      pluginData: [],
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: 'Edit',
        create: 'Create',
        clear: 'Clear'
      },
      rules: {
      },
      temp: {
        id: undefined,
        name: '',
        description: ''
      },
      visible: true,
      // 日期范围
      dateRange: []
    }
  },
  created() {
    this.getDictDetailInfo()
    // this.fetchData()
    this.getAllWFData()
  },
  methods: {
    getStatusText1(status) {

      const found = this.workflow_exec_status.filter(item => item.dictValue == status)
      return found[0].dictLabel
    },
      getStatusClass(status) {
      const found = this.workflow_exec_status.filter(item => item.dictValue == status)
      const executeResult = found[0].dictValue
      switch (executeResult) {
        case '2':
          return 'failure-class'
        case '3':
          return 'success-class'
        case '4':
          return 'part-class'
      }
    },
    getDictDetailInfo() {
      getDictData("workflow_exec_status").then(res => {
        this.workflow_exec_status = res.data
      })

      getDictData("workflow_status").then(res => {
        this.workflow_status = res.data
      })
    },
    fetchData() {
      this.listLoading = true
      const param = Object.assign({}, this.listQuery)
      const urlJobId = this.$route.query.jobId
      if (urlJobId > 0 && !param.jobId) {
        param.jobId = urlJobId
      } else if (!urlJobId && !param.jobId) {
        param.jobId = 0
      }

      if (this.dateRange) {
        param.startDateTime =this.dateRange[0]
        param.endDateTime =this.dateRange[1]
      }

      workflowLogApi.list(param).then(response => {
        const { records } = response.data
        const { total } = response.data
        this.total = total
        this.list = records
        this.listLoading = false
      })
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        name: '',
        description: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          workflowLogApi.created(this.temp).then(() => {
            this.fetchData()
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Created Successfully',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handlerDelete() {
      this.dialogStatus = 'clear'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    deleteLog() {
      log.deleteLog(this.temp.deleteType).then(() => {
          this.fetchData()
          this.dialogFormVisible = false
          this.$notify({
            title: 'Success',
            message: 'Delete Successfully',
            type: 'success',
            duration: 2000
          })
        })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row) // copy obj
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          workflowLogApi.updated(tempData).then(() => {
            this.fetchData()
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    //删除按钮提示模态框
    confirmDelete(row) {
      this.$confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 用户点击确定按钮时执行删除操作
        this.handleDelete(row);
        //   handleDelete 这个是原来的方法要设置
      }).catch(() => {
        // 用户点击取消按钮时的操作，可以不做任何处理或关闭模态框
      });
    },
    //删除按钮提示模态框
    handleStop(row) {
      this.$confirm('确认终止？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        workflowLogApi.updateFlowStatus(row.workflowId)
      }).catch(() => {
        // 用户点击取消按钮时的操作，可以不做任何处理或关闭模态框
      });
    },
    handleDelete(row) {
      const idList = []
      idList.push(row.id)
      workflowLogApi.deleted({ idList: row.id }).then(response => {
        this.fetchData()
        this.$notify({
          title: 'Success',
          message: 'Delete Successfully',
          type: 'success',
          duration: 2000
        })
      })
    },
    handleDetail(row) {
      this.$router.push({ path: '/DataDev/workflow/workflowTaskLog', query: { workflowInstanceId: row.id }})
    },
    getAllWFData() {
      getAllList().then((response) => {
        this.workflow_list = response.content
      })
    },
    handleReset() {
      this.listQuery = {
        pageNo: 1,
        pageSize: 10,
        workflowName: '',
        status: ''
      }
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-popover-content {
  max-height: 300px;
  max-width: 600px;
  overflow-y: auto;
  color: #1459ad;
}
.success-class {
  color: green;
}
.failure-class {
  color: red;
}
.part-class {
  color: #050fc5;
}
</style>
