<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select v-model="listQuery.taskType" placeholder="任务类型" style="width: 200px" clearable class="filter-item">
        <el-option
          v-for="item in taskTypeList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-input
        v-model="listQuery.taskName"
        placeholder="任务名称"
        style="width: 200px"
        class="filter-item"
      />
      <el-input v-model="listQuery.handleProcessTime" placeholder=">=执行时长" style="width: 200px;" class="filter-item" />
      <el-date-picker
        v-model="dateRange"
        style="width: 400px"
        value-format="yyyy-MM-dd HH:mm:ss"
        type="datetimerange"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        class="filter-item"
      ></el-date-picker>

      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="fetchData"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="日志ID">
        <template slot-scope="scope">{{ scope.row.logId }}</template>
      </el-table-column>
      <el-table-column align="center" label="工作流名称">
        <template slot-scope="scope">{{ scope.row.workflowName }}</template>
      </el-table-column>
      <el-table-column align="center" label="节点名称">
        <template slot-scope="scope">{{ scope.row.taskName }}</template>
      </el-table-column>
      <el-table-column align="center" label="任务名称">
        <template slot-scope="scope">{{ scope.row.nodeTaskName }}</template>
      </el-table-column>
      <el-table-column label="任务明细" align="center" width="200px">
        <template slot-scope="scope">{{ scope.row.jobDesc }}</template>
      </el-table-column>
      <el-table-column align="center" label="开始时间">
        <template slot-scope="scope">{{ scope.row.triggerTime }}</template>
      </el-table-column>
      <el-table-column align="center" label="结束时间">
        <template slot-scope="scope">{{ scope.row.handleEndTime }}</template>
      </el-table-column>
      <el-table-column align="center" label="执行时长(s)">
        <template slot-scope="scope">{{ scope.row.handleProcessTime }}</template>
      </el-table-column>
      <el-table-column align="center" label="任务类型">
        <template slot-scope="scope">{{ scope.row.taskType }}</template>
      </el-table-column>
      <el-table-column align="center" label="执行结果">
        <template slot-scope="scope"><span :style="`color:${scope.row.triggerCode===500?'red':''}`">
          {{ (statusList.find(t => t.value === scope.row.triggerCode) || {}).label || ''}}
        </span></template>
      </el-table-column>
      <el-table-column label="任务日志" align="center" width="300">
        <template slot-scope="{ row }">
          <el-button
            v-show="row.executorAddress"
            type="primary"
            @click="handleViewJobLog(row)"
          >日志查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNum"
      :limit.sync="listQuery.pageSize"
      @pagination="fetchData"
    />
    <el-dialog title="日志查看" :visible.sync="dialogVisible" width="95%">
      <div class="log-container">
        <pre :loading="logLoading" v-text="logContent" />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false"> 关闭 </el-button>
        <el-button type="primary" @click="loadLog"> 刷新日志 </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as log from '@/api/datax/datax-job-log'
import * as job from '@/api/datax/datax-job-info'
import * as jobMul from '@/api/datax/datax-job-summary'
import waves from '@/directive/waves' // waves directive
import Pagination from '@/components/Pagination'
import * as taskLog from '@/api/workflow/workflowTaskLog' // secondary package based on el-pagination

export default {
  name: 'WorkflowTaskLog',
  components: { Pagination },
  directives: { waves },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'gray',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      deleteDialogVisible: false,
      dialogVisible: false,
      list: null,
      listLoading: true,
      total: 0,
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        taskName: '',
        taskType: '',
        workflowInstanceId: 0,
        handleProcessTime: ''
      },
      dialogPluginVisible: false,
      pluginData: [],
      dialogFormVisible: false,
      dialogStatus: '',
      executorList: '',
      textMap: {
        create: 'Clear'
      },
      rules: {},
      temp: {
        deleteType: 1,
        jobGroup: 0,
        jobId: 0
      },
      jobSummaryList: [],
      statusList: [
        { value: 500, label: '失败' },
        { value: 502, label: '失败(超时)' },
        { value: 200, label: '成功' },
        { value: 300, label: '正在执行' },
        { value: 0, label: '无' }
      ],
      logStatusList: [
        { value: -1, label: '全部' },
        { value: 1, label: '成功' },
        { value: 2, label: '失败' },
        { value: 3, label: '进行中' }
      ],
      taskTypeList: [
        {value: 'DATAX', label: 'DATAX任务'},
        {value: 'DBMS', label: 'DBMS任务'}
      ],
      // 日志查询参数
      jobLogQuery: {
        executorAddress: '',
        triggerTime: '',
        id: '',
        fromLineNum: 1
      },
      // 日志内容
      logContent: '',
      // 显示日志
      logShow: false,
      // 日志显示加载中效果
      logLoading: false,
      // 日期范围
      dateRange: []
    }
  },
  mounted() {
    if (this.$route.query.workflowInstanceId) {
      this.listQuery.workflowInstanceId = this.$route.query.workflowInstanceId
    }
    this.fetchData()
  },
  created() {
    this.getExecutor()
  },

  methods: {
    fetchData() {
      this.listLoading = true
      const param = Object.assign({}, this.listQuery)

      if (this.dateRange) {
        param.startDateTime =this.dateRange[0]
        param.endDateTime =this.dateRange[1]
      }

      taskLog.list(param).then(response => {
        this.total = response.total
        this.list = response.rows
        this.listLoading = false
      })
      jobMul.getJobIdList().then(response => {
        this.jobSummaryList = response.content
      })
    },
    getExecutor() {
      job.getExecutorList().then((response) => {
        const { content } = response
        this.executorList = content
        const defaultParam = { id: 0, title: '全部' }
        this.executorList.unshift(defaultParam)
        this.listQuery.jobGroup = this.executorList[0].id
      })
    },
    //查看dbms日志
    // 获取DBMS日志
    loadDBMSLog(row) {
      this.logLoading = true
      this.logContent = row.executorAddress
      this.logLoading = false
    },
    // 查看日志
    handleViewJobLog(row) {
      if (row.taskType === 'DBMS') {
        this.dialogVisible = true
        if (this.logShow === false) {
          this.logShow = true
        }
        this.loadDBMSLog(row)
      }
      if (row.taskType === 'DATAX') {
        // const str = location.href.split('#')[0]
        // window.open(`${str}#/ router的name `)
        this.dialogVisible = true

        this.jobLogQuery.executorAddress = row.executorAddress
        this.jobLogQuery.id = row.id
        this.jobLogQuery.triggerTime = Date.parse(row.triggerTime)
        if (this.logShow === false) {
          this.logShow = true
        }
        // window.open(`#/data/log?executorAddress=${this.jobLogQuery.executorAddress}&triggerTime=${this.jobLogQuery.triggerTime}&id=${this.jobLogQuery.id}&fromLineNum=${this.jobLogQuery.fromLineNum}`)
        this.loadLog()
      }
    },
    // 获取日志
    loadLog() {
      this.logLoading = true
      log
        .viewJobLog(
          this.jobLogQuery.executorAddress,
          this.jobLogQuery.triggerTime,
          this.jobLogQuery.id,
          this.jobLogQuery.fromLineNum
        )
        .then((response) => {
          // 判断是否是 '\n'，如果是表示显示完成，不重新加载
          if (response.content.logContent === '\n') {
            // this.jobLogQuery.fromLineNum = response.toLineNum - 20;
            // 重新加载
            // setTimeout(() => {
            //   this.loadLog()
            // }, 2000);
          } else {
            this.logContent = response.content.logContent
          }
          this.logLoading = false
        })
    },
    killRunningJob(row) {
      log.killJob(row).then((response) => {
        this.fetchData()
        this.dialogFormVisible = false
        this.$notify({
          title: 'Success',
          message: 'Kill Successfully',
          type: 'success',
          duration: 2000
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.log-container {
  margin-bottom: 20px;
  background: #f5f5f5;
  width: 100%;
  height: 500px;
  overflow: scroll;
  pre {
    display: block;
    padding: 10px;
    margin: 0 0 10.5px;
    word-break: break-all;
    word-wrap: break-word;
    color: #334851;
    background-color: #f5f5f5;
    // border: 1px solid #ccd1d3;
    border-radius: 1px;
  }
}
</style>
