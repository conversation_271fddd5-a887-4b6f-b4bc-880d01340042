<template>
  <div class="app-container">
    <div ref="queryFormRef" class="filter-container">
      <el-select v-model="listQuery.jobId" placeholder="所属工作流" class="filter-item" filterable clearable>
        <el-option v-for="item in jobIdList" :key="item.id" :label="item.workflowName" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.nodeName" placeholder="节点名称" class="filter-item" clearable filterable>
        <el-option v-for="item in nodeList" :key="item" :label="item" :value="item" />
      </el-select>
      <el-select v-model="listQuery.projectIds" multiple placeholder="所属项目" class="filter-item" clearable filterable>
        <el-option v-for="item in jobProjectListArr" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <!--      <el-select v-model="listQuery.datasourceType" clearable placeholder="数据库类型" style="width: 200px" class="filter-item">-->
      <!--        <el-option v-for="item in datasourceTypes" :key="item.value" :label="item.label" :value="item.value" />-->
      <!--      </el-select>-->
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="fetchData">
        搜索
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-edit"
        @click="handleCreateChild">
        添加
      </el-button>
    </div>
    <el-table ref="fullHeightTableRef" :height="tableHeight" v-loading="listLoading" :data="list" element-loading-text="Loading" border fit highlight-current-row
      style="width: 100%" size="medium">
      <el-table-column label="序号" width="55" align="center">
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工作流节点名称" align="center" width="300px">
        <template slot-scope="scope">{{ scope.row.taskName }}</template>
      </el-table-column>
      <el-table-column label="所属工作流" align="center" width="200px">
        <template slot-scope="scope">{{ scope.row.workflowName }}</template>
      </el-table-column>
      <el-table-column label="调度顺序" align="center" width="80px">
        <template slot-scope="scope">{{ scope.row.scheduleSort }}</template>
      </el-table-column>
      <el-table-column label="节点任务" align="center" width="200px">
        <template slot-scope="scope">{{ scope.row.nodeTaskName }}</template>
      </el-table-column>
      <el-table-column label="任务类型" align="center" width="120">
        <template slot-scope="scope">{{ scope.row.taskType }}</template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="150">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.enable" active-color="#00A854" active-text="启用" :active-value="1"
            inactive-color="#F04134" inactive-text="禁用" :inactive-value="0" @change="changeSwitch(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="180">
        <template slot-scope="scope">{{ scope.row.createTime }}</template>
      </el-table-column>
      <el-table-column label="节点描述" align="center" width="120">
        <template slot-scope="scope">
          {{ scope.row.taskRemark }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="80px">
        <template slot-scope="{row}">
          <!-- 操作 -->
          <el-dropdown trigger="click">
            <span class="el-dropdown-link">
              操作<i class="el-icon-arrow-down el-icon--right" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-show="false" @click.native="handlerViewLog(row)">查看日志</el-dropdown-item>
              <el-dropdown-item @click.native="handleviewDetail(row)">查看明细</el-dropdown-item>
              <el-dropdown-item divided @click.native="handlerUpdate(row)">编辑</el-dropdown-item>
              <el-dropdown-item @click.native="handlerDelete(row)">删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size"
      @pagination="fetchData" />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="1000px"
      :before-close="handleClose">
      <el-form ref="workflow" :rules="rules" :model="temp" label-position="left" label-width="110px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item v-if="dialogStatus === 'createChild' || dialogStatus === 'updateChild'" label="所属工作流"
              prop="affiliationWorkflow">
              <el-select v-model="temp.workflowId" placeholder="所属工作流" filterable clearable>
                <el-option v-for="item in jobIdList" :key="item.id" :label="item.workflowName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="dialogStatus === 'createChild' || dialogStatus === 'updateChild'" label="节点名称"
              prop="taskName">
              <el-input v-model="temp.taskName" size="medium" placeholder="请输入节点名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item v-if="dialogStatus === 'createChild' || dialogStatus === 'updateChild'" label="节点任务类型"
              prop="taskType">
              <el-select v-model="temp.taskType" placeholder="请选择要创建的节点任务类型" @change="changeTaskType" filterable clearable>
                <el-option v-for="item in taskTypeList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="dialogStatus === 'createChild' || dialogStatus === 'updateChild'" label="任务"
              prop="taskId">
              <el-select v-model="temp.taskId" placeholder="请选择任务" filterable clearable>
                <el-option v-for="item in taskList" :key="item.id" :label="`${item.name} ${item.code}`"
                  :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item v-if="dialogStatus !== 'createChild' && dialogStatus !== 'updateChild'" label="工作流名称"
              prop="workflowName">
              <el-input v-model="temp.workflowName" size="medium" placeholder="请输入工作流名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-dialog title="提示" :visible.sync="showCronBox" width="60%" append-to-body>
              <cron v-model="temp.cron" />
              <span slot="footer" class="dialog-footer">
                <el-button @click="showCronBox = false;">关闭</el-button>
                <el-button type="primary" @click="showCronBox = false">确 定</el-button>
              </span>
            </el-dialog>
            <el-form-item v-if="dialogStatus !== 'createChild' && dialogStatus !== 'updateChild'" label="Cron"
              prop="cron">
              <el-input v-model="temp.cron" auto-complete="off" placeholder="请输入Cron表达式">
                <el-button v-if="!showCronBox" slot="append" icon="el-icon-turn-off" title="打开图形配置"
                  @click="showCronBox = true" />
                <el-button v-else slot="append" icon="el-icon-open" title="关闭图形配置" @click="showCronBox = false" />
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" />
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item v-if="dialogStatus !== 'createChild' && dialogStatus !== 'updateChild'" label="所属项目"
              prop="projectId">
              <el-select v-model="temp.projectId" placeholder="所属项目">
                <el-option v-for="item in jobProjectListArr" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="dialogStatus !== 'createChild' && dialogStatus !== 'updateChild'" label="任务描述"
              prop="taskRemark">
              <el-input v-model="temp.taskRemark" size="medium" placeholder="请输入任务描述" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item v-if="dialogStatus !== 'createChild' && dialogStatus !== 'updateChild'" label="上次执行时间"
              prop="lastTriggerTime">
              <el-date-picker v-model="temp.lastTriggerTime" disabled type="datetime" placeholder="上次执行时间"
                format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%"
                :picker-options="{ disabledDate: disableDate }" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col v-if="dialogStatus !== 'createChild' && dialogStatus !== 'updateChild'" :span="12">
            <el-switch v-model="temp.enable" active-color="#00A854" active-text="上线" :active-value="1"
              inactive-color="#F04134" inactive-text="下线" :inactive-value="0" />
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item v-if="dialogStatus === 'createChild' || dialogStatus === 'updateChild'" label="节点描述"
              prop="taskRemark">
              <el-input v-model="temp.taskRemark" size="medium" placeholder="请输入节点描述" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="dialogStatus === 'createChild' || dialogStatus === 'updateChild'" label="调度顺序"
              prop="scheduleSort">
              <el-input-number v-model="temp.scheduleSort" :min="1" :max="10000" label="调度顺序" @change="handleChange" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col v-if="dialogStatus === 'createChild' || dialogStatus === 'updateChild'" :span="12">
            <el-switch v-model="temp.enable" active-color="#00A854" active-text="启用" :active-value="1"
              inactive-color="#F04134" inactive-text="禁用" :inactive-value="0" />
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary"
          @click="dialogStatus === 'create' || dialogStatus === 'createChild' ? createChildData(dialogStatus) : updateChildData()">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import * as executor from '@/api/datax/datax-executor'
import * as job from '@/api/datax/datax-job-info'
import * as workflowTaskInfo from '@/api/workflow/workflow-task-info'
import waves from '@/directive/waves' // waves directive
import Cron from '@/components/Cron'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import * as datasourceApi from '@/api/datax/datax-jdbcDatasource'
import * as jobProjectApi from '@/api/datax/datax-job-project'
import { list as jdbcDsList } from '@/api/datax/datax-jdbcDatasource'

export default {
  mixins: [tableFullHeight],
  name: 'WorkflowTaskList',
  components: { Pagination, Cron },
  directives: { waves },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'gray',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },
  // watch: {
  //   // 'temp.datasourceType': function(newVal) {
  //   //   if (newVal !== null && newVal !== '' && newVal !== undefined){
  //   //     this.disableFlag = false
  //   //   }
  //   //   if(newVal === 'Mysql'){
  //   //     this.jdbcDsQuery.datasourceType = 'mysql'
  //   //     this.getJdbcDs()
  //   //   }
  //   // }
  // },
  data() {
    const validateIncParam = (rule, value, callback) => {
      if (!value) {
        callback(new Error('Increment parameters is required'))
      }
      callback()
    }
    const validatePartitionParam = (rule, value, callback) => {
      if (!this.partitionField) {
        callback(new Error('Partition parameters is required'))
      }
      callback()
    }
    const urlJobId = this.$route.query.jobId
    return {
      disableFlag: false,
      jdbcDsQuery: {
        current: 1,
        size: 200,
        datasource: '',
        ascs: 'datasource_name'
      },
      rDsList: [],
      // projectIds: '',
      list: null,
      listLoading: true,
      total: 0,
      listQuery: {
        current: 1,
        size: 10,
        jobGroup: 0,
        projectIds: '',
        triggerStatus: -1,
        nodeName: '',
        taskType: '',
        datasourceType: '',
        jobId: urlJobId ? Number(urlJobId) : undefined
      },
      showCronBox: false,
      dialogPluginVisible: false,
      pluginData: [],
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑',
        create: '新建',
        createChild: '新建节点',
        updateChild: '编辑节点'
      },
      rules: {
        jobGroup: [{ required: true, message: 'jobGroup is required', trigger: 'change' }],
        executorRouteStrategy: [{ required: true, message: 'executorRouteStrategy is required', trigger: 'change' }],
        executorBlockStrategy: [{ required: true, message: 'executorBlockStrategy is required', trigger: 'change' }],
        taskType: [{ required: true, message: 'jobType is required', trigger: 'change' }],
        projectId: [{ required: true, message: 'projectId is required', trigger: 'change' }],
        jobDesc: [{ required: true, message: 'jobDesc is required', trigger: 'blur' }],
        jobProject: [{ required: true, message: 'jobProject is required', trigger: 'blur' }],
        jobCron: [{ required: true, message: 'jobCron is required', trigger: 'blur' }],
        incStartId: [{ trigger: 'blur', validator: validateIncParam }],
        replaceParam: [{ trigger: 'blur', validator: validateIncParam }],
        primaryKey: [{ trigger: 'blur', validator: validateIncParam }],
        incStartTime: [{ trigger: 'change', validator: validateIncParam }],
        partitionField: [{ trigger: 'blur', validator: validatePartitionParam }],
        datasourceId: [{ trigger: 'change', validator: validateIncParam }],
        readerTable: [{ trigger: 'blur', validator: validateIncParam }],
        tableTimeParam: [{ trigger: 'change', validator: validateIncParam }],
        datasourceType: [{ trigger: 'change', validator: validateIncParam }],
        taskName: [{ required: true, message: 'nodename is required', trigger: 'blur' }],
        taskId: [{ required: true, message: 'taskId is required', trigger: 'blur' }]

      },
      temp: {
        id: undefined,
        workflowId: '',
        taskName: '',
        projectId: null,
        enable: '',
        taskType: '',
        cron: '',
        taskRemark: '',
        triggerStatus: '',
        scheduleSort: 1,
        taskId: null
      },
      resetTemp() {
        this.temp = this.$options.data.call(this).temp
        this.jobSql = ''
        this.timeOffset = 0
        this.timeFormatType = 'yyyy-MM-dd'
        this.partitionField = ''
      },
      enabled: '1',
      executorList: '',
      jobIdList: '',
      jobProjectListArr: [],
      dataSourceList: '',
      blockStrategies: [
        { value: 'SERIAL_EXECUTION', label: '单机串行' },
        { value: 'DISCARD_LATER', label: '丢弃后续调度' },
        { value: 'COVER_EARLY', label: '覆盖之前调度' }
      ],
      routeStrategies: [
        { value: 'FIRST', label: '第一个' },
        { value: 'LAST', label: '最后一个' },
        { value: 'ROUND', label: '轮询' },
        { value: 'RANDOM', label: '随机' },
        { value: 'CONSISTENT_HASH', label: '一致性HASH' },
        { value: 'LEAST_FREQUENTLY_USED', label: '最不经常使用' },
        { value: 'LEAST_RECENTLY_USED', label: '最近最久未使用' },
        { value: 'FAILOVER', label: '故障转移' },
        { value: 'BUSYOVER', label: '忙碌转移' }
        // { value: 'SHARDING_BROADCAST', label: '分片广播' }
      ],
      writeModeList: [
        // { value: 'insert', label: 'insert' },
        // { value: 'replace', label: 'replace' },
        { value: 'UPDATE', label: 'UPDATE' }
        // { value: 'on duplicated', label: 'on duplicated' }
      ],
      taskTypes: [
        { value: 'DATAX', label: 'DATAX' },
        { value: 'DBMS_MYSQL', label: 'MYSQL' },
        { value: 'DBMS_ORACLE', label: 'ORACLE' },
        { value: 'DBMS_SQLSERVER', label: 'SQLSERVER' }
      ],
      datasourceTypes: [
        { value: 'MYSQL', label: 'MYSQL' }
        // { value: 'ORACLE', label: 'ORACLE' },
        // { value: 'SQL SERVER', label: 'SQL SERVER' }
      ],
      incrementTypes: [
        { value: 0, label: '无' },
        { value: 1, label: '主键自增' },
        { value: 2, label: '时间自增' },
        { value: 3, label: 'HIVE分区' }
      ],
      triggerNextTimes: '',
      registerNode: [],
      jobSql: '',
      lastHandleCode: '',
      timeOffset: 0,
      timeFormatType: 'yyyy-MM-dd',
      partitionField: '',
      timeFormatTypes: [
        { value: 'yyyy-MM-dd', label: 'yyyy-MM-dd' },
        { value: 'yyyyMMdd', label: 'yyyyMMdd' },
        { value: 'yyyy/MM/dd', label: 'yyyy/MM/dd' }
      ],
      replaceFormatTypes: [
        { value: 'yyyy/MM/dd', label: 'yyyy/MM/dd' },
        { value: 'yyyy-MM-dd', label: 'yyyy-MM-dd' },
        { value: 'HH:mm:ss', label: 'HH:mm:ss' },
        { value: 'yyyy/MM/dd HH:mm:ss', label: 'yyyy/MM/dd HH:mm:ss' },
        { value: 'yyyy-MM-dd HH:mm:ss', label: 'yyyy-MM-dd HH:mm:ss' },
        { value: 'Timestamp', label: '时间戳' }
      ],
      statusList: [
        { value: 500, label: '失败' },
        { value: 502, label: '失败(超时)' },
        { value: 200, label: '成功' },
        { value: 0, label: '无' }
      ],
      taskTypeList: [
        { value: 'DATAX', label: 'DATAX任务' },
        { value: 'MYSQL', label: 'MYSQL任务' },
        { value: 'SHELL', label: 'SHELL任务' },
        { value: 'HTTP', label: 'HTTP任务' }
      ],
      taskList: [],
      nodeList: []
    }
  },
  created() {
    this.fetchData()
    this.getExecutor()
    this.getWorkflowList()
    this.getJobProject()
    this.getDataSourceList()
    this.getJdbcDs()
    this.getNodeList()
  },

  methods: {
    handleChange(value) {
      this.temp.scheduleSort = value
    },
    disableDate(time) {
      return +time >= +new Date()
    },
    handleSQLChange(sql) {
      this.jobSql = sql
    },
    handleDataSouceTypeChange(type) {
      this.jdbcDsQuery.datasource = type
      this.loading = true
      jdbcDsList(this.jdbcDsQuery).then(response => {
        const { records } = response.data
        this.rDsList = records
        this.loading = false
      })
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
        })
        .catch(_ => {
        })
    },
    getExecutor() {
      job.getExecutorList().then(response => {
        const { content } = response
        this.executorList = content
      })
    },
    getNodeList() {
      workflowTaskInfo.getNodeList().then(response => {
        const { content } = response
        this.nodeList = content
      })
    },
    getWorkflowList() {
      workflowTaskInfo.getJobIdList().then(response => {
        const { content } = response
        this.jobIdList = content
      })
      return this.jobIdList;
    },
    getJobProject() {
      jobProjectApi.getJobProjectList().then(response => {
        this.jobProjectListArr = response.data
      })
    },
    getDataSourceList() {
      datasourceApi.getDataSourceList().then(response => {
        this.dataSourceList = response
      })
    },
    fetchData() {
      this.listLoading = true
      const param = Object.assign({}, this.listQuery)
      param.jobId = param.jobId ? param.jobId : 0
      param.projectIds = param.projectIds.toString()
      workflowTaskInfo.getList(param).then(response => {
        const { content } = response
        this.total = content.recordsTotal
        this.list = content.data
        this.listLoading = false
      })
    },
    incStartTimeFormat(vData) {
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['workflow'].clearValidate()
      })
    },
    handleCreateChild() {
      const urlJobId = this.$route.query.jobId
      this.resetTemp()
      this.dialogStatus = 'createChild'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['workflow'].clearValidate()
        this.getWorkflowList()
        if (urlJobId) {
          console.log(this.jobIdList);
          this.temp.workflowId = Number(urlJobId);
          console.log(urlJobId)
        }
      })

    },
    createChildData(dialogStatus) {
      if (dialogStatus === 'createChild') {
        this.$refs['workflow'].validate((valid) => {
          if (valid) {
            workflowTaskInfo.createJob(this.temp).then(() => {
              this.fetchData()
              this.dialogFormVisible = false
              this.$notify({
                title: 'Success',
                message: 'Created Successfully',
                type: 'success',
                duration: 2000
              })
            })
          }
        })
      } else {
        this.$refs['workflow'].validate((valid) => {
          if (valid) {
            // 创建子任务
            workflowTaskInfo.createChildJob(this.temp).then(() => {
              this.fetchData()
              this.dialogFormVisible = false
              this.$notify({
                title: 'Success',
                message: 'Created Successfully',
                type: 'success',
                duration: 2000
              })
            })
          }
        })
      }
    },
    handlerUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign({}, row)
      this.getTaskList(row.taskType)
      const arrchildSet = []
      const arrJobIdList = []
      if (this.jobIdList) {
        for (const n in this.jobIdList) {
          if (this.jobIdList[n].id !== this.temp.id) {
            arrJobIdList.push(this.jobIdList[n])
          }
        }
        this.JobIdList = arrJobIdList
      }

      if (this.temp.childJobId) {
        const arrString = this.temp.childJobId.split(',')
        for (const i in arrString) {
          for (const n in this.jobIdList) {
            if (this.jobIdList[n].id === parseInt(arrString[i])) {
              arrchildSet.push(this.jobIdList[n])
            }
          }
        }
        this.temp.childJobId = arrchildSet
      }
      if (this.temp.partitionInfo) {
        const partition = this.temp.partitionInfo.split(',')
        this.partitionField = partition[0]
        this.timeOffset = partition[1]
        this.timeFormatType = partition[2]
      }
      this.dialogStatus = 'updateChild'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['workflow'].clearValidate()
      })
    },
    updateChildData() {
      this.$refs['workflow'].validate((valid) => {
        if (valid) {
          if (this.temp.childJobId) {
            const auth = []
            for (const i in this.temp.childJobId) {
              auth.push(this.temp.childJobId[i].id)
            }
            this.temp.childJobId = auth.toString()
          }
          this.temp.executorHandler = this.temp.glueType === 'BEAN' ? 'executorJobHandler' : ''
          if (this.partitionField) this.temp.partitionInfo = this.partitionField + ',' + this.timeOffset + ',' + this.timeFormatType
          workflowTaskInfo.updateJob(this.temp).then(() => {
            this.fetchData()
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handlerDelete(row) {
      this.$confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        workflowTaskInfo.removeWorkFLow(row.id).then(response => {
          this.fetchData()
          this.$notify({
            title: 'Success',
            message: 'Delete Successfully',
            type: 'success',
            duration: 2000
          })
        })
      })

      // const index = this.list.indexOf(row)
    },
    handlerExecute(row) {
      this.$confirm('确定执行吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const param = {}
        param.jobId = row.id
        param.executorParam = row.executorParam
        job.triggerJob(param).then(response => {
          this.$notify({
            title: 'Success',
            message: 'Execute Successfully',
            type: 'success',
            duration: 2000
          })
        })
      })
    },
    // 查看日志
    handlerViewLog(row) {
      const type = row.taskType
      // if (type === 'MYSQL') {
      //   this.$router.push({ path: '/dataDev/dbms/sqlTaskLog', query: { jobId: row.taskId }})
      // }
      // if (type === 'DATAX') {
      //   this.$router.push({ path: '/dataDev/dataxJob/dataxJobLog', query: { jobId: row.taskId }})
      // }
      // if (type === 'SHELL') {
      //   this.$router.push({ path: '/dataDev/dataxJob/dataxJobLog', query: { jobId: row.taskId }})
      // }
    },
    handleviewDetail(row) {
      const type = row.taskType
      if (type === 'MYSQL') {
        this.$router.push({ path: '/dataDev/dbms/sqlTaskList', query: { groupId: row.taskId } })
      }
      if (type === 'DATAX' || type === 'SHELL') {
        this.$router.push({ path: '/dataDev/dataxJob/dataxJobInfo', query: { missionId: row.taskId } })
      }
      if (type === 'HTTP') {
        this.$router.push({ path: '/dataDev/hts/httpJobInfo', query: { summaryId: row.taskId } })
      }
    },
    handlerStart(row) {
      workflowTaskInfo.startJob(row.id).then(response => {
        this.$notify({
          title: 'Success',
          message: 'Start Successfully',
          type: 'success',
          duration: 2000
        })
      })
    },
    handlerStop(row) {
      workflowTaskInfo.stopJob(row.id).then(response => {
        this.$notify({
          title: 'Success',
          message: 'Start Successfully',
          type: 'success',
          duration: 2000
        })
      })
    },
    changeSwitch(row) {
      row.enable === 1 ? this.handlerStart(row) : this.handlerStop(row)
    },
    nextTriggerTime(row) {
      workflowTaskInfo.nextTriggerTime(row.cron).then(response => {
        const { content } = response
        this.triggerNextTimes = content.join('<br>')
      })
    },
    loadById(row) {
      executor.loadById(row.jobGroup).then(response => {
        this.registerNode = []
        const { content } = response
        this.registerNode.push(content)
      })
    },
    // 获取可用数据源
    getJdbcDs() {
      this.loading = true
      jdbcDsList(this.jdbcDsQuery).then(response => {
        const { records } = response.data
        this.rDsList = records
        this.loading = false
      })
    },
    getTaskList(type) {
      this.taskList = [];
      if (type === 'DATAX') {
        workflowTaskInfo.getDataXTaskList(type).then(res => {
          res.content.forEach(item => {
            this.taskList.push({
              id: item.id,
              name: item.jobDesc,
              code: ""
            })
          })
        })
      } else if (type === 'MYSQL') {
        workflowTaskInfo.getSqlTaskList(type).then(res => {
          res.content.forEach(item => {
            this.taskList.push({
              id: item.id,
              name: item.jobDesc,
              code: item.code
            })
          })
          // const { content } = res
          // this.taskList = content
        })
      } else if (type === 'SHELL') {
        workflowTaskInfo.getShellTaskList(type).then(res => {
          res.content.forEach(item => {
            this.taskList.push({
              id: item.id,
              name: item.jobDesc,
              code: ""
            })
          })
          // const { content } = res
          // this.taskList = content
        })
      } else if (type === 'HTTP') {
        workflowTaskInfo.getHttpTaskList(type).then(res => {
          res.content.forEach(item => {
            this.taskList.push({
              id: item.id,
              name: item.name,
              code: item.code
            })
          })
          // const { content } = res
          // this.taskList = content
        })
      }
    },
    changeTaskType(value) {
      console.log(value)
      this.temp.taskType = value
      this.temp.taskId = null
      this.getTaskList(this.temp.taskType)
    }
  }
}
</script>

<style>
.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
}

.el-dropdown+.el-dropdown {
  margin-left: 15px;
}
</style>
