export default {
  name: 'report-type',
  data() {
    return {
      pageData: {
        //查询表单内容 start
        searchForm: [
          { type: 'Input', label: '类型名称', prop: 'reportTypeName' },
        ],
        //查询表单内容 end
        //查询条件 start
        queryData: {
          reportTypeName: "",//类型名称
        },
        //查询条件 end
        //查询表单按钮start
        searchHandle: [
          { label: '查询', type: 'primary', handle: () => this.getTableList(), auth: 'reportType_search' },
          { label: '重置', type: 'warning', handle: () => this.resetSearch(), auth: 'reportType_search' }
        ],
        //查询表单按钮end
        //表格数据start
        tableData: [],
        //表格数据end
        //表格工具栏按钮 start
        tableHandles: [
          { label: '新增', type: 'primary', handle: () => this.showModal(this.$commonConstants.modalType.insert), auth: 'reportType_insert' },
          { label: '批量删除', type: 'danger', handle: () => this.deleteBatch(), auth: 'reportType_batchDelete' }
        ],
        //表格工具栏按钮 end
        selectList: [],//表格选中的数据
        //表格分页信息start
        tablePage: {
          currentPage: 1,
          pageSize: 10,
          pageTotal: 0,
          pageSizeRange: [5, 10, 20, 50]
        },
        //表格分页信息end
        //表格列表头start
        tableCols: [
          { label: '类型名称', prop: 'reportTypeName', align: 'center', overflow: true },
          {
            label: '操作', prop: 'operation', align: 'center', type: 'button', width: 200, fixed: 'right',
            btnList: [
              { label: '查看', type: 'text', auth: 'reportType_getDetail', handle: (row) => this.showModal(this.$commonConstants.modalType.detail, row.id) },
              { label: '编辑', type: 'text', auth: 'reportType_update', handle: (row) => this.showModal(this.$commonConstants.modalType.update, row.id) },
              { label: '删除', type: 'text', auth: 'reportType_delete', handle: (row) => this.deleteOne(row.id) },
            ]
          }
        ],
        // 表格列表头end
        // modal配置 start
        modalConfig: {
          title: "新增", //弹窗标题,值为:新增，查看，编辑
          show: false, //弹框显示
          formEditDisabled: false,//编辑弹窗是否可编辑
          width: '700px',//弹出框宽度
          modalRef: "modalRef",//modal标识
          type: "1"//类型 1新增 2编辑 3保存
        },
        // modal配置 end
        // modal表单 start
        modalForm: [
          { type: 'Input', label: '类型名称', prop: 'reportTypeName', width: "100%", rules: { required: true, maxLength: 50 } },
        ],
        // modal表单 end
        // modal 数据 start
        modalData: {
          // modal页面数据
          reportTypeName: "",//类型名称
        },
        // modal 数据 end
        // modal 按钮 start
        modalHandles: [
          { label: '取消', type: 'default', handle: () => this.closeModal() },
          { label: '提交', type: 'primary', handle: () => this.save() }
        ],
        //modal 按钮 end
      }
    }
  },
  mounted() {
    this.pageData.tableData = [];
    this.getTableList();
  },
  methods: {
    /**
     * @description: 获取表格数据
     * @param {type} 
     * @return {void}
     */
    getTableList() {
      const requstOption = {
        url: this.$reportApis.reportType.listApi,
        params: Object.assign({}, this.pageData.queryData, this.pageData.tablePage),
      }
      this.$commonUtil.getTableList(requstOption).then(response => {
        this.$commonUtil.tableAssignment(response, this.pageData.tablePage, this.pageData.tableData);
      });
    },
    /**
     * @desc 重新请求
     */
    resetSearch() {
      this.$commonUtil.clearObj(this.pageData.queryData);
      this.getTableList();
    },
    /**
     * @description: modal显示
     * @param {type} 类型 1新增，2编辑 3详情 
     * @param {id} 数据唯一标识
     * @return {void}
     */
    showModal(type, id) {
      this.$commonUtil.showModal(this.pageData.modalConfig, type);
      if (type != this.$commonConstants.modalType.insert) {
        this.getDetail(id);

      }
    },
    /**
     * @description: 获取详细数据
     * @param {id} 数据唯一标识
     * @return {void}
     */
    getDetail(id) {
      const requesOption = {
        url: this.$reportApis.reportType.getDetailApi,
        params: { id },
      }
      this.$commonUtil.doGet(requesOption).then(response => {
        // 数据赋值
        this.$commonUtil.coperyProperties(this.pageData.modalData, response.responseData);
      });
    },
    /**
     * @description: 关闭modal
     * @return {void}
     */
    closeModal() {
      this.$refs['modalRef'].$refs['modalFormRef'].resetFields();// 校验重置
      this.pageData.modalConfig.show = false;//关闭modal
      this.$commonUtil.clearObj(this.pageData.modalData);//清空modalData
    },
    /**
     * @description: 保存数据
     * @return {void}
     */
    save() {
      this.$refs['modalRef'].$refs['modalFormRef'].validate((valid) => {
        if (valid) {
          const requestOption = {
            params: this.pageData.modalData,
            removeEmpty: false,
          }
          if (this.pageData.modalConfig.type == this.$commonConstants.modalType.insert) {
            requestOption.url = this.$reportApis.reportType.insertApi;
          } else {
            requestOption.url = this.$reportApis.reportType.updateApi
          }
          this.$commonUtil.doPost(requestOption).then(response => {
            if (response.code == "200") {
              this.$message.success("新建成功")
              this.closeModal();
              this.getTableList();
            }
          });
        } else {
          return false;
        }
      });
    },
    /**
     * @description: 删除一条数据
     * @param {id} 数据唯一标识 
     * @return: 
     * @author: caiyang
     */
    deleteOne(id) {
      const requesOption = {
        url: this.$reportApis.reportType.deleteOneApi,
        messageContent: this.$commonUtil.getMessageFromList("confirm.delete", null),
        callback: this.getTableList,
        params: { id: id },
        type: "get",
      }
      //弹出删除确认框
      this.$commonUtil.showConfirm(requesOption)
    },
    /**
     * @description: 批量删除
     * @return {void}
     */
    deleteBatch() {
      const length = this.pageData.selectList.length;
      if (length == 0) {
        this.$commonUtil.showMessage({ message: this.$commonUtil.getMessageFromList("error.batchdelete.empty", null), type: this.$commonConstants.messageType.error });
      } else {
        const ids = new Array();
        for (let i = 0; i < length; i++) {
          ids.push(this.pageData.selectList[i].id);
        }
        const requesOption = {
          url: this.$reportApis.reportType.deleteBatchApi,
          messageContent: this.$commonUtil.getMessageFromList("confirm.delete", null),
          callback: this.getTableList,
          params: ids,
          type: "post",
        }
        this.$commonUtil.showConfirm(requesOption);
      }
    },
    selectChange(rows) {
      this.pageData.selectList = rows;
    },
  }
};