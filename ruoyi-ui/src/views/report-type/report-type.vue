<template>
    <div class="_tablepage">
        <searchForm 
            :searchForm="pageData.searchForm" 
            :searchData="pageData.queryData"
            :searchHandle="pageData.searchHandle">
        </searchForm>
        <cusTable :isSelection='true' :isIndex='true' :isPagination='true' :isHandle='true'
            :tableCols='pageData.tableCols' :tableHandles='pageData.tableHandles' :tableData='pageData.tableData'
            :tablePage='pageData.tablePage' @handleCurrentChange='searchtablelist()' @selectChange='selectChange'>
        </cusTable>
        <!-- 新建报表类型 -->
        <modal ref="modalRef" :modalConfig='pageData.modalConfig' :modalForm='pageData.modalForm'
            :modalData='pageData.modalData' :modalHandles='pageData.modalHandles' @closeModal="closeModal()">
        </modal>
    </div>
</template>
<script src="./report-type.js"></script>