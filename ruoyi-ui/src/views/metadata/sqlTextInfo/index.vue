<template>
  <el-row :gutter="20">
    <el-col :span="6">
      <el-card class="box-card tree-wrapper" shadow="always">
        <div class="body-wrapper">
          <el-tree
            ref="type"
            :data="typeOptions"
            node-key="id"
            empty-text="加载中，请稍后"
            :props="defaultProps"
            default-expand-all
            highlight-current
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
          >
            <template slot-scope="{ node, data }">
              <span class="custom-tree-node" @mouseenter="mouseenter(data)" @mouseleave="mouseleave(data)">
                <span><i v-if="node.level === 1" class="iconfont icon-zuzhi tree-folder" />{{ node.label }}</span>
                <span class="tree-bts">
                  <i v-show="!data.id && data.show"  class="el-icon-circle-plus-outline bt-add" @click="() => handleAddType()" />
                  <i v-show="data.id && data.show"  class="el-icon-edit-outline bt-edit" @click="() => handleEditType(data)" />
                  <i v-show="data.id && data.show"  class="el-icon-delete bt-delete" @click="() => handleDelType(data)" />
                </span>
              </span>
            </template>
          </el-tree>
        </div>
      </el-card>
    </el-col>
    <el-col :span="18">
      <el-card class="box-card" shadow="always">
        <sql-editor
          ref="sqleditor"
          :value="formData.textInfo"
          style="height: 300px; margin: 20px 0;"
          @changed="changeTextarea($event)"
        />

        <el-button  style="position: absolute; right: 3vw; top: 20vw;"  type="primary" @click="confirmUpdate">保存</el-button>
      </el-card>
    </el-col>

    <!-- 数据标准类别对话框 -->
    <el-dialog :title="dialog.title" :visible.sync="dialog.open" width="500px" append-to-body>
      <el-form ref="dialogForm" :model="dialogForm" :rules="dialogRules" label-width="100px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="dialogForm.name" placeholder="请输入名称" />
        </el-form-item>
<!--        <el-form-item label="类型" prop="type">-->
<!--          <el-input v-model="dialogForm.type" placeholder="请输入类型" />-->
<!--        </el-form-item>-->
        <el-form-item label="文本信息" prop="textInfo">
          <el-input
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4}"
            placeholder="请输入内容"
            v-model="dialogForm.textInfo">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitDialogForm">确 定</el-button>
        <el-button @click="dialog.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </el-row>
</template>

<script>

import * as sqlTextInfoApi  from '@/api/metadata/sqlTextInfo'
import SqlEditor from "@/components/SqlEditor/index.vue";

export default {
  name: 'sqlTextInfo',
  components: {SqlEditor},
  data() {
    return {
      // 展示切换
      showOptions: {
        data: {},
        showList: true,
        showAdd: false,
        showEdit: false,
        showDetail: false
      },
      // 遮罩层
      loading: true,
      // // 查询参数
      // queryParams: {
      //   pageNum: 1,
      //   pageSize: 20,
      //   typeId: '',
      //   gbCode: '',
      //   gbName: ''
      // },

      formData:{
        id: '',
        name: '',
        type: '',
        textInfo: '',
      },
      // 左侧树
      typeOptions: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      dialog: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: 'sql文本信息'
      },
      dialogForm: {},
      dialogRules: {
        name: [
          { required: true, message: '名称不能为空', trigger: 'change' }
        ],
        type: [
          { required: true, message: '类型不能为空', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.getTree()
  },
  mounted() {
  },
  methods: {
    //点击保存按钮
    confirmUpdate(){
      if(this.formData.id === '' || !this.formData.id){
        this.$message.warning('请先选择左侧数据')
        return;
      }
      this.$confirm('是否确认修改此条数据信息?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        sqlTextInfoApi.updated(this.formData).then(response => {
          if (response.code === 0) {
            this.$message.success('修改成功')
            this.getTree()
          }
        })
      }).catch(() => {
      })
    },
    changeTextarea(val) {
      this.formData.textInfo = val
    },
    // 获取树信息
    getTree() {
      sqlTextInfoApi.list().then(response => {
        if (response.code === 0) {
          const { data } = response
          const tree = {}
          tree.name = 'sql文本列表'
          tree.children = data
          this.typeOptions = []
          this.typeOptions.push(tree)
        }
      })
    },
    /** 节点单击事件 */
    handleNodeClick(data) {
      if (data.id) {
        this.formData = data
        if(!this.formData.textInfo){
          this.formData.textInfo = ''
        }
      }
    },
    /* 点击新增按钮 */
    handleAddType() {
      this.dialog.open = true
      this.dialogForm = {
        name: '',
        type: '',
        textInfo: ''
      }
    },
    submitDialogForm() {
      this.$refs['dialogForm'].validate(valid => {
        if (valid) {
          if (this.dialogForm.id) {
            sqlTextInfoApi.updated(this.dialogForm).then(response => {
              if (response.code === 0) {
                this.$message.success('保存成功')
                this.dialog.open = false
                this.getTree()
              } else {
                this.$message.error('保存失败')
              }
            }).catch(error => {
              this.$message.error(error.msg || '保存失败')
            })
          } else {
            sqlTextInfoApi.created(this.dialogForm).then(response => {
              if (response.code === 0) {
                this.$message.success('保存成功')
                this.dialog.open = false
                this.getTree()
              } else {
                this.$message.error('保存失败')
              }
            }).catch(error => {
              this.$message.error(error.msg || '保存失败')
            })
          }
        }
      })
    },
    /** 树节点鼠标移入移出 */
    mouseenter(data) {
      this.$set(data, 'show', true)
    },
    mouseleave(data) {
      this.$set(data, 'show', false)
    },
    //点击修改按钮
    handleEditType(data) {
      this.dialog.open = true
      this.dialogForm = {
        id: data.id,
        name: data.name,
        type: data.type
      }
    },
    //点击删除按钮
    handleDelType(data) {
      this.$confirm('选中数据将被永久删除, 是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        sqlTextInfoApi.deleted({idList: data.id }).then(response => {
          if (response.code === 0) {
            this.$message.success('删除成功')
            this.getTree()
          }
        })
      }).catch(() => {
      })
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.showOptions.data = {}
      if (!this.queryParams.typeId) {
        this.$message.warning('请先选择数据标准类别')
        return
      }
      this.showOptions.data.typeId = this.queryParams.typeId
      this.showOptions.showList = false
      this.showOptions.showAdd = true
      this.showOptions.showEdit = false
      this.showOptions.showDetail = false
      this.$emit('showCard', this.showOptions)
    },
    /** 修改按钮操作 */
    handleEdit(row) {
      this.showOptions.data.id = row.id
      this.showOptions.showList = false
      this.showOptions.showAdd = false
      this.showOptions.showEdit = true
      this.showOptions.showDetail = false
      this.$emit('showCard', this.showOptions)
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.showOptions.data.id = row.id
      this.showOptions.showList = false
      this.showOptions.showAdd = false
      this.showOptions.showEdit = false
      this.showOptions.showDetail = true
      this.$emit('showCard', this.showOptions)
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm('选中数据将被永久删除, 是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delDataDict(row.id).then(response => {
          if (response.success) {
            this.$message.success('删除成功')
          }
        })
      }).catch(() => {
      })
    }

  }
}
</script>

<style lang="scss" scoped>
.right-toolbar {
  float: right;
}
.el-card ::v-deep .el-card__body {
  height: calc(100vh - 170px);
}
.tree-wrapper {
  overflow-y: auto;
  .body-wrapper {
    margin: -10px;
    ::v-deep .custom-tree-node {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
      .tree-folder {
        margin-right: 5px;
        color: #f6cf07;
      }
      .tree-bts {
        .bt-add {
          color: #409eff;
        }
        .bt-edit {
          color: #67c23a;
        }
        .bt-delete {
          color: #f56c6c;
        }
        i {
          margin-right: 10px;
          padding: 0px;
        }
      }
    }
  }
}
</style>
