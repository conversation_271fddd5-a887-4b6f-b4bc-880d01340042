<template>
  <el-row :gutter="20">
    <el-col :span="6">
      <el-card class="box-card tree-wrapper" shadow="always">
        <el-input
          v-model="tableName"
          placeholder="请输入表名称"
          clearable
          size="small"
          prefix-icon="el-icon-search"
          style="margin-bottom: 20px"
        />
        <el-tree
          ref="tree"
          :data="treeOptions"
          :filter-node-method="filterNode"
          node-key="id"
          empty-text="加载中，请稍后"
          :props="defaultProps"
          highlight-current
          :expand-on-click-node="false"
          @node-click="handleNodeClick"
        >
          <!--   el-tree的属性 默认打开展示树结构       default-expand-all-->

          <span slot-scope="{ node, data }" class="custom-tree-node">
            <i v-if="node.level === 1" class="iconfont icon-shujuku" />
            <i v-else-if="node.level === 2" class="iconfont icon-shujubiao" />
            <span>{{ data.label }}  {{ data.code }}</span>
          </span>
        </el-tree>
      </el-card>
    </el-col>
    <el-col :span="18" v-show="this.queryParams.tableId">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="字段设置" name="0">
          <el-card class="box-card" shadow="always">
            <el-form ref="queryForm" :model="queryParams" :inline="true">
              <el-form-item label="字段名称" prop="columnName">
                <el-input
                  v-model="queryParams.columnName"
                  placeholder="请输入字段名称"
                  clearable
                  size="small"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
            <el-table
              v-loading="loading"
              :data="columnList"
              border
              tooltip-effect="dark"
              :size="tableSize"
              :height="tableHeight"
              style="width: 100%;margin: 15px 0;"
            >
              <el-table-column type="selection" width="55" align="center"/>
              <el-table-column label="序号" width="55" align="center">
                <template slot-scope="scope">
                  <span>{{ scope.$index + 1 }}</span>
                </template>
              </el-table-column>
              <template v-for="(item, index) in tableColumns">
                <el-table-column
                  v-if="item.show"
                  :key="index"
                  :prop="item.prop"
                  :label="item.label"
                  :formatter="item.formatter"
                  align="center"
                  show-overflow-tooltip
                />
              </template>
              <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <el-popover
                    placement="left"
                    trigger="click"
                  >
                    <!--                v-hasPerm="['metadata:datacolumn:detail']"-->
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-view"
                      @click="handleDetail(scope.row)"
                    >详情
                    </el-button>
                    <el-button slot="reference">操作</el-button>
                  </el-popover>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :current-page.sync="queryParams.pageNum"
              :page-size.sync="queryParams.pageSize"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="索引设置" name="1">
          <el-card class="box-card" shadow="always">
            <el-form ref="queryForm" :model="queryParams" :inline="true">
              <el-form-item>
                <el-button type="primary" size="mini" @click="handleRefresh">刷新</el-button>
                <el-button type="success" icon="el-icon-circle-plus-outline" size="mini" @click="handleAddIndex">添加索引</el-button>
              </el-form-item>
            </el-form>
            <el-table
              v-loading="loading"
              :data="indexInfoList"
              border
              tooltip-effect="dark"
              :size="tableSize"
              :height="tableHeight"
              style="width: 100%;margin: 15px 0;"
            >
              <el-table-column type="selection" width="55" align="center"/>
              <el-table-column label="序号" width="55" align="center">
                <template slot-scope="scope">
                  <span>{{ scope.$index + 1 }}</span>
                </template>
              </el-table-column>
              <template v-for="(item, index) in tableIndexes">
                <el-table-column
                  v-if="item.show"
                  :key="index"
                  :prop="item.prop"
                  :label="item.label"
                  :formatter="item.formatter"
                  align="center"
                  show-overflow-tooltip
                />
              </template>
              <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <el-popover
                    placement="left"
                    trigger="click"
                  >
                    <el-button slot="reference">操作</el-button>
                    <el-button type="success" icon="el-icon-remove-outline" size="mini" @click="showUpdateIndex(scope.row)">更新索引</el-button>
                    <el-button type="warning" icon="el-icon-remove-outline" size="mini" @click="handleDeleteIndex(scope.row)">删除索引</el-button>
                  </el-popover>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :current-page.sync="queryParams.pageNum"
              :page-size.sync="queryParams.pageSize"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
            <!-- 变更弹框 -->
            <el-dialog :title="this.title" :visible.sync="open" min-width="600px" append-to-body>
              <el-form ref="dialogFormData" :model="dialogFormData" :rules="rules" label-width="80px">
                <el-form-item label="索引名称" prop="indexName">
                  <el-input v-model="dialogFormData.indexName" placeholder="请输入索引名称" :disabled="stage == 'upd'"/>
                </el-form-item>
                <el-form-item v-show="stage == 'upd'" label="新名称" prop="changeIndexName">
                  <el-input v-model="dialogFormData.changeIndexName" placeholder="请输入新的索引名称)"/>
                </el-form-item>
                <el-form-item label="字段" prop="tableColumnArray">
                  <el-select
                    v-model="dialogFormData.tableColumnArray"
                    filterable
                    multiple
                    @change="$forceUpdate()"
                    placeholder="请选择字段"
                  >
                    <el-option
                      v-for="item in tableColumnList"
                      :key="item.columnName"
                      :label="item.columnName + ' ' + item.columnComment"
                      :value="item.columnName"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="索引类型" prop="indexType">
                  <el-select
                    v-model="dialogFormData.indexType"
                    filterable
                    placeholder="请选择索引类型"
                  >
                    <el-option
                      v-for="item in tableIndexTypeList"
                      :key="item.dictValue"
                      :label="item.dictLabel"
                      :value="item.dictValue"
                    />
                  </el-select>
                </el-form-item>
<!--                <el-form-item label="索引方法" prop="indexMethod">-->
<!--                  <el-select-->
<!--                    v-model="dialogFormData.indexMethod"-->
<!--                    filterable-->
<!--                    placeholder="请选择索引方法"-->
<!--                  >-->
<!--                    <el-option-->
<!--                      v-for="item in tableIndexMethodList"-->
<!--                      :key="item.dictValue"-->
<!--                      :label="item.dictLabel"-->
<!--                      :value="item.dictValue"-->
<!--                    />-->
<!--                  </el-select>-->
<!--                </el-form-item>-->
                <el-form-item label="注释">
                  <el-input v-model="dialogFormData.indexComment" placeholder="请输入注释" />
                </el-form-item>
              </el-form>
              <div slot="footer" class="dialog-footer">
                <el-button @click="cancelAddIndex">取 消</el-button>
                <el-button type="primary" @click="submitIndexInfo">确定</el-button>
              </div>
            </el-dialog>
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </el-col>
  </el-row>
</template>

<script>
import {
  pageDataColumn,
  getDataMetadataTree,
  queryAllColumnsByTable,
  addIndexInfo,
  updateIndexInfo,
  pageDataIndex, deleteIndexInfo
} from '@/api/metadata/datacolumn'
import { refreshMetaTable } from '@/api/metadata/datatable'
import { refreshMetadata } from '@/api/metadata/datasource'
import { getDicts as getDictData } from '@/api/system/dict/data'

export default {
  name: 'DataColumnList',
  data() {
    return {
      tableName: undefined,
      tableColumnList: [],
      tableIndexTypeList: [],
      tableIndexMethodList: [],
      // 弹出层标题
      title: '',
      open: false,
      stage: '',
      activeName: '0',
      tableHeight: document.body.offsetHeight - 310 + 'px',
      // 展示切换
      showOptions: {
        data: {},
        showList: true,
        showDetail: false
      },
      // 遮罩层
      loading: true,
      // 表格头
      tableColumns: [
        { prop: 'columnName', label: '字段名称', show: true },
        { prop: 'columnComment', label: '字段注释', show: true },
        { prop: 'columnKey', label: '是否主键', show: true, formatter: this.keyFormatter },
        { prop: 'columnNullable', label: '是否允许为空', show: true, formatter: this.nullableFormatter },
        { prop: 'dataType', label: '数据类型', show: true },
        { prop: 'dataLength', label: '数据长度', show: true },
        { prop: 'dataPrecision', label: '数据精度', show: true },
        { prop: 'dataScale', label: '数据小数位', show: true },
        { prop: 'dataDefault', label: '数据默认值', show: true }
    ],
      // 索引设置表格头
      tableIndexes: [
        { prop: 'indexName', label: '索引名称', show: true },
        { prop: 'tableColumn', label: '字段', show: true },
        { prop: 'indexType', label: '索引类型', show: true },
        { prop: 'indexMethod', label: '索引方法', show: true },
        { prop: 'indexComment', label: '注释', show: true }
      ],
      // 默认选择中表格头
      checkedTableColumns: [],
      tableSize: 'medium',
      // 表格数据
      columnList: [],
      indexInfoList: [],
      // 总数据条数
      total: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        columnName: '',
        sourceId: '',
        tableId: '',
        id: ''
      },
      // 左侧树
      treeOptions: [],
      defaultProps: {
        children: 'children',
        label: 'label',
        code: 'code'
      },
      // 表单参数
      dialogFormData: {
        indexName: '',
        changeIndexName: '',
        tableColumnArray: [],
        tableColumn: '',
        indexType: '',
        indexMethod: '',
        indexComment: '',
        tableId: '',
        sourceId: ''
      },
      rules: {
        indexName: [{ required: true, message: 'this is required', trigger: 'blur' }],
        // changeIndexName: [{ required: true, message: 'this is required', trigger: 'blur' }],
        tableColumnArray: [{ required: true, message: 'this is required', trigger: 'change' }],
        indexType: [{ required: true, message: 'this is required', trigger: 'change' }],
        indexMethod: [{ required: true, message: 'this is required', trigger: 'change' }]
      }
    }
  },
  watch: {
    // 根据名称筛选树
    tableName(val) {
      this.$refs.tree.filter(val);
    }
  },
  created() {
    //this.getList()
    this.getTree()
  },
  mounted() {
    this.initCols()
  },
  methods: {
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      const labelMatch = data.label.indexOf(value) !== -1; // 检查名称
      const codeMatch = data.code && data.code.indexOf(value) !== -1; // 检查编码
      return labelMatch || codeMatch; // 如果名称或编码匹配，返回 true
    },
    // 取消按钮
    cancelAddIndex() {
      this.open = false;
    },
    /** 新增按钮操作 */
    handleAddIndex() {
      this.open = true;
      this.stage = 'add'
      this.title = '新建索引'
      this.resetDialog()
      this.getIndexTypeDict()
      // this.getIndexMethodDict()
      this.queryAllColumnsByTable(this.queryParams.sourceId, this.queryParams.tableId)
    },
    /** 更新按钮操作 */
    showUpdateIndex(row) {
      this.getIndexTypeDict()
      this.queryAllColumnsByTable(this.queryParams.sourceId, this.queryParams.tableId)
      this.open = true
      this.dialogFormData = row
      this.dialogFormData.tableColumnArray = row.tableColumn.split(",")

      this.stage = 'upd'
      this.title = '更新索引'
    },
    /** 删除按钮操作 */
    handleDeleteIndex(row) {
      const idList = []
      idList.push(row.id)
      deleteIndexInfo({ idList: row.id }).then(res => {
        this.getIndexInfoList()
        this.$notify({
          title: 'Success',
          message: 'Delete Successfully',
          type: 'success',
          duration: 2000
        })
      })
    },
    /** 点击确定 */
    submitIndexInfo() {
      this.$refs['dialogFormData'].validate(valid => {
        if (valid) {
          this.dialogFormData.tableId = this.queryParams.tableId
          this.dialogFormData.sourceId = this.queryParams.sourceId
          this.dialogFormData.tableColumn = this.dialogFormData.tableColumnArray.join(",")
          if (this.stage == 'add') {
            addIndexInfo(this.dialogFormData).then(response => {
              if (response.data) {
                this.getIndexInfoList()
                this.$notify({
                  title: 'Success',
                  message: 'Add Successfully',
                  type: 'success',
                  duration: 2000
                })
                this.open = false
              } else {
                this.$notify({
                  title: 'Fail',
                  message: 'Failed: ' + response.msg,
                  type: 'error',
                  duration: 2000
                })
              }
            })
          } else if (this.stage = 'upd') {
            // 更新

            updateIndexInfo(this.dialogFormData).then(response => {
              if (response.data) {
                this.getIndexInfoList()
                this.$notify({
                  title: 'Success',
                  message: 'Add Successfully',
                  type: 'success',
                  duration: 2000
                })
                this.open = false
              } else {
                this.$notify({
                  title: 'Fail',
                  message: 'Failed: ' + response.msg,
                  type: 'error',
                  duration: 2000
                })
              }
            })
          }
        }
      })
    },
    /** 刷新表 */
    refreshTable() {
      this.queryParams.id = this.queryParams.tableId
      refreshMetaTable(this.queryParams).then((response) => {
        if (response.msg != '') {
          this.$message.error(response.msg)
        }
      }).finally(() => {
        // 刷新缓存
        this.refreshMeta()
      })
    },
    /** 刷新缓存 */
    refreshMeta() {
      refreshMetadata()
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      pageDataColumn(this.queryParams).then(response => {
        this.loading = false
        if (response.success) {
          const { data } = response
          this.columnList = data.data
          this.total = data.data.total
        }
      })
    },
    getIndexInfoList() {
      this.loading = true
      pageDataIndex(this.queryParams).then(response => {
        this.loading = false
        this.indexInfoList = response.data.data
        this.total = response.data.total
      })
    },
    /** 查询列表 */
    queryAllColumnsByTable(sourceId, tableId) {
      const data = {
        sourceId,
        tableId
      }
      queryAllColumnsByTable(data).then(response => {
        this.tableColumnList = response.data
      })
    },
    /** 查询树结构 */
    getTree() {
      this.loading = false
      getDataMetadataTree('table').then(response => {
        if (response.success) {
          const { data } = response
          this.treeOptions = data
        }
      })
    },
    initCols() {
      this.checkedTableColumns = this.tableColumns.map(col => col.prop)
    },
    handleCheckedColsChange(val) {
      this.tableColumns.forEach(col => {
        if (!this.checkedTableColumns.includes(col.prop)) {
          col.show = false
        } else {
          col.show = true
        }
      })
    },
    handleCommand(command) {
      this.tableSize = command
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        columnName: '',
        sourceId: this.queryParams.sourceId,
        tableId: this.queryParams.tableId
      }
      this.handleQuery()
    },
    /** 重置弹窗操作 */
    resetDialog() {
      this.dialogFormData = {
        indexName: '',
        tableColumnArray: '',
        indexType: '',
        indexMethod: '',
        indexComment: '',
        tableId: '',
        sourceId: ''
      }
    },
    /** 刷新列表 */
    handleRefresh() {
      this.getIndexInfoList()
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.showOptions.data.id = row.id
      this.showOptions.showList = false
      this.showOptions.showDetail = true
      this.$emit('showCard', this.showOptions)
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.queryParams.pageNum = val
      this.getList()
    },
    getParentNode(data, id) {
      for (const node of data) {
        if (node.children) {
          for (const child of node.children) {
            if (child.id === id) {
              return node; // 返回父节点
            }
          }
          const parentNode = this.getParentNode(node.children, id);
          if (parentNode) {
            return parentNode; // 在子树中查找
          }
        }
      }
      return null; // 找不到父节点
    },
    /** 节点单击事件 */
    handleNodeClick(data) {
      console.log("data",data)
      const parentNode = this.getParentNode(this.treeOptions, data.id);
      if (parentNode) {
        this.queryParams.sourceId = parentNode.id;
        console.log('父节点id:' + this.queryParams.sourceId)
      } else {
        this.queryParams.sourceId = ''
        console.log('没有父节点')
      }

      if (data.type === 'database') {
        this.queryParams.sourceId = data.id
        this.queryParams.tableId = ''
      } else if (data.type === 'table') {
        this.queryParams.tableId = data.id
        this.refreshTable()
        this.getList()
        this.getIndexInfoList()
      }
    },
    keyFormatter(row, column, cellValue, index) {
      if (cellValue === '1') {
        return 'Y'
      } else {
        return 'N'
      }
    },
    nullableFormatter(row, column, cellValue, index) {
      if (cellValue === '1') {
        return 'Y'
      } else {
        return 'N'
      }
    },
    handleClick(tab, event) {
      console.log(tab, event);
      this.activeName = tab.index
    },
    getIndexTypeDict() {
      getDictData("index_type").then(res => {
        this.tableIndexTypeList = res.data
      })
    }
    // getIndexMethodDict() {
    //   getDictData("index_method").then(res => {
    //     this.tableIndexMethodList = res.data
    //   })
    // }
  }
}
</script>

<style lang="scss" scoped>

::v-deep .el-tree-node__children {
  overflow-x: scroll;
}

.right-toolbar {
  float: right;
}
.el-card ::v-deep .el-card__body {
  height: calc(100vh - 140px);
}
.container {
  display: flex;              /* 使用 Flexbox 布局 */
  height: 100vh;             /* 满足全屏 */
}
.tree-wrapper {
  overflow-y: auto;
  ::v-deep .custom-tree-node {
    flex: 1;
    display: flex;
    font-size: 14px;
    i {
      margin-right: 10px;
    }
    .icon-shujuku {
      color: #20a0ff;
    }
    .icon-shujubiao {
      color: #38dcec;
    }
  }
}

</style>
