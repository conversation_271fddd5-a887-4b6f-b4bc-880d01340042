<template>
  <div class="log-list">
    <el-form ref="queryFormRef" :model="queryParams" :inline="true">
      <el-form-item label="调用时间">
        <el-date-picker v-model="queryParams.datePicker" type="datetimerange" size="small" format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-select v-model="queryParams.category" placeholder="请选择分类" size="small" clearable filterable
          style="width: 120px;">
          <el-option v-for="dict in dict.type.form_category" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item prop="apiName">
        <el-input v-model="queryParams.apiName" placeholder="请输入接口名称" clearable size="small"
          @keyup.enter.native="handleQuery" style="width: 150px;" />
      </el-form-item>
      <el-form-item>
        <el-select v-model="queryParams.status" placeholder="调用结果" size="small" clearable style="width: 120px;">
          <el-option v-for="dict in dict.type.sys_normal_status" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input v-model="queryParams.time" placeholder=">=耗时(ms)" size="small" style="width: 120px;" />
      </el-form-item>
      <el-form-item>
        <el-select v-model="queryParams.deleteType" placeholder="请选择清理范围" size="small" clearable style="width: 200px;">
          <el-option v-for="item in deleteTypeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button size="mini" type="danger" icon="el-icon-delete" @click="handlerDelete">
          清除
        </el-button>
      </el-form-item>
    </el-form>

    <el-table ref="fullHeightTableRef" :height="tableHeight" v-loading="loading" :data="logList" border
      tooltip-effect="dark" :size="tableSize" style="width: 100%;margin: 15px 0;height: 80%">
      <el-table-column type="selection" width="55" align="center" />
      <!--      <el-table-column label="序号" width="55" align="center">-->
      <!--        <template slot-scope="scope">-->
      <!--          <span>{{ scope.$index + 1 }}</span>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column label="分类" width="100" align="center">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.form_category" :value="scope.row.category" />
        </template>
      </el-table-column>
      <template v-for="(item, index) in tableColumns">
        <el-table-column v-if="item.show" :key="index" :prop="item.prop" :label="item.label" :formatter="item.formatter"
          align="center" show-overflow-tooltip />
      </template>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-popover placement="left" trigger="click">
            <el-button v-hasPerm="['market:api:log:detail']" size="mini" type="text" icon="el-icon-view"
              @click="handleDetail(scope.row)">详情</el-button>
            <el-button v-hasPerm="['market:api:log:remove']" size="mini" type="text" icon="el-icon-delete"
              @click="handleDelete(scope.row)">删除</el-button>
            <el-button slot="reference">操作</el-button>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
      :current-page.sync="queryParams.pageNum" :page-size.sync="queryParams.pageSize" :total="total"
      @size-change="handleSizeChange" @current-change="handleCurrentChange" />
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import { pageApiLog, delApiLog, delApiLogs } from '@/api/market/apilog'

export default {
  mixins: [tableFullHeight],
  name: 'ApiLogList',
  dicts: ['form_category', 'sys_normal_status'],
  data() {
    return {
      // 展示切换
      showOptions: {
        data: {},
        showList: true,
        showDetail: false
      },
      // 遮罩层
      loading: true,
      // 表格头
      tableColumns: [
        { prop: 'apiName', label: '接口名称', show: true },
        { prop: 'callerIp', label: '调用者ip', show: true },
        { prop: 'callerSize', label: '调用数据量', show: true },
        { prop: 'time', label: '调用耗时(ms)', show: true },
        {
          prop: 'status',
          label: '状态',
          show: true,
          formatter: this.statusFormatter
        },
        { prop: 'callerDate', label: '调用时间', show: true }
      ],
      deleteTypeList: [
        { value: '1', label: '清理一周之前日志数据' },
        { value: '2', label: '清理两周之前日志数据' },
        { value: '3', label: '清理一个月之前日志数据' },
        { value: '4', label: '清理半年前日志数据' },
        { value: '5', label: '清理所有日志数据' }
      ],
      // 默认选择中表格头
      checkedTableColumns: [],
      tableSize: 'medium',
      // 日志表格数据
      logList: [],
      // 总数据条数
      total: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        apiName: '',
        deleteType: '',
        category: '',
        datePicker: [],
        time: ''
      },
      // 状态数据字典
      statusOptions: []
    }
  },
  created() {
    this.getDicts('sys_normal_status').then(response => {
      if (response.code === 200) {
        this.statusOptions = response.data
      }
    })
    this.getList()
  },
  mounted() {
    this.initCols()
  },
  methods: {
    /** 查询日志列表 */
    getList() {
      this.loading = true
      console.log(this.queryParams)
      pageApiLog(this.queryParams).then(response => {
        this.loading = false
        if (response.code === 200) {
          this.logList = response.rows
          this.total = response.total
        }
      })
    },
    initCols() {
      this.checkedTableColumns = this.tableColumns.map(col => col.prop)
    },
    handleCheckedColsChange(val) {
      this.tableColumns.forEach(col => {
        if (!this.checkedTableColumns.includes(col.prop)) {
          col.show = false
        } else {
          col.show = true
        }
      })
    },
    handleCommand(command) {
      this.tableSize = command
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        apiName: ''
      }
      this.handleQuery()
    },
    /** 刷新列表 */
    handleRefresh() {
      this.getList()
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.showOptions.data.id = row.id
      this.showOptions.showList = false
      this.showOptions.showDetail = true
      this.$emit('showCard', this.showOptions)
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm('选中数据将被永久删除, 是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delApiLog(row.id).then(response => {
          if (response.success) {
            this.$message.success('删除成功')
            this.getList()
          }
        })
      }).catch(() => {
      })
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.queryParams.pageNum = val
      this.getList()
    },
    statusFormatter(row, column, cellValue, index) {
      const dictLabel = this.mySelectDictLabel(this.statusOptions, cellValue)
      if (cellValue === '1') {
        return <el-tag type='success'>{dictLabel}</el-tag>
      } else if (cellValue === '2') {
        // 执行中
        return <el-tag type='warning'>{dictLabel}</el-tag>
      } else if (cellValue === '0') {
        // 失败
        return <el-tag type='danger'>{dictLabel}</el-tag>
      }
    },
    handlerDelete() {
      console.log(this.queryParams.deleteType)
      if (this.queryParams.deleteType === null || this.queryParams.deleteType === '') {
        this.$message.error('请选择日志清理范围')
        return
      }
      delApiLogs(this.queryParams.deleteType).then(result => {
        if (result.success) {
          this.$message.success('删除成功')
          this.getList()
        } else {
          this.$message.error(result.msg)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.log-list {
  .right-toolbar {
    float: right;
  }

  .el-card ::v-deep .el-card__body {
    height: calc(100vh - 170px);
  }
}
</style>
