<template>
  <div class="app-container">
    <log-list v-if="options.showList" @showCard="showCard" />
    <log-detail v-if="options.showDetail" :data="options.data" @showCard="showCard" />
  </div>
</template>

<script>
import LogList from './LogList.vue'
import LogDetail from './LogDetail.vue'

export default {
  name: 'ApiLog',
  components: { 
    LogList, 
    LogDetail 
  },
  data() {
    return {
      options: {
        data: {},
        showList: true,
        showDetail: false
      }
    }
  },
  methods: {
    showCard(data) {
      Object.assign(this.options, data)
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
