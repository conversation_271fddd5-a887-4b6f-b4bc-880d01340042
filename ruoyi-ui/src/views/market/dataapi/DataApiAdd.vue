<template>
  <el-card class="box-card" shadow="always">
    <div slot="header" class="clearfix">
      <span>{{ title }}</span>
      <span>&nbsp;&nbsp;&nbsp;</span>
      <span style="color: tomato; font-size: 14px">{{ tips }}</span>
      <el-button-group style="float: right;">
        <el-button size="mini" icon="el-icon-plus" round :loading="loadingOptions.loading" :disabled="loadingOptions.isDisabled" @click="submitForm">{{ loadingOptions.loadingText }}</el-button>
        <el-button size="mini" icon="el-icon-back" round @click="showCard">返回</el-button>
      </el-button-group>
    </div>
    <div class="body-wrapper">
      <el-steps :active="active" finish-status="success" align-center>
        <el-step title="属性配置" />
        <el-step title="执行配置" />
        <el-step title="参数配置" />
      </el-steps>
      <el-form v-if="active == 1" ref="form1" :model="form1" :rules="rules1" label-width="80px">
        <el-form-item label="分类" label-width="100px" prop="category">
          <el-select
            v-model="form1.category"
            placeholder="分类"
            class="filter-item"
            clearable
          >
            <el-option
              v-for="dict in categoryOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="API名称" label-width="100px" prop="apiName">
          <el-input v-model="form1.apiName" placeholder="请输入API名称" />
        </el-form-item>
        <el-form-item label="API版本" label-width="100px" prop="apiVersion">
          <el-input v-model="form1.apiVersion" placeholder="请输入API版本，如v1.0.0" />
        </el-form-item>
        <el-form-item label="API路径" label-width="100px" prop="apiUrl">
          <el-input v-model="form1.apiUrl" placeholder="请输入API路径" />
        </el-form-item>
        <el-form-item label="请求方式" label-width="100px" prop="reqMethod">
          <el-select v-model="form1.reqMethod" placeholder="请选择请求方式">
            <el-option
              v-for="dict in reqMethodOptions"
              :key="dict.id"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="返回格式" label-width="100px" prop="resType">
          <el-select v-model="form1.resType" placeholder="请选择返回格式">
            <el-option
              v-for="dict in resTypeOptions"
              :key="dict.id"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="IP黑名单" label-width="100px" prop="deny">
          <el-input v-model="form1.deny" type="textarea" placeholder="请输入IP黑名单多个用英文,隔开" />
        </el-form-item>
        <el-form-item label="是否分页" label-width="100px" prop="whetherPage">
          <el-radio-group v-model="form1.whetherPage">
            <el-radio
              v-for="dict in pageOptions"
              :key="dict.id"
              :label="dict.value"
            >{{ dict.title }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否限流" label-width="100px" prop="rateLimit">
          <el-radio-group v-model="form1.rateLimit.enable">
            <el-radio
              v-for="dict in whetherOptions"
              :key="dict.id"
              :label="dict.dictValue"
            >{{ dict.dictLabel }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form1.rateLimit.enable === '1'" label="限流配置" label-width="100px">
          每<el-input-number v-model="form1.rateLimit.seconds" controls-position="right" :min="1" />秒内限制请求
          <el-input-number v-model="form1.rateLimit.times" controls-position="right" :min="1" />次
        </el-form-item>
        <el-form-item label="动态阈值" label-width="130px" prop="dynThreshold" style="width: 25%">
          <template #label>
            <span style="margin-right: 15px;">动态阈值</span>
            <el-tooltip class="item" effect="dark" content="接口调用一次返回的数量限制，数据量超过此限制必须开启缓存，否则请求数据不予返回" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
          <el-input v-model="form1.dynThreshold" placeholder="请输入数据动态阈值" type="number" :min="0" :max="form1.maxThreshold" @input="validateThreshold">>
            <template slot="append">条/次</template>
          </el-input>
        </el-form-item>
        <el-form-item label="最大阈值" label-width="130px" style="width: 25%">
          <template #label>
            <span style="margin-right: 15px;">最大阈值</span>
            <el-tooltip class="item" effect="dark" content="接口调用一次返回的最大数量限制，请求返回数据量不允许超过此限制，否则请求数据不予返回" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
          <el-input v-model="form1.maxThreshold"  type="number" :min="0" disabled>
            <template slot="append">条/次</template>
          </el-input>
        </el-form-item>
        <el-form-item label="是否缓存" label-width="100px" prop="cacheSwitch">
          <el-select v-model="form1.cacheSwitch">
            <el-option
              v-for="dict in cacheOptions"
              :key="dict.id"
              :label="dict.title"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="form1.cacheSwitch === 1" label="超时时长(s)" label-width="100px" prop="expireTime">
          <el-input v-model="form1.expireTime" placeholder="请输入缓存超时时长"/>
        </el-form-item>
        <el-form-item label="顺序" label-width="100px" prop="sort">
          <el-input-number v-model="form1.sort" placeholder="请输入顺序"></el-input-number>
        </el-form-item>
        <el-form-item label="状态" label-width="100px" prop="status">
          <el-radio-group v-model="form1.status" disabled>
            <el-radio
              v-for="dict in statusOptions"
              :key="dict.id"
              :label="dict.dictValue"
            >{{ dict.dictLabel }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" label-width="100px" prop="remark">
          <el-input v-model="form1.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <el-form v-if="active == 2" ref="form2" :model="form2" :rules="rules2" label-width="80px">
        <el-form-item label="配置方式" prop="configType">
          <el-select v-model="form2.configType" placeholder="请选择配置方式" @change="configTypeSelectChanged">
            <el-option
              v-for="dict in configTypeOptions"
              :key="dict.id"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="数据源" prop="sourceId">
          <el-select v-model="form2.sourceId" placeholder="请选择数据源" @change="sourceSelectChanged" filterable>
            <el-option
              v-for="source in sourceOptions"
              :key="source.id"
              :label="source.sourceName"
              :value="source.id"
              :disabled="source.status === '0'"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="form2.configType === '1'" label="数据库表" prop="tableName">
          <el-select v-model="form2.table" value-key="id" placeholder="请选择数据库表" @change="tableSelectChanged" filterable>
            <el-option
              v-for="item in tableOptions"
              :key="item.id"
              :label="item.tableComment ? item.tableComment : item.tableName"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="form2.configType === '1'" label="字段列表">
          <el-table
            :data="form2.fieldParams"
            stripe
            border
            :max-height="300"
            style="width: 100%; margin: 15px 0;"
          >
            <el-table-column prop="columnPosition" label="序号" width="55" align="center" />
            <el-table-column prop="columnName" label="列名" align="center" show-overflow-tooltip />
            <el-table-column prop="dataType" label="数据类型" align="center" show-overflow-tooltip />
            <el-table-column prop="dataLength" label="数据长度" align="center" show-overflow-tooltip />
            <el-table-column prop="dataPrecision" label="数据精度" align="center" show-overflow-tooltip />
            <el-table-column prop="dataScale" label="数据小数位" align="center" show-overflow-tooltip />
            <el-table-column prop="columnKey" label="是否主键" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                <span v-if="scope.row.columnKey === '1'">Y</span>
                <span v-if="scope.row.columnKey === '0'">N</span>
              </template>
            </el-table-column>
            <el-table-column prop="columnNullable" label="是否允许为空" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                <span v-if="scope.row.columnNullable === '1'">Y</span>
                <span v-if="scope.row.columnNullable === '0'">N</span>
              </template>
            </el-table-column>
            <el-table-column prop="dataDefault" label="列默认值" align="center" show-overflow-tooltip />
            <el-table-column prop="columnComment" label="列注释" align="center" show-overflow-tooltip />
            <el-table-column prop="reqable" label="是否作为请求参数" align="center" width="50">
              <template slot-scope="scope">
                <el-checkbox v-model="scope.row.reqable" true-label="1" false-label="0" @change="checked=>reqCheckChange(scope.row, checked)" />
              </template>
            </el-table-column>
            <el-table-column prop="resable" label="是否作为返回参数" align="center" width="50">
              <template slot-scope="scope">
                <el-checkbox v-model="scope.row.resable" true-label="1" false-label="0" @change="checked=>resCheckChange(scope.row, checked)" />
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-row v-if="form2.configType === '2'">
          <el-col :span="24">
            <sql-editor
              ref="sqleditor"
              v-model="this.form2.sqlText"
              style="height: 300px;margin: 10px 10px;"
              @changed="changeTextarea($event)"
            />
          </el-col>
        </el-row>
        <el-form-item v-if="form2.configType === '2'">
          <el-button size="mini" type="primary" @click="sqlParse">SQL解析</el-button>
        </el-form-item>
      </el-form>
      <el-form v-if="active == 3" ref="form3" :model="form3" label-width="80px">
        <el-divider content-position="left">请求参数</el-divider>
        <el-table
          :data="form3.reqParams"
          stripe
          border
          :max-height="300"
          style="width: 100%; margin: 15px 0;"
        >
          <el-table-column label="序号" width="55" align="center">
            <template slot-scope="scope">
              <span>{{ scope.$index +1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="paramName" label="字段名称" align="center" show-overflow-tooltip />
          <el-table-column prop="paramAliasName" label="参数名称" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-input v-model="scope.row.paramAliasName" placeholder="请输入参数名称" />
            </template>
          </el-table-column>
          <el-table-column prop="nullable" label="是否允许为空" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.nullable" true-label="1" false-label="0" />
            </template>
          </el-table-column>
          <el-table-column prop="paramComment" label="描述" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-input v-model="scope.row.paramComment" placeholder="请输入描述" />
            </template>
          </el-table-column>
          <el-table-column prop="paramType" label="参数类型" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-select v-model="scope.row.paramType" placeholder="请选择参数类型">
                <el-option
                  v-for="dict in paramTypeOptions"
                  :key="dict.id"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="whereType" label="操作符" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-select v-model="scope.row.whereType" placeholder="请选择操作符">
                <el-option
                  v-for="dict in whereTypeOptions"
                  :key="dict.id"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="exampleValue" label="示例值" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-input v-model="scope.row.exampleValue" placeholder="请输入示例值" />
            </template>
          </el-table-column>
          <el-table-column prop="defaultValue" label="默认值" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-input v-model="scope.row.defaultValue" placeholder="请输入默认值" />
            </template>
          </el-table-column>
        </el-table>
        <el-divider v-if="this.databaseType !== '9'" content-position="left">返回字段</el-divider>
        <el-table
          :data="form3.resParams"
          stripe
          border
          :max-height="300"
          style="width: 100%; margin: 15px 0;"
          v-if="this.databaseType !== '9'"
        >
          <el-table-column label="序号" width="55" align="center">
            <template slot-scope="scope">
              <span>{{ scope.$index +1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="fieldName" label="字段名称" align="center" show-overflow-tooltip />
          <el-table-column prop="fieldComment" label="描述" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-input v-model="scope.row.fieldComment" placeholder="请输入描述" />
            </template>
          </el-table-column>
          <el-table-column prop="dataType" label="数据类型" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-input v-model="scope.row.dataType" placeholder="请输入数据类型" />
            </template>
          </el-table-column>
          <el-table-column prop="exampleValue" label="示例值" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-input v-model="scope.row.exampleValue" placeholder="请输入示例值" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <el-button v-if="active > 1" style="margin-top: 12px;" @click="handleLastStep">上一步</el-button>
      <el-button v-if="active < 3" style="margin-top: 12px;" @click="handleNextStep">下一步</el-button>
    </div>
  </el-card>
</template>

<script>
import { addDataApi, sqlParse } from '@/api/market/dataapi'
import { listDataSource } from '@/api/metadata/datasource'
import { listDataTable } from '@/api/metadata/datatable'
import { listDataColumn } from '@/api/metadata/datacolumn'
import SqlEditor from '@/components/SqlEditor'
import {encodeSqlToEncryptedBase64} from "@/utils/sqlHandle";

export default {
  name: 'DataApiAdd',
  components: {
    SqlEditor
  },
  props: {
    data: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      title: '数据API新增',
      tips: '步骤: 属性配置->执行配置->参数配置->提交',
      databaseType: '',
      // 展示切换
      showOptions: {
        data: {},
        showList: true,
        showAdd: false,
        showEdit: false,
        showDetail: false,
        showExample: false
      },
      // 保存按钮
      loadingOptions: {
        loading: false,
        loadingText: '提交',
        isDisabled: false
      },
      active: 1,
      // 表单参数
      form1: {
        id: undefined,
        category: undefined,
        apiName: undefined,
        apiVersion: undefined,
        sort:undefined,
        apiUrl: undefined,
        reqMethod: undefined,
        resType: undefined,
        deny: undefined,
        rateLimit: {
          enable: '1',
          times: 5,
          seconds: 60
        },
        whetherPage: '',
        status: '1',
        remark: undefined,
        executeConfig: {},
        reqParams: [],
        resParams: [],
        cacheSwitch: 0,
        expireTime: 0,
        dynThreshold: 30000,
        maxThreshold: 30000
      },
      // 表单校验
      rules1: {
        category: [
          { required: true, message: '分类不能为空', trigger: 'blur' }
        ],
        apiName: [
          { required: true, message: 'API名称不能为空', trigger: 'blur' }
        ],
        apiVersion: [
          { required: true, message: 'API版本不能为空', trigger: 'blur' }
        ],
        apiUrl: [
          { required: true, message: 'API路径不能为空', trigger: 'blur' }
        ],
        reqMethod: [
          { required: true, message: '请求方式不能为空', trigger: 'change' }
        ],
        resType: [
          { required: true, message: '返回格式不能为空', trigger: 'change' }
        ],
        whetherPage: [
          { required: true, message: '请选择是否分页', trigger: 'change'}
        ],
        cacheSwitch: [
          { required: true, message: '请选择是否开启缓存', trigger: 'change'}
        ],
        expireTime: [
          { required: true, message: '请输入缓存超时时长', trigger: 'blur'}
        ],
        dynThreshold: [
          { required: true, message: '请输入动态阈值', trigger: 'blur'}
        ]
      },
      form2: {
        configType: undefined,
        sourceId: undefined,
        tableId: undefined,
        tableName: undefined,
        fieldParams: [],
        sqlText: undefined
      },
      rules2: {
        configType: [
          { required: true, message: '配置方式不能为空', trigger: 'change' }
        ],
        sourceId: [
          { required: true, message: '数据源不能为空', trigger: 'change' }
        ]
      },
      form3: {
        reqParams: [],
        resParams: []
      },
      //分类数据字典
      categoryOptions: [],
      // 请求方式数据字典
      reqMethodOptions: [],
      // 返回格式数据字典
      resTypeOptions: [],
      // 是否数据字典
      whetherOptions: [],
      pageOptions: [
        { id: 'x001', title: '开启', value: 'true'},
        { id: 'x002', title: '关闭', value: 'false'}
      ],
      cacheOptions: [
        { id: 1, title: '开启', value: 1},
        { id: 0, title: '关闭', value: 0}
      ],
      // 状态数据字典
      statusOptions: [],
      // 数据源数据字典
      sourceOptions: [],
      // 数据库表数据字典
      tableOptions: [],
      // 配置方式数据字典
      configTypeOptions: [],
      // 操作符数据字典
      whereTypeOptions: [],
      // 参数类型数据字典
      paramTypeOptions: []
    }
  },
  created() {
    this.getDicts('form_category').then(response => {
      if (response.code === 200) {
        this.categoryOptions = response.data
      }
    })
    this.getDicts('data_req_method').then(response => {
      if (response.code === 200) {
        this.reqMethodOptions = response.data
      }
    })
    this.getDicts('data_res_type').then(response => {
      if (response.code === 200) {
        this.resTypeOptions = response.data
      }
    })
    this.getDicts('sys_yes_no').then(response => {
      if (response.code === 200) {
        this.whetherOptions = response.data
      }
    })
    this.getDicts('data_api_status').then(response => {
      if (response.code === 200) {
        this.statusOptions = response.data
      }
    })
    this.getDataSourceList()
    this.getDicts('data_config_type').then(response => {
      if (response.code === 200) {
        this.configTypeOptions = response.data
      }
    })
    this.getDicts('data_where_type').then(response => {
      if (response.code === 200) {
        this.whereTypeOptions = response.data
      }
    })
    this.getDicts('data_param_type').then(response => {
      if (response.code === 200) {
        this.paramTypeOptions = response.data
      }
    })
  },
  methods: {
    showCard() {
      this.$emit('showCard', this.showOptions)
    },
    getDataSourceList() {
      listDataSource().then(response => {
        if (response.code === 200) {
          this.sourceOptions = response.data
        }
      })
    },
    /** 步骤条下一步 */
    handleNextStep() {
      if (this.active === 1) {
        this.$refs['form1'].validate(valid => {
          if (valid) {
            this.active++
          }
        })
      } else if (this.active === 2) {
        this.$refs['form2'].validate(valid => {
          if (valid) {
            this.active++
          }
        })
      }
    },
    /** 步骤条上一步 */
    handleLastStep() {
      this.active--
    },
    changeTextarea(val) {
      this.form2.sqlText = val
    },
    configTypeSelectChanged(val) {
      if (this.form2.configType === '1' && this.form2.sourceId && this.tableOptions.length <= 0) {
        const data = {}
        data.sourceId = this.form2.sourceId
        listDataTable(data).then(response => {
          if (response.code === 200) {
            this.tableOptions = response.data
            this.form2.fieldParams = []
          }
        })
      }
    },
    sourceSelectChanged(val) {
      if (this.form2.configType && this.form2.configType === '2') {
        const data = {}
        data.sourceId = val
        const selectedSource = this.sourceOptions.find(source => source.id === val);
        if (selectedSource) {
          this.databaseType = selectedSource.dbType
        }
      }
      if (this.form2.configType && this.form2.configType === '1') {
        const data = {}
        data.sourceId = val
        listDataTable(data).then(response => {
          if (response.code === 200) {
            this.tableOptions = response.data
            this.form2.fieldParams = []
          }
        })
      }
    },
    tableSelectChanged(item) {
      const data = {}
      data.sourceId = item.sourceId
      data.tableId = item.id
      this.form2.tableId = item.id
      this.form2.tableName = item.tableName
      listDataColumn(data).then(response => {
        if (response.code === 200) {
          this.form2.fieldParams = response.data
          this.form3.reqParams = []
          this.form3.resParams = []
        }
      })
    },
    sqlParse() {
      if (!this.form2.sourceId) {
        this.$message.error('数据源不能为空')
        return
      }
      if (!this.form2.sqlText) {
        this.$message.error('解析SQL不能为空')
        return
      }
      const data = {}
      data.sourceId = this.form2.sourceId
      if (this.form2.sqlText) {
        data.sqlText = encodeSqlToEncryptedBase64(this.form2.sqlText)
      }
      sqlParse(data).then(response => {
        if (response.code === 200) {
          const { data } = response
          this.form3.reqParams = data.reqParams
          this.form3.resParams = data.resParams
          this.$message.success('解析成功，请进行下一步')
        } else {
          this.$message.error(response.msg)
        }
      })
    },
    reqCheckChange(row, checked) {
      if (checked === '1') {
        const json = {}
        json.paramName = row.columnName
        json.paramAliasName = row.columnName
        json.paramComment = row.columnComment || undefined
        json.nullable = '0'
        json.paramType = '1'
        json.whereType = '1'
        this.form3.reqParams.push(json)
      } else {
        this.form3.reqParams.splice(this.form3.reqParams.findIndex(item => item.paramName === row.columnName), 1)
      }
    },
    resCheckChange(row, checked) {
      if (checked === '1') {
        const json = {}
        json.fieldName = row.columnName
        json.fieldComment = row.columnComment || undefined
        json.dataType = row.dataType || undefined
        this.form3.resParams.push(json)
      } else {
        this.form3.resParams.splice(this.form3.resParams.findIndex(item => item.fieldName === row.columnName), 1)
      }
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form3'].validate(valid => {
        if (valid) {
          if (this.databaseType !== '9' && this.form3.reqParams.length <= 0) {
            this.$message.error('请求参数不能为空')
            return
          }
          if (this.databaseType === '9') {
            //this.form3.reqParams = [{"nullable": "null", "paramName": "null", "paramType": "null", "whereType": "null", "defaultValue": "null", "exampleValue": "null", "paramComment": "null"}]
            this.form3.resParams = [{"dataType": "0", "fieldName": "0", "exampleValue": "0", "fieldComment": "0", "fieldAliasName": "0"}]
          }
          if (this.databaseType !== '9' && this.form3.resParams.length <= 0) {
            this.$message.error('返回字段不能为空')
            return
          }

          this.form1.sourceId = this.form2.sourceId
          this.form1.executeConfig = this.form2
          this.form1.reqParams = this.form3.reqParams
          this.form1.resParams = this.form3.resParams
          this.loadingOptions.loading = true
          this.loadingOptions.loadingText = '提交中...'
          this.loadingOptions.isDisabled = true
          if (this.form1.executeConfig.sqlText) {
            this.form1.executeConfig.sqlText = encodeSqlToEncryptedBase64(this.form1.executeConfig.sqlText)
          }
          addDataApi(this.form1).then(response => {
            if (response.code === 200) {
              this.$message.success('提交成功')
              setTimeout(() => {
                // 2秒后跳转列表页
                this.$emit('showCard', this.showOptions)
              }, 2000)
            } else {
              this.$message.error('提交失败')
              this.loadingOptions.loading = false
              this.loadingOptions.loadingText = '提交'
              this.loadingOptions.isDisabled = false
            }
          }).catch(() => {
            this.loadingOptions.loading = false
            this.loadingOptions.loadingText = '提交'
            this.loadingOptions.isDisabled = false
          })
        }
      })
    },
    validateThreshold() {
      const threshold = this.form1.dynThreshold;
      if (threshold > this.form1.maxThreshold) {
        // 当用户输入的值大于最大值时，回退到最大值
        this.form1.dynThreshold = this.form1.maxThreshold;
      }
      if (threshold < 0) {
        // 可以选择回退到0，或将其设置为一个有效值
        this.form1.dynThreshold = 0;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.el-card ::v-deep .el-card__body {
  height: calc(100vh - 230px);
  overflow-y: auto;
}
</style>
