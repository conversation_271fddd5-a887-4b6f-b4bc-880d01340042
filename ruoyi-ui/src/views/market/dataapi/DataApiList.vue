<template>
  <el-card class="box-card" shadow="always">
    <el-form ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item label="分类" prop="category">
        <el-select v-model="queryParams.category" placeholder="请选择分类" size="small">
          <el-option
            v-for="dict in categoryOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="API名称" prop="apiName">
        <el-input
          v-model="queryParams.apiName"
          placeholder="请输入API名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">刷新</el-button>
      </el-form-item>
    </el-form>

    <el-row type="flex" justify="space-between">
      <el-col :span="12">
        <el-button-group>
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
          >新增</el-button>
        </el-button-group>
      </el-col>
<!--      <el-col :span="12">-->
<!--        <div class="right-toolbar">-->
<!--          <el-tooltip content="密度" effect="dark" placement="top">-->
<!--            <el-dropdown trigger="click" @command="handleCommand">-->
<!--              <el-button circle size="mini">-->
<!--                <svg-icon class-name="size-icon" icon-class="colum-height" />-->
<!--              </el-button>-->
<!--              <el-dropdown-menu slot="dropdown">-->
<!--                <el-dropdown-item command="medium">正常</el-dropdown-item>-->
<!--                <el-dropdown-item command="small">中等</el-dropdown-item>-->
<!--                <el-dropdown-item command="mini">紧凑</el-dropdown-item>-->
<!--              </el-dropdown-menu>-->
<!--            </el-dropdown>-->
<!--          </el-tooltip>-->
<!--          <el-tooltip content="刷新" effect="dark" placement="top">-->
<!--            <el-button circle size="mini" @click="handleRefresh">-->
<!--              <svg-icon class-name="size-icon" icon-class="shuaxin" />-->
<!--            </el-button>-->
<!--          </el-tooltip>-->
<!--          <el-tooltip content="列设置" effect="dark" placement="top">-->
<!--            <el-popover placement="bottom" width="100" trigger="click">-->
<!--              <el-checkbox-group v-model="checkedTableColumns" @change="handleCheckedColsChange">-->
<!--                <el-checkbox-->
<!--                  v-for="(item, index) in tableColumns"-->
<!--                  :key="index"-->
<!--                  :label="item.prop"-->
<!--                >{{ item.label }}</el-checkbox>-->
<!--              </el-checkbox-group>-->
<!--              <span slot="reference">-->
<!--                <el-button circle size="mini">-->
<!--                  <svg-icon class-name="size-icon" icon-class="shezhi" />-->
<!--                </el-button>-->
<!--              </span>-->
<!--            </el-popover>-->
<!--          </el-tooltip>-->
<!--        </div>-->
<!--      </el-col>-->
    </el-row>

    <el-table
      v-loading="loading"
      :data="dataApiList"
      border
      tooltip-effect="dark"
      :size="tableSize"
      :height="tableHeight"
      style="width: 100%;margin: 15px 0;height: 80%"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="顺序" width="100" align="center" prop="sort">
      </el-table-column>
      <el-table-column label="分类" width="100" align="center">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.form_category" :value="scope.row.category"/>
        </template>
      </el-table-column>
      <template v-for="(item, index) in tableColumns">
        <el-table-column
          v-if="item.show"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :formatter="item.formatter"
          align="center"
          :min-width="getMinWidth(item.prop)"
          show-overflow-tooltip
        />
      </template>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-popover
            placement="left"
            trigger="click"
          >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit-outline"
              v-if="scope.row.status !== '2'"
              @click="handleEdit(scope.row)"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleDetail(scope.row)"
            >详情</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              v-if="scope.row.status !== '2'"
              @click="handleDelete(scope.row)"
            >删除</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-copy-document"
              @click="handleCopy(scope.row)"
            >复制</el-button>
            <el-button
              v-if="scope.row.status !== '2'"
              size="mini"
              type="text"
              icon="el-icon-upload2"
              @click="handleRelease(scope.row)"
            >发布</el-button>
            <el-button
              v-if="scope.row.status === '2'"
              size="mini"
              type="text"
              icon="el-icon-download"
              @click="handleCancel(scope.row)"
            >下线</el-button>
            <el-button
              v-if="scope.row.status === '2' && scope.row.cacheSwitch === 1"
              size="mini"
              type="text"
              icon="el-icon-download"
              @click="handleRemoveCache(scope.row.id)"
            >清除缓存</el-button>
            <el-button slot="reference">操作</el-button>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :current-page.sync="queryParams.pageNum"
      :page-size.sync="queryParams.pageSize"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </el-card>
</template>

<script>
import { pageDataApi, delDataApi, copyDataApi, releaseDataApi, cancelDataApi, removeCache } from '@/api/market/dataapi'
import dict from '@/utils/dict'

export default {
  name: 'DataApiList',
  props: {
    listParams: {
      type: Object,
      default: () => ({})
    }
  },
  dicts: [ 'form_category' ],
  data() {
    return {
      baseURL: window.location.protocol + "//" + window.location.host + ((process.env.NODE_ENV === 'production' || process.env.NODE_ENV === 'test') ? process.env.VUE_APP_BASE_API : '' ) + '/data/api/services/',
      tableHeight: document.body.offsetHeight - 310 + 'px',
      // 展示切换
      showOptions: {
        data: {},
        showList: true,
        showAdd: false,
        showEdit: false,
        showDetail: false,
        showExample: false
      },
      // 遮罩层
      loading: true,
      // 表格头
      tableColumns: [
        { prop: 'apiName', label: 'API名称', show: true },
        { prop: 'apiVersion', label: 'API版本', show: true },
        { prop: 'api',
          label: '接口地址',
          show: true ,
          formatter : this.apiUrlFormatter
        },
        { prop: 'reqMethod', label: '请求类型', show: true },
        { prop: 'resType', label: '返回格式', show: true },
        {
          prop: 'status',
          label: '状态',
          show: true,
          formatter: this.statusFormatter
        },
        { prop: 'createTime', label: '创建时间', show: true }
      ],
      // 默认选择中表格头
      checkedTableColumns: [],
      tableSize: 'medium',
      //分类数据字典
      categoryOptions: [],
      // categoryForm: {
      //   categoryName: "",
      //   categoryCode: "",
      //   category: undefined,
      // },
      // 状态数据字典
      statusOptions: [],
      // 数据集表格数据
      dataApiList: [],
      // 总数据条数
      total: 0,
      // 查询参数
      queryParams: {
        category: '',
        pageNum: 1,
        pageSize: 20,
        apiName: ''
      }
    }
  },
  watch: {
    queryParams: {
      handler(newVal) {
        this.$emit('changeListParams', newVal)
      },
      deep: true
    }
  },
  created() {
    this.queryParams = this.listParams
    this.getDicts('data_api_status').then(response => {
      if (response.code === 200) {
        this.statusOptions = response.data
      }
    })
    this.getList()
    this.getDicts('form_category').then(response => {
      if (response.code === 200) {
        this.categoryOptions = response.data
      }
    })
    function getCategoryName(provinceCode) {
      return categoryMap[provinceCode] || '未知省份';
    }
  },
  mounted() {
    this.initCols()
  },
  methods: {
    /** 查询数据Api列表 */
    getList() {
      this.loading = true
      pageDataApi(this.queryParams).then(response => {
        this.loading = false
        if (response.code === 200) {
          // const { data } = response
          this.dataApiList = response.rows
          this.total = response.total
        }
      })
    },
    initCols() {
      this.checkedTableColumns = this.tableColumns.map(col => col.prop)
    },
    handleCheckedColsChange(val) {
      this.tableColumns.forEach(col => {
        if (!this.checkedTableColumns.includes(col.prop)) {
          col.show = false
        } else {
          col.show = true
        }
      })
    },
    handleCommand(command) {
      this.tableSize = command
    },
    /** 搜索按钮操作 */
    handleQuery() {
     
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        apiName: ''
      }
      this.handleQuery()
    },
    /** 刷新列表 */
    handleRefresh() {
      this.getList()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.showOptions.data = {}
      this.showOptions.showList = false
      this.showOptions.showAdd = true
      this.showOptions.showEdit = false
      this.showOptions.showDetail = false
      this.showOptions.showExample = false
      this.$emit('showCard', this.showOptions)
    },
    /** 修改按钮操作 */
    handleEdit(row) {
      this.showOptions.data.id = row.id
      this.showOptions.showList = false
      this.showOptions.showAdd = false
      this.showOptions.showEdit = true
      this.showOptions.showDetail = false
      this.showOptions.showExample = false
      this.$emit('showCard', this.showOptions)
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.showOptions.data.id = row.id
      this.showOptions.showList = false
      this.showOptions.showAdd = false
      this.showOptions.showEdit = false
      this.showOptions.showDetail = true
      this.showOptions.showExample = false
      this.$emit('showCard', this.showOptions)
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm('选中数据将被永久删除, 是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delDataApi(row.id).then(response => {
          if (response.success) {
            this.$message.success('删除成功')
            this.getList()
          }
        })
      }).catch(() => {
      })
    },
    /** 接口拷贝 */
    handleCopy(row) {
      this.$confirm('确认拷贝当前接口, 是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        copyDataApi(row.id).then(response => {
          if (response.success) {
            this.$message.success('拷贝成功')
            this.getList()
          }
        })
      }).catch(() => {
      })
    },
    /** 接口发布 */
    handleRelease(row) {
      releaseDataApi(row.id).then(response => {
        if (response.success) {
          this.$message.success('接口发布成功')
          this.getList()
        }
      })
    },
    /** 接口注销 */
    handleCancel(row) {
      cancelDataApi(row.id).then(response => {
        if (response.success) {
          this.$message.success('接口注销成功')
          this.getList()
        }
      })
    },
    handleRemoveCache(id) {
      removeCache(id).then(response => {
        if (response.success) {
          this.$message.success('缓存清空成功')
        }
      })
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.queryParams.pageNum = val
      this.getList()
    },
    statusFormatter(row, column, cellValue, index) {
      const dictLabel = this.mySelectDictLabel(this.statusOptions, cellValue)
      if (cellValue === '1') {
        return <el-tag>{dictLabel}</el-tag>
      } else if (cellValue === '2') {
        return <el-tag type='success'>{dictLabel}</el-tag>
      } else if (cellValue === '3') {
        return <el-tag type='warning'>{dictLabel}</el-tag>
      }
    },
    apiUrlFormatter(row) {
      return this.baseURL+ row.apiVersion + row.apiUrl
    },
    getMinWidth(prop) {
      const minWidthMap = {
        api: '520px',
        apiName: '300px',
        createTime: '200px',
        default: '100px'
      };
      return minWidthMap[prop] || minWidthMap.default;
    }
  }
}
</script>

<style lang="scss" scoped>
.right-toolbar {
  float: right;
}
.el-card ::v-deep .el-card__body {
  height: calc(100vh - 170px);
}
</style>
