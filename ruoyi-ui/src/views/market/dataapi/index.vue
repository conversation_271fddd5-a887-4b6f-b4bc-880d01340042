<template>
  <div class="app-container">
    <transition name="el-zoom-in-center">
      <data-api-list :list-params="options.listParams" @changeListParams="changeListParams" v-if="options.showList"
        @showCard="showCard" />
    </transition>
    <transition name="el-zoom-in-top">
      <data-api-add v-if="options.showAdd" :data="options.data" @showCard="showCard" />
    </transition>
    <transition name="el-zoom-in-top">
      <data-api-edit v-if="options.showEdit" :data="options.data" @showCard="showCard" />
    </transition>
    <transition name="el-zoom-in-bottom">
      <data-api-detail v-if="options.showDetail" :data="options.data" @showCard="showCard" />
    </transition>
    <transition name="el-zoom-in-bottom">
      <data-api-example v-if="options.showExample" :data="options.data" @showCard="showCard" />
    </transition>
  </div>
</template>

<script>
import DataApiList from './DataApiList.vue'
import DataApiAdd from './DataApiAdd.vue'
import DataApiEdit from './DataApiEdit.vue'
import DataApiDetail from './DataApiDetail.vue'
import DataApiExample from './DataApiExample.vue'

export default {
  name: 'DataApi',
  components: { DataApiList, DataApiAdd, DataApiEdit, DataApiDetail, DataApiExample },
  data() {
    return {
      options: {
        listParams: {},
        data: {},
        showList: true,
        showAdd: false,
        showEdit: false,
        showDetail: false,
        showExample: false
      }
    }
  },
  methods: {
    showCard(data) {
      Object.assign(this.options, data)
    },
    changeListParams(params) {
      this.options.listParams = params
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
