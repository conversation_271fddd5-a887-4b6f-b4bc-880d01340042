<template>
  <div class="app-container">
    <div ref="queryFormRef" class="filter-container">
      <el-select v-model="listQuery.httpJobId" placeholder="任务明细" style="width: 200px" clearable filterable
        class="filter-item">
        <el-option v-for="item in httpJobList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.requestStatus" placeholder="请求状态" style="width: 200px" clearable filterable
        class="filter-item">
        <el-option v-for="item in requestStatusList" :key="item.key" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.processingStatus" placeholder="数据处理状态" style="width: 200px" clearable filterable
        class="filter-item">
        <el-option v-for="dict in dict.type.hts_job_status" :key="dict.value" :label="dict.label" :value="dict.value" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="fetchData">
        搜索
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px" type="primary" icon="el-icon-edit"
        @click="handlerDelete">
        清除
      </el-button>
    </div>
    <el-table ref="fullHeightTableRef" :height="tableHeight" v-loading="listLoading" :data="list" element-loading-text="Loading" border fit highlight-current-row>
      <el-table-column label="序号" align="center" width="95">
        <template slot-scope="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>
      <el-table-column align="center" label="任务名称" width="200px">
        <template slot-scope="scope">{{ scope.row.summaryName }}</template>
      </el-table-column>
      <el-table-column align="center" label="明细名称" width="200px">
        <template slot-scope="scope">{{ scope.row.name }}</template>
      </el-table-column>
      <el-table-column align="center" label="认证或前置请求" width="120px">
        <template slot-scope="scope">{{ getPreRequestLabel(scope.row.preRequest) }}</template>
      </el-table-column>
      <el-table-column align="center" label="目标数据源" width="160px">
        <template slot-scope="scope">{{ scope.row.datasourceName }}</template>
      </el-table-column>
      <el-table-column align="center" label="目标数据表" width="160px">
        <template slot-scope="scope">{{ scope.row.targetTable }}</template>
      </el-table-column>
      <el-table-column align="center" label="请求方式" width="160px">
        <template slot-scope="scope">{{ scope.row.requestMethod }}</template>
      </el-table-column>
      <el-table-column align="center" label="调用时间" width="160px">
        <template slot-scope="scope">{{ scope.row.requestTime }}</template>
      </el-table-column>
      <el-table-column align="center" label="接口耗时(s)" width="160px">
        <template slot-scope="scope">{{ scope.row.requestConsume }}</template>
      </el-table-column>
      <el-table-column align="center" label="请求状态" width="160px">
        <template slot-scope="scope">{{ requestStatusMap[scope.row.requestStatus] }}</template>
      </el-table-column>
      <el-table-column align="center" label="数据处理耗时(s)" width="160px">
        <template slot-scope="scope">{{ scope.row.processingConsume }}</template>
      </el-table-column>
      <el-table-column align="center" label="数据处理状态" width="160px">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.hts_job_status" :value="scope.row.processingStatus" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="任务执行开始时间" width="160px">
        <template slot-scope="scope">{{ scope.row.startTime }}</template>
      </el-table-column>
      <el-table-column align="center" label="任务执行结束时间" width="160px">
        <template slot-scope="scope">{{ scope.row.endTime }}</template>
      </el-table-column>
      <el-table-column align="center" label="任务执行耗时(s)" width="160px">
        <template slot-scope="scope">{{ scope.row.jobConsume }}</template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="100px" fixed="right">
        <template slot-scope="{ row }">
          <!-- 操作 -->
          <el-dropdown trigger="click">
            <span class="el-dropdown-link">
              操作<i class="el-icon-arrow-down el-icon--right" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item divided @click.native="handlerViewLog(row)">日志查看</el-dropdown-item>
              <el-dropdown-item @click.native="deleteLog(row)">删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageNum" :limit.sync="listQuery.pageSize"
      @pagination="fetchData" />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="1000px"
      :before-close="handleClose" :fullscreen="true">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="任务名称" prop="summaryName" label-width="150px">
              <el-input v-model="temp.summaryName" size="medium" placeholder="请输入任务名称" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="明细名称" prop="name" label-width="150px">
              <el-input v-model="temp.name" size="medium" placeholder="请输入明细名称" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="4">
            <el-form-item label="请求方式及地址" prop="requestMethod" label-width="150px">
              <el-input v-model="temp.requestMethod" size="medium" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item prop="url" label-width="0px">
              <el-input v-model="temp.url" size="medium" placeholder="请输入接口地址" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="请求状态" prop="requestStatus" label-width="150px">
              <el-input v-model="temp.requestStatus" size="medium" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="请求时间" prop="requestTime" label-width="150px">
              <el-input v-model="temp.requestTime" size="medium" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请求耗时(s)" prop="requestConsume" label-width="150px">
              <el-input v-model="temp.requestConsume" size="medium" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item v-if="!isPreRequest" label="目标数据源" prop="datasourceName" label-width="150px">
              <el-input v-model="temp.datasourceName" size="medium" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="!isPreRequest" label="目标数据表" prop="targetTable" label-width="150px">
              <el-input v-model="temp.targetTable" size="medium" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="!isPreRequest" label="upsert字段" prop="upsertFields" label-width="150px">
              <el-input v-model="temp.upsertFields" size="medium" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item v-if="!isPreRequest" label="数据处理耗时(s)" prop="processingConsume" label-width="150px">
              <el-input v-model="temp.processingConsume" size="medium" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="!isPreRequest" label="数据处理状态" prop="processingStatus" label-width="150px">
              <el-select v-model="temp.processingStatus" style="width: 100%" class="filter-item" disabled>
                <el-option v-for="dict in dict.type.hts_job_status" :key="Number(dict.value)" :label="dict.label"
                  :value="Number(dict.value)" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="任务执行开始时间" prop="startTime" label-width="150px">
              <el-input v-model="temp.startTime" size="medium" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务执行结束时间" prop="endTime" label-width="150px">
              <el-input v-model="temp.endTime" size="medium" disabled />
            </el-form-item>
          </el-col>
        </el-row>


        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="任务执行耗时(s)" prop="jobConsume" label-width="150px">
              <el-input v-model="temp.jobConsume" size="medium" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否为前置请求" prop="preRequest" label-width="150px">
              <el-switch v-model="temp.preRequest" active-color="#3d8ee1" :active-value="1" inactive-color="#918f8f"
                :inactive-value="0" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="Header" name="0">
            <el-table :data="headerTableData" style="width: 100%" border>
              <el-table-column prop="key" align="center" label="参数名">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.key" disabled></el-input>
                </template>
              </el-table-column>

              <el-table-column prop="type" align="center" label="参数类型">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.type" class="filter-item" style="width: 100%;" filterable clearable
                    placeholder="请选择参数类型" disabled>
                    <el-option v-for="dict in dict.type.hts_param_type" :key="dict.value" :label="dict.label"
                      :value="dict.value" />
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column prop="value" align="center" label="参数值">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.value" disabled></el-input>
                </template>
              </el-table-column>

              <el-table-column prop="desc" align="center" label="参数描述">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.desc" disabled></el-input>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <el-tab-pane label="Query" name="1">
            <el-table :data="queryTableData" style="width: 100%" border>
              <el-table-column prop="key" align="center" label="参数名">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.key" disabled></el-input>
                </template>
              </el-table-column>

              <el-table-column prop="type" align="center" label="参数类型">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.type" class="filter-item" style="width: 100%;" filterable clearable
                    placeholder="请选择参数类型" disabled>
                    <el-option v-for="dict in dict.type.hts_param_type" :key="dict.value" :label="dict.label"
                      :value="dict.value" />
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column prop="value" align="center" label="参数值">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.value" disabled></el-input>
                </template>
              </el-table-column>

              <el-table-column prop="desc" align="center" label="参数描述">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.desc" disabled></el-input>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <el-tab-pane label="Body" name="2">
          </el-tab-pane>
          <el-row v-if="activeName === '2'" :gutter="20">
            <json-editor ref="jsonEditor" v-model="parsedRequestParam" />
          </el-row>

          <el-tab-pane label="认证" name="3">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-select v-model="temp.authWay" class="filter-item" style="width: 80%;" filterable
                  placeholder="请选择认证类型">
                  <el-option v-for="dict in dict.type.hts_auth_way" :key="dict.value" :label="dict.label"
                    :value="dict.value" />
                </el-select>
              </el-col>

              <el-col :span="18">
                <el-form-item v-if="temp.authWay === '1'" label="该接口无需认证" label-width="150px" />
                <el-form-item v-if="temp.authWay === '2'" label="key" label-width="50px">
                  <el-input v-model="auth.KeyValuePairs.key" placeholder="键" clearable size="small" />
                </el-form-item>
                <el-form-item v-if="temp.authWay === '2'" label="value" label-width="50px">
                  <el-input v-model="auth.KeyValuePairs.value" placeholder="值" clearable size="small" />
                </el-form-item>
                <el-form-item v-if="temp.authWay === '3'" label="token" label-width="150px">
                  <el-input v-model="auth.BearerToken.token" placeholder="Token（支持变量 ${param}）" clearable
                    size="small" />
                </el-form-item>
                <el-form-item v-if="temp.authWay === '3'" label="认证请求" label-width="150px">
                  <el-select v-model="auth.BearerToken.httpJobId" placeholder="请选择认证请求" style="width: 100%"
                    class="filter-item" filterable clearable>
                    <el-option v-for="item in this.preHttpTaskList" :key="item.id" :label="item.name"
                      :value="item.id" />
                  </el-select>
                </el-form-item>
                <el-form-item v-if="temp.authWay === '3'" label="认证请求名称" label-width="150px">
                  <el-input v-model="auth.BearerToken.httpJobName" placeholder="认证请求名称" clearable size="small" />
                </el-form-item>
                <el-form-item v-if="temp.authWay === '4'" label="用户名" label-width="150px">
                  <el-input v-model="auth.BasicAuth.username" placeholder="用户名" clearable size="small" />
                </el-form-item>
                <el-form-item v-if="temp.authWay === '4'" label="密码" label-width="150px">
                  <el-input v-model="auth.BasicAuth.password" placeholder="密码" clearable size="small" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="前置任务" name="4">
            <el-row :gutter="20">
              <el-col :span="4">
                <el-form-item label="前置任务类型" label-width="150px">
                  <el-select v-model="temp.preTaskType" disabled style="width: 100%" class="filter-item">
                    <el-option v-for="item in this.preHttpTaskTypeList" :key="item.key" :label="item.label"
                      :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item v-if="temp.preTaskType === 'sql'" label="数据源类型" label-width="150px">
                  <el-select v-model="temp.preDatasourceType" disabled style="width: 100%">
                    <el-option v-for="item in datasourceTypes" :key="item.value" :label="item.label"
                      :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item v-if="temp.preTaskType === 'sql'" label="数据库源：" label-width="150px">
                  <el-select disabled v-model="temp.preDatasourceId" filterable style="width: 100%">
                    <el-option v-for="item in preDatasourceList" :key="item.id" :label="item.datasourceName"
                      :value="item.id" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="2">
                <el-button v-if="activeName === '4'" @click="handleViewJobLog(temp.sqlResultSet)">SQL执行结果</el-button>
              </el-col>
              <el-col :span="10">
                <el-form-item v-if="temp.preTaskType === 'http'" label="前置请求任务" label-width="150px">
                  <el-select v-model="temp.preHttpTask" disabled style="width: 100%" class="filter-item" filterable
                    clearable>
                    <el-option v-for="item in this.preHttpTaskList" :key="item.id" :label="item.name"
                      :value="item.id" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item v-if="temp.preTaskType === 'http'" label="前置请求任务" label-width="150px">
                  <el-input v-model="temp.preHttpTaskName" disabled clearable size="small" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="Result" name="5">
          </el-tab-pane>
          <el-row v-if="activeName === '5'" :gutter="20" style="margin-bottom: 30px;">
            <json-editor ref="jsonEditor" v-model="parsedRequestResult" />
          </el-row>
        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          关闭
        </el-button>
      </div>
    </el-dialog>

    <el-dialog :title="textMap[delDialogStatus]" :visible.sync="delDialogFormVisible" width="600px">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="center" label-width="100px">
        <el-row>
          <el-col :span="14" :offset="5">
            <el-form-item label="清理范围">
              <el-select v-model="temp.deleteType" placeholder="请选择清理范围" style="width: 230px">
                <el-option v-for="item in deleteTypeList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="delDialogFormVisible = false"> 取消</el-button>
        <el-button type="primary" @click="clearLog"> 确定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="日志查看" :visible.sync="logDialogVisible" width="95%">
      <div class="log-container">
        <pre :loading="logLoading" v-text="logContent" />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="logDialogVisible = false"> 关闭 </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import * as log from '@/api/hts/http-job-log'
import * as job from '@/api/hts/http-job-info'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import JsonEditor from "@/components/JsonEditor/index.vue";
import { getDataSourceList as jdbcDsList } from '@/api/dts/datax-jdbcDatasource'
import * as jobInfo from '@/api/hts/http-job-info'
import { list as dsList } from '@/api/datax/datax-jdbcDatasource'

export default {
  mixins: [tableFullHeight],
  name: 'HttpJobLog',
  components: { JsonEditor, Pagination },
  directives: { waves },
  dicts: ['hts_job_status', 'hts_param_type', 'hts_auth_way'],
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'gray',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      deleteDialogVisible: false,
      dialogVisible: false,
      logDialogVisible: false,
      list: null,
      listLoading: true,
      total: 0,
      listQuery: {
        current: 1,
        size: 10,
        httpJobId: this.$route.query.httpJobId ? Number(this.$route.query.httpJobId) : undefined,
        requestStatus: '',
        processingStatus: ''
      },
      delDialogStatus: '',
      delDialogFormVisible: false,
      dialogPluginVisible: false,
      pluginData: [],
      dialogFormVisible: false,
      dialogStatus: '',
      executorList: '',
      textMap: {
        create: 'Clear'
      },
      rules: {},
      temp: {
        id: undefined,
        deleteType: 1,
        summaryName: '',
        httpJobId: undefined,
        name: '',
        url: '',
        datasourceId: undefined,
        datasourceName: '',
        targetTable: '',
        upsertFields: '',
        authWay: '1',
        auth: {
          None: null,
          KeyValuePairs: { key: '', value: '' },
          BearerToken: { token: '', httpJobId: null, httpJobName: '' },
          BasicAuth: { username: '', password: '' }
        },
        requestMethod: '',
        requestHeader: null,
        requestParam: null,
        requestTime: null,
        requestConsume: undefined,
        requestResult: null,
        requestStatus: '',
        processingConsume: undefined,
        processingStatus: undefined,
        startTime: undefined,
        endTime: undefined,
        jobConsume: undefined,
        preDatasourceId: undefined,
        preDatasourceType: '',
        sqlResultSet: ''
      },
      httpJobList: [],
      datasourceList: [],
      tableList: [],
      preHttpTaskList: [],
      jdbcDsQuery: {
        datasource: '',
        ascs: 'datasource_name'
      },
      preDatasourceList: [],
      preHttpTaskTypeList: [{ key: 'sql', value: 'sql', label: 'SQL脚本' }, { key: 'http', value: 'http', label: 'HTTP任务' }],
      datasourceTypes: [{ key: 'mysql', value: 'mysql', label: 'MYSQL' }],
      requestStatusList: [{ key: 'SUCCESS', value: 'SUCCESS', label: '成功' }, { key: 'FAIL', value: 'FAIL', label: '失败' }],
      deleteTypeList: [
        { value: 1, label: '清理一个月之前日志数据' },
        { value: 2, label: '清理三个月之前日志数据' },
        { value: 3, label: '清理六个月之前日志数据' },
        { value: 4, label: '清理一年之前日志数据' },
        { value: 5, label: '清理一千条以前日志数据' },
        { value: 6, label: '清理一万条以前日志数据' },
        { value: 7, label: '清理三万条以前日志数据' },
        { value: 8, label: '清理十万条以前日志数据' },
        { value: 9, label: '清理所有日志数据' }
      ],
      // 日志查询参数
      jobLogQuery: {
        id: '',
        fromLineNum: 1
      },
      // 日志内容
      logContent: '',
      // 显示日志
      logShow: false,
      // 日志显示加载中效果
      logLoading: false,
      activeName: '0',
      headerTableData: [
        { key: '', type: '', value: undefined, desc: '' }
      ],
      queryTableData: [
        { key: '', type: '', value: undefined, desc: '' }
      ],
      auth: {
        None: null,
        KeyValuePairs: { key: '', value: '' },
        BearerToken: { token: '', httpJobId: null, httpJobName: '' },
        BasicAuth: { username: '', password: '' }
      },
      preRequestList: [{ key: 1, value: 1, label: '是' }, { key: 0, value: 0, label: '否' }],
      preRequestIconList: [{ key: 1, value: 1, label: '是' }, { key: 0, value: 0, label: '' }]
    }
  },
  created() {
    this.fetchData()
    this.getJobList()
    this.getDatasourceList()
    this.getPreHttpTask()
    this.queryPreDataSource()
  },
  computed: {
    isPreRequest() {
      return this.temp.preRequest === 1;
    },
    parsedRequestResult: {
      get() {
        try {
          if (typeof this.temp.requestResult === 'string') {
            return JSON.parse(this.temp.requestResult);
          }
          return this.temp.requestResult;
        } catch (error) {
          console.error('JSON 解析错误:', error);
          return {}; //或者设置一个默认值
        }
      },
      set(value) {
        // this.temp.requestResult = JSON.stringify(value);
      }
    },
    parsedRequestParam: {
      get() {
        try {
          if (typeof this.temp.requestParam === 'string') {
            return JSON.parse(this.temp.requestParam);
          }
          return this.temp.requestParam;
        } catch (error) {
          console.error('JSON 解析错误:', error);
          return {}; //或者设置一个默认值
        }
      },
      set(value) {
        // this.temp.requestResult = JSON.stringify(value);
      }
    },
    requestStatusMap() {
      const map = {};
      this.requestStatusList.forEach(item => {
        map[item.value] = item.label;
      });
      return map;
    }
  },
  methods: {
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
        })
        .catch(_ => {
        })
    },
    fetchData() {
      this.listLoading = true
      const param = Object.assign({}, this.listQuery)
      log.getPageList(param).then((response) => {
        if (response.code === 200) {
          this.total = response.total
          this.list = response.rows
          this.listLoading = false
        }
      })
    },
    getJobList() {
      job.getList().then(response => {
        if (response.code === 200) {
          this.httpJobList = response.data
        }
      })
    },
    handlerDelete() {
      this.delDialogStatus = 'del'
      this.delDialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    deleteLog(row) {
      log.remove(row.id).then((response) => {
        if (response.code === 200) {
          this.fetchData()
          this.$notify({
            title: 'Success',
            message: 'Delete Successfully',
            type: 'success',
            duration: 2000
          })
        } else {
          this.$notify({
            title: 'Fail',
            message: 'Delete Failed',
            type: 'danger',
            duration: 2000
          })
        }
      })
    },
    clearLog() {
      log.clearLog(this.temp.deleteType).then((response) => {
        if (response.code === 200) {
          this.fetchData()
          this.delDialogFormVisible = false
          this.$notify({
            title: 'Success',
            message: 'Delete Successfully',
            type: 'success',
            duration: 2000
          })
        } else {
          this.$notify({
            title: 'Fail',
            message: 'Delete Failed',
            type: 'danger',
            duration: 2000
          })
        }
      })
    },
    resetTemp() {
      this.temp = this.$options.data.call(this).temp
      this.activeName = '0'
      this.headerTableData = [
        { key: '', type: '', value: undefined, desc: '' }
      ]
      this.queryTableData = [
        { key: '', type: '', value: undefined, desc: '' }
      ]
    },
    handlerViewLog(row) {
      this.resetTemp()
      this.temp = Object.assign({}, row)
      this.headerTableData = this.temp.requestHeader.header ? this.temp.requestHeader.header : this.headerTableData
      this.queryTableData = this.temp.requestQuery.query ? this.temp.requestQuery.query : this.queryTableData
      this.auth = this.temp.auth
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    // 切换tab页
    handleClick(tab) {
      this.activeName = tab.index
    },
    getDatasourceList() {
      jdbcDsList().then(response => {
        this.datasourceList = response.data
      })
    },
    getPreHttpTask() {
      const queryParam = {
        preRequest: 1
      }
      jobInfo.getList(queryParam).then(response => {
        if (response.code === 200) {
          this.preHttpTaskList = response.data
        }
      })
    },
    getPreRequestLabel(preRequest) {
      const item = this.preRequestIconList.find(item => item.key === preRequest);
      return item ? item.label : '';
    },
    queryPreDataSource(type) {
      this.jdbcDsQuery.datasource = type
      dsList(this.jdbcDsQuery).then(response => {
        const { records } = response.data
        this.preDatasourceList = records
      })
    },
    handleViewJobLog(logContent) {
      this.logDialogVisible = true
      if (this.logShow === false) {
        this.logShow = true
      }
      this.loadLog(logContent)
    },
    loadLog(logContent) {
      this.logLoading = true
      this.logContent = logContent
      this.logLoading = false
    },
  }
}
</script>

<style lang="scss" scoped>
.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
}

.log-container {
  margin-bottom: 20px;
  background: #f5f5f5;
  width: 100%;
  height: 500px;
  overflow: scroll;

  pre {
    display: block;
    padding: 10px;
    margin: 0 0 10.5px;
    word-break: break-all;
    word-wrap: break-word;
    color: #334851;
    background-color: #f5f5f5;
    border-radius: 1px;
  }
}
</style>
