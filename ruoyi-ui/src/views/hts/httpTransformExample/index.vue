<template>
  <div class="app-container">
    <div ref="queryFormRef" class="filter-container">
      <el-select v-model="listQuery.name" placeholder="用例名称" class="filter-item" filterable clearable>
        <el-option v-for="item in exampleList" :key="item.id" :label="item.name" :value="item.name" />
      </el-select>
      <el-select v-model="listQuery.code" placeholder="用例编码" class="filter-item" filterable clearable>
        <el-option v-for="item in exampleList" :key="item.id" :label="item.code" :value="item.code" />
      </el-select>
      <el-input v-model="listQuery.className" placeholder="报文处理实现类名" style="width: 200px;" class="filter-item" />

      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="fetchData">
        搜索
      </el-button>
      <el-button v-if="showSearch" class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-edit"
        @click="handleCreate">
        添加
      </el-button>
      <el-button v-if="showSearch" class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-refresh"
        @click="handleReset">
        重置
      </el-button>
    </div>
    <el-table ref="fullHeightTableRef" :height="tableHeight" v-loading="listLoading" :data="list" element-loading-text="Loading" border fit highlight-current-row
      style="width: 100%" size="medium">
      <el-table-column label="序号" align="center" width="95">
        <template slot-scope="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>
      <el-table-column label="用例名称" align="center">
        <template slot-scope="scope">{{ scope.row.name }}</template>
      </el-table-column>
      <el-table-column label="用例编码" align="center">
        <template slot-scope="scope">{{ scope.row.code }}</template>
      </el-table-column>
      <el-table-column label="自定义实现类" align="center">
        <template slot-scope="scope">{{ scope.row.className }}</template>
      </el-table-column>
      <el-table-column label="报文数据字段" align="center">
        <template slot-scope="scope">{{ scope.row.dataName }}</template>
      </el-table-column>
      <el-table-column label="任务备注" align="center">
        <template slot-scope="scope">{{ scope.row.remark }}</template>
      </el-table-column>
      <el-table-column label="创建时间" align="center">
        <template slot-scope="scope">{{ scope.row.createTime }}</template>
      </el-table-column>
      <el-table-column label="更新时间" align="center">
        <template slot-scope="scope">{{ scope.row.updateTime }}</template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right">
        <template slot-scope="{row}">
          <!-- 操作 -->
          <el-dropdown trigger="click">
            <span class="el-dropdown-link">
              操作<i class="el-icon-arrow-down el-icon--right" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item divided @click.native="handleViewDialog(row)">查看</el-dropdown-item>
              <el-dropdown-item v-if="showSearch" divided @click.native="handleUpdate(row)">编辑</el-dropdown-item>
              <el-dropdown-item v-if="showSearch" @click.native="handleDelete(row)">删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageNum" :limit.sync="listQuery.pageSize"
      @pagination="fetchData" />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="100%" :before-close="handleClose"
      :fullscreen="true" :append-to-body="true">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="110px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用例名称" prop="name">
              <el-input v-model="temp.name" size="medium" placeholder="请输入用例名称" :disabled="dialogStatus === 'view'" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用例编码" prop="code">
              <el-input v-model="temp.code" size="medium" placeholder="请输入用例编码" :disabled="dialogStatus === 'view'" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报文处理类名" prop="className">
              <el-input v-model="temp.className" size="medium" placeholder="请输入报文处理类名(首字母小写，后缀已拼好)"
                :disabled="dialogStatus === 'view'">
                <template slot="append">ApiDataHandler</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报文数据字段" prop="dataName">
              <el-input v-model="temp.dataName" size="medium" placeholder="请输入报文数据字段"
                :disabled="dialogStatus === 'view'" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用例描述">
              <el-input v-model="temp.remark" type="textarea" :disabled="dialogStatus === 'view'" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-scrollbar style="height: 500px;">
            <json-editor ref="jsonEditor" v-model="temp.originJson" :disabled="dialogStatus === 'view'" />
          </el-scrollbar>
        </el-col>
        <el-col :span="12">
          <el-scrollbar style="height: 500px;">
            <json-editor ref="jsonEditor" v-model="temp.newJson" :disabled="dialogStatus === 'view'" />
          </el-scrollbar>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="transformInterfaceMessage" :disabled="dialogStatus === 'view'">
          转换
        </el-button>
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="handleOperate">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import * as example from '@/api/hts/http-transform-example'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import JsonEditor from '@/components/JsonEditor/index.vue'
import { transformIMData } from '@/api/hts/http-job-info'
import { deepClone } from '@/utils'

export default {
  mixins: [tableFullHeight],
  name: 'httpTransformExample',
  components: { JsonEditor, Pagination },
  directives: { waves },
  props: {
    showSearch: {
      type: Boolean,
      default: () => true
    }
  },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'gray',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      list: null,
      listLoading: true,
      total: 0,
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        name: '',
        code: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑',
        create: '创建',
        view: '查看'
      },
      rules: {
        name: [{ required: true, message: 'this is required', trigger: 'change' }],
        code: [{ required: true, message: 'this is required', trigger: 'change' }],
        className: [{ required: true, message: 'this is required', trigger: 'change' }],
        dataName: [{ required: true, message: 'this is required', trigger: 'change' }],
        remark: [{ required: true, message: 'this is required', trigger: 'change' }]
      },
      temp: {
        id: undefined,
        name: '',
        code: '',
        className: '',
        dataName: '',
        originJson: null,
        newJson: null,
        remark: ''
      },
      resetTemp() {
        this.temp = this.$options.data.call(this).temp
      },
      exampleList: []
    }
  },
  async created() {
    this.fetchData()
    this.getExampleList()
  },

  methods: {
    fetchData() {
      this.listLoading = true
      example.getPageList(this.listQuery).then(response => {
        if (response.code === 200) {
          this.total = response.total
          this.list = response.rows
        } else {
          this.$message.error(response.msg)
        }
        this.listLoading = false
      })
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
        })
        .catch(_ => {
        })
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleReset() {
      this.listQuery = {
        pageNum: 1,
        pageSize: 10,
        name: '',
        code: ''
      }
      this.fetchData()
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          example.createExample(this.temp).then(result => {
            this.fetchData()
            this.dialogFormVisible = false
            if (result.code === 200) {
              this.$notify({
                title: 'Success',
                message: 'Created Successfully',
                type: 'success',
                duration: 2000
              })
            } else {
              this.$notify({
                title: 'Error',
                message: 'Created Failed',
                type: 'error',
                duration: 2000
              })
            }
          })
        }
      })
    },
    handleViewDialog(row) {
      this.resetTemp()
      this.temp = Object.assign({}, row)
      if (typeof this.temp.originJson === 'string') {
        this.temp.originJson = JSON.parse(this.temp.originJson)
      }
      if (typeof this.temp.newJson === 'string') {
        this.temp.newJson = JSON.parse(this.temp.newJson)
      }
      this.dialogStatus = 'view'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign({}, row)
      if (typeof this.temp.originJson === 'string') {
        this.temp.originJson = JSON.parse(this.temp.originJson)
      }
      if (typeof this.temp.newJson === 'string') {
        this.temp.newJson = JSON.parse(this.temp.newJson)
      }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const param = deepClone(this.temp)
          example.updateExample(param).then(result => {
            this.fetchData()
            this.dialogFormVisible = false
            if (result.code === 200) {
              this.$notify({
                title: 'Success',
                message: 'Created Successfully',
                type: 'success',
                duration: 2000
              })
            } else {
              this.$notify({
                title: 'Error',
                message: 'Created Failed',
                type: 'error',
                duration: 2000
              })
            }
          })
        }
      })
    },
    handleDelete(row) {
      this.$confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        example.removeExample(row.id).then(response => {
          if (response.code === 200) {
            this.fetchData()
            this.$notify({
              title: 'Success',
              message: 'Delete Successfully',
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: 'Error',
              message: 'Delete Failed',
              type: 'error',
              duration: 2000
            })
          }
        })
      })
    },
    transformInterfaceMessage() {
      const param = {
        testResultStr: this.temp.originJson,
        className: this.temp.className,
        dataName: this.temp.dataName
      }
      transformIMData(param).then(result => {
        if (result.code === 200) {
          this.temp.newJson = JSON.parse(result.data)
        }
      })
    },
    handleOperate() {
      if (this.dialogStatus === 'view') {
        this.dialogFormVisible = false
      }

      if (this.dialogStatus === 'create') {
        this.createData()
      }

      if (this.dialogStatus === 'update') {
        this.updateData()
      }
    },
    getExampleList() {
      example.getList().then(response => {
        if (response.code === 200) {
          this.exampleList = response.data
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.filter-container {
  display: flex;
  align-items: center;
}

.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
}

.el-dropdown+.el-dropdown {
  margin-left: 15px;
}
</style>
