<template>
  <div class="app-container">
    <div ref="queryFormRef" class="filter-container">
      <el-select v-model="listQuery.category" placeholder="任务分类" class="filter-item" filterable clearable>
        <el-option v-for="dict in dict.type.hts_category" :key="dict.value" :label="dict.label" :value="dict.value" />
      </el-select>
      <el-select v-model="listQuery.summaryId" placeholder="所属任务" style="width: 200px;" class="filter-item" clearable
        filterable>
        <el-option v-for="item in summaryList" :key="item.id" :label="`${item.name} (${item.code})`" :value="item.id" />
      </el-select>
      <el-input v-model="listQuery.name" placeholder="明细名称" style="width: 200px;" class="filter-item" />
      <el-select v-model="listQuery.preRequest" placeholder="是否前置请求" class="filter-item" filterable clearable>
        <el-option v-for="item in preRequestList" :key="item.key" :label="item.label" :value="item.value" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="fetchData">
        搜索
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-edit"
        @click="handleCreate">
        添加
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-refresh"
        @click="handleReset">
        重置
      </el-button>
    </div>
    <el-table ref="fullHeightTableRef" :height="tableHeight" v-loading="listLoading" :data="list" element-loading-text="Loading" border fit highlight-current-row
      style="width: 100%" size="medium">
      <el-table-column label="序号" align="center" width="95px">
        <template slot-scope="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>
      <el-table-column label="分类" align="center">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.hts_category" :value="scope.row.category" />
        </template>
      </el-table-column>
      <el-table-column label="任务名称" align="center">
        <template slot-scope="scope">{{ scope.row.summaryName }}</template>
      </el-table-column>
      <el-table-column label="明细名称" align="center">
        <template slot-scope="scope">{{ scope.row.name }}</template>
      </el-table-column>
      <el-table-column align="center" label="认证或前置请求" width="120px">
        <template slot-scope="scope">{{ getPreRequestLabel(scope.row.preRequest) }}</template>
      </el-table-column>
      <el-table-column label="是否在流程内开启" align="center" width="150">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.wfEnable" active-color="#00A854" active-text="启动" :active-value="1"
            inactive-color="#F04134" inactive-text="停止" :inactive-value="0" @change="changeWfSwitch(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="上次执行时间" align="center">
        <template slot-scope="scope">{{ scope.row.lastTriggerTime }}</template>
      </el-table-column>
      <el-table-column label="请求方式" align="center" width="95px">
        <template slot-scope="scope">{{ scope.row.requestMethod }}</template>
      </el-table-column>
      <el-table-column label="接口地址" align="center" width="350px">
        <template slot-scope="scope">{{ scope.row.url }}</template>
      </el-table-column>
      <el-table-column label="目标数据源" align="center">
        <template slot-scope="scope">{{ scope.row.datasourceName }}</template>
      </el-table-column>
      <el-table-column label="目标数据表" align="center">
        <template slot-scope="scope">{{ scope.row.targetTable }}</template>
      </el-table-column>
      <el-table-column label="创建时间" align="center">
        <template slot-scope="scope">{{ scope.row.createTime }}</template>
      </el-table-column>
      <el-table-column label="更新时间" align="center">
        <template slot-scope="scope">{{ scope.row.updateTime }}</template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right" width="100px">
        <template slot-scope="{row}">
          <!-- 操作 -->
          <el-dropdown trigger="click">
            <span class="el-dropdown-link">
              操作<i class="el-icon-arrow-down el-icon--right" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="handleExecute(row)">执行一次</el-dropdown-item>
              <el-dropdown-item divided @click.native="handleViewTaskLog(row)">查看日志</el-dropdown-item>
              <el-dropdown-item divided @click.native="handlerUpdate(row)">编辑</el-dropdown-item>
              <el-dropdown-item @click.native="handlerDelete(row)">删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageNum" :limit.sync="listQuery.pageSize"
      @pagination="fetchData" />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="100%" :before-close="handleClose"
      :fullscreen="true">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分类" prop="category" label-width="150px">
              <el-select v-model="temp.category" placeholder="任务分类" style="width: 100%" class="filter-item" filterable
                clearable>
                <el-option v-for="dict in dict.type.hts_category" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否为前置请求" prop="preRequest" label-width="150px">
              <el-switch v-model="temp.preRequest" active-color="#3d8ee1" :active-value="1" inactive-color="#918f8f"
                :inactive-value="0" @change="handlePreRequestChange" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属任务" prop="summaryId" label-width="150px">
              <el-select v-model="temp.summaryId" placeholder="请选择所属任务" style="width: 100%" class="filter-item"
                filterable clearable>
                <el-option v-for="item in this.summaryList" :key="item.id" :label="`${item.name} ${item.code}`"
                  :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="明细名称" prop="name" label-width="150px">
              <el-input v-model="temp.name" size="medium" placeholder="请输入明细名称" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="4">
            <el-form-item label="请求方式及地址" prop="requestMethod" label-width="150px">
              <el-select v-model="temp.requestMethod" placeholder="请求方式" style="width: 100%" class="filter-item"
                clearable>
                <el-option v-for="item in this.requestMethodList" :key="item.key" :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item prop="url" label-width="0px">
              <el-input v-model="temp.url" size="medium" placeholder="请输入接口地址" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="请求成功标识字段" prop="successFlag" label-width="150px">
              <el-input v-model="temp.successFlag" size="medium" placeholder="如没有请填写NULL:NULL" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="返回格式" prop="responseType" label-width="150px">
              <el-input v-model="temp.responseType" size="medium" placeholder="返回格式" style="width: 100%" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="请求返回主数据字段" prop="dataFlag" label-width="150px">
              <el-input v-model="temp.dataFlag" size="medium" placeholder="请填写请求返回主数据字段($根节点 .子节点)" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="失败策略" prop="failStrategy" label-width="150px">
              <el-select v-model="temp.failStrategy" placeholder="失败策略(前置任务不生效)" style="width: 100%">
                <el-option v-for="item in failStrategies" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <!--          <el-col :span="12">-->
          <!--            <el-form-item label="获取方式" prop="mongoTbWay" label-width="150px" v-if="isMongoDB(temp.datasourceId) && !isPreRequest">-->
          <!--              <el-select v-model="temp.mongoTbWay" placeholder="请选择数据表获取方式" style="width: 100%" @change="mongoTbWayChange"-->
          <!--                         class="filter-item" clearable-->
          <!--              >-->
          <!--                <el-option-->
          <!--                  v-for="item in this.mongoTbWayList"-->
          <!--                  :key="item.key"-->
          <!--                  :label="item.label"-->
          <!--                  :value="item.value"-->
          <!--                />-->
          <!--              </el-select>-->
          <!--            </el-form-item>-->
          <!--          </el-col>-->
          <!--          <el-col :span="12">-->
          <!--            <el-form-item v-if="!isPreRequest" label="目标数据表" prop="targetTable" label-width="150px">-->
          <!--              <el-select v-model="temp.targetTable" placeholder="请选择目标数据表" style="width: 100%" class="filter-item" filterable clearable>-->
          <!--                <el-option-->
          <!--                  v-for="item in this.tableList"-->
          <!--                  :key="item"-->
          <!--                  :label="item"-->
          <!--                  :value="item"-->
          <!--                />-->
          <!--              </el-select>-->
          <!--            </el-form-item>-->
          <!--          </el-col>-->
        </el-row>

        <el-tabs v-model="activeName" @tab-click="handleClick">

          <el-tab-pane label="数据处理" name="0" v-if="!hideFirstTab">

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item v-if="!isPreRequest" label="报文处理方式" prop="imway" label-width="150px">
                  <el-select v-model="temp.imway" style="width: 100%" class="filter-item" clearable>
                    <el-option v-for="item in this.IMWayList" :key="item.key" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item v-if="!isPreRequest && !isConfig" label="自定义处理类" prop="className" label-width="150px">
                  <el-input v-model="temp.className" size="medium" placeholder="填写类名(首字母小写，后缀已拼写好)">
                    <template slot="append">ApiDataHandler</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item v-if="!isPreRequest && !isConfig" label="原报文数据字段名" prop="dataName" label-width="150px">
                  <el-input v-model="temp.dataName" size="medium" placeholder="填写原报文数据字段名(非表达式)" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item v-if="!isPreRequest && !isConfig" label-width="150px">
                  <el-button type="primary" size="mini" icon="el-icon-news" style="margin-right: 30px;"
                    @click="viewExample">
                    具体用例查看
                  </el-button>
                  <el-button type="primary" size="mini" icon="el-icon-view" @click="viewTransformInterfaceMessage">
                    预览报文处理
                  </el-button>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item v-if="!isPreRequest" label="目标数据源" prop="datasourceId" label-width="150px">
                  <el-select v-model="temp.datasourceId" placeholder="请选择目标数据源" style="width: 100%" class="filter-item"
                    clearable>
                    <el-option v-for="item in this.datasourceList" :key="item.id" :label="item.datasourceName"
                      :value="item.id" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item v-if="!isPreRequest" label="目标数据表" prop="targetTable" label-width="150px">
                  <el-input v-model="temp.targetTable" size="medium" placeholder="请输入目标数据表" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item v-if="!isPreRequest" label="是否清空集合" prop="clearTable" label-width="150px">
                  <el-select v-model="temp.clearTable" style="width: 100%" class="filter-item" clearable>
                    <el-option v-for="item in this.clearTableList" :key="item.key" :label="item.label"
                      :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item v-if="!isPreRequest && temp.clearTable === 0" label="upsert字段" prop="upsertFields"
                  label-width="150px">
                  <el-input v-model="temp.upsertFields" size="medium" placeholder="upsert字段（多个以英文逗号分隔）"
                    style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <span class="custom-alert-title">子表配置（若有需要）</span>
            <el-table :data="dataTableData" style="width: 100%" border>
              <el-table-column prop="field" align="center" label="主数据中的子数据字段">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.field" />
                </template>
              </el-table-column>

              <el-table-column prop="table" align="center" label="子数据的目标数据表">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.table" />
                </template>
              </el-table-column>

              <el-table-column prop="table" align="center" label="子数据的upsertFields">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.upsertKey" />
                </template>
              </el-table-column>

              <el-table-column prop="desc" align="center" label="描述">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.desc" />
                </template>
              </el-table-column>

              <el-table-column align="center">
                <template slot-scope="scope">
                  <el-button icon="el-icon-plus" @click="addDataRow(scope.$index)" style="margin-right: 10px;"
                    class="add-button">
                  </el-button>
                  <el-button icon="el-icon-minus" @click="removeDataRow(scope.$index)" class="delete-button">
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="Header" name="1">
            <el-table v-if="activeName === '1'" :data="headerTableData" style="width: 100%" border>
              <el-table-column prop="key" align="center" label="参数名">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.key"></el-input>
                </template>
              </el-table-column>

              <el-table-column prop="type" align="center" label="参数类型">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.type" class="filter-item" style="width: 100%;" filterable clearable
                    placeholder="请选择参数类型">
                    <el-option v-for="dict in dict.type.hts_param_type" :key="dict.value" :label="dict.label"
                      :value="dict.value" />
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column prop="value" align="center" label="参数值">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.value"></el-input>
                </template>
              </el-table-column>

              <el-table-column prop="desc" align="center" label="参数描述">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.desc"></el-input>
                </template>
              </el-table-column>

              <el-table-column align="center">
                <template slot-scope="scope">
                  <el-button icon="el-icon-plus" @click="addHeaderRow(scope.$index)" style="margin-right: 10px;"
                    class="add-button">
                  </el-button>
                  <el-button icon="el-icon-minus" @click="removeHeaderRow(scope.$index)" class="delete-button">
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <el-tab-pane label="Query" name="2">
            <el-table v-if="activeName === '2'" :data="queryTableData" style="width: 100%" border>
              <el-table-column prop="key" align="center" label="参数名">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.key"></el-input>
                </template>
              </el-table-column>

              <el-table-column prop="type" align="center" label="参数类型">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.type" class="filter-item" style="width: 100%;" filterable clearable
                    placeholder="请选择参数类型">
                    <el-option v-for="dict in dict.type.hts_param_type" :key="dict.value" :label="dict.label"
                      :value="dict.value" />
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column prop="value" align="center" label="参数值">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.value"></el-input>
                </template>
              </el-table-column>

              <el-table-column prop="desc" align="center" label="参数描述">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.desc"></el-input>
                </template>
              </el-table-column>

              <el-table-column align="center">
                <template slot-scope="scope">
                  <el-button icon="el-icon-plus" @click="addQueryRow(scope.$index)" style="margin-right: 10px;"
                    class="add-button">
                  </el-button>
                  <el-button icon="el-icon-minus" @click="removeQueryRow(scope.$index)" class="delete-button">
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <el-tab-pane label="Body" name="3">

          </el-tab-pane>
          <json-editor v-if="activeName === '3'" ref="jsonEditor" v-model="temp.requestParam" />

          <el-tab-pane label="认证" name="4">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-select v-model="temp.authWay" class="filter-item" style="width: 80%;" filterable
                  placeholder="请选择认证类型">
                  <el-option v-for="dict in dict.type.hts_auth_way" :key="dict.value" :label="dict.label"
                    :value="dict.value" />
                </el-select>
              </el-col>

              <el-col :span="18">
                <el-form-item v-if="temp.authWay === '1'" label="该接口无需认证" label-width="150px" />
                <el-form-item v-if="temp.authWay === '2'" label="key" label-width="50px">
                  <el-input v-model="auth.KeyValuePairs.key" placeholder="键" clearable size="small" />
                </el-form-item>
                <el-form-item v-if="temp.authWay === '2'" label="value" label-width="50px">
                  <el-input v-model="auth.KeyValuePairs.value" placeholder="值" clearable size="small" />
                </el-form-item>
                <el-form-item v-if="temp.authWay === '3'" label="token" label-width="150px">
                  <el-input v-model="auth.BearerToken.token" placeholder="Token（支持变量 ${param}）" clearable
                    size="small" />
                </el-form-item>
                <el-form-item v-if="temp.authWay === '3'" label="认证请求" label-width="150px">
                  <el-select v-model="auth.BearerToken.httpJobId" placeholder="请选择认证请求" @change="handleAuthTaskChange"
                    style="width: 100%" class="filter-item" filterable clearable>
                    <el-option v-for="item in this.preHttpTaskList" :key="item.id" :label="item.name"
                      :value="item.id" />
                  </el-select>
                </el-form-item>
                <el-form-item v-if="temp.authWay === '3'" label="认证请求名称" label-width="150px">
                  <el-input v-model="auth.BearerToken.httpJobName" placeholder="认证请求名称" clearable size="small" />
                </el-form-item>
                <el-form-item v-if="temp.authWay === '4'" label="用户名" label-width="150px">
                  <el-input v-model="auth.BasicAuth.username" placeholder="用户名" clearable size="small" />
                </el-form-item>
                <el-form-item v-if="temp.authWay === '4'" label="密码" label-width="150px">
                  <el-input v-model="auth.BasicAuth.password" placeholder="密码" clearable size="small" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>

          <el-tab-pane v-if="!isPreRequest" label="前置任务" name="5">
            <el-row :gutter="20">
              <el-col :span="4">
                <el-form-item label="前置任务类型" label-width="150px">
                  <el-select v-model="temp.preTaskType" placeholder="请选择前置任务类型" @change="handleTaskChange"
                    style="width: 100%" class="filter-item" filterable clearable>
                    <el-option v-for="item in this.preHttpTaskTypeList" :key="item.key" :label="item.label"
                      :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item v-if="temp.preTaskType === 'sql'" label="数据源类型" label-width="150px">
                  <el-select v-model="temp.preDatasourceType" clearable placeholder="数据源类型" style="width: 100%"
                    @change="handleDataSourceTypeChange(temp.preDatasourceType)">
                    <el-option v-for="item in datasourceTypes" :key="item.value" :label="item.label"
                      :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item v-if="temp.preTaskType === 'sql'" label="数据库源：" label-width="150px">
                  <el-select :disabled="isDatasourceDisabled" v-model="temp.preDatasourceId" filterable
                    style="width: 100%">
                    <el-option v-for="item in preDatasourceList" :key="item.id" :label="item.datasourceName"
                      :value="item.id" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item v-if="temp.preTaskType === 'http'" label="前置请求任务" label-width="150px">
                  <el-select v-model="temp.preHttpTask" placeholder="请选择前置任务" @change="handleTaskChange"
                    style="width: 100%" class="filter-item" filterable clearable>
                    <el-option v-for="item in this.preHttpTaskList" :key="item.id" :label="item.name"
                      :value="item.id" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item v-if="temp.preTaskType === 'http'" label="前置请求任务" label-width="150px">
                  <el-input v-model="temp.preHttpTaskName" placeholder="前置任务名称" clearable size="small" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-alert type="warn" :closable="false" v-if="temp.preTaskType === 'sql'">
                  <template #title>
                    <span class="custom-alert-title">注意事项</span>
                  </template>
                  <template #default>
                    <pre class="custom-alert-text">
1.该编辑器只支持输入DQL语句，不可执行DDL、DML等语句。
2.SQL查询结果值类型需可以被转换为字符串，如不可以需要在SQL中进行转换，例：SQL中DATE类型需要提前在SQL中转换成字符串查询。
        </pre>
                  </template>
                </el-alert>
              </el-col>
            </el-row>
          </el-tab-pane>
          <sql-editor v-if="temp.preTaskType === 'sql' && activeName === '5'" ref="sqlEditor" v-model="temp.jobSql"
            @changeTextarea="handleSQLChange" />
        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>

    <el-dialog :title="exampleDialogTitle" :visible.sync="exampleDialogFormVisible" width="100%"
      :before-close="handleClose" :fullscreen="true">
      <httpTransformExample :showSearch="false">

      </httpTransformExample>
    </el-dialog>

    <el-dialog :title="testDialogTitle" :visible.sync="testDialogFormVisible" width="100%" :before-close="handleClose"
      :fullscreen="true">
      <el-form :model="temp" label-position="left" label-width="150px"
        style="width: 95%; margin-left:50px; margin-right:50px;">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="原始报文" label-width="150px" />
          </el-col>
          <el-col :span="12">
            <el-form-item label="转换后报文" label-width="150px" />
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-scrollbar style="height: 600px;">
              <json-editor ref="jsonEditor" v-model="temp.testResultStr" />
            </el-scrollbar>
          </el-col>
          <el-col :span="12">
            <el-scrollbar style="height: 600px;">
              <json-editor ref="jsonEditor" v-model="temp.testHandleResultStr" />
            </el-scrollbar>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button style="margin-right: 10px;" icon="el-icon-switch-button" @click="handleCloseTestDialog">
          关闭
        </el-button>
        <el-button type="primary" icon="el-icon-refresh" @click="transformInterfaceMessage">
          转换
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import * as jobSummary from '@/api/hts/http-job-summary'
import * as jobInfo from '@/api/hts/http-job-info'
import { getDataSourceListByType as jdbcDsList } from '@/api/dts/datax-jdbcDatasource'
import * as queryTables from '@/api/dts/metadata-query'
import * as dsQueryApi from '@/api/dts/metadata-query'
import { getAllTableCodeFromFormDesign as getTableCodeList } from '@/api/codeDev/formDesign/formDesign'
import { list as dsList } from '@/api/datax/datax-jdbcDatasource'
import httpTransformExample from '@/views/hts/httpTransformExample/index.vue'
import waves from '@/directive/waves' // waves directive
import Pagination from '@/components/Pagination'
import JsonEditor from '@/components/JsonEditor'
import SqlEditor from '@/components/SqlEditor'
import { encodeSqlToEncryptedBase64 } from '@/utils/sqlHandle'

export default {
  mixins: [tableFullHeight],
  name: 'HttpJobInfo',
  components: { SqlEditor, JsonEditor, Pagination, httpTransformExample },
  directives: { waves },
  dicts: ['hts_category', 'hts_param_type', 'hts_auth_way'],
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'gray',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      list: null,
      listLoading: true,
      total: 0,
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        name: '',
        summaryId: this.$route.query.summaryId ? Number(this.$route.query.summaryId) : undefined
      },
      jdbcDsQuery: {
        datasource: '',
        ascs: 'datasource_name'
      },
      preDatasourceList: [],
      activeName: '0',
      hideFirstTab: false,
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: 'Edit',
        create: 'Create'
      },
      exampleDialogFormVisible: false,
      exampleDialogTitle: '用例列表',
      testDialogFormVisible: false,
      testDialogTitle: '报文转换预览',
      rules: {
        summaryId: [{ required: true, message: 'this is required', trigger: 'change' }],
        name: [{ required: true, message: 'this is required', trigger: 'blur' }],
        category: [{ required: true, message: 'this is required', trigger: 'change' }],
        datasourceId: [{ required: true, message: 'this is required', trigger: 'change' }],
        url: [{ required: true, message: 'this is required', trigger: 'blur' }],
        targetTable: [{ required: true, message: 'this is required', trigger: 'blur' }],
        requestMethod: [{ required: true, message: 'this is required', trigger: 'blur' }],
        successFlag: [{ required: true, message: 'this is required', trigger: 'blur' }],
        dataFlag: [{ required: true, message: 'this is required', trigger: 'blur' }],
        mongoTbWay: [{ required: true, message: 'this is required', trigger: 'change' }],
        preRequest: [{ required: true, message: 'this is required', trigger: 'change' }],
        responseType: [{ required: true, message: 'this is required', trigger: 'change' }],
        clearTable: [{ required: true, message: 'this is required', trigger: 'change' }],
        imway: [{ required: true, message: 'this is required', trigger: 'change' }],
        className: [{ required: true, message: 'this is required', trigger: 'blur' }],
        dataName: [{ required: true, message: 'this is required', trigger: 'blur' }],
        failStrategy: [
          { required: true, message: 'failStrategy is required', trigger: 'change' }
        ]
      },
      temp: {
        id: undefined,
        name: '',
        summaryId: undefined,
        category: undefined,
        url: '',
        datasourceId: undefined,
        tableSchema: '',
        targetTable: '',
        targetChildTable: null,
        upsertFields: '',
        requestMethod: '',
        requestHeader: null,
        responseType: 'application/json',
        requestParam: null,
        requestQuery: null,
        successFlag: '',
        dataFlag: '',
        mongoTbWay: undefined,
        authWay: '1',
        clearTable: 0,
        auth: {
          None: null,
          KeyValuePairs: { key: '', value: '' },
          BearerToken: { token: '', httpJobId: null, httpJobName: '' },
          BasicAuth: { username: '', password: '' }
        },
        preRequest: 0,
        preHttpTask: undefined,
        preHttpTaskName: '',
        preTaskType: '',
        preDatasourceId: undefined,
        jobSql: '',
        preDatasourceType: '',
        imway: 'config',
        className: '',
        dataName: '',
        testResultStr: null,
        testHandleResultStr: null,
        failStrategy: ''
      },
      resetTemp() {
        this.temp = this.$options.data.call(this).temp
        this.summaryId = undefined
        this.name = ''
        this.code = ''
        this.sort = undefined
        this.category = undefined
        this.remark = ''
        this.activeName = '0'
        this.authWay = '1'
        this.preRequest = 0
        this.preHttpTask = undefined
        this.preHttpTaskName = ''
        this.dataTableData = [
          { field: '', table: '', upsertKey: '', desc: '' }
        ]
        this.headerTableData = [
          { key: '', type: '', value: undefined, desc: '' }
        ]
        this.queryTableData = [
          { key: '', type: '', value: undefined, desc: '' }
        ]
        this.auth = {
          None: null,
          KeyValuePairs: { key: '', value: '' },
          BearerToken: { token: '', httpJobId: null, httpJobName: '' },
          BasicAuth: { username: '', password: '' }
        }
        this.failStrategy = ''
      },
      isMongo: false,
      jobInfoList: [],
      summaryList: [],
      datasourceList: [],
      datasourceTypes: [{ key: 'mysql', value: 'mysql', label: 'MYSQL' }],
      clearTableList: [{ key: 1, value: 1, label: '清空' }, { key: 0, value: 0, label: '不清空' }],
      isDatasourceDisabled: true,
      tableList: [],
      preHttpTaskList: [],
      preHttpTaskTypeList: [{ key: 'sql', value: 'sql', label: 'SQL脚本' }, { key: 'http', value: 'http', label: 'HTTP任务' }],
      requestMethodList: [{ key: 'GET', value: 'GET', label: 'GET' }, { key: 'POST', value: 'POST', label: 'POST' }],
      mongoTbWayList: [{ key: 'source', value: 1, label: 'Mongo数据源' }, { key: 'form', value: 2, label: '表单设计' }],
      dataTableData: [
        { field: '', table: '', upsertKey: '', desc: '' }
      ],
      headerTableData: [
        { key: '', type: '', value: undefined, desc: '' }
      ],
      queryTableData: [
        { key: '', type: '', value: undefined, desc: '' }
      ],
      auth: {
        None: null,
        KeyValuePairs: { key: '', value: '' },
        BearerToken: { token: '', httpJobId: null, httpJobName: '' },
        BasicAuth: { username: '', password: '' }
      },
      preRequestList: [{ key: 1, value: 1, label: '是' }, { key: 0, value: 0, label: '否' }],
      preRequestIconList: [{ key: 1, value: 1, label: '是' }, { key: 0, value: 0, label: '' }],
      IMWayList: [{ key: 'config', value: 'config', label: '无需处理，直接配置' }, { key: 'custom', value: 'custom', label: '自定义开发' }],
      failStrategies: [
        { value: 'STOP_EXECUTION', label: '停止执行后续任务' },
        { value: 'CONTINUE_EXECUTION', label: '继续执行后续任务' }
      ]
    }
  },
  created() {
    this.fetchData()
    this.getSummaryList()
    this.getDatasourceList()
    this.getPreHttpTask()
    this.queryPreDataSource('mysql')
  },
  watch: {
    hideFirstTab(newValue) {
      if (newValue) {
        this.activeName = '1';
      } else {
        this.activeName = '0';
      }
    }
  },
  computed: {
    isPreRequest() {
      return this.temp.preRequest === 1;
    },
    isConfig() {
      return this.temp.imway === 'config'
    }
  },
  methods: {
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
        })
        .catch(_ => {
        })
    },
    fetchData() {
      this.listLoading = true
      jobInfo.getPageList(this.listQuery).then(response => {
        if (response.code === 200) {
          this.total = response.total
          this.list = response.rows
        } else {
          this.$message.error(response.msg)
        }
        this.listLoading = false
      })
    },
    getPreHttpTask() {
      const queryParam = {
        preRequest: 1
      }
      jobInfo.getList(queryParam).then(response => {
        if (response.code === 200) {
          this.preHttpTaskList = response.data
        }
      })
    },
    getSummaryList() {
      jobSummary.getList(this.listQuery).then(response => {
        if (response.code === 200) {
          this.summaryList = response.data
        }
      })
    },
    getDatasourceList() {
      jdbcDsList('mongodb').then(response => {
        this.datasourceList = response.data
      })
    },
    handleCreate() {
      this.resetTemp()
      this.hideFirstTab = false
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      this.isDatasourceDisabled = !this.temp.preDatasourceType
    },
    handleReset() {
      this.listQuery = {
        pageNum: 1,
        pageSize: 10,
        jobDesc: '',
        code: ''
      }
      this.fetchData()
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // 有sql的话需要加密处理
          if (this.temp.jobSql) {
            this.temp.jobSql = encodeSqlToEncryptedBase64(this.temp.jobSql)
          }
          this.temp.requestHeader = { header: this.headerTableData }
          this.temp.requestQuery = { query: this.queryTableData }
          this.temp.targetChildTable = { data: this.dataTableData }

          if (typeof this.temp.requestParam === 'string') {
            this.temp.requestParam = JSON.parse(this.temp.requestParam);
          }
          this.temp.auth = this.auth
          jobInfo.create(this.temp, {
            headers: {
              'Content-Type': 'application/json'
            }
          }).then(result => {
            this.fetchData()
            this.getPreHttpTask()
            this.dialogFormVisible = false
            if (result.code === 200) {
              this.$notify({
                title: 'Success',
                message: 'Created Successfully',
                type: 'success',
                duration: 2000
              })
            } else {
              this.$notify({
                title: 'Error',
                message: 'Created Failed',
                type: 'error',
                duration: 2000
              })
            }
          })
        }
      })
    },
    handleExecute(row) {
      this.$confirm('确定执行吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$notify({
          title: 'Info',
          message: 'Executing',
          type: 'warning',
          duration: 2000
        })
        jobInfo.triggerJob(row.id).then(response => {
          if (response.code === 200) {
            this.$notify({
              title: 'Success',
              message: 'Execute Successfully',
              type: 'success',
              duration: 2000
            })
          } else if (response.code === 500) {
            this.$notify({
              title: 'Error',
              message: 'Execute Failed',
              type: 'error',
              duration: 5000
            })
          }
          this.fetchData()
        })
      })
    },
    // 查询任务明细
    handleViewTaskLog(row) {
      this.$router.push({
        path: '/DataDev/hts/httpJobLog',
        query: { httpJobId: row.id }
      })
    },
    handlerUpdate(row) {
      this.resetTemp()
      this.hideFirstTab = (row.preRequest === 1)
      this.temp = Object.assign({}, row)
      this.headerTableData = this.temp.requestHeader.header ? this.temp.requestHeader.header : this.headerTableData
      this.queryTableData = this.temp.requestQuery.query ? this.temp.requestQuery.query : this.queryTableData
      this.dataTableData = this.temp.requestQuery.query ? this.temp.targetChildTable.data : this.dataTableData
      this.auth = this.temp.auth
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      this.isDatasourceDisabled = !this.temp.preDatasourceType
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {

          if (this.temp.jobSql) {
            this.temp.jobSql = encodeSqlToEncryptedBase64(this.temp.jobSql)
          }
          this.temp.requestHeader = { header: this.headerTableData }
          this.temp.requestQuery = { query: this.queryTableData }
          this.temp.targetChildTable = { data: this.dataTableData }

          if (typeof this.temp.requestParam === 'string') {
            this.temp.requestParam = JSON.parse(this.temp.requestParam);
          }

          jobInfo.update(this.temp, {
            headers: {
              'Content-Type': 'application/json'
            }
          }).then(result => {
            this.fetchData()
            this.dialogFormVisible = false
            if (result.code === 200) {
              this.$notify({
                title: 'Success',
                message: 'Created Successfully',
                type: 'success',
                duration: 2000
              })
            } else {
              this.$notify({
                title: 'Error',
                message: 'Created Failed',
                type: 'error',
                duration: 2000
              })
            }
          })
        }
      })
    },
    handlerDelete(row) {
      this.$confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        jobInfo.remove(row.id).then(response => {
          if (response.code === 200) {
            this.fetchData()
            this.getPreHttpTask()
            this.$notify({
              title: 'Success',
              message: 'Delete Successfully',
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: 'Error',
              message: 'Delete Failed',
              type: 'error',
              duration: 2000
            })
          }
        })
      })
    },
    datasourceChange(id) {
      this.tableList = []
      this.temp.targetTable = ''
      // mysql/doris
      const selectedDatasource = this.datasourceList.find(item => item.id === id)
      // 如果找到了对应的数据源，则获取数据源类型
      if (selectedDatasource) {
        const datasourceType = selectedDatasource.datasource
        if (datasourceType === 'mysql' || datasourceType === 'doris') {
          this.isMongo = false
          let obj = { datasourceId: this.temp.datasourceId }
          queryTables.getTables(obj).then(response => {
            if (response) {
              this.tableList = response.data
            }
          })
        } else this.isMongo = datasourceType === 'mongodb';
      }
    },
    isMongoDB(id) {
      const selectedDatasource = this.datasourceList.find(item => item.id === id)
      // 如果找到了对应的数据源，则获取数据源类型
      if (selectedDatasource) {
        const datasourceType = selectedDatasource.datasource
        if (datasourceType === 'mysql' || datasourceType === 'doris') {
          return false
        } else return datasourceType === 'mongodb';
      }
    },
    mongoTbWayChange(way) {
      if (way === 1) {
        const obj = {
          datasourceId: this.temp.datasourceId
        }
        // 数据源
        dsQueryApi.getTables(obj).then(response => {
          this.tableList = response.data
        })
      } else if (way === 2) {
        // 从表单设计获取表
        getTableCodeList().then((result) => {
          this.tableList = result.data
        })
      }
    },
    // 切换tab页
    handleClick(tab) {
      // this.activeName = tab.index
      // jsonEditor会把用户输入变为字符串展示 如果是json对象 jsonEditor会展示格式化后信息
      // 切换tab页时，判断如果是字符串就解析为对象，解决将用户输入展示为字符串的问题
      if (this.activeName === '2' && typeof this.temp.requestParam === 'string') {
        this.temp.requestParam = JSON.parse(this.temp.requestParam)
      }
    },

    addDataRow(index) {
      this.dataTableData.splice(index + 1, 0, { field: '', table: '', upsertKey: '', desc: '' });
    },
    removeDataRow(index) {
      if (this.dataTableData.length > 1) {
        this.dataTableData.splice(index, 1); j
      }
    },
    addHeaderRow(index) {
      this.headerTableData.splice(index + 1, 0, { key: '', type: '', value: undefined, desc: '' });
    },
    removeHeaderRow(index) {
      if (this.headerTableData.length > 1) {
        this.headerTableData.splice(index, 1); j
      }
    },
    addQueryRow(index) {
      this.queryTableData.splice(index + 1, 0, { key: '', type: '', value: undefined, desc: '' });
    },
    removeQueryRow(index) {
      if (this.queryTableData.length > 1) {
        this.queryTableData.splice(index, 1);
      }
    },
    handlePreRequestChange(value) {
      const fieldsToValidate = ['datasourceId', 'mongoTbWay', 'targetTable'];
      if (value === 1) {
        this.hideFirstTab = true
        fieldsToValidate.forEach(field => {
          this.$set(this.rules, field, []);
        });
      } else {
        this.hideFirstTab = false
        this.$set(this.rules, 'datasourceId', [{ required: true, message: 'this is required', trigger: 'change' }],);
        this.$set(this.rules, 'targetTable', [{ required: true, message: 'this is required', trigger: 'change' }],);
        this.$set(this.rules, 'mongoTbWay', [{ required: true, message: 'this is required', trigger: 'change' }],);
      }
    },
    handleTaskChange(id) {
      const selectedTask = this.preHttpTaskList.find(task => task.id === id);
      this.temp.preHttpTaskName = selectedTask ? selectedTask.name : this.temp.preHttpTaskName;
    },
    handleAuthTaskChange(id) {
      const selectedTask = this.preHttpTaskList.find(task => task.id === id);
      this.auth.BearerToken.httpJobName = selectedTask ? selectedTask.name : this.auth.BearerToken.httpJobName;
    },
    getPreRequestLabel(preRequest) {
      const item = this.preRequestIconList.find(item => item.key === preRequest);
      return item ? item.label : '';
    },
    changeWfSwitch(row) {
      row.wfEnable === 1 ? this.handlerWfStart(row) : this.handlerWfStop(row)
    },
    handlerWfStart(row) {
      jobInfo.startWfJob(row.id).then(() => {
        this.$notify({
          title: 'Success',
          message: 'Start Successfully',
          type: 'success',
          duration: 2000
        })
      })
    },
    handlerWfStop(row) {
      jobInfo.stopWfJob(row.id).then(() => {
        this.$notify({
          title: 'Success',
          message: 'Close Successfully',
          type: 'success',
          duration: 2000
        })
      })
    },
    handleDataSourceTypeChange(type) {
      this.isDatasourceDisabled = !type;
      this.jdbcDsQuery.datasource = type
      dsList(this.jdbcDsQuery).then(response => {
        const { records } = response.data
        this.preDatasourceList = records
      })
    },
    queryPreDataSource(type) {
      this.jdbcDsQuery.datasource = type
      dsList(this.jdbcDsQuery).then(response => {
        const { records } = response.data
        this.preDatasourceList = records
      })
    },
    handleSQLChange(sql) {
      this.temp.jobSql = sql
    },
    transformInterfaceMessage() {
      const param = {
        testResultStr: this.temp.testResultStr,
        className: this.temp.className,
        dataName: this.temp.dataName
      }
      jobInfo.transformIMData(param).then(result => {
        if (result.code === 200) {
          this.temp.testHandleResultStr = JSON.parse(result.data)
        }
      })
    },
    viewTransformInterfaceMessage() {
      this.testDialogFormVisible = true
      this.temp.testResultStr = null
      this.temp.testHandleResultStr = null
    },
    handleCloseTestDialog() {
      this.testDialogFormVisible = false
    },
    viewExample() {
      this.exampleDialogFormVisible = true
    },
    handleCloseExampleDialog() {
      this.exampleDialogFormVisible = false
    },
  }
}
</script>

<style scoped lang="scss">
.label-custom {
  width: 150px;
}

.filter-container {
  display: flex;
  align-items: center;
}

.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
}

.el-dropdown+.el-dropdown {
  margin-left: 15px;
}

.el-table {
  margin-top: 20px;
}

.add-button {
  background-color: #d9e9f8;
  border: none;
  border-radius: 20px;
  color: #1890FF;
}

.delete-button {
  background-color: #ffe1e1;
  border: none;
  border-radius: 20px;
  color: #FF9292;
}

.add-button:hover,
.delete-button:hover {
  opacity: 0.8;
}

.custom-alert-title {
  font-weight: bold;
  color: black;
  font-size: 14px;
  white-space: pre-wrap;
}

.custom-alert-text {
  color: #1890FF;
  font-size: 14px;
  white-space: pre-wrap;
}
</style>
