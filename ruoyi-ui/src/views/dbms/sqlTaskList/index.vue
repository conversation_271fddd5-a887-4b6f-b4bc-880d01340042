<template>
  <div class="app-container">
    <div ref="queryFormRef" class="filter-container">
<!--      <el-select v-model="listQuery.category" placeholder="任务类型" style="width: 200px;" class="filter-item" clearable filterable>-->
<!--        <el-option v-for="item in categoryOptions" :key="item.value" :label="item.label" :value="item.value" />-->
<!--      </el-select>-->
      <el-select v-model="listQuery.groupId" placeholder="任务分组" style="width: 200px;" class="filter-item" clearable filterable>
        <el-option v-for="item in sqlGroupList" :key="item.id" :label="item.jobDesc" :value="item.id" />
      </el-select>
      <el-input v-model="listQuery.jobDesc" placeholder="任务名称" style="width: 200px;" class="filter-item" />
      <el-select v-model="projectIds" multiple placeholder="所属项目" class="filter-item">
        <el-option v-for="item in jobProjectList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <el-input v-model="listQuery.lastProcessTime" placeholder=">=执行耗时" style="width: 200px;" class="filter-item" type="number"/>
      <el-select v-model="listQuery.lastHandleCode" placeholder="执行结果"  class="filter-item" clearable>
        <el-option
          v-for="item in statusList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="fetchData">
        搜索
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-edit"
        @click="handleCreate">
        添加
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-refresh"
                 @click="handleReset">
        重置
      </el-button>
    </div>
    <el-table  :height="tableHeight"
    ref="fullHeightTableRef" v-loading="listLoading" :data="list" element-loading-text="Loading" border fit highlight-current-row
      style="width: 100%" size="medium">
      <el-table-column align="center" label="ID" width="120">
        <template slot-scope="scope">{{ scope.row.id }}</template>
      </el-table-column>
      <el-table-column align="center" label="数据源" width="120">
        <template slot-scope="scope">{{ getDatasourceNameById(scope.row.datasourceId) }}</template>
      </el-table-column>
<!--      <el-table-column label="类型" align="center" width="200">-->
<!--        <template slot-scope="scope">{{ scope.row.categoryName }}</template>-->
<!--      </el-table-column>-->
      <el-table-column label="所属分组" align="center" width="250">
        <template slot-scope="scope">{{ scope.row.groupName }}</template>
      </el-table-column>
      <el-table-column label="任务名称" align="center" width="350">
        <template slot-scope="scope">{{ scope.row.jobDesc }}</template>
      </el-table-column>
      <el-table-column label="所属项目" align="center" width="120">
        <template slot-scope="scope">{{ scope.row.projectName }}</template>
      </el-table-column>
      <el-table-column label="是否在流程内开启" align="center" width="150">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.wfEnable" active-color="#00A854" active-text="启动" :active-value="1"
                     inactive-color="#F04134" inactive-text="停止" :inactive-value="0" @change="changeWfSwitch(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="执行顺序" align="center" width="100">
        <template slot-scope="scope">{{ scope.row.sort }}</template>
      </el-table-column>
      <el-table-column label="Cron" align="center" width="150">
        <template slot-scope="scope">
          <span>{{ scope.row.jobCron }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="150">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.triggerStatus" active-color="#00A854" active-text="启动" :active-value="1"
            inactive-color="#F04134" inactive-text="停止" :inactive-value="0" @change="changeSwitch(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="180">
        <template slot-scope="scope">{{ scope.row.addTime }}</template>
      </el-table-column>
      <el-table-column label="修改时间" align="center" width="180">
        <template slot-scope="scope">{{ scope.row.updateTime }}</template>
      </el-table-column>
      <el-table-column label="修改人" align="center" width="180">
        <template slot-scope="scope">{{ scope.row.updateBy }}</template>
      </el-table-column>
      <el-table-column label="最后执行时间" align="center" width="180">
        <template slot-scope="scope">{{ scope.row.lastTriggerTime }}</template>
      </el-table-column>
      <el-table-column label="最后执行耗时(s)" align="center" width="120">
        <template slot-scope="scope">{{ scope.row.lastProcessTime }}</template>
      </el-table-column>
      <el-table-column label="任务类型" align="center" width="100">
        <template slot-scope="scope">
          <template v-if="scope.row.glueType === 'BEAN'">
            BEAN任务
          </template>
          <template v-else>
            {{ scope.row.glueType }}
          </template>
        </template>
      </el-table-column>
      <el-table-column label="下次触发时间" align="center" width="120">
        <template slot-scope="scope">
          <el-popover placement="bottom" width="300" @show="nextTriggerTime(scope.row)">
            <h5 v-html="triggerNextTimes" />
            <el-button slot="reference" size="small">查看</el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="执行状态" align="center" width="80">
<!--        <template slot-scope="scope">-->
<!--          <el-tag :type="scope.row.lastHandleCode == 500 ? 'danger' : 'success'">{{-->
<!--              !scope.row.lastCode ? '无' : statusList.find((t) => t.value == scope.row.lastHandleCode).label-->
<!--            }}</el-tag>-->
<!--        </template>-->
        <template slot-scope="scope"> <el-tag :type="scope.row.lastHandleCode == 500 ? 'danger' : 'success'">{{ statusList.find(t => t.value === scope.row.lastHandleCode).label }}
        </el-tag></template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right">
        <template slot-scope="{row}">
          <!-- <el-dropdown type="primary" size="small"> -->
          <!-- 操作 -->
          <el-dropdown trigger="click">
            <span class="el-dropdown-link">
              操作<i class="el-icon-arrow-down el-icon--right" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="handlerExecute(row)">执行一次</el-dropdown-item>
              <el-dropdown-item @click.native="handlerViewLog(row)">查询日志</el-dropdown-item>
              <el-dropdown-item divided @click.native="handlerUpdate(row)">编辑</el-dropdown-item>
              <el-dropdown-item @click.native="handlerDelete(row)">删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size"
      @pagination="fetchData" />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="1000px"
      :before-close="handleClose">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="110px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="任务名称" prop="jobDesc">
              <el-input v-model="temp.jobDesc" size="medium" placeholder="请输入任务描述" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-dialog title="提示" :visible.sync="showCronBox" width="60%" append-to-body>
              <cron v-model="temp.jobCron" />
              <span slot="footer" class="dialog-footer">
                <el-button @click="showCronBox = false;">关闭</el-button>
                <el-button type="primary" @click="showCronBox = false">确 定</el-button>
              </span>
            </el-dialog>
            <el-form-item label="Cron" prop="jobCron">
              <el-input v-model="temp.jobCron" auto-complete="off" placeholder="请输入Cron表达式">
                <el-button v-if="!showCronBox" slot="append" icon="el-icon-turn-off" title="打开图形配置"
                  @click="showCronBox = true" />
                <el-button v-else slot="append" icon="el-icon-open" title="关闭图形配置" @click="showCronBox = false" />
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属分组" prop="groupId">
              <el-select v-model="temp.groupId" placeholder="任务分组" clearable filterable>
                <el-option v-for="item in sqlGroupList" :key="item.id" :label="item.jobDesc" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属项目" prop="projectId">
              <el-select v-model="temp.projectId" placeholder="所属项目" class="filter-item">
                <el-option v-for="item in jobProjectList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="失败策略" prop="failStrategy">
              <el-select
                v-model="temp.failStrategy"
                placeholder="失败策略"
              >
                <el-option
                  v-for="item in failStrategies"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="超时时间(分钟)">
              <el-input-number v-model="temp.executorTimeout" :min="0" :max="120" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="子任务">
              <el-select v-model="temp.childJobId" multiple placeholder="子任务" value-key="id">
                <el-option v-for="item in jobIdList" :key="item.id" :label="item.jobDesc" :value="item" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" />
          <el-col :span="12">
            <el-form-item label="失败重试次数">
              <el-input-number v-model="temp.executorFailRetryCount" :min="0" :max="20" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-show="false" label="写入模式">
              <el-select v-model="temp.writeMode" value-key="id">
                <el-option v-for="item in writeModeList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="数据库类型" prop="datasourceType">
              <el-select v-model="temp.datasourceType" clearable placeholder="数据库类型"
                @change="handleDataSouceTypeChange(temp.datasourceType, 'Manual')">
                <el-option v-for="item in datasourceTypes" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据库源：" prop="datasourceId">
              <el-select :disabled="disableFlag" v-model="temp.datasourceId" filterable style="width: 200px">
                <el-option v-for="item in rDsList" :key="item.id" :label="item.datasourceName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="上次执行时间" prop="lastTriggerTime">
              <el-date-picker v-model="temp.lastTriggerTime" type="datetime"
                placeholder="配置后可在脚本中通过${lastTriggerTime}使用" format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%"
                :picker-options="{ disabledDate: disableDate }" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报警邮件">
              <el-input v-model="temp.alarmEmail" placeholder="请输入报警邮件，多个用逗号分隔" />
            </el-form-item>
          </el-col>
          <el-col :span="12" />
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="执行顺序">
              <el-input-number
                v-model="temp.sort"
                :min="1"
                :max="10000"
                :step="1"
                precision="0"
              />
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <sql-editor v-if="temp.glueType === 'BEAN'" ref="sqlEditor" v-model="jobSql" @changeTextarea="handleSQLChange" />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          关闭
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import * as executor from '@/api/datax/datax-executor'
import * as job from '@/api/datax/datax-job-info'
import * as sqlJob from '@/api/dbms/dbms-job-info'
import * as sqlGroup from '@/api/dbms/dbms-job-group'
import waves from '@/directive/waves' // waves directive
import Cron from '@/components/Cron'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import SqlEditor from '@/components/SqlEditor'
import * as datasourceApi from '@/api/datax/datax-jdbcDatasource'
import * as jobProjectApi from '@/api/datax/datax-job-project'
import { list as jdbcDsList } from '@/api/datax/datax-jdbcDatasource'
import {encodeSqlToEncryptedBase64} from "@/utils/sqlHandle";
export default {
  mixins: [tableFullHeight],
  name: 'SqlTaskList',
  components: { Pagination, SqlEditor, Cron },
  directives: { waves },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'gray',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },
  data() {
    const validateIncParam = (rule, value, callback) => {
      if (!value) {
        callback(new Error('Increment parameters is required'))
      }
      callback()
    }
    const validatePartitionParam = (rule, value, callback) => {
      if (!this.partitionField) {
        callback(new Error('Partition parameters is required'))
      }
      callback()
    }
    return {
      disableFlag: false,
      jdbcDsQuery: {
        current: 1,
        size: 200,
        datasource: '',
        ascs: 'datasource_name'
      },
      groupList: [],
      rDsList: [],
      datasourceList: [],
      projectIds: '',
      list: null,
      listLoading: true,
      total: 0,
      listQuery: {
        category:null,
        current: 1,
        size: 10,
        jobGroup: 0,
        projectIds: '',
        triggerStatus: -1,
        jobDesc: '',
        glueType: 'BEAN',
        datasourceType: '',
        groupId: this.$route.query.groupId ? Number(this.$route.query.groupId) : undefined,
        lastProcessTime: ''
      },
      showCronBox: false,
      dialogPluginVisible: false,
      pluginData: [],
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: 'Edit',
        create: 'Create'
      },
      rules: {
        jobGroup: [{ required: true, message: 'jobGroup is required', trigger: 'change' }],
        executorRouteStrategy: [{ required: true, message: 'executorRouteStrategy is required', trigger: 'change' }],
        executorBlockStrategy: [{ required: true, message: 'executorBlockStrategy is required', trigger: 'change' }],
        glueType: [{ required: true, message: 'jobType is required', trigger: 'change' }],
        projectId: [{ required: true, message: 'projectId is required', trigger: 'change' }],
        jobDesc: [{ required: true, message: 'jobDesc is required', trigger: 'blur' }],
        jobProject: [{ required: true, message: 'jobProject is required', trigger: 'blur' }],
        jobCron: [{ required: true, message: 'jobCron is required', trigger: 'blur' }],
        incStartId: [{ trigger: 'blur', validator: validateIncParam }],
        replaceParam: [{ trigger: 'blur', validator: validateIncParam }],
        primaryKey: [{ trigger: 'blur', validator: validateIncParam }],
        incStartTime: [{ trigger: 'change', validator: validateIncParam }],
        partitionField: [{ trigger: 'blur', validator: validatePartitionParam }],
        datasourceId: [{ trigger: 'change', validator: validateIncParam }],
        readerTable: [{ trigger: 'blur', validator: validateIncParam }],
        tableTimeParam: [{ trigger: 'change', validator: validateIncParam }],
        datasourceType: [{ trigger: 'change', validator: validateIncParam }],
        failStrategy: [
          { required: true, message: 'failStrategy is required', trigger: 'change' }
        ]
      },
      temp: {
        id: undefined,
        jobGroup: '',
        jobCron: '',
        jobDesc: '',
        executorRouteStrategy: '',
        executorBlockStrategy: '',
        childJobId: '',
        executorFailRetryCount: '',
        alarmEmail: '',
        executorTimeout: '',
        userId: 0,
        jobConfigId: '',
        executorHandler: '',
        glueType: 'BEAN',
        datasourceType: '',
        jobSql: '',
        executorParam: '',
        replaceParam: '',
        replaceParamType: 'Timestamp',
        jvmParam: '',
        incStartTime: '',
        partitionInfo: '',
        incrementType: 0,
        incStartId: '',
        primaryKey: '',
        projectId: '',
        datasourceId: '',
        readerTable: '',
        tableTimeParam: '',
        lastTriggerTime: '',
        writeMode: 'UPDATE',
        lastHandleCode: '',
        category: "",
        groupId: undefined,
        sort: 1,
        failStrategy: ''
      },
      resetTemp() {
        this.temp = this.$options.data.call(this).temp
        this.jobSql = ''
        this.timeOffset = 0
        this.timeFormatType = 'yyyy-MM-dd'
        this.partitionField = ''
      },
      executorList: '',
      jobIdList: '',
      jobProjectList: '',
      dataSourceList: '',
      blockStrategies: [
        { value: 'SERIAL_EXECUTION', label: '单机串行' },
        { value: 'DISCARD_LATER', label: '丢弃后续调度' },
        { value: 'COVER_EARLY', label: '覆盖之前调度' }
      ],
      failStrategies: [
        { value: 'STOP_EXECUTION', label: '停止执行后续任务' },
        { value: 'CONTINUE_EXECUTION', label: '继续执行后续任务' }
      ],
      categoryOptions: [],
      sqlGroupList: [],
      writeModeList: [
        // { value: 'insert', label: 'insert' },
        // { value: 'replace', label: 'replace' },
        { value: 'UPDATE', label: 'UPDATE' }
        // { value: 'on duplicated', label: 'on duplicated' }
      ],
      glueTypes: [
        { value: 'BEAN', label: 'BEAN' }
      ],
      datasourceTypes: [
        { value: 'MYSQL', label: 'MYSQL' },
        { value: 'doris', label: 'DORIS' }
        // { value: 'ORACLE', label: 'ORACLE' },
        // { value: 'SQLSERVER', label: 'SQL SERVER' }
      ],
      incrementTypes: [
        { value: 0, label: '无' },
        { value: 1, label: '主键自增' },
        { value: 2, label: '时间自增' },
        { value: 3, label: 'HIVE分区' }
      ],
      triggerNextTimes: '',
      registerNode: [],
      jobSql: '',
      lastHandleCode: '',
      timeOffset: 0,
      timeFormatType: 'yyyy-MM-dd',
      partitionField: '',
      timeFormatTypes: [
        { value: 'yyyy-MM-dd', label: 'yyyy-MM-dd' },
        { value: 'yyyyMMdd', label: 'yyyyMMdd' },
        { value: 'yyyy/MM/dd', label: 'yyyy/MM/dd' }
      ],
      replaceFormatTypes: [
        { value: 'yyyy/MM/dd', label: 'yyyy/MM/dd' },
        { value: 'yyyy-MM-dd', label: 'yyyy-MM-dd' },
        { value: 'HH:mm:ss', label: 'HH:mm:ss' },
        { value: 'yyyy/MM/dd HH:mm:ss', label: 'yyyy/MM/dd HH:mm:ss' },
        { value: 'yyyy-MM-dd HH:mm:ss', label: 'yyyy-MM-dd HH:mm:ss' },
        { value: 'Timestamp', label: '时间戳' }
      ],
      statusList: [
        { value: 500, label: '失败' },
        { value: 502, label: '失败(超时)' },
        { value: 200, label: '成功' },
        { value: 0, label: '无' }
      ]
    }
  },
  async created() {
    await this.getDicts('dbms_category').then(response => {
      this.categoryOptions = response.data.map(item => {
        return {
          ...item,
          label: item.dictLabel,
          value: item.dictValue
        }
      });
    });
    this.fetchData()
    this.getExecutor()
    this.getJobIdList()
    this.getJobProject()
    this.getDataSourceList()
    this.getJdbcDs()
    this.getGroupList()
  },

  methods: {
    getGroupList() {
      sqlGroup.getDataList().then(result => {
        this.sqlGroupList = result.data
      })
    },
    disableDate(time) {
      return +time >= +new Date()
    },
    handleSQLChange(sql) {
      this.jobSql = sql
    },
    handleDataSouceTypeChange(type, way) {
      if (way === 'Manual') {
        this.temp.datasourceId = ''
      }
      this.jdbcDsQuery.datasource = type
      this.loading = true
      jdbcDsList(this.jdbcDsQuery).then(response => {
        const { records } = response.data
        this.rDsList = records
        this.loading = false
      })
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
        })
        .catch(_ => { })
    },
    getExecutor() {
      job.getExecutorList().then(response => {
        const { content } = response
        this.executorList = content
      })
    },
    getJobIdList() {
      sqlJob.getJobIdList().then(response => {
        const { content } = response
        this.jobIdList = content
      })
    },
    getJobProject() {
      jobProjectApi.getJobProjectList().then(response => {
        this.jobProjectList = response.data
      })
    },
    getDataSourceList() {
      datasourceApi.getDataSourceList().then(response => {
        this.dataSourceList = response
      })
    },
    fetchData() {

      // if (this.listQuery.groupId) {
      //   const path = this.$route.path
      //   this.$router.replace({path, query: {}})
      // }

      this.listLoading = true
      if (this.projectIds) {
        this.listQuery.projectIds = this.projectIds.toString()
      }

      sqlJob.getList(this.listQuery).then(response => {
        const { content } = response
        this.total = content.recordsTotal
        this.list = content.data.map(item => {
          const categoryOption = this.categoryOptions.find(categoryItem => categoryItem.value === item.category)
          return {
            ...item,
            categoryName: categoryOption ? categoryOption.label : ""
          }
        })
        this.listLoading = false
      })
    },
    handleCreate() {
      this.resetTemp()
      this.rDsList = []
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleReset() {
      this.listQuery = {
        category:null,
        current: 1,
        size: 10,
        jobGroup: 0,
        projectIds: '',
        triggerStatus: -1,
        jobDesc: '',
        glueType: 'BEAN',
        datasourceType: ''
      }
      this.fetchData()
    },
    createData() {
      this.$refs['dataForm'].validate(async (valid) => {
        if (valid) {
          if (this.temp.childJobId) {
            const auth = []
            for (const i in this.temp.childJobId) {
              auth.push(this.temp.childJobId[i].id)
            }
            this.temp.childJobId = auth.toString()
          }
          this.temp.jobSql = await encodeSqlToEncryptedBase64(this.jobSql);
          this.temp.executorHandler = this.temp.glueType === 'BEAN' ? 'executorJobHandler' : ''
          sqlJob.createJob(this.temp).then(() => {
            this.fetchData()
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Created Successfully',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handlerUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign({}, row) // copy obj
      this.jobSql = this.temp.jobSql
      const arrchildSet = []
      const arrJobIdList = []
      if (this.jobIdList) {
        for (const n in this.jobIdList) {
          if (this.jobIdList[n].id !== this.temp.id) {
            arrJobIdList.push(this.jobIdList[n])
          }
        }
        this.JobIdList = arrJobIdList
      }

      if (this.temp.childJobId) {
        const arrString = this.temp.childJobId.split(',')
        for (const i in arrString) {
          for (const n in this.jobIdList) {
            if (this.jobIdList[n].id === parseInt(arrString[i])) {
              arrchildSet.push(this.jobIdList[n])
            }
          }
        }
        this.temp.childJobId = arrchildSet
      }
      if (this.temp.partitionInfo) {
        const partition = this.temp.partitionInfo.split(',')
        this.partitionField = partition[0]
        this.timeOffset = partition[1]
        this.timeFormatType = partition[2]
      }

      this.handleDataSouceTypeChange(this.temp.datasourceType, 'Auto')

      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate(async (valid) => {
        if (valid) {
          if (this.temp.childJobId) {
            const auth = []
            for (const i in this.temp.childJobId) {
              auth.push(this.temp.childJobId[i].id)
            }
            this.temp.childJobId = auth.toString()
          }
          this.temp.jobSql = await encodeSqlToEncryptedBase64(this.jobSql);
          this.temp.executorHandler = this.temp.glueType === 'BEAN' ? 'executorJobHandler' : ''
          if (this.partitionField) this.temp.partitionInfo = this.partitionField + ',' + this.timeOffset + ',' + this.timeFormatType
          sqlJob.updateJob(this.temp).then(() => {
            this.fetchData()
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handlerDelete(row) {
      this.$confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        sqlJob.removeJob(row.id).then(response => {
          this.fetchData()
          this.$notify({
            title: 'Success',
            message: 'Delete Successfully',
            type: 'success',
            duration: 2000
          })
        })
      })

      // const index = this.list.indexOf(row)
    },
    handlerExecute(row) {
      this.$confirm('确定执行吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const param = {}
        param.jobId = row.id
        param.executorParam = row.executorParam
        job.triggerJob(param).then(response => {
          this.$notify({
            title: 'Success',
            message: 'Execute Successfully',
            type: 'success',
            duration: 2000
          })
        })
      })
    },
    // 查看日志
    handlerViewLog(row) {
      this.$router.push({ path: '/dataDev/dbms/sqlTaskLog', query: { jobId: row.id } })
    },
    handlerStart(row) {
      sqlJob.startJob(row.id).then(response => {
        this.$notify({
          title: 'Success',
          message: 'Start Successfully',
          type: 'success',
          duration: 2000
        })
      })
    },
    handlerStop(row) {
      sqlJob.stopJob(row.id).then(response => {
        this.$notify({
          title: 'Success',
          message: 'Start Successfully',
          type: 'success',
          duration: 2000
        })
      })
    },
    changeSwitch(row) {
      row.triggerStatus === 1 ? this.handlerStart(row) : this.handlerStop(row)
    },
    changeWfSwitch(row) {
      row.wfEnable === 1 ? this.handlerWfStart(row) : this.handlerWfStop(row)
    },
    handlerWfStart(row) {
      sqlJob.startWfJob(row.id).then(response => {
        this.$notify({
          title: 'Success',
          message: 'Start Successfully',
          type: 'success',
          duration: 2000
        })
      })
    },
    handlerWfStop(row) {
      sqlJob.stopWfJob(row.id).then(response => {
        this.$notify({
          title: 'Success',
          message: 'Close Successfully',
          type: 'success',
          duration: 2000
        })
      })
    },
    nextTriggerTime(row) {
      job.nextTriggerTime(row.jobCron).then(response => {
        const { content } = response
        this.triggerNextTimes = content.join('<br>')
      })
    },
    loadById(row) {
      executor.loadById(row.jobGroup).then(response => {
        this.registerNode = []
        const { content } = response
        this.registerNode.push(content)
      })
    },
    // 获取可用数据源
    getJdbcDs() {
      this.loading = true
      jdbcDsList(this.jdbcDsQuery).then(response => {
        const { records } = response.data
        this.datasourceList = records
        this.loading = false
      })
    },
    getDatasourceNameById(id) {
      const found = this.datasourceList.find(item => item.id === id);
      return found ? found.datasourceName : null;
    }
  }
}
</script>

<style scoped lang="scss">
.filter-container {
  display: flex;
  align-items: center;
}
.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
}

.el-dropdown+.el-dropdown {
  margin-left: 15px;
}
</style>
