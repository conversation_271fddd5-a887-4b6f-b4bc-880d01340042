<template>
  <div class="app-container">
    <div ref="queryFormRef" class="filter-container">
      <el-select v-model="listQuery.id" placeholder="分组名称" style="width: 200px;" class="filter-item" clearable filterable>
        <el-option v-for="item in sqlGroupList" :key="item.id" :label="item.jobDesc" :value="item.id" />
      </el-select>
      <el-input v-model="listQuery.code" placeholder="分组编码" style="width: 200px;" class="filter-item" />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="fetchData">
        搜索
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-edit"
        @click="handleCreate">
        添加
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-refresh"
                 @click="handleReset">
        重置
      </el-button>
    </div>
    <el-table  :height="tableHeight"
    ref="fullHeightTableRef" v-loading="listLoading" :data="list" element-loading-text="Loading" border fit highlight-current-row
      style="width: 100%" size="medium">
      <el-table-column align="center" label="ID">
        <template slot-scope="scope">{{ scope.row.id }}</template>
      </el-table-column>
      <el-table-column label="分组名称" align="center">
        <template slot-scope="scope">{{ scope.row.jobDesc }}</template>
      </el-table-column>
      <el-table-column label="分组编码" align="center">
        <template slot-scope="scope">{{ scope.row.code }}</template>
      </el-table-column>
      <el-table-column label="所属工作流" align="center">
        <template slot-scope="scope">{{ scope.row.workflowNames }}</template>
      </el-table-column>
      <el-table-column label="所属节点" align="center">
        <template slot-scope="scope">{{ scope.row.taskNames }}</template>
      </el-table-column>
      <el-table-column label="排序" align="center">
        <template slot-scope="scope">{{ scope.row.sort }}</template>
      </el-table-column>
      <el-table-column label="分组备注" align="center">
        <template slot-scope="scope">{{ scope.row.remark }}</template>
      </el-table-column>
      <el-table-column label="创建时间" align="center">
        <template slot-scope="scope">{{ scope.row.createTime }}</template>
      </el-table-column>
      <el-table-column label="更新时间" align="center">
        <template slot-scope="scope">{{ scope.row.updateTime }}</template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right">
        <template slot-scope="{row}">
          <!-- 操作 -->
          <el-dropdown trigger="click">
            <span class="el-dropdown-link">
              操作<i class="el-icon-arrow-down el-icon--right" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="handlerExecute(row)">执行一次</el-dropdown-item>
              <el-dropdown-item divided @click.native="handleViewTask(row)">查看节点</el-dropdown-item>
              <el-dropdown-item divided @click.native="handlerUpdate(row)">编辑</el-dropdown-item>
              <el-dropdown-item @click.native="handlerDelete(row)">删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageNum" :limit.sync="listQuery.pageSize"
      @pagination="fetchData" />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="1000px"
      :before-close="handleClose">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="110px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分组名称" prop="jobDesc">
              <el-input v-model="temp.jobDesc" size="medium" placeholder="请输入分组名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分组编码" prop="code">
              <el-input v-model="temp.code" size="medium" placeholder="请输入分组编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="排序" prop="sort">
              <el-input-number v-model="temp.sort" :min="1" :max="10000" label="排序" @change="handleChange"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="失败策略" prop="failStrategy">
              <el-select
                v-model="temp.failStrategy"
                placeholder="失败策略"
              >
                <el-option
                  v-for="item in failStrategies"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="分组备注">
              <el-input v-model="temp.remark" type="textarea" placeholder=""/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import * as sqlGroup from '@/api/dbms/dbms-job-group'
import waves from '@/directive/waves' // waves directive
import Cron from '@/components/Cron'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import SqlEditor from '@/components/SqlEditor'

export default {
  mixins: [tableFullHeight],
  name: 'SqlList',
  components: { Pagination, SqlEditor, Cron },
  directives: { waves },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'gray',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      list: null,
      listLoading: true,
      total: 0,
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        jobDesc: '',
        code: ''
      },
      dialogPluginVisible: false,
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: 'Edit',
        create: 'Create'
      },
      rules: {
        jobDesc: [{ required: true, message: 'this is required', trigger: 'blur' }],
        code: [{ required: true, message: 'this is required', trigger: 'blur' }],
        failStrategy: [
          { required: true, message: 'failStrategy is required', trigger: 'change' }
        ]
      },
      temp: {
        id: undefined,
        jobDesc: '',
        sort: 1,
        code: '',
        failStrategy: '',
        remark: ''
      },
      resetTemp() {
        this.temp = this.$options.data.call(this).temp
        this.jobDesc = ''
        this.code = ''
      },
      sqlGroupList: [],
      failStrategies: [
        { value: 'STOP_EXECUTION', label: '停止执行后续任务' },
        { value: 'CONTINUE_EXECUTION', label: '继续执行后续任务' }
      ]
    }
  },
  async created() {
    this.fetchData()
    this.getGroupList()
  },

  methods: {
    handleChange(value) {
      this.temp.sort = value
    },
    getGroupList() {
      sqlGroup.getDataList().then(result => {
        this.sqlGroupList = result.data
      })
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
        })
        .catch(_ => { })
    },
    fetchData() {
      this.listLoading = true
      sqlGroup.getList(this.listQuery).then(response => {
        if (response.code === 200) {
          this.total = response.total
          this.list = response.rows
        } else {
          this.$message.error(response.msg)
        }
        this.listLoading = false
      })
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleReset() {
      this.listQuery = {
        pageNum: 1,
        pageSize: 10,
        jobDesc: '',
        code: ''
      }
      this.fetchData()
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          sqlGroup.createGroup(this.temp).then(result => {
            this.fetchData()
            this.dialogFormVisible = false
            if (result.code === 200) {
              this.$notify({
                title: 'Success',
                message: 'Created Successfully',
                type: 'success',
                duration: 2000
              })
            } else {
              this.$notify({
                title: 'Error',
                message: 'Created Failed',
                type: 'error',
                duration: 2000
              })
            }
          })
        }
      })
    },
    handlerExecute(row) {
      this.$confirm('确定执行吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const param = {}
        param.jobId = row.id
        param.executorParam = row.executorParam
        sqlGroup.triggerJob(param).then(response => {
          if (response.code === 200) {
            this.$notify({
              title: 'Success',
              message: 'Execute Successfully',
              type: 'success',
              duration: 2000
            })
          } else if (response.code === 500) {
            this.$notify({
              title: 'Error',
              message: 'Execute Failed',
              type: 'error',
              duration: 2000
            })
          }
        })
      })
    },
    // 查询任务明细
    handleViewTask(row) {
      this.$router.push({
        path: '/dataDev/dbms/sqlTaskList',
        query: { groupId: row.id }
      })
    },
    handlerUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          sqlGroup.updateGroup(this.temp).then(result => {
            this.fetchData()
            this.dialogFormVisible = false
            if (result.code === 200) {
              this.$notify({
                title: 'Success',
                message: 'Created Successfully',
                type: 'success',
                duration: 2000
              })
            } else {
              this.$notify({
                title: 'Error',
                message: 'Created Failed',
                type: 'error',
                duration: 2000
              })
            }
          })
        }
      })
    },
    handlerDelete(row) {
      this.$confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const idList = []
        idList.push(row.id)
        sqlGroup.removeGroup({ idList: row.id }).then(response => {
          if (response.code === 200) {
            this.fetchData()
            this.$notify({
              title: 'Success',
              message: 'Delete Successfully',
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: 'Error',
              message: 'Delete Failed',
              type: 'error',
              duration: 2000
            })
          }
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.filter-container {
  display: flex;
  align-items: center;
}
.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
}

.el-dropdown+.el-dropdown {
  margin-left: 15px;
}
</style>
