<template>
  <div class="app-container">
    <div ref="queryFormRef" class="filter-container">
      <el-select v-model="listQuery.jobType" placeholder="任务类型" style="width: 200px;" class="filter-item" clearable
        filterable>
        <el-option v-for="dict in options.versionTypeOptions" :key="dict.dictValue" :label="dict.dictLabel"
          :value="dict.dictValue" />
      </el-select>
      <el-select v-model="listQuery.sqlJobId" placeholder="任务明细" style="width: 200px;" class="filter-item" clearable
        filterable>
        <el-option v-for="item in jobList" :key="item.id" :label="item.jobDesc" :value="item.id" />
      </el-select>
      <el-input v-model="listQuery.version" placeholder="版本号" style="width: 200px;" class="filter-item" />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="fetchData">
        搜索
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-refresh"
        @click="handleReset">
        重置
      </el-button>
    </div>
    <el-table :height="tableHeight" ref="fullHeightTableRef" v-loading="listLoading" :data="list"
      element-loading-text="Loading" border fit highlight-current-row style="width: 100%" size="medium">
      <el-table-column label="序号" align="center" width="95">
        <template slot-scope="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>
      <el-table-column label="类型" align="center">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.version_type" :value="scope.row.jobType" />
        </template>
      </el-table-column>
      <el-table-column label="任务明细" align="center">
        <template slot-scope="scope">{{ scope.row.sqlJobName }}</template>
      </el-table-column>
      <el-table-column label="当前版本" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.latestVersion === 1" class="tag-label">当前版本</span>
          <span v-else-if="scope.row.latestVersion === 0" class="history-label">历史版本</span>
        </template>
      </el-table-column>
      <el-table-column label="版本号" align="center">
        <template slot-scope="scope">{{ scope.row.version }}</template>
      </el-table-column>
      <el-table-column label="上一版本" align="center">
        <template slot-scope="scope">{{ scope.row.previousVersion }}</template>
      </el-table-column>
      <el-table-column label="修改人" align="center">
        <template slot-scope="scope">{{ scope.row.modifyBy }}</template>
      </el-table-column>
      <el-table-column label="修改时间" align="center">
        <template slot-scope="scope">{{ scope.row.modifyTime }}</template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right">
        <template slot-scope="{row}">
          <!-- 操作 -->
          <el-dropdown trigger="click">
            <span class="el-dropdown-link">
              操作<i class="el-icon-arrow-down el-icon--right" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item divided @click.native="handleViewSql(row)">查看</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageNum" :limit.sync="listQuery.pageSize"
      @pagination="fetchData" />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="1000px"
      :before-close="handleClose">
      <sql-editor ref="sqlEditor" v-model="temp.jobSql" />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          关闭
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import * as jobHistory from '@/api/dbms/dbms-job-history'
import * as job from '@/api/dbms/dbms-job-info'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import SqlEditor from '@/components/SqlEditor/index.vue'

export default {
  mixins: [tableFullHeight],
  name: 'httpJobSummary',
  components: { SqlEditor, Pagination },
  directives: { waves },
  dicts: ['hts_category', 'version_type'],
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'gray',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      list: null,
      listLoading: true,
      total: 0,
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        sqlJobId: undefined,
        version: undefined,
        jobType: undefined
      },
      jobList: [],
      temp: {},
      dialogStatus: '',
      dialogFormVisible: false,
      textMap: {
        info: 'SQL信息'
      },
      options: {
        versionTypeOptions: []
      }
    }
  },
  async created() {
    this.fetchData()
    this.getJobList()
    this.getDicts("version_type").then(response => {
      this.options.versionTypeOptions = response.data;
    });
  },

  methods: {
    fetchData() {
      this.listLoading = true
      jobHistory.getPageList(this.listQuery).then(response => {
        if (response.code === 200) {
          this.total = response.total
          this.list = response.rows
        } else {
          this.$message.error(response.msg)
        }
        this.listLoading = false
      })
    },
    getJobList() {
      job.getJobIdList().then(response => {
        if (response.code === 200) {
          this.jobList = response.content
        }
      })
    },
    handleReset() {
      this.listQuery = {
        pageNum: 1,
        pageSize: 10,
        sqlJobId: undefined,
        version: undefined
      }
      this.fetchData()
    },
    // 查询任务明细
    handleViewSql(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'info'
      this.dialogFormVisible = true
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
        })
        .catch(_ => {
        })
    }
  }
}
</script>

<style scoped lang="scss">
.filter-container {
  display: flex;
  align-items: center;
}

.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
}

.el-dropdown+.el-dropdown {
  margin-left: 15px;
}

.tag-label {
  display: inline-block;
  background-color: #8bdd8b;
  color: white;
  text-align: center;
  min-width: 100px;
  border-radius: 50px;
  font-weight: bold;
}

.history-label {
  display: inline-block;
  background-color: #d1dad1;
  color: white;
  text-align: center;
  min-width: 100px;
  border-radius: 50px;
  font-weight: bold;
}
</style>
