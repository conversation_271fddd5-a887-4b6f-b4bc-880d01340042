<template>
  <div class="permission-container">
    <el-dialog title="数据权限配置" append-to-body :visible.sync="permissionConfigDialogShow" width="80%">
      <div style="margin-bottom: 10px">
        <el-button type="primary" @click="openModal('add')">新增</el-button>
        <el-button @click="openModal('edit')" :disabled="!permissionRecordSelectedData">修改</el-button>
        <el-button @click="openModal('delete')" :disabled="!permissionRecordSelectedData">删除</el-button>
        <el-button @click="handleExport">导出</el-button>
        <el-button @click="handleImport">导入</el-button>
      </div>
      <!-- <el-form :model="queryFormData" inline label-width="80px">
        <el-form-item label="角色名称">
          <el-select v-model="queryFormData.roleCode" remote reserve-keyword placeholder="请输入角色名称"
            :remote-method="getRoleList" filterable>
            <el-option v-for="option in roleRecord" :key="option.roleKey" :label="option.roleName"
              :value="option.roleKey">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="queryPermissionList">查询</el-button>
        </el-form-item>
      </el-form> -->
      <el-table v-loading="permissionRecordLoading" :data="permissionRecord" highlight-current-row
        @current-change="handleCurrentChange">
        <!-- <el-table-column label="分类" align="center" prop="classify" width="100px" /> -->
        <el-table-column label="表名" align="center" prop="tableName" />
        <el-table-column label="表编码" align="center" prop="tableCode" />
        <el-table-column label="创建时间" width="160px" align="center" prop="createTime" />
        <el-table-column label="权限" align="center" prop="permissionType">
          <template slot-scope="scope">
            {{ getPermissionLabel(scope.row.permissionType) }}
          </template>
        </el-table-column>
        <el-table-column label="按钮权限" align="center" prop="buttonPermissions">
          <template slot-scope="scope">
            {{ getButtonLabel(scope.row.buttonPermissions) }}
          </template>
        </el-table-column>
      </el-table>
      <el-pagination @size-change="handlePageSizeChange" @current-change="handlePageNumChange" :current-page="pageNum"
        :page-sizes="[10, 20, 30, 40]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </el-dialog>

    <!-- 新增/修改 弹窗 -->
    <el-dialog :title="modalType == 'add' ? '新增权限' : '修改权限'" append-to-body :visible.sync="rolePermissionDialogVisible"
      width="500px">
      <el-form :model="modalFormData" ref="modalFormRef" size="small" inline :rules="modalFormRules" label-width="85px">
        <el-form-item label="报表" prop="tableCode">
          <el-select v-model="modalFormData.tableCode" filterable>
            <el-option v-for="option in tableRecord" :key="option.id" :label="option.label" :value="option.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="权限范围" prop="permissionType">
          <el-select v-model="modalFormData.permissionType" style="width: 100%">
            <el-option v-for="option in dataPermissionRange" :key="option.value" :label="option.label"
              :value="option.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="数据权限" v-show="modalFormData.permissionType == 2">
          <el-checkbox v-model="deptExpand" @change="handleCheckedTreeExpand($event, 'dept')">展开/折叠</el-checkbox>
          <el-checkbox v-model="deptNodeAll" @change="handleCheckedTreeNodeAll($event, 'dept')">全选/全不选</el-checkbox>
          <el-checkbox v-model="dataScopeForm.deptCheckStrictly" @change="handleCheckedTreeConnect($event, 'dept')">
            父子联动
          </el-checkbox>

          <el-tree v-if="rolePermissionDialogVisible" class="tree-border" :data="deptOptions" show-checkbox
            default-expand-all ref="dept" node-key="id" :check-strictly="!dataScopeForm.deptCheckStrictly"
            empty-text="加载中，请稍后" :props="defaultProps"
            :default-checked-keys="modalFormData.dept != '' ? modalFormData.dept.split(',') : []">
          </el-tree>
        </el-form-item>

        <el-form-item label="按钮权限" prop="permissionButtons">
          <el-checkbox-group v-model="modalFormData.permissionButtons" style="width:350px">
            <el-checkbox v-for="(item, index) in permissionTypeList" :key="index" :label="item.label">
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitModal">确 定</el-button>
        <el-button @click="cancelModal">取 消</el-button>
      </div>
    </el-dialog>



    <!-- 导入对话框 -->
    <el-dialog :title="uploadOption.title" :visible.sync="uploadOption.open" width="400px">
      <el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="uploadOption.headers"
        :action="uploadOption.url + '?updateSupport=' + uploadOption.updateSupport" :disabled="uploadOption.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" style="color:red" slot="tip">提示：仅允许导入“xls”或“xlsx”格式文件！</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="uploadOption.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getUnassignedReportsForRole } from "@/api/codeDev/formDesign/formDesign";
import * as Api from "@/api/codeDev/formPermissionConfig/formPermissionConfig.js";
// import { listRole } from "@/api/system/role.js";
import * as userApi from "@/api/system/user";
import { getToken } from "@/utils/auth";
export default {
  dicts: ["field_display_or_hide"],
  props: {
    roleOption: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      permissionConfigDialogShow: false,
      pageSize: 10,
      pageNum: 1,
      total: 0,
      permissionRecord: [],
      permissionRecordLoading: false,
      permissionRecordSelectedData: null,
      // 弹窗
      rolePermissionDialogVisible: false,
      modalType: "add",
      modalFormData: {
        tableCode: "",
        permissionType: "",
        dept: "",
        permissionButtons: [],
      },
      // 报表列表
      tableRecord: [],

      permissionTypeList: [
        { label: '新增', value: 'added' },
        { label: '批量删除', value: 'batchDelete' },
        { label: '导出', value: 'export' },
        { label: '导入', value: 'import' },
        { label: '复制', value: 'copy' },
        { label: '编辑', value: 'edit' },
        { label: '删除', value: 'delete' }
      ],
      // 数据范围选项
      dataPermissionRange: [
        {
          value: "1",
          label: "全部数据权限",
        },
        {
          value: "2",
          label: "自定数据权限",
        },
        {
          value: "3",
          label: "本部门数据权限",
        },
        {
          value: "4",
          label: "本部门及以下数据权限",
        },
        {
          value: "5",
          label: "仅本人数据权限",
        },
      ],
      // 部门下拉树
      deptExpand: true,
      deptNodeAll: false,
      deptOptions: [],
      dataScopeForm: {
        tableName: "",
        tableCode: "",
        dataScope: undefined,
      },
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 其他数据属性
      modalFormRules: {
        tableCode: [
          { required: true, message: '请选择报表', trigger: 'change' }
        ],
        permissionType: [
          { required: true, message: '请选择权限范围', trigger: 'change' }
        ],
        // 其他字段的验证规则
      },
      // 导入参数
      uploadOption: {
        // 是否显示弹出层（导入）
        open: false,
        // 弹出层标题（导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/codeDev/permissionConfig/importData"
      },
      queryFormData: {
        tableCode: null
      }
    };
  },
  watch: {
    permissionConfigDialogShow(value) {
      if (!!value) {
        this.getPermissionList();
        this.getDeptTree();
      }
    }
  },
  methods: {
    getButtonLabel(permissions) {
      if (!permissions) return '';
      const permissionList = permissions.split(',');
      const labels = permissionList.map(permission => {
        const button = this.permissionTypeList.find(item => item.value == permission);
        return button ? button.label : permission;
      });
      return labels.join(', ');
    },
    getPermissionLabel(permissionType) {
      const button = this.dataPermissionRange.find(item => item.value == permissionType);
      return button ? button.label : permissionType;
    },
    handlePageNumChange(val) {
      this.pageNum = val
      this.getPermissionList()
    },
    handlePageSizeChange(val) {
      this.pageNum = 1
      this.pageSize = val
      this.getPermissionList()
    },
    queryPermissionList() {
      this.pageNum = 1
      this.getPermissionList()
    },
    /**
     * 权限列表
     */
    async getPermissionList() {
      const params = {
        roleCode: this.roleOption.roleKey,
        // tableCode: this.formDesignData.tableCode,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      };
      this.permissionRecordLoading = true;
      const res = await Api.listPermissionConfig(params).finally(() => {
        this.permissionRecordLoading = false;
      });
      if (res.code == 200) {
        this.permissionRecord = res.rows.map((cur) => {
          cur.permissionTypeName = this.permissionTypeList.find((item) => {
            return item.value == cur.permissionType;
          })?.label;
          return cur;
        });
        this.total = res.total;
      }
    },

    // 行选中
    handleCurrentChange(selectedData) {
      this.permissionRecordSelectedData = selectedData;
    },
    async getUnassignedReportsForRole() {
      const res = await getUnassignedReportsForRole(this.roleOption.roleKey)
      if (res.code === 200) {
        this.tableRecord = res.rows.map(item => {
          return {
            ...item,
            label: item.tableName,
            value: item.tableCode
          }
        })
      }
    },
    /** 弹窗  */
    openModal(type) {
      this.modalType = type;
      switch (type) {
        case "add":
          this.rolePermissionDialogVisible = true;
          this.modalFormData.permissionButtons = []
          this.getUnassignedReportsForRole()
          break;
        case "edit":
          this.rolePermissionDialogVisible = true;
          this.getPermissionDetail();
          break;
        case "delete":
          Api.delPermissionConfig(this.permissionRecordSelectedData.id).then(
            (res) => {
              if (res.code == 200) {
                this.getPermissionList();
              }
            }
          );
          break;
      }
    },
    cancelModal() {
      this.modalFormData.dept = "";
      for (let key in this.modalFormData) {
        this.modalFormData[key] = "";
      }
      this.rolePermissionDialogVisible = false;
    },

    /** 部门下拉树 */
    // 树权限（展开/折叠）
    handleCheckedTreeExpand(value, type) {
      if (type === "menu") {
        let treeList = this.menuOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.menu.store.nodesMap[treeList[i].id].expanded = value;
        }
      } else if (type === "dept") {
        let treeList = this.deptOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.dept.store.nodesMap[treeList[i].id].expanded = value;
        }
      }
    },
    // 树权限（全选/全不选）
    handleCheckedTreeNodeAll(value, type) {
      if (type === "menu") {
        this.$refs.menu.setCheckedNodes(value ? this.menuOptions : []);
      } else if (type === "dept") {
        this.$refs.dept.setCheckedNodes(value ? this.deptOptions : []);
      }
    },
    // 树权限（父子联动）
    handleCheckedTreeConnect(value, type) {
      if (type == "menu") {
        this.dataScopeForm.menuCheckStrictly = value ? true : false;
      } else if (type == "dept") {
        this.dataScopeForm.deptCheckStrictly = value ? true : false;
      }
    },
    // 所有部门节点数据
    getDeptAllCheckedKeys() {
      // 目前被选中的部门节点
      let checkedKeys = this.$refs.dept.getCheckedKeys();
      // 半选中的部门节点
      let halfCheckedKeys = this.$refs.dept.getHalfCheckedKeys();
      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
      return checkedKeys;
    },
    /**
     * 编辑
     */
    getPermissionDetail() {
      Api.getPermissionConfig(this.permissionRecordSelectedData.id).then(
        (res) => {
          if (res.code == 200) {
            this.modalFormData.permissionButtons = []
            let arr = res.data.buttonPermissions.split(',')
            if (arr.includes('all')) {
              this.modalFormData.permissionButtons = this.permissionTypeList.map(item => item.label)
            } else {
              this.permissionTypeList.forEach(item => {
                arr.forEach(ite => {
                  if (item.value === ite) {
                    this.modalFormData.permissionButtons.push(item.label)
                  }
                })
              })
            }

            for (let key in res.data) {
              if (key == "dept") {
                this.modalFormData[key] = res.data[key];
              } else {
                this.modalFormData[key] = res.data[key];
              }
            }
          }
        }
      );
    },

    async submitModal() {
      const valid = await this.$refs.modalFormRef.validate().catch(() => false)
      if (!valid) return
      let params;
      const arr = [];
      this.modalFormData.permissionButtons.forEach(item => {
        const button = this.permissionTypeList.find(ite => ite.label === item);
        if (button) {
          arr.push(button.value);
        }
      });
      const tableName = this.tableRecord.find(item => item.value == this.modalFormData.tableCode)?.label

      switch (this.modalType) {
        case "add":
          params = {
            tableName: tableName || null,
            tableCode: this.modalFormData.tableCode,
            permissionType: this.modalFormData.permissionType,
            roleCode: this.roleOption.roleKey,
            dept: this.getDeptAllCheckedKeys().join(","),
            buttonPermissions: arr.join(',')
          };
          Api.addPermissionConfig(params).then((res) => {
            if (res.code == 200) {
              this.cancelModal();
              this.getPermissionList();
            }
          });
          break;
        case "edit":
          this.modalFormData.dept = this.getDeptAllCheckedKeys().join(",");
          params = this.modalFormData;
          params.tableName = tableName || null
          params.buttonPermissions = arr.join(',');
          Api.updatePermissionConfig(params).then((res) => {
            if (res.code == 200) {
              this.cancelModal();
              this.getPermissionList();
            }
          });
          break;
      }
    },
    /**
     * 查询部门下拉树结构
     */
    getDeptTree() {
      userApi.deptTreeSelect().then((response) => {
        this.deptOptions = response.data;
      });
    },

    /**
     * 导出按钮操作
     */
    handleExport() {
      this.download('/codeDev/permissionConfig/export/', {
        roleCode: this.roleOption.roleKey
      }, `权限配置.xlsx`)
    },
    handleImport() {
      this.uploadOption.title = "配置导入";
      this.uploadOption.open = true;
    },
    // 文件上传中处理
    handleFileUploadProgress() {
      this.uploadOption.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response) {
      this.uploadOption.open = false;
      this.uploadOption.isUploading = false;
      this.$refs.uploadRef.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getPermissionList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.uploadRef.submit();
    },
  },
};
</script>

<style scoped lang="scss">
.permission-container {
  position: relative;
}
</style>
