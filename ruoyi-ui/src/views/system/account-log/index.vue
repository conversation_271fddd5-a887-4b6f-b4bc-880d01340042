<template>
  <div class="app-container">
    <!-- 角色管理 -->
    <el-form :model="queryParams" ref="queryFormRef" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="用户名" prop="accountNo">
        <el-input v-model="queryParams.accountNo" placeholder="用户名" clearable style="width: 240px"
                  @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="推送类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="推送类型" clearable style="width: 240px">
          <el-option v-for="dict in dict.type.iam_action_flag" :key="dict.value" :label="dict.label"
                     :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="账号推送状态" prop="triggerStatus">
        <el-select v-model="queryParams.triggerStatus" placeholder="角色状态" clearable filterable style="width: 240px">
          <el-option v-for="dict in dict.type.iam_account_status" :key="dict.value" :label="dict.label"
                     :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="数据推送方式" prop="triggerWay">
        <el-select v-model="queryParams.triggerWay" placeholder="数据推送方式" clearable filterable
                   style="width: 240px"
        >
          <el-option v-for="dict in dict.type.iam_data_trigger_way" :key="dict.value" :label="dict.label"
                     :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="推送时间" prop="triggerWay">
        <el-date-picker v-model="dateRange" style="width: 400px" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" size="small"
        ></el-date-picker>
      </el-form-item>
      <!--      <el-form-item label="推送时间">-->
      <!--        <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"-->
      <!--                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"-->
      <!--        ></el-date-picker>-->
      <!--      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete">
          删除
        </el-button>
      </el-form-item>
    </el-form>

    <el-table ref="fullHeightTableRef" :height="tableHeight" v-loading="loading" :data="tableList"
              @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="序号" width="55" align="center" key="id" prop="id">
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="用户姓名" align="center" prop="userName" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.userName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户名" align="center" prop="accountNo" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.accountNo }}</span>
        </template>
      </el-table-column>
      <el-table-column label="邮箱" align="center" prop="email" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.email }}</span>
        </template>
      </el-table-column>
      <el-table-column label="手机号" align="center" prop="mobile" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.mobile }}</span>
        </template>
      </el-table-column>
      <el-table-column label="组织信息" align="center" prop="org" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.org }}</span>
        </template>
      </el-table-column>
      <el-table-column label="推送类型" align="center" prop="type" width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.iam_action_flag" :value="scope.row.type"/>
        </template>
      </el-table-column>
      <el-table-column label="推送时间" align="center" prop="time" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.triggerTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="账号推送状态" align="center" prop="status" width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.iam_account_status" :value="scope.row.triggerStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="数据推送方式" align="center" prop="status" width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.iam_data_trigger_way" :value="scope.row.triggerWay"/>
        </template>
      </el-table-column>
      <el-table-column label="失败原因" align="center" prop="reason" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.reason }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right" width="180">
        <template slot-scope="scope">
          <el-dropdown trigger="click">
            <span class="el-dropdown-link">
              操作<i class="el-icon-arrow-down el-icon--right"/>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="handleView(scope.row)">查看</el-dropdown-item>
              <el-dropdown-item @click.native="handleRetry(scope.row)">重试</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList"
    />

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" :before-close="handleClose" :fullscreen="true">
      <el-form ref="dataForm" :rules="rules" :model="form" label-position="left">

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="accountNo" label-width="150px">
              <el-input v-model="form.accountNo" size="medium" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户姓名" prop="userName" label-width="150px">
              <el-input v-model="form.userName" size="medium" disabled/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="手机号码" prop="mobile" label-width="150px">
              <el-input v-model="form.mobile" size="medium" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email" label-width="150px">
              <el-input v-model="form.email" size="medium" disabled/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="性别" prop="gender" label-width="150px">
              <el-select v-model="form.gender" class="filter-item" style="width: 80%;" filterable disabled>
                <el-option v-for="dict in dict.type.sys_user_sex" :key="dict.value" :label="dict.label"
                           :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据推送方式" prop="triggerWay" label-width="150px">
              <el-select v-model="form.triggerWay" class="filter-item" style="width: 80%;" filterable disabled>
                <el-option v-for="dict in dict.type.iam_data_trigger_way" :key="dict.value" :label="dict.label"
                           :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="组织信息" prop="org" label-width="150px">
              <el-input v-model="form.org" size="medium" disabled/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="认证信息" prop="authInfo" label-width="150px">
              <el-input v-model="form.authInfo" size="medium" disabled/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="推送时间" prop="triggerTime" label-width="150px">
              <el-input v-model="form.triggerTime" size="medium" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="推送类型" prop="type" label-width="150px">
              <el-select v-model="form.type" class="filter-item" style="width: 80%;" filterable disabled>
                <el-option v-for="dict in dict.type.iam_action_flag" :key="dict.value" :label="dict.label"
                           :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="推送状态" prop="triggerStatus" label-width="150px">
              <el-select v-model="form.triggerStatus" class="filter-item" style="width: 80%;" filterable disabled>
                <el-option v-for="dict in dict.type.iam_account_status" :key="dict.value" :label="dict.label"
                           :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="失败原因" prop="reason" label-width="150px">
              <el-input v-model="form.reason" size="medium" disabled/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div style="margin-bottom: 10px;">
              <h3 style="font-weight: bold; color: black;">原始报文</h3> <!-- 加粗和纯黑色 -->
            </div>
            <el-scrollbar style="height: 500px;">
              <json-editor ref="jsonEditor" v-model="parsedOriginJson"/>
            </el-scrollbar>
          </el-col>
          <el-col :span="12">
            <div style="margin-bottom: 10px;">
              <h3 style="font-weight: bold; color: black;">解密后报文</h3> <!-- 加粗和纯黑色 -->
            </div>
            <el-scrollbar style="height: 500px;">
              <json-editor ref="jsonEditor" v-model="parsedDecryptJson"/>
            </el-scrollbar>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import tableFullHeight from '@/utils/tableFullHeight'
import * as userLog from '@/api/system/userAccountLog'
import JsonEditor from '@/components/JsonEditor/index.vue'

export default {
  name: 'UserAccountLog',
  components: { JsonEditor },
  mixins: [tableFullHeight],
  dicts: ['iam_action_flag', 'iam_data_trigger_way', 'iam_account_status', 'sys_user_sex'],
  data() {
    return {
      // 显示搜索条件
      showSearch: true,
      // 弹出层标题
      title: '用户账号推送日志',
      // 日期范围
      dateRange: [],
      // 选中数组
      ids: [],
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 是否显示弹出层
      open: false,
      // 非多个禁用
      multiple: true,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountNo: undefined,
        type: undefined,
        startTime: undefined,
        endTime: undefined,
        triggerStatus: undefined,
        triggerWay: undefined
      },
      // 表单参数
      form: {
        id: undefined,
        userName: '',
        accountNo: '',
        gender: '',
        email: '',
        mobile: '',
        org: '',
        authInfo: '',
        originMessage: null,
        decryptMessage: null,
        type: '',
        triggerTime: undefined,
        reason: '',
        triggerStatus: '',
        triggerWay: ''
      },
      tableList: [],
      // 表单校验
      rules: {
        roleName: [
          { required: true, message: '角色名称不能为空', trigger: 'blur' }
        ],
        roleKey: [
          { required: true, message: '权限字符不能为空', trigger: 'blur' }
        ],
        roleSort: [
          { required: true, message: '角色顺序不能为空', trigger: 'blur' }
        ]
      },
      triggerStatusList: [
        { key: 'success', value: 'success', label: '成功' },
        { key: 'fail', value: 'fail', label: '失败' }
      ],
      triggerWayList: [
        { key: 'API', value: 'API', label: '接口推送' },
        { key: 'MANUAL', value: 'MANUAL', label: '手动重试' }
      ]
    }
  },
  computed: {
    parsedOriginJson: {
      get() {
        try {
          if (typeof this.form.originMessage === 'string') {
            return JSON.parse(this.form.originMessage)
          }
          return this.form.originMessage
        } catch (error) {
          console.error('JSON 解析错误:', error)
          return {} //或者设置一个默认值
        }
      },
      set(value) {
        // this.temp.requestResult = JSON.stringify(value);
      }
    },
    parsedDecryptJson: {
      get() {
        try {
          if (typeof this.form.decryptMessage === 'string') {
            return JSON.parse(this.form.decryptMessage)
          }
          return this.form.decryptMessage
        } catch (error) {
          console.error('JSON 解析错误:', error)
          return {} //或者设置一个默认值
        }
      },
      set(value) {
        // this.temp.requestResult = JSON.stringify(value);
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
        })
        .catch(_ => {
        })
    },
    getList() {
      const param = Object.assign({}, this.queryParams)
      if (this.dateRange) {
        param.startTime = this.dateRange[0]
        param.endTime = this.dateRange[1]
      }
      this.loading = true
      userLog.list(param).then(result => {
        if (result.code === 200) {
          this.tableList = result.rows
          this.total = result.total
        }
        this.loading = false
      })
    },
    handleQuery() {
      this.getList()
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        accountNo: undefined,
        type: undefined,
        startTime: undefined,
        endTime: undefined,
        triggerStatus: undefined,
        triggerWay: undefined
      }
      this.getList()
    },
    resetForm() {
      this.form = this.$options.data.call(this).form
    },
    handleView(row) {
      this.resetForm()
      this.form = Object.assign({}, row)
      this.open = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleDelete() {
      userLog.delLog(this.ids).then(result => {
        this.getList()
        this.$notify({
          title: 'Success',
          message: 'Delete Successfully',
          type: 'success',
          duration: 2000
        })
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    async handleRetry(row) {
      const result = await userLog.sync(row.id).finally(() => {
        this.getList()
      })
      if (result.code === 200) {
        this.$message.success('重试完成')
      }
    }
  }
}
</script>
