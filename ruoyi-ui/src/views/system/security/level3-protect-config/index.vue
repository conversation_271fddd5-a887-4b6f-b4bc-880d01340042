<template>
  <div class="app-container">
    <el-alert
      title="三级等保"
      type="info"
      closable
    >
      <template #default>
        <pre>
1.三级等保是中国国家等级保护认证中的最高级别认证，该认证包含了五个等级保护安全技术要求和五个安全管理要求，共涉及测评分类73类，要求非常严格。
2.三级等保是地市级以上国家机关、重要企事业单位需要达成的认证，在金融行业中，可以看作是除了银行机构以外最高级别的信息安全等级保护。
        </pre>
      </template>
    </el-alert>

    <br/>

    <el-card :header="'三级等保配置'">
      <el-form :model="form" label-width="200px" ref="formRef">
        <el-form-item label-width="0">
          <el-alert title="三级等保配置" type="info" effect="light"></el-alert>
        </el-form-item>
        <el-form-item label-width="0">
          <el-alert title="level3_protect_config" type="info" effect="light"></el-alert>
        </el-form-item>

        <br/>
        <span style="color: tomato">注：值为-1时表示对应配置不生效</span>
        <br/>
        <br/>
        <el-form-item label="最大连续登录失败次数">
          <el-input
            type="number"
            style="width: 300px"
            v-model="form.loginFailMaxTimes"
            :min="-1"
            :max="10"
          >
            <template slot="append">次</template>
          </el-input>
          <div style="color: rgba(0, 0, 0, 0.45)">连续登录失败超过一定次数，则需要锁定</div>
        </el-form-item>

        <el-form-item label="连续登录失败锁定分钟">
          <el-input
            type="number"
            style="width: 300px"
            v-model="form.loginFailLockMinutes"
            :min="-1"
          >
            <template slot="append">分钟</template>
          </el-input>
          <div style="color: rgba(0, 0, 0, 0.45)">连续登录失败锁定的时间</div>
        </el-form-item>

        <el-form-item label="登录后无操作自动退出的分钟">
          <el-input
            type="number"
            style="width: 300px"
            v-model="form.loginActiveTimeoutMinutes"
            :min="1"
            :max="10000"
          >
            <template slot="append">分钟</template>
          </el-input>
          <div style="color: rgba(0, 0, 0, 0.45)">如：登录1小时没操作自动退出当前登录状态</div>
        </el-form-item>

        <el-form-item label="定期修改密码时间间隔（天）">
          <el-input
            type="number"
            style="width: 300px"
            v-model="form.regularChangePasswordDays"
            :min="1"
            :max="10000"
          >
            <template slot="append">天</template>
          </el-input>
          <div style="color: rgba(0, 0, 0, 0.45)">定期修改密码时间间隔</div>
        </el-form-item>

        <el-form-item label="定期修改密码不允许重复次数">
          <el-input
            type="number"
            style="width: 300px"
            v-model="form.regularChangePasswordNotAllowRepeatTimes"
            :min="-1"
            :max="6"
          >
            <template slot="append">次</template>
          </el-input>
          <div style="color: rgba(0, 0, 0, 0.45)">定期修改密码不允许重复次数</div>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click.prevent="saveConfig">保存配置</el-button>
          <el-button @click="resetConfig">恢复三级等保默认配置</el-button>
          <el-button type="danger" @click="clearConfig">清除所有配置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import {
  addOrUpdLevel3ProtectConfig,
  getDefaultLevel3ProtectConfig,
  getLevel3ProtectConfig
} from '@/api/system/security/level3-protect-config/level3-protect-config'

export default {
  name: 'level3-protect-config',
  data() {
    return {
      // 表单
      form: {
        loginFailMaxTimes: undefined,
        loginFailLockMinutes: undefined,
        loginActiveTimeoutMinutes: undefined,
        regularChangePasswordDays: undefined,
        regularChangePasswordNotAllowRepeatTimes: undefined
      }
    }
  },
  created() {
    this.getConfig()
  },
  methods: {
    // 保存或更新配置
    saveConfig() {
      const data = this.form
      addOrUpdLevel3ProtectConfig(data).then(result => {
        if (result.code === 200) {
          this.$message.success(result.msg)
        }
      })
    },
    // 加载配置
    getConfig() {
      getLevel3ProtectConfig().then(result => {
        if (result) {
          this.form = result
        }
      })
    },
    // 恢复默认配置
    resetConfig() {
      getDefaultLevel3ProtectConfig().then(result => {
        if (result) {
          this.$message.success("刷新成功")
          this.form = result
        }
      })
    },
    // 清除配置
    clearConfig() {
      this.form = {}
    }
  }
}
</script>

<style>
.input-title {
  margin-bottom: 20px; /* 调整这个值以获取所需的间距 */
}
</style>
