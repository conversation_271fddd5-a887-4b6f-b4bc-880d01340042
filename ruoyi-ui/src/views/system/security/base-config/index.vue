<template>
  <div class="app-container">
    <el-alert
      title="基础信息"
      type="info"
      closable
    >
      <template #default>
        <pre>
基础信息包含系统logo、标题、登录页标题、系统描述、版权信息等相关设置
        </pre>
      </template>
    </el-alert>

    <br/>
    <el-card :header="'基础信息配置'">
      <el-form :model="formData" label-width="auto" ref="formRef" :rules="formRules">
        <el-form-item label-width="0">
          <el-alert title="基础信息配置" type="info" effect="light"></el-alert>
        </el-form-item>
        <el-form-item label-width="0">
          <el-alert title="base_config" type="info" effect="light"></el-alert>
        </el-form-item>

        <!-- <el-alert title="base_config" type="info" effect="light"></el-alert> -->
        <el-form-item label="菜单logo" prop="menuLogo">
          <el-upload class="login-uploader" :headers="headers" :action="uploadUrl" :show-file-list="false"
            :on-success="handleUploadMenuLogoSuccess" :on-error="handleUploadError" :before-upload="handleBeforeUpload">
            <img v-if="formData.menuLogo" :src="this.baseUrl + formData.menuLogo" class="login" alt="菜单logo">
            <i v-else class="el-icon-plus login-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="菜单标题" prop="menuName">
          <el-input v-model="formData.menuName" placeholder="菜单标题" style="width: 300px" />
        </el-form-item>
        <el-form-item label="favicon" prop="favicon">
          <el-upload class="login-uploader" :headers="headers" :action="uploadUrl" :show-file-list="false"
                     :on-success="handleUploadFaviconSuccess" :on-error="handleUploadError" :before-upload="handleBeforeUpload">
            <img v-if="formData.favicon" :src="this.baseUrl + formData.favicon" class="login" alt="favicon">
            <i v-else class="el-icon-plus login-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="系统名称" prop="systemName">
          <el-input v-model="formData.systemName" placeholder="系统名称" style="width: 300px" />
        </el-form-item>
        <el-form-item label="登录页名称" prop="loginPageName">
          <el-input v-model="formData.loginPageName" placeholder="登录页名称" style="width: 300px" />
        </el-form-item>
        <el-form-item label="登录页背景" prop="loginPageBG">
          <el-upload class="login-uploader" :headers="headers" :action="uploadUrl" :show-file-list="false"
                     :on-success="handleUploadLoginBGSuccess" :on-error="handleUploadError" :before-upload="handleBeforeUpload">
            <img v-if="formData.loginPageBG" :src="this.baseUrl + formData.loginPageBG" class="login" alt="登录页背景">
            <i v-else class="el-icon-plus login-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="系统描述" prop="systemDesc">
          <el-input v-model="formData.systemDesc" placeholder="系统描述" style="width: 300px" />
        </el-form-item>
        <el-form-item label="版权信息" prop="copyright">
          <el-input v-model="formData.copyright" placeholder="版权信息" style="width: 300px" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="saveConfig">保存配置</el-button>
          <el-button @click="resetConfig">恢复默认基础信息配置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>
<script>
import { addOrUpdBaseConfig, getBaseConfig, getDefaultBaseConfig } from '@/api/system/security/base-config/base-config'
import { getToken } from '@/utils/auth'
import * as attach from '@/api/file/attachment'
export default {
  name: 'base-config',
  data() {
    return {
      imageUrl: '',
      uploadUrl: process.env.VUE_APP_BASE_API + '/file/attachments/upload',
      baseUrl: window.location.origin + "/opsyndex-file",
      // 表单
      formData: {
        menuLogo: "",
        menuName: "",
        favicon: "",
        systemName: "",
        loginPageName: "",
        loginPageBG: "",
        systemDesc: "",
        copyright: ""
      },
      menuLogoFileId: "",
      faviconFileId: "",
      loginPageBGFileId: "",
      formRules: {
        menuLogo: [
          {
            required: true,
            message: "请上传logo",
            trigger: ["change"]
          }
        ],
        menuName: [
          {
            required: true,
            message: "请输入菜单名称",
            trigger: ["blur"]
          }
        ],
        favicon: [
          {
            required: true,
            message: "上传favicon",
            trigger: ["change"]
          }
        ],
        systemName: [
          {
            required: true,
            message: "请输入系统名称",
            trigger: ["blur"]
          }
        ],
        loginPageName: [
          {
            required: true,
            message: "请输入登录页名称",
            trigger: ["blur"]
          }
        ],
        loginPageBG: [
          {
            required: true,
            message: "请上传登录页背景图",
            trigger: ["change"]
          }
        ],
        systemDesc: [
          {
            required: true,
            message: "请输入系统描述",
            trigger: ["blur"]
          }
        ],
        copyright: [
          {
            required: true,
            message: "请输入版权信息",
            trigger: ["blur"]
          }
        ]
      }
    }
  },
  computed: {
    headers() {
      return {
        Authorization: 'Bearer ' + getToken()
      }
    }
  },
  created() {
    this.getConfig()
  },
  methods: {
    // 保存或更新配置
    async saveConfig() {
      const valid = await this.$refs.formRef.validate().catch(() => false)
      if (!valid) return
      const fileIdList = [this.menuLogoFileId, this.faviconFileId, this.loginPageBGFileId]
      console.log(fileIdList)
      const bindParam = {
        fileIdList: fileIdList,
        fileStatus: ''
      }
      console.log(bindParam)

      await attach.batchBindAttachment(bindParam)

      const result = await addOrUpdBaseConfig(this.formData)
      if (result.code === 200) {
        this.$message.success(result.msg)
      }
    },
    // 加载配置
    async getConfig() {
      const result = await getBaseConfig()
      if (result.code === 200) {
        Object.keys(this.formData).forEach(k => {
          this.formData[k] = result.data[k]
        })
      }
    },
    // 恢复默认配置
    async resetConfig() {
      const result = await getDefaultBaseConfig()
      if (result.code === 200) {
        this.$message.success('刷新成功')
        this.formData = result.data
      }
    },
    /**
     * 上传之前
     */
    handleBeforeUpload(file) {
      // 通常会校验文件格式 校验通过 返回 true  校验失败返回 false
      // 只能上传jpeg/jpg/png文件，且不超过20M
      const type = ['image/jpeg', 'image/jpg', 'image/png']
      const isPic = type.includes(file.type)
      if (!isPic) {
        this.$message.error('只能上传jpeg/jpg/png文件')
        return false
      }
      const isLt20M = file.size / 1024 / 1024 < 20
      if (!isLt20M) {
        this.$message.error('上传文件大小不能超过20M!')
        return false
      }
      return true
    },
    /**
     * 上传成功
     */
    handleUploadMenuLogoSuccess(res) {
      console.log(res.data)
      this.menuLogoFileId = res.data.fileId
      this.formData.menuLogo = res.data.relativePath
    },
    handleUploadLoginBGSuccess(res) {
      // 调用上传接口成功返回结果
      this.loginPageBGFileId = res.data.fileId
      this.formData.loginPageBG = res.data.relativePath
    },
    handleUploadFaviconSuccess(res) {
      // 调用上传接口成功返回结果
      this.faviconFileId = res.data.fileId
      this.formData.favicon = res.data.relativePath
    },
    /**
     * 上传失败
     */
    handleUploadError() {
      // 调用上传接口失败返回结果
      console.log('上传失败')
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  position: relative;

  ::v-deep .login-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  ::v-deep .login-uploader .el-upload:hover {
    border-color: #409EFF;
  }

  ::v-deep .login-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }

  ::v-deep .login {
    width: 178px;
    height: 178px;
    display: block;
  }
}
</style>
