<template>
  <div class="app-container">
    <el-alert title="登录配置，用于开启系统OAuth2认证方式。" type="info" show-icon>
    </el-alert>
    <el-form :model="loginConfigFormData" label-width="150px" ref="formRef" :rules="formRules">
      <el-form-item label="是否开启OAuth2">
        <el-switch v-model="loginConfigFormData.enableOAuth2" active-color="#1890ff" inactive-color="#F04134"
          :active-value="true" :inactive-value="false" active-text="开启" inactive-text="关闭" />
      </el-form-item>
      <el-divider />
      <template v-if="loginConfigFormData.enableOAuth2">
        <el-form-item v-for="(item, index) in oauth2Fields" :key="index" :label="item.label" :prop="item.prop">
          <el-input v-model="loginConfigFormData[item.prop]" :placeholder="item.placeholder" style="width: 65%" />
        </el-form-item>
      </template>
      <el-divider />
      <el-form-item>
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { addOrUpdLoginConfig, getLoginConfig } from '@/api/system/security/login-config/login-config'

export default {
  name: 'login-config',
  data() {
    return {
      loginConfigFormData: {
        enableOAuth2: false,
        clientId: '',
        clientSecret: '',
        authorizeUri: '',
        redirectUri: '',
        authorizeStateType: '',
        logoutUri: '',
        accessTokenUri: '',
        profileUri: '',
        appId: '',
        appSecret: '',
        rsaPrivateKey: ''
      },
      formRules: {
        clientId: [{ required: true, message: '请输入 ClientId', trigger: 'blur' }],
        clientSecret: [{ required: true, message: '请输入 Client Secret', trigger: 'blur' }],
        authorizeUri: [{ required: true, message: '请输入 Authorize URI', trigger: 'blur' }],
        redirectUri: [{ required: true, message: '请输入 Redirect URI', trigger: 'blur' }],
        logoutUri: [{ required: true, message: '请输入 Logout URI', trigger: 'blur' }],
        accessTokenUri: [{ required: true, message: '请输入 Access Token URI', trigger: 'blur' }],
        appId: [{ required: true, message: '请输入 AppId', trigger: 'blur' }],
        appSecret: [{ required: true, message: '请输入 AppSecret', trigger: 'blur' }],
        rsaPrivateKey: [{ required: true, message: '请输入 RSA Private Key', trigger: 'blur' }]
      }
    }
  },
  computed: {
    oauth2Fields() {
      return [
        { label: 'Client Id', prop: 'clientId', placeholder: '请输入 ClientId', required: true },
        { label: 'Client Secret', prop: 'clientSecret', placeholder: '请输入 ClientSecret', required: true },
        { label: 'Authorize Uri', prop: 'authorizeUri', placeholder: '请输入 AuthorizeUri', required: true },
        { label: 'Redirect Uri', prop: 'redirectUri', placeholder: '请输入 RedirectUri', required: true },
        { label: 'Authorize StateType', prop: 'authorizeStateType', placeholder: '请选择 AuthorizeStateType', required: false },
        { label: 'Logout Uri', prop: 'logoutUri', placeholder: '请输入 LogoutUri', required: true },
        { label: 'Access TokenUri', prop: 'accessTokenUri', placeholder: '请输入 AccessTokenUri', required: true },
        { label: 'Profile Uri', prop: 'profileUri', placeholder: '请输入 ProfileUri', required: false },
        { label: 'AppId', prop: 'appId', placeholder: '请输入 AppId', required: false },
        { label: 'AppSecret', prop: 'appSecret', placeholder: '请输入 AppSecret', required: false },
        { label: 'RSA PrivateKey', prop: 'rsaPrivateKey', placeholder: '请输入 RSA PrivateKey', required: false }
      ]
    },
    defaultFormData() {
      return {
        enableOAuth2: false,
        clientId: '',
        clientSecret: '',
        authorizeUri: '',
        redirectUri: '',
        authorizeStateType: '',
        logoutUri: '',
        accessTokenUri: '',
        profileUri: '',
        appId: '',
        appSecret: '',
        rsaPrivateKey: ''
      }
    }
  },
  created() {
    this.getConfig()
  },
  methods: {
    async getConfig() {
      const res = await getLoginConfig()
      if (res.code === 200) {
        this.loginConfigFormData = res.data
      }
    },
    async handleSave() {
      const valid = await this.$refs.formRef.validate().catch(() => false)
      if (!valid) return
      const result = await addOrUpdLoginConfig(this.loginConfigFormData)
      if (result.code === 200) {
        this.$message.success('保存成功')
      }
    },
    handleReset() {
      this.$refs.formRef.resetFields()
      this.loginConfigFormData = { ...this.defaultFormData }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  position: relative;
}
</style>
