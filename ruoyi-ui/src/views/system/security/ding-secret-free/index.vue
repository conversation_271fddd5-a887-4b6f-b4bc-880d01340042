<template>
  <div class="app-container">
    <el-card shadow="hover" class="config-card">
      <el-alert title="钉钉应用信息配置" type="info" effect="light" show-icon>
        1、钉钉应用信息配置，用于免密登录使用的钉钉应用信息。
      </el-alert>
      <br />
      <el-form :model="formData" label-width="150px" ref="formRef" :rules="formRules">
        <el-form-item label="agent Id" prop="agentId">
          <el-input v-model="formData.agentId" placeholder="请输入 agent Id" style="width: 400px" />
        </el-form-item>
        <el-form-item label="App Id" prop="appId">
          <el-input v-model="formData.appId" placeholder="请输入 App Id" style="width: 400px" />
        </el-form-item>
        <el-form-item label="App Key" prop="appKey">
          <el-input v-model="formData.appKey" placeholder="请输入 App Key" style="width: 400px" />
        </el-form-item>
        <el-form-item label="App Secret" prop="appSecret">
          <el-input v-model="formData.appSecret" placeholder="请输入 App Secret" style="width: 400px" />
        </el-form-item>
        <el-divider />
        <el-form-item label="获取 Token URL" prop="tokenUrl">
          <el-input v-model="formData.tokenUrl" placeholder="请输入获取 Token 的 URL" style="width: 400px" />
        </el-form-item>
        <el-form-item label="获取用户信息 URL" prop="userInfoUrl">
          <el-input v-model="formData.userInfoUrl" placeholder="请输入获取用户信息的 URL" style="width: 400px" />
        </el-form-item>
        <el-divider />
        <el-form-item>
          <el-button type="primary" @click="handleSave">保存</el-button>
<!--          <el-button @click="handleReset">重置</el-button>-->
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import {queryDingConfig, addOrUpdDingConfig} from '@/api/system/security/ding-config/ding-config'

export default {
  name: "ding-config",
  data() {
    return {
      formData : {
        appId: "",
        appKey: "",
        appSecret: "",
        tokenUrl: "",
        userInfoUrl: "",
      },
      formRules:{
        appKey: [{ required: true, message: "请输入 App Key", trigger: "blur" }],
        appSecret: [{ required: true, message: "请输入 App Secret", trigger: "blur" }],
        tokenUrl: [{ required: true, message: "请输入获取 Token 的 URL", trigger: "blur" }],
        userInfoUrl: [{ required: true, message: "请输入获取用户信息的 URL", trigger: "blur" }],
      }

    }
  },
  created() {
    this.getConfig()
  },
  methods: {
    getConfig() {
      queryDingConfig().then(res => {
        this.formData = res.data
      })
    },
    handleSave() {
      console.log(this.formData, "数据保存")
      this.$refs["formRef"].validate(valid => {
        if (valid) {
          addOrUpdDingConfig(this.formData).then(res => {
            this.$message({
              type: "success",
              message: "保存成功"
            });
          });
        }
      });
    },
    handleReset() {
      Object.assign(this.formData, {
        appId: "",
        appKey: "",
        appSecret: "",
        tokenUrl: "",
        userInfoUrl: "",
      });
    },
  }
}

</script>

<style scoped>
.config-card {
  max-width: 600px;
  margin: 20px auto;
  padding: 20px;
}
</style>
