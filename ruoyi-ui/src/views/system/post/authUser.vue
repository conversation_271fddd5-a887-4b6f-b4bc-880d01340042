<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryFormRef" size="small" inline v-show="showSearch">
      <el-form-item label="用户名称" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入用户名称" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="手机号码" prop="phonenumber">
        <el-input v-model="queryParams.phonenumber" placeholder="请输入手机号码" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="openSelectUser"
          v-hasPermi="['system:role:add']">添加用户</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-circle-close" size="mini" :disabled="cancelAuthUserAllDisabled"
          @click="cancelAuthUserAll" v-hasPermi="['system:role:remove']">批量取消授权</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport">导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-close" size="mini"
          @click="$router.push({ path: '/system/post' })">返回</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getPostUserList"></right-toolbar>
    </el-row>

    <el-table v-loading="postUserTableLoading" :data="postUserList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="用户名称" prop="userName" show-overflow-tooltip />
      <el-table-column label="用户昵称" prop="nickName" show-overflow-tooltip />
      <el-table-column label="邮箱" prop="email" show-overflow-tooltip />
      <el-table-column label="手机" prop="phonenumber" show-overflow-tooltip />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-circle-close" @click="cancelAuthUser(scope.row)"
            v-hasPermi="['system:role:remove']">取消授权</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getPostUserList" />


    <select-user ref="selectUserRef" :postId="queryParams.postId" @ok="handleQuery" />

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url"
        :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess"
        :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
            @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { allocatedUserList, authUserCancel, authUserCancelAll } from "@/api/system/post";
import selectUser from "./selectUser";
import { getToken } from "@/utils/auth";

export default {
  name: "AuthUser",
  dicts: ['sys_normal_disable'],
  components: { selectUser },
  data() {
    return {
      // 遮罩层
      postUserTableLoading: false,
      // 选中用户组
      userIds: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      postUserList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        postId: undefined,
        userName: undefined,
        phonenumber: undefined
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/post/importAuthUser"
      },
    };
  },
  computed: {
    cancelAuthUserAllDisabled() {
      return this.userIds.length == 0
    }
  },
  created() {
    const postId = this.$route.params && this.$route.params.postId;
    if (postId) {
      this.queryParams.postId = postId;
      this.getPostUserList();
    }
  },
  methods: {
    /**
     * 查询授权用户列表
     */
    async getPostUserList() {
      this.postUserTableLoading = true;
      const response = await allocatedUserList(this.queryParams)
        .finally(() => { this.postUserTableLoading = false; });
      if (response.code == 200) {
        this.postUserList = response.rows;
        this.total = response.total || 0;
      }
    },
    /**
     * 搜索按钮操作
     */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getPostUserList();
    },
    /**
     * 重置按钮操作
     */
    resetQuery() {
      this.resetForm("queryFormRef");
      this.handleQuery();
    },
    /**
     * 多选框选中数据
     * @param selection
     */
    handleSelectionChange(selection) {
      this.userIds = selection.map(item => item.userId)
    },
    /**
     * 打开授权用户表弹窗
     */
    openSelectUser() {
      this.$refs.selectUserRef.show();
    },
    /**
     * 取消授权按钮操作
     */
    cancelAuthUser(row) {
      const postId = this.queryParams.postId;
      this.$modal.confirm('确认要取消该用户"' + row.userName + '"角色吗？').then( ()=> {
        return authUserCancelAll({ userIds: row.userId, postId: postId });
      }).then(() => {
        this.getPostUserList();
        this.$modal.msgSuccess("取消授权成功");
      }).catch(() => { });
    },
    /**
     * 批量取消授权按钮操作
     */
    cancelAuthUserAll(row) {
      const postId = this.queryParams.postId;
      const userIds = this.userIds.join(",");
      this.$modal.confirm('是否取消选中用户授权数据项？').then(() => {
        return authUserCancelAll({ postId: postId, userIds: userIds });
      }).then(() => {
        this.getPostUserList();
        this.$modal.msgSuccess("取消授权成功");
      }).catch(() => { });
    },
    /**
     * 导入按钮操作
     */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /**
     * 文件上传中处理
     * @param event
     * @param file
     * @param fileList
     */
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    /**
     * 文件上传成功处理
     * @param response
     * @param file
     * @param fileList
     */
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getPostUserList();
    },
    /**
     * 提交上传文件
     */
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /**
     * 下载模板操作
     */
    importTemplate() {
      this.download('system/post/importTemplate' + '?postId=' + this.queryParams.postId, {
      }, `user_template_${new Date().getTime()}.xlsx`)
    },
  }
};
</script>
