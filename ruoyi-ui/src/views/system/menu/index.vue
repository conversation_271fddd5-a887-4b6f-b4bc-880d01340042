<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryFormRef" size="small" inline v-show="showSearch">
      <el-form-item label="菜单名称" prop="menuName">
        <el-input v-model="queryParams.menuName" placeholder="请输入菜单名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="菜单状态" clearable>
          <el-option v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['system:menu:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-sort" size="mini" @click="toggleExpandAll">展开/折叠</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table ref="fullHeightTableRef" :height="tableHeight" v-if="refreshTable" v-loading="loading" :data="menuList" row-key="menuId" border
      :default-expand-all="isExpandAll" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
      <el-table-column prop="menuName" label="菜单名称" :show-overflow-tooltip="true" width="200"></el-table-column>
      <el-table-column prop="icon" label="图标" align="center" width="60">
        <template slot-scope="scope">
          <svg-icon :icon-class="scope.row.icon" />
        </template>
      </el-table-column>
      <el-table-column prop="orderNum" label="排序" width="60"></el-table-column>
      <el-table-column prop="perms" label="权限标识" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="component" label="组件路径" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="status" label="状态" width="80">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-copy-document" @click="handleCopy(scope.row)"
                     v-hasPermi="['system:menu:copy']">复制</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:menu:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-plus" @click="handleAdd(scope.row)"
            v-hasPermi="['system:menu:add']">新增</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:menu:remove']">删除</el-button>
          <el-button size="mini" type="text" icon="el-icon-user" @click="handleRole(scope.row)">角色</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改菜单对话框 -->
    <el-dialog :title="title" :visible.sync="menuFormDialogVisible" width="680px" append-to-body>
      <el-form ref="menuFormRef" :model="menuFormData" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="上级菜单" prop="parentId">
              <treeselect v-model="menuFormData.parentId" :options="menuOptions" :normalizer="normalizer"
                :show-count="true" placeholder="选择上级菜单" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="菜单类型" prop="menuType">
              <el-radio-group v-model="menuFormData.menuType">
                <el-radio label="M">目录</el-radio>
                <el-radio label="C">菜单</el-radio>
                <el-radio label="F">按钮</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="APP菜单类型" prop="menuType">
              <el-radio-group v-model="menuFormData.appMenuType">
                <el-radio label="H">首页</el-radio>
                <el-radio label="M">目录</el-radio>
                <el-radio label="C">菜单</el-radio>
                <el-radio label="N">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="menuFormData.menuType != 'F'">
            <el-form-item label="菜单图标" prop="icon">
              <el-popover placement="bottom-start" width="460" trigger="click" @show="$refs['iconSelect'].reset()">
                <IconSelect ref="iconSelect" @selected="selected" :active-icon="menuFormData.icon" />
                <el-input slot="reference" v-model="menuFormData.icon" placeholder="点击选择图标" readonly>
                  <svg-icon v-if="menuFormData.icon" slot="prefix" :icon-class="menuFormData.icon"
                    style="width: 25px;" />
                  <i v-else slot="prefix" class="el-icon-search el-input__icon" />
                </el-input>
              </el-popover>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="菜单名称" prop="menuName">
              <el-input v-model="menuFormData.menuName" placeholder="请输入菜单名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="orderNum">
              <el-input-number v-model="menuFormData.orderNum" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="menuFormData.menuType != 'F'">
            <el-form-item prop="isFrame">
              <span slot="label">
                <el-tooltip content="选择是外链则路由地址需要以`http(s)://`开头" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                是否外链
              </span>
              <el-radio-group v-model="menuFormData.isFrame">
                <el-radio label="0">是</el-radio>
                <el-radio label="1">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="menuFormData.menuType != 'F'">
            <el-form-item prop="path">
              <span slot="label">
                <el-tooltip content="访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                路由地址
              </span>
              <el-input v-model="menuFormData.path" placeholder="请输入路由地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="menuFormData.menuType == 'C'">
            <el-form-item prop="component">
              <span slot="label">
                <el-tooltip content="访问的组件路径，如：`system/user/index`，默认在`views`目录下" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                组件路径
              </span>
              <el-input v-model="menuFormData.component" placeholder="请输入组件路径" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="menuFormData.menuType != 'M'">
            <el-form-item prop="perms">
              <el-input v-model="menuFormData.perms" placeholder="请输入权限标识" maxlength="100" />
              <span slot="label">
                <el-tooltip content="控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermi('system:user:list')`)" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                权限字符
              </span>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="menuFormData.menuType == 'C'">
            <el-form-item prop="query">
              <el-input v-model="menuFormData.query" placeholder="请输入路由参数" maxlength="255" />
              <span slot="label">
                <el-tooltip content='访问路由的默认传递参数，如：`{"id": 1, "name": "ry"}`' placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                路由参数
              </span>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="menuFormData.menuType == 'C'">
            <el-form-item prop="isCache">
              <span slot="label">
                <el-tooltip content="选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                是否缓存
              </span>
              <el-radio-group v-model="menuFormData.isCache">
                <el-radio label="0">缓存</el-radio>
                <el-radio label="1">不缓存</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="menuFormData.menuType != 'F'">
            <el-form-item prop="visible">
              <span slot="label">
                <el-tooltip content="选择隐藏则路由将不会出现在侧边栏，但仍然可以访问" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                显示状态
              </span>
              <el-radio-group v-model="menuFormData.visible">
                <el-radio v-for="dict in dict.type.sys_show_hide" :key="dict.value" :label="dict.value">{{ dict.label
                  }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="status">
              <span slot="label">
                <el-tooltip content="选择停用则路由将不会出现在侧边栏，也不能被访问" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
                菜单状态
              </span>
              <el-radio-group v-model="menuFormData.status">
                <el-radio v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.value">{{
                  dict.label
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 角色按钮对话框 -->
    <el-dialog
      title="角色绑定关系"
      :visible.sync="role.roleDialogVisible"
      width="30%">
      <el-table
        v-loading="role.loading"
        :data="role.tableData"
        height="250"
        border
        style="width: 100%">
        <el-table-column
          prop="roleId"
          label="id"
          width="80">
        </el-table-column>
        <el-table-column
          prop="roleCode"
          label="角色编码"
          width="180">
        </el-table-column>
        <el-table-column
          prop="roleName"
          label="角色名称">
        </el-table-column>
      </el-table>
      <pagination
        v-show="role.total>0"
        :total="role.total"
        :page.sync="role.queryParams.pageNum"
        :limit.sync="role.queryParams.pageSize"
        @pagination="getMenuRoleList()"
      />
    </el-dialog>


  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import { listMenu, getMenu, delMenu, addMenu, updateMenu,getMenuRole } from "@/api/system/menu";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import IconSelect from "@/components/IconSelect";
import {servicesLoading} from "@/utils/eleUtils";

export default {
  mixins: [tableFullHeight],
  name: "Menu",
  dicts: ['sys_show_hide', 'sys_normal_disable'],
  components: { Treeselect, IconSelect },
  data() {
    return {
      tl: true,
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 菜单表格树数据
      menuList: [],
      // 原始数据
      originalMenuList: [],
      // 菜单树选项
      menuOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      menuFormDialogVisible: false,
      // 是否展开，默认全部折叠
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 查询参数
      queryParams: {
        menuName: undefined,
        visible: undefined
      },
      // 表单参数
      menuFormData: {
        menuId: null,
        parentId: 0,
        menuName: null,
        icon: null,
        menuType: "M",
        appMenuType: "M",
        orderNum: null,
        path: "",
        query: "",
        isFrame: "1",
        isCache: "0",
        visible: "0",
        status: "0",
        perms: "",
        component:""
      },
      // 表单校验
      rules: {
        menuName: [
          { required: true, message: "菜单名称不能为空", trigger: "blur" }
        ],
        orderNum: [
          { required: true, message: "菜单顺序不能为空", trigger: "blur" }
        ],
        path: [
          { required: true, message: "路由地址不能为空", trigger: "blur" }
        ]
      },
      role: {
        roleDialogVisible: false,
        tableData: [],
        loading: false,
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          menuId: undefined,
        },
        total: 0,
      }
    };
  },
  async created() {
    await this.getList();
    this.handleCreateMenuByTableCode()
  },
  // 兼容被keep-alive缓存的组件
  activated() {
    this.handleCreateMenuByTableCode()
  },
  methods: {
    /**
     * 根据当前路由，判断是否走新建或者编辑
     */
    handleCreateMenuByTableCode() {
      const tableCode = this.$route.query.tableCode
      if (!tableCode) return
      const menuOption = this.originalMenuList.find(item => item.query && item.query.includes(tableCode))
      if (!menuOption) {
        // 走新建
        this.handleAdd()
      } else {
        // 走编辑
        this.handleUpdate(menuOption)
      }
    },
    /**
     * 选择图标
     * @param {String} name
     */
    selected(name) {
      this.menuFormData.icon = name;
    },
    /** 查询菜单列表 */
    async getList() {
      this.loading = true;
      const response = await listMenu(this.queryParams)
      if (response.code == 200) {
        this.originalMenuList = response.data;
        this.menuList = this.handleTree(response.data, "menuId");
        this.loading = false;
      };
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.menuId,
        label: node.menuName,
        children: node.children
      };
    },
    /**
     * 查询菜单下拉树结构
     */
    async getTreeselect(func) {
      const response = await listMenu();
      if (response.code == 200) {
        this.menuOptions = [];
        const menu = { menuId: 0, menuName: '主类目', children: [] };
        menu.children = this.handleTree(response.data, "menuId");
        this.menuOptions.push(menu);
      }
    },
    /**
     * 取消按钮
     */
    cancel() {
      this.menuFormDialogVisible = false;
      // this.resetMenuFormData(); del by wangyouzheng 2025年4月19日15:52:45 避免鬼畜
    },
    /**
     * 表单重置
     */
    resetMenuFormData() {
      this.menuFormData.menuId = null
      this.menuFormData.parentId = 0
      this.menuFormData.menuName = null
      this.menuFormData.icon = null
      this.menuFormData.menuType = "M"
      this.menuFormData.orderNum = null
      this.menuFormData.path = ""
      this.menuFormData.query = ""
      this.menuFormData.isFrame = "1"
      this.menuFormData.isCache = "0"
      this.menuFormData.visible = "0"
      this.menuFormData.status = "0"
      this.menuFormData.component = ""
      this.resetForm("menuFormRef");
    },
    /**
     * 搜索按钮操作
     */
    handleQuery() {
      this.getList();
    },
    /**
     * 重置按钮操作
     */
    resetQuery() {
      this.resetForm("queryFormRef");
      this.handleQuery();
    },
    /**
     * 新增按钮操作
     */
    async handleAdd(row) {
      let loading = servicesLoading('正在加载', true, 'rgba(0,0,0,0.7)')

      this.title = "添加菜单";
      this.resetMenuFormData();

      await this.getTreeselect();

      this.$nextTick(() => {
        const tableCode = this.$route.query.tableCode
        const tableName = this.$route.query.tableName
        if (!row && tableCode) {
          this.menuFormData.path = tableCode
          this.menuFormData.menuName = tableName
          this.menuFormData.orderNum = 0
          this.menuFormData.menxuType = "C"
          this.menuFormData.appMenxuType = "C"
          this.menuFormData.query = `{"tableCode":"${tableCode}"}`
          this.menuFormData.component = "codeDev/formData/index"
        }
        if (row != null && row.menuId) {
          this.menuFormData.parentId = row.menuId;
        } else {
          this.menuFormData.parentId = 0;
        }

        this.menuFormDialogVisible = true;

        loading.close();
      })
    },
    /**
     * 展开/折叠操作
     */
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    /**
     * 复制按钮操作
     */
    async handleCopy(row) {

      this.title = "复制菜单";
      this.resetMenuFormData();
      let loading = servicesLoading('正在加载', true, 'rgba(0,0,0,0.7)')

      await this.getTreeselect();
      this.$nextTick(() => {
        getMenu(row.menuId).then(response => {
          Object.keys(this.menuFormData).forEach(key => {
            this.menuFormData[key] = response.data[key];
          })
          //清空ID 路由地址拼接-copy 防止重复
          this.menuFormData['menuId'] = null;
          this.menuFormData["path"] = this.menuFormData["path"] + "-copy";

          this.menuFormDialogVisible = true;
          loading.close();
        });
      })
    },
    /**
     * 修改按钮操作
     */
    handleUpdate(row) {
      this.title = "修改菜单";
      // 重置表单数据。
      this.resetMenuFormData();
      let loading = servicesLoading('正在加载', true, 'rgba(0,0,0,0.7)')
      // 渲染下拉列表。
      this.getTreeselect();
      getMenu(row.menuId).then(response => {
        Object.keys(this.menuFormData).forEach(key => {
          this.menuFormData[key] = response.data[key];
        })
        // this.menuFormData = response.data;
        this.menuFormDialogVisible = true;
        loading.close();
      })
    },

    /**
     * 提交按钮
     */
    submitForm() {
      this.$refs["menuFormRef"].validate(valid => {
        if (valid) {
          if (this.menuFormData.menuId != undefined) {
            updateMenu(this.menuFormData).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.menuFormDialogVisible = false;
              this.getList();
              // 创建成功之后清空路径中的参数
              const path = this.$route.path
              this.$router.replace({ path: path, query: {} })
            });
          } else {
            addMenu(this.menuFormData).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.menuFormDialogVisible = false;
              this.getList();
              // 创建成功之后清空路径中的参数
              const path = this.$route.path
              this.$router.replace({ path: path, query: {} })
            });
          }
        }
      });
    },
    /**
     * 删除按钮操作
     */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除名称为"' + row.menuName + '"的数据项？').then(function () {
        return delMenu(row.menuId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /**
     * 角色按钮操作
     */
    handleRole(row) {
      this.role.tableData = [];
      this.role.queryParams.pageNum = 1;
      this.role.roleDialogVisible = true;
      this.role.queryParams.menuId = row.menuId;
      this.getMenuRoleList();
    },
    getMenuRoleList() {
      this.role.loading = true;
      getMenuRole(this.role.queryParams).then(response => {
          this.role.tableData = response.rows;
          this.role.total = response.total;
          this.role.loading = false;
      })
    },
  }
};
</script>
