<template>
  <div class="app-container">
    <h4 class="form-header h4">基本信息</h4>
    <el-form ref="form" :model="userInfo" label-width="80px">
      <el-row>
        <el-col :span="8" :offset="2">
          <el-form-item label="用户昵称" prop="nickName">
            <el-input v-model="userInfo.nickName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8" :offset="2">
          <el-form-item label="登录账号" prop="userName">
            <el-input v-model="userInfo.userName" disabled />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <h4 class="form-header h4">角色信息</h4>
    <el-form :model="queryParams" ref="queryForm" size="small" inline>
      <el-form-item label="角色分类" prop="category">
        <el-select v-model="queryParams.category" clearable filterable placeholder="角色分类">
          <el-option v-for="item in roleTypeOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="角色名称" prop="role">
        <el-select v-model="queryParams.role" clearable filterable remote reserve-keyword placeholder="角色名称"
          :remote-method="handleFilterRoleList">
          <el-option v-for="item in roleOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" row-key="roleId" @row-click="clickRow" ref="tableRef"
      @selection-change="handleSelectionChange" :data="roleTableData">
      <el-table-column type="selection" reserve-selection width="55"></el-table-column>
      <el-table-column label="角色编号" align="center" prop="roleId" />
      <el-table-column label="角色分类" prop="category" width="150">
        <template slot-scope="scope">
          {{ getCategoryLabel(scope.row.category) }}
        </template>
      </el-table-column>
      <el-table-column label="角色名称" align="center" prop="roleName" />
      <el-table-column label="权限字符" align="center" prop="roleKey" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- <el-pagination v-show="total > 0" @size-change="handlePageSizeChange" @current-change="handlePageNumChange"
      :current-page="pageNum" :page-sizes="[10, 20, 30, 40]" :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper" :total="total">
    </el-pagination> -->

    <el-form label-width="100px">
      <el-form-item style="text-align: center;margin-left:-120px;margin-top:30px;">
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="close">返回</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getAuthRole, updateAuthRole } from "@/api/system/user";
import { listRole } from "@/api/system/role";
export default {
  name: "AuthRole",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 分页信息
      total: 0,
      pageNum: 1,
      pageSize: 10,
      // 选中角色编号
      roleIds: [],
      // 角色信息
      roleTableData: [],
      // 用户信息
      userInfo: {
        nickName: "",
        userName: "",
        userId: null
      },
      roleOptions: [],
      queryParams: {
        role: "",
        category: "",
      },
      roleTypeOptions: []
    };
  },
  async created() {
    this.handleFilterRoleList("")
    // await this.handleGetAllRoleList()
    await this.haneleGetRoleTypeOptions()
    this.handleGetAuthRole()

  },
  watch: {
    // 解决清空无法重新触发远程方法的bug
    "queryParams.role"(val) {
      if (!val) {
        this.handleFilterRoleList("")
      }
    }
  },
  methods: {
    getCategoryLabel(value) {
      let label = "";
      this.roleTypeOptions.forEach(item => {
        if (item.dictValue == value) {
          label = item.dictLabel;
        }
      });
      return label;
    },
    haneleGetRoleTypeOptions() {
      // 获取角色分类
      this.getDicts('role_classify').then(response => {
        this.roleTypeOptions = response.data;
      });
    },
    /**
     * 获取用户信息&之前所分配的角色
     */
    handleGetAuthRole() {
      const userId = this.$route.params && this.$route.params.userId;
      const roleName = this.queryParams.role || null
      const category = this.queryParams.category || null
      if (userId) {
        this.loading = true;
        getAuthRole(userId, roleName,category).then((response) => {
          this.userInfo.userId = response.user.userId;
          this.userInfo.userName = response.user.userName;
          this.userInfo.nickName = response.user.nickName;
          // 所有的角色
          this.roleTableData = response.roles;
          // const allocatedRoleIds = allocatedRoles.map(r => r.roleId)
          // const allocatedRoles = response.user.roles
          console.log("response", response.user.roles)
          const roleTableData = this.roleTableData;
          roleTableData.forEach((row) => {
            if (row.flag) {
              //   console.log(row);
              // if (allocatedRoleIds.includes(row.roleId)) {
              this.$refs.tableRef.toggleRowSelection(row, true);
              // }
            }
          });
          this.loading = false;
        });
      }
    },
    async handlePageSizeChange(val) {
      this.pageSize = val
      this.pageNum = 1
      // await this.handleGetAllRoleList()
      this.handleGetAuthRole()
    },
    async handlePageNumChange(val) {
      this.pageNum = val
      // await this.handleGetAllRoleList()
      this.handleGetAuthRole()
    },
    async handleQuery() {
      this.pageNum = 1
      // await this.handleGetAllRoleList()
      this.handleGetAuthRole()
    },
    /**
     * 获取所有的角色列表
     */
    async handleGetAllRoleList() {
      const result = await listRole({ pageSize: this.pageSize, pageNum: this.pageNum });
      if (result.code == 200) {
        this.roleTableData = result.rows;
        this.total = result.total;
      }
    },
    async handleFilterRoleList(value) {
      const result = await listRole({ roleName: value, pageSize: 20, pageNum: 1 });
      if (result.code == 200) {
        this.roleOptions = result.rows.map((item) => {
          return { value: item.roleName, label: item.roleName };
        });
      }
    },
    /** 单击选中行数据 */
    clickRow(row) {
      this.$refs.tableRef.toggleRowSelection(row);
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.roleIds = selection.map((item) => item.roleId);
    },
    /** 提交按钮 */
    submitForm() {
      const userId = this.userInfo.userId;
      const roleIds = this.roleIds.join(",");
      updateAuthRole({ userId: userId, roleIds: roleIds }).then((response) => {
        this.$modal.msgSuccess("授权成功");
        this.close();
      });
    },
    /** 关闭按钮 */
    close() {
      const option = { path: "/system/user" };
      this.$tab.closeOpenPage(option);
    },
  },
};
</script>