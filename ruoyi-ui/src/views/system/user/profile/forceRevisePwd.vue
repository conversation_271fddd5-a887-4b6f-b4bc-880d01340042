<template>
  <div class="container" :style="{ backgroundImage: `url(${backgroundImage})` }">
    <el-form ref="form" :model="user" :rules="rules" label-width="80px" class="forget-form">
      <el-form-item label="账号" prop="username">
        <el-input v-model="user.username" placeholder="请输入账号" type="text"/>
      </el-form-item>
      <el-form-item label="旧密码" prop="oldPassword">
        <el-input v-model="user.oldPassword" placeholder="请输入旧密码" type="password" show-password/>
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword">
        <el-input v-model="user.newPassword" placeholder="请输入新密码" type="password" show-password/>
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input v-model="user.confirmPassword" placeholder="请确认新密码" type="password" show-password/>
      </el-form-item>
      <el-form-item>
        <div class="button-container">
          <el-button type="info" size="mini" style="width: 100px" @click="close">返回</el-button>
          <el-button type="primary" size="mini" style="width: 100px" @click="submit">确认</el-button>
        </div>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-login-footer">
      <span>{{ copyright }}</span>
    </div>
  </div>
</template>

<script>
import { forgetPwd, updateUserPwd } from '@/api/system/user'
import { getBaseConfig } from '@/api/system/security/base-config/base-config'

export default {
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.user.newPassword !== value) {
        callback(new Error("两次输入的密码不一致"));
      } else {
        callback();
      }
    };
    return {
      baseUrl: window.location.origin + "/opsyndex-file",
      backgroundImage: '',
      copyright: '',
      user: {
        username: undefined,
        oldPassword: undefined,
        newPassword: undefined,
        confirmPassword: undefined
      },
      // 表单校验
      rules: {
        username: [
          { required: true, message: "账号不能为空", trigger: "blur" }
        ],
        oldPassword: [
          { required: true, message: "旧密码不能为空", trigger: "blur" }
        ],
        newPassword: [
          { required: true, message: "新密码不能为空", trigger: "blur" },
          { min: 8, max: 20, message: "长度在 8 到 20 个字符", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (!value) {
                callback();
                return;
              }

              // 必须包含的字符类型
              const hasUpper = /[A-Z]/.test(value);
              const hasLower = /[a-z]/.test(value);
              const hasSpecial = /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(value);

              // 缺失条件检测
              const missing = [];
              if (!hasUpper) missing.push("大写字母");
              if (!hasLower) missing.push("小写字母");
              if (!hasSpecial) missing.push("特殊字符");

              // 返回复合错误提示
              if (missing.length > 0) {
                callback(new Error(`必须包含英文字母大小写、数字和特殊符号`));
                return;
              }
              callback();
            },
            trigger: ["blur"]  // 同时响应失焦和输入变化
          }
        ],
        confirmPassword: [
          { required: true, message: "确认密码不能为空", trigger: "blur" },
          { required: true, validator: equalToPassword, trigger: "blur" }
        ]
      }
    };
  },
  mounted() {
    this.fetchBaseConfig()
  },
  methods: {
    async fetchBaseConfig() {
      try {
        const res = await getBaseConfig(); // 处理异步请求
        if (res.code === 200) {
          this.loginPageName = res.data.loginPageName;

          this.copyright = res.data.copyright

          this.backgroundImage = this.baseUrl + res.data.loginPageBG

          document.title = res.data.systemName

          var link = document.querySelector("link[rel*='icon']") || document.createElement('link');
          link.type = 'image/x-icon';
          link.rel = 'shortcut icon';
          link.href = this.baseUrl + res.data.favicon
          document.getElementsByTagName('head')[0].appendChild(link);
        }
      } catch (error) {
        console.error("Error fetching base config:", error);
      }
    },
    submit() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          forgetPwd(this.user.username, this.user.oldPassword, this.user.newPassword).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.$router.push('login');
          });
        }
      });
    },
    close() {
      // 返回登录页
      this.$router.push('login');
    }
  }
};
</script>

<style scoped>
.container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-size: cover;
}

.forget-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 35px 25px 5px 5px;
}

.button-container {
  display: flex;
  justify-content: space-between; /* 按钮两边的空间填充 */
  width: 100%; /* 确保容器宽度占满父容器 */
}
</style>
