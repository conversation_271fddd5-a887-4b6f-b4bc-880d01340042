<template>
    <div class="contentbox _tablepage" v-loading="loading" :element-loading-text="loadingText"  style="height: 100%;display: flex;flex-direction: column;">
        <div style="width: 100%;flex: none;">
            <el-header class="_header df-c-b">
                <div class="headerLeft df-c" style="width:30%">
                <div class="tplname" style="width: 100%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;" :title="tplName">
                        {{tplName}}
                    </div>
                </div>
                <div class="headerRight df-c">
                <el-dropdown class="white font" trigger="click" placement="bottom" v-if="users.length > 0">
                    <span class="el-dropdown-link df-c">
                    <el-avatar size="small" :style="{marginRight:'4px',backgroundColor:item.color+' !important'}" shape="circle" :title="item.userName" v-for="(item,index) in headerUsers" :key="index"> {{(item.userName.slice(0,1)).toUpperCase()}} </el-avatar>
                    <i class="el-icon-arrow-down el-icon--right" ></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item  v-for="(item,index) in users" :key="index">{{item.userName}}</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
                </div>
            </el-header>
        </div>
        <div :style="{flex:1,display:'flex',height:designHeight+'px'}">
        <div class="left">
            <div class="left-dataset-title">
                <span class="dataset-title">数据集管理</span>
                <el-button class="addBtn" @click="addDataSets" v-has="'reportForms_addDataSet'">添加<i class="el-icon-plus el-icon--right"></i></el-button>
            </div>
            <div v-for="o in datasets" :key="o.id">
                <div :class="o.isActive?'dataset-box-active':'dataset-box'" style="position:relative">
                    <i :class="o.isActive?'el-icon-arrow-down el-icon-arrow-down_dataset':'el-icon-arrow-right'" @click="clickDatasets(o)"></i>
                    <span class="dataset-name" @click="clickDatasets(o)" :title="o.datasetName">{{o.datasetName}}</span>
                    <i class="el-icon-edit" @click="editDataSet(o)" v-has="'reportForms_editDataSet'"></i>
                    <i class="el-icon-delete" @click="deleteDataSet(o)" v-has="'reportForms_deleteDataSet'"></i>
                </div>
                <div class="dataset-box-content" v-if="o.isActive">
                    <vuedraggable class="wrapper" v-model="o.columns" :sort= "false" :disabled= "false" >
                        <p class="column-tag" v-for="(column,index) in o.columns" :key="index" :title="column.name" @dragend="endDraggable(o.datasetName,column.name)"><i class="el-icon-copy-document" title="复制" @click="copyColumn(o.datasetName,column.name)"></i>{{column.name}}</p>
                    </vuedraggable>
                    <el-input v-show="o.apiResult" type="textarea" placeholder="" v-model="o.apiResult" rows="6"></el-input>
                </div>
            </div>
        </div>
        <div class="center">
          <div style="display:none">
            <input id="uploadBtn" type="file" accept="xlsx/*"  @change="loadExcel" />
          </div>
          <vuedraggable class="wrapper" :sort="false" style="width:100%;height:100%;overflow:auto;" :disabled="true">
                <div id="luckysheet" style="width:100%;height:100%;left: 0px;overflow:auto;"></div>
            </vuedraggable>
        </div>
        <div class="right">
            <!-- <div class="right-head">
                <i class="el-icon-s-unfold"></i>
            </div> -->
            <div class="right-title">
                <span :class="tabIndex==1?'cell-property':'cell-property cell-property-noactive'" @click="clickTab(1)">单元格属性</span>
                <span :class="tabIndex==2?'cell-property':'cell-property cell-property-noactive'" @click="clickTab(2)">报表属性</span>
            </div>
            <div class="right-form">
                <el-form :inline="true" class="demo-form-inline" :model="cellForm" ref="reportCellForm" v-show="tabIndex==1">
                      <el-form-item label="扩展方向">
                          <el-select  style="width:150px" placeholder="扩展方向" size="small" v-model="cellForm.cellExtend" @change="changeCellAttr('cellExtend')" :disabled="attrDisabled">
                            <el-option label="不扩展" value="1"></el-option>
                            <el-option label="向右扩展" value="2"></el-option>
                            <el-option label="向下扩展" value="3"></el-option>
                          </el-select>
                      </el-form-item>
                      <el-form-item label="聚合方式" v-show="cellForm.cellExtend != 4">
                          <el-select  style="width:150px" placeholder="聚合方式" size="small" v-model="cellForm.aggregateType" @change="changeCellAttr('aggregateType')" :disabled="attrDisabled">
                            <el-option label="列表" value="list"></el-option>
                            <el-option label="分组" value="group"></el-option>
                            <el-option label="分组汇总" value="groupSummary"></el-option>
                            <el-option label="汇总" value="summary"></el-option>
                          </el-select>
                      </el-form-item>
                      <el-form-item label="分组属性" v-show="cellForm.aggregateType=='group' || cellForm.aggregateType=='groupSummary'">
                        <el-input v-model="cellForm.groupProperty" style="width:125px" size="small" placeholder="分组属性" @input="changeCellAttr('groupProperty')" :disabled="attrDisabled"></el-input>
                    </el-form-item>
                      <el-form-item label="数据来源">
                          <el-select  style="width:150px" placeholder="数据来源" size="small" v-if="cellForm.cellExtend != 4" v-model="cellForm.dataFrom" @change="changeCellAttr('dataFrom')" :disabled="attrDisabled">
                            <el-option label="默认" :value=1></el-option>
                            <el-option label="原始数据" :value=2></el-option>
                            <el-option label="单元格" :value=3></el-option>
                          </el-select>
                      </el-form-item>
                    <el-form-item label="来源单元格" v-show="cellForm.dataFrom==3">
                        <el-input v-model="cellForm.dependencyCell" style="width:125px" size="small" placeholder="来源单元格" @input="changeCellAttr('dependencyCell')" :disabled="attrDisabled"></el-input>
                        <el-alert title="填写单元格坐标,如B1" type="success" :closable="false" style="height:30px;width:140px;padding:0"></el-alert>
                    </el-form-item>
                    <el-form-item label="汇总方式" size="small" v-show="(cellForm.aggregateType=='summary' || cellForm.aggregateType=='groupSummary') && cellForm.dataFrom!=4">
                        <el-select v-model="cellForm.cellFunction" style="width:150px" placeholder="汇总方式" @change="changeCellAttr('cellFunction')" :disabled="attrDisabled">
                        <el-option label="合计" value="1"></el-option>
                        <el-option label="平均值" value="2"></el-option>
                        <el-option label="最大值" value="3"></el-option>
                        <el-option label="最小值" value="4"></el-option>
                        <el-option label="计数" value="5"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="是否数值单位转换" size="small">
                        <el-switch
                        v-model="cellForm.unitTransfer"
                        active-text="是"
                        inactive-text="否" @change="changeCellAttr('unitTransfer')"  :disabled="attrDisabled">
                    </el-switch>
                    </el-form-item>
                    <el-form-item label="转换方式" size="small" v-show="cellForm.unitTransfer">
                        <el-select v-model="cellForm.transferType" style="width:150px" placeholder="转换方式" @change="changeCellAttr('transferType')" :disabled="attrDisabled">
                        <el-option label="乘法" :value=1></el-option>
                        <el-option label="除法" :value=2></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="倍数" size="small" v-show="cellForm.unitTransfer">
                        <el-select v-model="cellForm.multiple" style="width:150px" placeholder="倍数" @change="changeCellAttr('multiple')" :disabled="attrDisabled">
                        <el-option label="10" value="10"></el-option>
                        <el-option label="100" value="100"></el-option>
                        <el-option label="1000" value="1000"></el-option>
                        <el-option label="10000" value="10000"></el-option>
                        <el-option label="100000" value="100000"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="小数位数" size="small" v-show="(cellForm.aggregateType=='summary' || cellForm.aggregateType=='groupSummary' || cellForm.unitTransfer) && cellForm.dataFrom!=4">
                        <el-input v-model="cellForm.digit" style="width:150px"  placeholder="小数位数" @input="changeCellAttr('digit')" :disabled="attrDisabled"></el-input>
                    </el-form-item>
                    <el-form-item label="是否允许修改" size="small">
                        <el-switch
                        v-model="cellForm.allowEdit"
                        active-text="是"
                        inactive-text="否" @change="changeCellAttr('allowEdit')" :disabled="attrDisabled">
                        </el-switch>
                      </el-form-item>
                       <el-form-item label="值类型">
                          <el-select  style="width:150px" placeholder="值类型" size="small" v-model="cellForm.valueType" @change="changeCellAttr('valueType')" :disabled="attrDisabled">
                            <el-option label="文本" value="1"></el-option>
                            <el-option label="数值" value="2"></el-option>
                            <el-option label="日期时间" value="3"></el-option>
                            <el-option label="下拉单选" value="4"></el-option>
                          </el-select>
                      </el-form-item>
                      <el-form-item label="必填项" size="small">
                        <el-switch
                        v-model="cellForm.require"
                        active-text="是"
                        inactive-text="否" @change="changeCellAttr('require')" :disabled="attrDisabled">
                        </el-switch>
                      </el-form-item>
                      <el-form-item label="长度校验" size="small" v-show="cellForm.valueType == '1'">
                        <el-switch
                        v-model="cellForm.lengthValid"
                        active-text="是"
                        inactive-text="否" @change="changeCellAttr('lengthValid')" :disabled="attrDisabled">
                        </el-switch>
                      </el-form-item>
                      <el-form-item label="最小长度" size="small" v-show="cellForm.lengthValid && cellForm.valueType == '1'">
                        <el-input v-model="cellForm.minLength" style="width:150px"  placeholder="最小长度" @input="changeCellAttr('minLength')" :disabled="attrDisabled"></el-input>
                      </el-form-item>
                      <el-form-item label="最大长度" size="small" v-show="cellForm.lengthValid && cellForm.valueType == '1'">
                        <el-input v-model="cellForm.maxLength" style="width:150px"  placeholder="最大长度" @input="changeCellAttr('maxLength')" :disabled="attrDisabled"></el-input>
                      </el-form-item>
                      <el-form-item label="校验规则" v-show="cellForm.valueType == '1'">
                          <el-select  style="width:150px" placeholder="校验规则" size="small" v-model="cellForm.textValidRule" @change="changeCellAttr('textValidRule')" :disabled="attrDisabled">
                            <el-option label="无" value='0'></el-option>
                            <el-option label="邮箱" value='1'></el-option>
                            <el-option label="手机号" value='2'></el-option>
                            <el-option label="座机号" value='3'></el-option>
                            <el-option label="身份证" value='4'></el-option>
                            <el-option label="自定义" value='99'></el-option>
                          </el-select>
                      </el-form-item>
                      <el-form-item label="正则表达式" size="small" v-show="cellForm.textValidRule == '99' && cellForm.valueType == '1'">
                        <el-input v-model="cellForm.regex" style="width:140px"  placeholder="正则表达式" @input="changeCellAttr('regex')" :disabled="attrDisabled"></el-input>
                      </el-form-item>
                      <el-form-item label="大小校验" size="small" v-show="cellForm.valueType == '2'">
                        <el-switch
                        v-model="cellForm.numberRangeValid"
                        active-text="是"
                        inactive-text="否" @change="changeCellAttr('numberRangeValid')" :disabled="attrDisabled">
                        </el-switch>
                      </el-form-item>
                      <el-form-item label="最小值" size="small" v-show="cellForm.valueType == '2' && cellForm.numberRangeValid">
                        <el-input v-model="cellForm.minValue" style="width:150px"  placeholder="最小值" @input="changeCellAttr('minValue')" :disabled="attrDisabled"></el-input>
                      </el-form-item>
                      <el-form-item label="最大值" size="small" v-show="cellForm.valueType == '2' && cellForm.numberRangeValid">
                        <el-input v-model="cellForm.maxValue" style="width:150px"  placeholder="最大值" @input="changeCellAttr('maxValue')" :disabled="attrDisabled"></el-input>
                      </el-form-item>
                      <el-form-item label="小数位数" size="small" v-show="cellForm.valueType == '2'">
                        <el-input v-model="cellForm.digit" style="width:150px"  placeholder="小数位数" @input="changeCellAttr('digit')" :disabled="attrDisabled"></el-input>
                      </el-form-item>
                      <el-form-item label="格式" size="small" v-show="cellForm.valueType == '3'">
                        <!-- <el-input v-model="cellForm.dateFormat" style="width:150px"  placeholder="日期格式" @input="changeCellAttr('dateFormat')"></el-input> -->
                         <el-select  style="width:150px" placeholder="日期格式" size="small" v-model="cellForm.dateFormat" @change="changeCellAttr('dateFormat')" :disabled="attrDisabled">
                            <el-option label="年-月-日" value='yyyy-MM-dd'></el-option>
                            <el-option label="年-月" value='yyyy-MM'></el-option>
                            <el-option label="年" value='yyyy'></el-option>
                            <el-option label="年-月-日 时:分:秒" value='yyyy-MM-dd HH:mm:ss'></el-option>
                            <el-option label="年-月-日 时:分" value='yyyy-MM-dd HH:mm'></el-option>
                            <el-option label="时:分:秒" value='HH:mm:ss'></el-option>
                            <el-option label="时:分" value='HH:mm'></el-option>
                          </el-select>
                      </el-form-item>
                      <el-form-item label="数据源" v-show="cellForm.valueType == '4'">
                          <el-select  style="width:150px" placeholder="数据源" size="small" v-model="cellForm.datasourceId" @change="changeCellAttr('datasourceId')" :disabled="attrDisabled">
                            <el-option v-for="op in dataSource" :label="op.dataSourceName" :value="op.datasourceId" :key="op.datasourceId"></el-option>
                          </el-select>
                      </el-form-item>
                      <el-form-item label="字典类型" v-show="cellForm.valueType == '4'">
                          <el-select  style="width:150px" placeholder="字典类型" size="small" v-model="cellForm.dictType" @change="changeCellAttr('dictType')" :disabled="attrDisabled">
                            <el-option v-for="op in dictTypes" :label="op.dictType" :value="op.dictType" :key="op.id"></el-option>
                          </el-select>
                      </el-form-item>
                      <el-form-item label="与其他单元格比较" size="small">
                        <el-switch
                        v-model="cellForm.otherCellCompare"
                        active-text="是"
                        inactive-text="否" @change="changeCellAttr('otherCellCompare')" :disabled="attrDisabled">
                        </el-switch>
                      </el-form-item>
                      <div class="right-dataset-title" v-if="cellForm.otherCellCompare">
                        <span class="attr-dataset-title">比较单元格</span>
                        <el-button class="addBtn" @click="addCompareCells" :disabled="attrDisabled">添加<i class="el-icon-plus el-icon--right"></i></el-button>
                     </div>
                     <el-collapse v-if="cellForm.otherCellCompare && cellForm.compareCells && cellForm.compareCells.length > 0">
                        <el-collapse-item v-for="(o,index) in cellForm.compareCells" :key="index">
                            <template slot="title">
                                比较单元格{{index+1}}
                                 <el-button class="right-block-el-icon-edit" title="编辑" type="primary" icon="el-icon-edit" circle size="mini" @click.stop="editCompareCell(o,index)" :disabled="attrDisabled"></el-button>
                                 <el-button class="right-el-icon-delete" type="danger" title="删除" icon="el-icon-delete" circle size="mini" @click.stop="deleteCompareCell(index)" :disabled="attrDisabled"></el-button>
                            </template>
                            <p class="column-tag" :title="o.sheetName" style="min-width:220px;max-width:220px">sheet名称：{{o.sheetName}}</p>
                            <p class="column-tag" style="min-width:220px;max-width:220px">坐标：{{o.coordinate}}</p>
                            <p class="column-tag" style="min-width:220px;max-width:220px">单元格类型：{{commonUtil.getDictionaryValueName('cellType',o.cellType)}}</p>
                            <p class="column-tag" style="min-width:220px;max-width:220px">比较类型：{{o.compareType}}</p>
                        </el-collapse-item>
                    </el-collapse>
                    <!-- <el-form-item label="是否预警" size="small">
                        <el-switch
                        v-model="cellForm.warning"
                        active-text="是"
                        inactive-text="否" @change="changeCellAttr('warning')" :disabled="attrDisabled">
                    </el-switch>
                    </el-form-item>
                    <el-form-item label="预警规则" v-show="cellForm.warning">
                          <el-select  style="width:150px" placeholder="预警规则" size="small" v-model="cellForm.warningRules" @change="changeCellAttr('warningRules')" :disabled="attrDisabled">
                            <el-option label="大于" value=">"></el-option>
                            <el-option label="大于等于" value=">="></el-option>
                            <el-option label="等于" value="="></el-option>
                            <el-option label="小于" value="<"></el-option>
                            <el-option label="小于等于" value="<="></el-option>
                          </el-select>
                      </el-form-item>
                    <el-form-item label="预警阈值" size="small" v-show="cellForm.warning">
                        <el-input v-model="cellForm.threshold" style="width:150px"  placeholder="预警阈值" @input="changeCellAttr('threshold')" :disabled="attrDisabled"></el-input>
                    </el-form-item>
                    <el-form-item label="预警颜色" size="small" v-show="cellForm.warning">
                        <el-color-picker v-model="cellForm.warningColor" size="small" :predefine="commonConstants.predefineColors" @change="changeCellAttr('warningColor')" :disabled="attrDisabled"></el-color-picker>
                    </el-form-item>
                    <el-form-item label="预警内容" size="small" v-show="cellForm.warning">
                        <el-input type="textarea" :rows="4" v-model="cellForm.warningContent" style="width:150px"  placeholder="预警内容" @input="changeCellAttr('warningContent')" :disabled="attrDisabled"></el-input>
                    </el-form-item> -->
                    <el-form-item label="是否下钻" size="small">
                        <el-switch
                            v-model="cellForm.isDrill"
                            active-text="是"
                            inactive-text="否" @change="changeCellAttr('isDrill')" :disabled="attrDisabled">
                        </el-switch>
                    </el-form-item>
                    <el-form-item label="下钻报表" v-show="cellForm.isDrill">
                          <el-select  style="width:150px" placeholder="数据源" size="small" filterable clearable v-model="cellForm.drillId" @change="changeCellAttr('drillId')" :disabled="attrDisabled">
                            <el-option v-for="op in reportTpls" :label="op.tplName" :value="op.id" :key="op.id"></el-option>
                          </el-select>
                      </el-form-item>
                      <el-form-item label="参数属性" size="small" v-show="cellForm.isDrill">
                        <el-input type="textarea" v-model="cellForm.drillAttrs" style="width:150px"  placeholder="多个属性用,分割" @input="changeCellAttr('drillAttrs')" :disabled="attrDisabled"></el-input>
                    </el-form-item>
                    <div class="right-dataset-title">
                        <span class="attr-dataset-title">单元格过滤条件</span>
                        <el-button class="addBtn" @click="addCellConditions" :disabled="attrDisabled">添加<i class="el-icon-plus el-icon--right"></i></el-button>
                    </div>
                    <el-radio v-model="cellForm.filterType" label="and" @change="changeCellAttr('filterType')" :disabled="attrDisabled">AND</el-radio>
                    <el-radio v-model="cellForm.filterType" label="or" @change="changeCellAttr('filterType')" :disabled="attrDisabled">OR</el-radio>
                    <el-collapse v-if="cellForm.cellconditions && cellForm.cellconditions.length > 0">
                        <el-collapse-item v-for="(o,index) in cellForm.cellconditions" :key="index">
                            <template slot="title">
                                过滤条件{{index+1}}
                                 <el-button class="right-el-icon-edit" title="编辑" type="primary" icon="el-icon-edit" circle size="mini" @click.stop="editCellCondition(index)" :disabled="attrDisabled"></el-button>
                                 <el-button class="right-el-icon-copy-document" title="复制" type="warning" icon="el-icon-copy-document" circle size="mini" @click.stop="copyCellCondition(o)" :disabled="attrDisabled"></el-button>
                                 <el-button class="right-el-icon-delete" type="danger" title="删除" icon="el-icon-delete" circle size="mini" @click.stop="deleteCellCondition(index)" :disabled="attrDisabled"></el-button>
                            </template>
                            <p class="column-tag" :title="o.property" style="min-width:220px;max-width:220px">属性：{{o.property}}</p>
                            <p class="column-tag" style="min-width:220px;max-width:220px">操作符：{{o.operator}}</p>
                            <p class="column-tag" style="min-width:220px;max-width:220px">数据类型：<label v-if="o.type=='varchar'">字符串</label>
                            <label v-else-if="o.type=='number'">数值</label><label v-else>日期</label></p>
                            <p class="column-tag" :title="o.value" style="min-width:220px;max-width:220px">条件值：{{o.value}}</p>
                            <p class="column-tag" :title="o.dateFormat" v-if="o.type == 'date'" style="min-width:220px;max-width:220px">日期格式：{{o.dateFormat}}</p>
                        </el-collapse-item>
                    </el-collapse>
                  </el-form>
                  <div v-show="tabIndex==2" class="demo-form-inline">
                    <el-switch
                        v-model="isParamMerge"
                        active-text="参数合并"
                        inactive-text="参数不合并"
                         @change="changeParamMerge">
                    </el-switch>
                  </div>
            </div>
        </div>
        </div>
        <el-dialog title="数据集" :visible.sync="addDatasetsDialogVisiable" width="80%" height="80%" top="20px" :close-on-click-modal='false' @close='closeAddDataSet'>
              <el-tabs type="border-card">
                  <el-tab-pane label="sql语句">
                  <div>
                      <el-form :inline="true" :model="sqlForm" class="demo-form-inline" ref="sqlRef">
                      <el-form-item label="数据集名称"  prop="datasetName" :rules="filter_rules('数据集名称',{required:true})">
                          <el-input v-model="sqlForm.datasetName" placeholder="数据集名称" size="small"></el-input>
                      </el-form-item>
                      <el-form-item  label="选择数据源" prop="datasourceId" :rules="filter_rules('选择数据源',{required:true})">
                          <el-select v-model="sqlForm.datasourceId" placeholder="选择数据源" size="small" @change="changeDatasource">
                              <el-option v-for="op in dataSource" :label="op.dataSourceName" :value="op.datasourceId" :key="op.datasourceId"></el-option>
                          </el-select>
                          </el-form-item>
                          <el-form-item  label="sql类型" prop="sqlType" :rules="filter_rules('sql类型',{required:true})" v-if="datasourceType == 1">
                          <el-select v-model="sqlForm.sqlType" placeholder="选择sql类型" size="small">
                              <el-option v-for="op in selectUtil.sqlType" :label="op.label" :value="op.value" :key="op.value"></el-option>
                          </el-select>
                          </el-form-item><br>
                          <el-form-item  label="系统变量">
                             <p class="column-tag" v-for="(item,index) in commonConstants.systemParam" :key="index" ><i class="el-icon-copy-document" title="复制" @click="doCopy(item)">{{item.label}}({{item.value}})</i></p> 
                          </el-form-item>
                      </el-form>

                  <div style="height:25px;" v-if="datasourceType == 1">
                  <el-tooltip content="该操作将执行sql语句并校验sql语句的正确性，并将查询字段全部显示到下方的表格中" placement="bottom"><el-tag type="success" @click="execSql" size="small" style="cursor:pointer" ><i class="el-icon-caret-right"></i>执行</el-tag></el-tooltip>
                  <el-tooltip content="该操作会将sql语句进行格式化并显示" placement="right"><el-tag @click="formatSql" size="small" style="cursor:pointer"><i class="el-icon-document"></i>格式化</el-tag> </el-tooltip>
                  </div>
                  <div style="height:300px;" v-if="datasourceType == 1">
                  <codemirror ref="codeMirror"  :options="codemirrorOptions"></codemirror>
                  </div>
                  <div style="height:1px"></div>
                  <div>
                      <!--表格 start-->
                      <el-table :data="sqlColumnTableData.tableData.slice((sqlColumnTableData.tablePage.currentPage-1)*sqlColumnTableData.tablePage.pageSize,sqlColumnTableData.tablePage.currentPage*sqlColumnTableData.tablePage.pageSize)" border style="width: 100%" align="center" size="small" height="230px" :header-cell-style="{background:'#eef1f6',color:'#606266'}">
                      <el-table-column prop="columnName" label="列名"  align="center"></el-table-column>
                      <el-table-column prop="name" label="别名"  align="center"></el-table-column>
                      <el-table-column prop="dataType" label="数据类型"  align="center"></el-table-column>
                      <el-table-column prop="width" label="宽度"  align="center"></el-table-column>
                          </el-table>
                          <!--表格 end-->
                          <!--分页 start-->
                          <el-pagination
                          @current-change="handleCurrentChange"
                          @size-change="handleSizeChange"
                          :current-page="sqlColumnTableData.tablePage.currentPage"
                          :page-sizes="sqlColumnTableData.tablePage.pageSizeRange"
                          :page-size="sqlColumnTableData.tablePage.pageSize"
                          layout="total, sizes, prev, pager, next, jumper"
                          :total="sqlColumnTableData.tablePage.pageTotal">
                          </el-pagination>
                          <!--分页 end-->
                  </div>
                  </div>
                  </el-tab-pane>
                  <el-tab-pane label="参数配置" >
                      <div v-show="sqlForm.sqlType == '1' || datasourceType == 2">
                      <el-divider content-position="left" v-if="datasourceType == 1 || datasourceType == 2">分页参数</el-divider>
                      <el-form :inline="true" :model="paginationForm" class="demo-form-inline" ref="paginationRef">
                          <el-form-item label="是否分页"  prop="isPagination" v-if="datasourceType == 1 || datasourceType == 2">
                              <el-select v-model="paginationForm.isPagination" placeholder="是否分页" size="small">
                              <el-option label="是" :value=1></el-option>
                              <el-option label="否" :value=2></el-option>
                              </el-select>
                          </el-form-item>
                          <el-form-item v-if="paginationForm.isPagination == '1'" label="每页条数"  prop="pageCount" :rules="filter_rules('每页条数',{required:true})">
                              <!-- <el-input v-model="paginationForm.pageCount" placeholder="每页条数" size="small"></el-input> -->
                              <el-select v-model="paginationForm.pageCount" placeholder="请选择" size="small">
                                <el-option
                                    v-for="item in selectUtil.pageCount"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                                </el-select>
                          </el-form-item>
                          <el-form-item v-if="paginationForm.isPagination == '1' && datasourceType == 2" label="当前页参数属性"  prop="currentPageAttr" :rules="filter_rules('当前页参数属性',{required:true})">
                                <el-input v-model="paginationForm.currentPageAttr" placeholder="当前页参数属性" size="small"></el-input>
                          </el-form-item>
                          <el-form-item v-if="paginationForm.isPagination == '1' && datasourceType == 2" label="每页条数参数属性"  prop="pageCountAttr" :rules="filter_rules('每页条数参数属性',{required:true})">
                                <el-input v-model="paginationForm.pageCountAttr" placeholder="每页条数参数属性" size="small"></el-input>
                          </el-form-item>
                          <el-form-item v-if="paginationForm.isPagination == '1' && datasourceType == 2" label="总条数属性"  prop="totalAttr" :rules="filter_rules('总条数条数属性',{required:true})">
                                <el-input v-model="paginationForm.totalAttr" placeholder="总条数属性" size="small"></el-input>
                          </el-form-item>
                      </el-form>
                      </div>
                      <div v-show="sqlForm.sqlType == 1 || datasourceType == 2">
                      <el-divider content-position="left">字段参数</el-divider>
                      <el-form :inline="true" :model="paramForm" class="demo-form-inline" ref="paramRef">
                          <el-form-item label="参数名称"  prop="paramName" :rules="filter_rules('参数名称',{required:true})">
                          <el-input v-model="paramForm.paramName" placeholder="参数名称" size="small"></el-input>
                          </el-form-item>
                          <el-form-item label="参数编码"  prop="paramCode" :rules="filter_rules('参数编码',{required:true})">
                          <el-input v-model="paramForm.paramCode" placeholder="参数编码" size="small"></el-input>
                          </el-form-item>
                          <el-form-item label="参数类型" prop="paramType" :rules="filter_rules('参数类型',{required:true})">
                          <el-select v-model="paramForm.paramType" placeholder="参数类型"  size="small">
                              <el-option label="字符串" value="varchar"></el-option>
                              <el-option label="数值" value="number"></el-option>
                              <el-option label="日期" value="date"></el-option>
                              <el-option label="下拉单选" value="select"></el-option>
                              <el-option label="下拉多选" value="mutiselect"></el-option>
                              <el-option label="下拉树(单选)" value="treeSelect"></el-option>
                              <el-option label="下拉树(多选)" value="multiTreeSelect"></el-option>
                          </el-select>
                          </el-form-item>
                          <el-form-item label="默认值">
                          <el-input v-model="paramForm.paramDefault" placeholder="默认值" size="small"></el-input>
                          </el-form-item>
                          <el-form-item label="是否必填" prop="paramRequired" :rules="filter_rules('参数必填',{required:true})">
                          <el-select v-model="paramForm.paramRequired" placeholder="是否必填" size="small">
                              <el-option label="是" value="1"></el-option>
                              <el-option label="否" value="2"></el-option>
                          </el-select>
                          </el-form-item>
                          <el-form-item label="是否隐藏" prop="paramHidden" :rules="filter_rules('是否隐藏',{required:true})" key="paramHidden">
                          <el-select v-model="paramForm.paramHidden" placeholder="是否隐藏" size="small">
                              <el-option label="是" value="1"></el-option>
                              <el-option label="否" value="2"></el-option>
                          </el-select>
                          </el-form-item>
                          <el-form-item v-if="paramForm.paramType == 'select' || paramForm.paramType == 'mutiselect'" label="选择内容来源" key="selectType" prop="selectType" :rules="filter_rules('选择内容来源',{required:true})">
                          <el-select v-model="paramForm.selectType" placeholder="选择内容来源" size="small">
                              <el-option label="自定义" value="1"></el-option>
                              <el-option label="sql语句" value="2"></el-option>
                          </el-select>
                          </el-form-item>
                          <el-form-item v-if="(paramForm.paramType == 'select') && paramForm.selectType == '2'" label="是否依赖其他参数" prop="isRelyOnParams" key="isRelyOnParams" :rules="filter_rules('是否依赖其他参数',{required:true})">
                          <el-select v-model="paramForm.isRelyOnParams" placeholder="是否依赖其他参数" size="small">
                              <el-option label="是" value="1"></el-option>
                              <el-option label="否" value="2"></el-option>
                          </el-select>
                          </el-form-item>
                          <el-form-item v-if="(paramForm.paramType == 'select') && paramForm.selectType == '2' && paramForm.isRelyOnParams == '1'" label="依赖参数代码" prop="relyOnParams" key="relyOnParams" :rules="filter_rules('依赖参数代码',{required:true})">
                            <el-input v-model="paramForm.relyOnParams" placeholder="依赖参数代码" size="small"></el-input>
                          </el-form-item>
                          <el-form-item v-if="paramForm.paramType == 'multiTreeSelect'" label="父子联动" prop="checkStrictly" key="checkStrictly" :rules="filter_rules('父子联动',{required:true})">
                          <el-select v-model="paramForm.checkStrictly" placeholder="选择父子联动" size="small">
                              <el-option label="是" value="1"></el-option>
                              <el-option label="否" value="2"></el-option>
                          </el-select>
                          </el-form-item>
                          <el-form-item v-if="paramForm.paramType == 'select' || paramForm.paramType == 'mutiselect' || paramForm.paramType == 'treeSelect' || paramForm.paramType == 'multiTreeSelect'" key="selectContent" label="下拉选择内容" prop="selectContent" :rules="filter_rules('下拉选择内容',{required:true})">
                          <el-input type="textarea" :cols="80" v-model="paramForm.selectContent" placeholder="下拉选择内容" size="small"></el-input>
                          <div class="sub-title">{{selectContentSuggestion}}</div>
                          </el-form-item>
                          <el-form-item>
                            <el-button type="primary" @click="addParam" size="small">添加</el-button>
                          </el-form-item>
                          <el-tag v-if="paramForm.paramType == 'date'" type="warning">注：当参数类型选择日期时，如果想让默认日期是当前日期，则默认值填写current或者CURRENT，如果想让默认日期是当前日期的天几天或者后几天，则填天数，例如前七天则填写-7，后七天则填写7。</el-tag>
                          <el-tag v-if="paramForm.paramType == 'select' || paramForm.paramType == 'mutiselect'" type="warning">自定义数据格式：[{"value":"value1","name":"name1"},{"value":"value2","name":"name2"}] 注意：两个key必须是value 和 name</el-tag><br v-if="paramForm.paramType == 'select' || paramForm.paramType == 'mutiselect'">
                          <el-tag v-if="paramForm.paramType == 'select' || paramForm.paramType == 'mutiselect'" type="warning">sql语句格式：select code as value, name as name from table 注意：返回的属性中必须有 value 和 name</el-tag>
                          <el-tag v-if="paramForm.paramType == 'treeSelect' || paramForm.paramType == 'multiTreeSelect'" type="warning">sql语句格式：select deptId as id, deptName as name,parentId as pid from table 注意：返回的属性中必须有 id,name和pid</el-tag>
                      </el-form>
                      <div style="height:2px"></div>
                      <div style="height:50%">
                      <!--表格 start-->
                      <el-table :data="paramTableData.tableData" border style="width: 98%" align="center" size="small" height="230px" :header-cell-style="{background:'#eef1f6',color:'#606266'}">
                      <el-table-column prop="paramName" label="参数名"  align="center"></el-table-column>
                      <el-table-column prop="paramCode" label="参数编码"  align="center"></el-table-column>
                      <el-table-column prop="paramType" label="参数类型"  align="center"></el-table-column>
                      <el-table-column prop="paramDefault" label="默认值"  align="center"></el-table-column>
                      <el-table-column prop="paramRequired" label="是否必填"  align="center" :formatter="commonUtil.formatterTableValue"></el-table-column>
                      <el-table-column prop="isRelyOnParams" label="是否依赖其他参数"  align="center" :formatter="commonUtil.formatterTableValue"></el-table-column>
                      <el-table-column prop="relyOnParams" label="依赖参数"  align="center"></el-table-column>
                      <el-table-column  label="操作"  align="center">
                          <template slot-scope="scope">
                              <el-button @click="editParam(scope.row)" type="text" size="small">编辑</el-button>
                              <el-button @click="deleteParam(scope.$index)" type="text" size="small">删除</el-button>
                          </template>
                      </el-table-column>
                      </el-table>
                          <!--表格 end-->
                      </div>
                      </div>
                      <div v-show="sqlForm.sqlType == 2 && datasourceType == 1">
                          <el-divider content-position="left">输入参数</el-divider>
                          <el-form :inline="true" :model="procedureParamForm" class="demo-form-inline" ref="inParamRef">
                              <el-form-item label="参数名称"  prop="paramName" :rules="filter_rules('参数名称',{required:true})">
                                  <el-input v-model="procedureParamForm.paramName" placeholder="参数名称" ></el-input>
                              </el-form-item>
                              <el-form-item label="参数编码"  prop="paramCode" :rules="filter_rules('参数编码',{required:true})">
                                  <el-input v-model="procedureParamForm.paramCode" placeholder="参数编码"></el-input>
                              </el-form-item>
                              <el-form-item label="参数类型" prop="paramType" :rules="filter_rules('参数类型',{required:true})">
                              <el-select v-model="procedureParamForm.paramType" placeholder="参数类型"  >
                                  <el-option label="int" value="int"></el-option>
                                  <el-option label="String" value="String"></el-option>
                                  <el-option label="Long" value="Long"></el-option>
                                  <el-option label="BigDecimal" value="BigDecimal"></el-option>
                                  <el-option label="Double" value="Double"></el-option>
                                  <el-option label="Float" value="Float"></el-option>
                                  <el-option label="Date" value="Date"></el-option>
                              </el-select>
                              </el-form-item>
                              <el-form-item label="默认值" prop="paramDefault" :rules="filter_rules('默认值',{required:true})">
                                  <el-input v-model="procedureParamForm.paramDefault" placeholder="默认值"></el-input>
                              </el-form-item>
                              <el-form-item>
                              <el-button type="primary" @click="addInParam">添加</el-button>
                              </el-form-item>
                          </el-form>
                          <div style="height:40%">
                              <!--表格 start-->
                              <el-table :data="procedureInParamTableData.tableData" border style="width: 100%" align="center" size="small" height="230px" :header-cell-style="{background:'#eef1f6',color:'#606266'}">
                              <el-table-column prop="paramName" label="参数名"  align="center"></el-table-column>
                              <el-table-column prop="paramCode" label="参数编码"  align="center"></el-table-column>
                              <el-table-column prop="paramType" label="参数类型"  align="center"></el-table-column>
                              <el-table-column prop="paramDefault" label="默认值"  align="center"></el-table-column>
                              <el-table-column fixed="right" label="操作" width="180" align="center">
                                  <template slot-scope="scope">
                                      <el-button @click="editInParam(scope.row)" type="text" size="small">编辑</el-button>
                                      <el-button @click="moveUp(scope.$index,'1')" type="text" size="small">上移</el-button>
                                      <el-button @click="moveDown(scope.$index,'1')" type="text" size="small">下移</el-button>
                                      <el-button @click="deleteInParam(scope.$index)" type="text" size="small">删除</el-button>
                                  </template>
                              </el-table-column>
                              </el-table>
                              <!--表格 end-->
                          </div>
                          <el-divider content-position="left">输出参数</el-divider>
                          <el-form :inline="true" :model="procedureOutParamForm" class="demo-form-inline" ref="outParamRef">
                              <el-form-item label="参数名称"  prop="paramName" :rules="filter_rules('参数名称',{required:true})">
                                  <el-input v-model="procedureOutParamForm.paramName" placeholder="参数名称" ></el-input>
                              </el-form-item>
                              <el-form-item label="参数编码"  prop="paramCode" :rules="filter_rules('参数编码',{required:true})">
                                  <el-input v-model="procedureOutParamForm.paramCode" placeholder="参数编码"></el-input>
                              </el-form-item>
                              <el-form-item label="参数类型" prop="paramType" :rules="filter_rules('参数类型',{required:true})">
                              <el-select v-model="procedureOutParamForm.paramType" placeholder="参数类型">
                                  <el-option label="VARCHAR" value="VARCHAR"></el-option>
                                  <el-option label="INTEGER" value="INTEGER"></el-option>
                                  <el-option label="BIGINT" value="BIGINT"></el-option>
                                  <el-option label="FLOAT" value="FLOAT"></el-option>
                                  <el-option label="DOUBLE" value="DOUBLE"></el-option>
                                  <el-option label="DECIMAL" value="DECIMAL"></el-option>
                              </el-select>
                              </el-form-item>
                              <el-form-item>
                              <el-button type="primary" @click="addOutParam">添加</el-button>
                              </el-form-item>
                          </el-form>
                          <div style="height:30%">
                              <!--表格 start-->
                              <el-table :data="procedureOutParamTableData.tableData" border style="width: 100%" align="center" size="small" height="230px" :header-cell-style="{background:'#eef1f6',color:'#606266'}">
                              <el-table-column prop="paramName" label="参数名"  align="center"></el-table-column>
                              <el-table-column prop="paramCode" label="参数编码"  align="center"></el-table-column>
                              <el-table-column prop="paramType" label="参数类型"  align="center"></el-table-column>
                              <el-table-column fixed="right" label="操作" width="180" align="center">
                                  <template slot-scope="scope">
                                      <el-button @click="editOutParam(scope.row)" type="text" size="small">编辑</el-button>
                                      <el-button @click="moveUp(scope.$index,'2')" type="text" size="small">上移</el-button>
                                      <el-button @click="moveDown(scope.$index,'2')" type="text" size="small">下移</el-button>
                                      <el-button @click="deleteOutParam(scope.$index)" type="text" size="small">删除</el-button>
                                  </template>
                              </el-table-column>
                              </el-table>
                              <!--表格 end-->
                          </div>
                      </div>
                  </el-tab-pane>
                  </el-tabs>
                  <span slot="footer" class="dialog-footer">
                  <el-button @click="closeAddDataSet" size="small">取 消</el-button>
                  <el-button type="primary" @click="addDataSet" size="small">确 定</el-button>
                  </span>
              </el-dialog>
            <el-dialog v-drag2anywhere title="填报属性" :visible.sync="datasourceDialog" width="50%" top="5vh" :modal="false" custom-class="test" :close-on-click-modal='false' @close='closeDatasourceDialog'>
                <div class="table-box">
                    <div class="table-box-left">
                        <el-button type="primary" size="small" icon="el-icon-plus" @click="addDatasourceAttr">添加填报属性</el-button>
                        <div v-for="(o,index) in datasources" :key="index">
                        <div :class="o.isActive?'dataset-attr-active':'dataset-attr'" style="position:relative" >
                            <span class="dataset-name2" :title="o.name" @click="clickAttrName(o,index)">{{o.name}}</span>
                            <i class="el-icon-edit" @click="editDatasourceAttr(o)"></i>
                            <i class="el-icon-delete" @click="deleteDatasourceAttr(index)"></i>
                        </div>
                    </div>
                    </div>
                    <div class="table-box-right">
                        <div v-show="datasourceAttr.name">
                        <el-form :inline="true" :model="datasourceAttr" class="demo-form-inline">
                            <el-form-item label="数据源"  prop="datasourceId">
                                <el-select  style="width:150px" placeholder="数据源" size="small" v-model="datasourceAttr.datasourceId" @change="getDatabaseTables">
                                    <el-option v-for="op in dataSource" :label="op.dataSourceName" :value="op.datasourceId" :key="op.datasourceId"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="表"  prop="table">
                                <el-select  style="width:150px" placeholder="表" size="small" filterable v-model="datasourceAttr.table" @change="getTableColumns">
                                    <el-option v-for="op in dataSourceTables" :label="op.name" :value="op.value" :key="op.value"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-form>

                        <el-button type="primary" size="small" icon="el-icon-plus" @click="addDatasourceColumn">添加关联</el-button>
                        <el-table :data="datasourceAttr.tableDatas" border style="width: 100%" align="center" height="300" size="small" :header-cell-style="{background:'#eef1f6',color:'#606266'}">
                        <el-table-column prop="columnName" label="列"  align="center"></el-table-column>
                        <el-table-column prop="cellCoords" label="单元格坐标"  align="center"></el-table-column>
                        <el-table-column label="操作" align="center">
                            <template slot-scope="scope">
                                <el-button @click="deleteDatasourceColumn(scope.$index)" type="text" size="small">删除</el-button>
                            </template>
                        </el-table-column>
                        </el-table><br>
                        <el-button type="primary" size="small" icon="el-icon-plus" @click="addDatasourceKey">添加主键</el-button>
                        <el-table :data="datasourceAttr.keys" border style="width: 100%" height="200" align="center" size="small" :header-cell-style="{background:'#eef1f6',color:'#606266'}">
                        <el-table-column prop="columnName" label="主键属性"  align="center"></el-table-column>
                        <el-table-column prop="idType" label="主键规则"  align="center">
                            <template slot-scope="scope">
                                <span>
                                <span v-if="scope.row.idType == '1'">自定义填写</span>
                                <span v-else-if="scope.row.idType == '2'">雪花算法</span>
                                <span v-else>自增主键</span>
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" align="center">
                            <template slot-scope="scope">
                                <el-button @click="deleteDatasourceKey(scope.$index)" type="text" size="small">删除</el-button>
                            </template>
                        </el-table-column>
                        </el-table>
                        </div>
                    </div>
                </div>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="closeDatasourceDialog" size="small">取 消</el-button>
                    <el-button type="primary" @click="confirmAddDatasource" size="small">确 定</el-button>
                </span>
            </el-dialog>
            <el-dialog title="添加数据源绑定" :visible.sync="datasourceAttrDialog" width="50%" height="80%" :close-on-click-modal='false' @close='closeDatasourceAttr'>
                <el-form :inline="true" :model="datasourceAttrForm" class="demo-form-inline" ref="datasourceAttrRef">
                    <el-form-item label="绑定名称"  prop="name" :rules="filter_rules('绑定名称',{required:true})">
                        <el-input v-model="datasourceAttrForm.name" placeholder="绑定名称" size="small"></el-input>
                    </el-form-item>
                </el-form>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="closeDatasourceAttr" size="small">取 消</el-button>
                    <el-button type="primary" @click="confirmDatasourceAttr" size="small">确 定</el-button>
                </span>
            </el-dialog>
            <el-dialog v-drag2anywhere title="添加属性关联" :visible.sync="datasourceColumnDialog" width="50%" height="80%" :close-on-click-modal='false' @close='closeDatasourceColumn'>
                <el-form :inline="true" :model="datasourceColumnForm" class="demo-form-inline" ref="datasourceColumnRef">
                    <el-form-item label="列名"  prop="columnName">
                        <el-select  style="width:150px" placeholder="列名" size="small" filterable v-model="datasourceColumnForm.columnName" :rules="filter_rules('列名',{required:true})">
                            <el-option v-for="op in tableColumns" :label="op.columnName" :value="op.columnName" :key="op.columnName"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="单元格坐标"  prop="cellCoords" :rules="filter_rules('单元格坐标',{required:true})">
                        <el-input v-model="datasourceColumnForm.cellCoords" placeholder="单元格坐标" size="small"></el-input>
                    </el-form-item>
                </el-form>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="closeDatasourceColumn" size="small">取 消</el-button>
                    <el-button type="primary" @click="confirmDatasourceColumn" size="small">确 定</el-button>
                </span>
            </el-dialog>
            <el-dialog v-drag2anywhere title="添加主键" :visible.sync="datasourceKeyDialog" width="50%" height="80%" :close-on-click-modal='false' @close='closeDatasourceKey'>
                <el-form :inline="true" :model="datasourceKeyForm" class="demo-form-inline" ref="datasourceKeyRef">
                    <el-form-item label="主键列名"  prop="columnName">
                        <el-select  style="width:150px" placeholder="列名" filterable size="small" v-model="datasourceKeyForm.columnName" :rules="filter_rules('列名',{required:true})">
                            <el-option v-for="op in tableColumns" :label="op.columnName" :value="op.columnName" :key="op.columnName"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="主键生成规则"  prop="idType" :rules="filter_rules('主键生成规则',{required:true})">
                        <el-select  style="width:150px" placeholder="主键生成规则" size="small" v-model="datasourceKeyForm.idType">
                            <el-option label="自定义填写" value="1"></el-option>
                            <el-option label="雪花算法" value="2"></el-option>
                            <el-option label="自增主键" value="3"></el-option>
                          </el-select>
                    </el-form-item>
                </el-form>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="closeDatasourceKey" size="small">取 消</el-button>
                    <el-button type="primary" @click="confirmDatasourceKey" size="small">确 定</el-button>
                </span>
            </el-dialog>
            <el-dialog title="单元格比较" :visible.sync="cellCompareVisiable" width="50%" height="80%" :close-on-click-modal='false' @close='closeCompareCells'>
                <el-form :inline="true" :model="cellCompareForm" class="demo-form-inline" ref="compareRef">
                    <el-form-item label="sheet名称"  prop="sheetName" :rules="filter_rules('sheet名称',{required:true})">
                        <el-select  placeholder="sheet名称" size="small" v-model="cellCompareForm.sheetName" @focus="onfocuseSheet">
                            <el-option v-for="op in sheets" :label="op.label" :value="op.value" :key="op.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="坐标"  prop="coordinate" :rules="filter_rules('坐标',{required:true})">
                        <el-input v-model="cellCompareForm.coordinate" placeholder="坐标" size="small"></el-input>
                    </el-form-item>
                    <el-form-item label="单元格类型"  prop="cellType" :rules="filter_rules('单元格类型',{required:true})">
                        <el-select  placeholder="单元格类型" size="small" v-model="cellCompareForm.cellType">
                            <el-option v-for="op in selectUtil.cellType" :label="op.label" :value="op.value" :key="op.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="比较类型"  prop="compareType" :rules="filter_rules('比较类型',{required:true})">
                        <el-select  placeholder="比较类型" size="small" v-model="cellCompareForm.compareType">
                            <el-option v-for="op in selectUtil.operate" :label="op.label" :value="op.value" :key="op.value"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="closeCompareCells" size="small">取 消</el-button>
                    <el-button type="primary" @click="confirmAddCompareCells" size="small">确 定</el-button>
                </span>
            </el-dialog>
            <el-dialog title="单元格过滤" :visible.sync="cellConditionVisiable" width="50%" height="80%" :close-on-click-modal='false' @close='closeCellConditionDialog'>
                <el-form :inline="true" :model="cellConditionForm" class="demo-form-inline" ref="conditionRef">
                    <el-form-item label="属性"  prop="property" key="property" :rules="filter_rules('属性',{required:true})">
                        <el-input v-model="cellConditionForm.property" placeholder="属性" size="small"></el-input>
                    </el-form-item>
                    <el-form-item label="操作符"  prop="operator" key="operator" :rules="filter_rules('操作符',{required:true})">
                        <!-- <el-input v-model="cellConditionForm.operator" placeholder="操作符" size="small"></el-input> -->
                        <el-select  placeholder="操作符" size="small" v-model="cellConditionForm.operator">
                            <el-option v-for="op in selectUtil.operate" :label="op.label" :value="op.value" :key="op.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="数据类型"  prop="type" key="type" :rules="filter_rules('数据类型',{required:true})">
                        <el-select  placeholder="数据类型" size="small" v-model="cellConditionForm.type">
                            <el-option v-for="op in selectUtil.type" :label="op.label" :value="op.value" :key="op.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item v-if="cellConditionForm.type == 'date'" label="日期格式"  prop="dateFormat" key="dateFormat" :rules="filter_rules('日期格式',{required:false})">
                         <el-select  placeholder="日期格式" size="small" v-model="cellConditionForm.dateFormat">
                            <el-option v-for="op in selectUtil.dateFormat2" :label="op.label" :value="op.value" :key="op.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="条件值"  prop="value" key="value" :rules="filter_rules('条件值',{required:true})">
                        <el-input type="textarea" :rows="2" style="width:480px" v-model="cellConditionForm.value" placeholder="条件值" size="small"></el-input>
                    </el-form-item>
                </el-form>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="closeCellConditionDialog" size="small">取 消</el-button>
                    <el-button type="primary" @click="confirmAddCellCondition" size="small">确 定</el-button>
                </span>
            </el-dialog>
            <el-dialog :title="authTitle" :visible.sync="addAuthVisiable" width="650px" height="80%" custom-class="addauthdialog"  :modal="true" :close-on-click-modal='false' @close='closeAddAuth'>
                <el-form :inline="true" :model="addAuthForm" class="demo-form-inline" ref="addAuthRef">
                    <el-transfer
                        v-model="addAuthForm.userIds"
                        :data="authUsers"
                        :titles="['用户信息', '授权用户']"
                        :filterable="true"
                        :props="{key:'id',label:'userName'}"
                    >
                    </el-transfer>
                </el-form>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="closeAddAuth" size="small">取 消</el-button>
                    <el-button type="primary" @click="confirmAddAuth" size="small">确 定</el-button>
                </span>
            </el-dialog>
            <modal
                ref="settingRef"
                :modalConfig="settingModalConfig"
                :modalForm="settingModalForm"
                :modalData="settingFormData"
                :modalHandles="settingModalHandles"
                @closeModal="closeSettingModal()"
            ></modal>
            <el-dialog
            :modal="false"
            :close-on-click-modal='false'
            :title="authedRangeTitle"
            :visible.sync="authdialogVisible"
             @close="closeAuthDialog"
             custom-class="authdialog"
            width="253px"
            >
            <div class="el-dialog-div" v-if="authedRange && authedRange.length > 0" >
                <div v-for="(item,index) in authedRange" :key="index">
                <el-descriptions title="" :column="1" border>
                    <el-descriptions-item label="保护范围">{{item.rangeAxis}}</el-descriptions-item>
                    <el-descriptions-item label="授权人数" v-if="isCreator">{{item.userIds.length}}</el-descriptions-item>
                </el-descriptions>
                <div style="text-align:right;margin-top:5px" v-if="isCreator">
                <el-button type="primary" title="编辑" icon="el-icon-edit" circle size="mini" @click="editRange(item)"></el-button>
                    <el-button type="warning" icon="el-icon-monitor" title="显示选区" circle size="mini" @click="showRange(item)"></el-button>
                    <el-button type="danger" icon="el-icon-delete" title="删除" circle size="mini" @click="deleteRange(item,index)"></el-button>
                </div>
                <el-divider content-position="left"></el-divider>
                </div>
            </div>
            <el-empty v-if="(!authedRange || authedRange.length == 0) && isCreator" description="暂无授权信息"></el-empty>
            <el-empty v-if="(!authedRange || authedRange.length == 0) && !isCreator" description="暂无操作权限"></el-empty>
        </el-dialog>
        <textarea id="clipboradInput" value="" style="opacity:0;position:absolute" />
    </div>
</template>

<script src="./report-froms.js"></script>

<style scoped lang="scss">
.table-box{
    width: 100%;
    display: flex;
    justify-content: space-between;
    &-left{
        width: 180px;
    }
    &-right{
        // flex: 1;
        width:calc(100% - 180px)
    }
}
.pagebox {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #ffffff;
}
.left {
    box-sizing: border-box;
    width: 254px;
    height: 99vh;
    background: #FFFFFF;
    // box-shadow: 0px 4px 8px #D2E3FF;
    overflow-y:auto;
    overflow-x:hidden;
    border-top: 1px solid #E7E7E7;
}
.left-head{
    box-sizing: border-box;
    position: absolute;
    width: 254px;
    height: 32px;
    left: 0px;
    background: #FFFFFF;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.left-head .el-icon-s-fold{
    position: absolute;
    right: 5%;
    top: 18.75%;
    bottom: 18.66%;
}
.left-dataset-title{
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 5px 16px;
    gap: 62px;
    width: 254px;
    height: 40px;
    background: #FFFFFF;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    flex: none;
    order: 1;
    flex-grow: 0;
    // margin-top: 2px;
}
.dataset-title{
    width: 80px;
    height: 22px;
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
    color: rgba(0, 0, 0, 0.9);
    flex: none;
    order: 0;
    flex-grow: 0;
}
.right-dataset-title{
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 62px;
    width: 240px;
    height: 40px;
    background: #FFFFFF;
    /* border-bottom: 1px solid rgba(0, 0, 0, 0.1); */
    flex: none;
    order: 1;
    flex-grow: 0;
    margin-top: 2px;
}
.attr-dataset-title{
    width: 100px;
    height: 32px;
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 32px;
    color:#606266;
    flex: none;
    order: 0;
    flex-grow: 0;
    font-weight: bold;
}
.addBtn{
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 2px 12px;
    gap: 4px;
    width: 66px;
    height: 22px;
    background: #45c5a9;
    border-radius: 4px;
    flex: none;
    order: 1;
    flex-grow: 0;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
    color: #FFFFFF;
}
.dataset-box{
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    padding: 7px 16px;
    gap: 103px;

    width: 254px;
    height: 36px;

    background: #FFFFFF;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    flex: none;
    order: 0;
    flex-grow: 0;
}
.el-icon-arrow-right{
    position: absolute;
    left: 5.25%;
    cursor:pointer;
}
.el-icon-arrow-down_dataset{
    position: absolute;
    left: 5.25%;
    cursor:pointer;
}
.dataset-name{
    width: 140px;
    height: 22px;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #191919;
    flex: none;
    order: 1;
    flex-grow: 0;
    max-width: 190px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow:ellipsis;
    padding-right:50px;
    cursor:pointer;
}
.dataset-name2{
    width: 100px;
    height: 22px;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #191919;
    flex: none;
    order: 1;
    flex-grow: 0;
    max-width: 190px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow:ellipsis;
    padding-right:50px;
    cursor:pointer;
}
.el-icon-edit{
    position: absolute;
    right: 17.3%;
    color: #ED7B2F;
    cursor:pointer;
}
.el-icon-delete{
    position: absolute;
    right: 7.3%;
    color: #ED7B2F;
    cursor:pointer;
}
.right-el-icon-edit{
    position: absolute;
    right: 32.3%;
    cursor:pointer;
}
.right-block-el-icon-edit{
    position: absolute;
    right: 20.3%;
    cursor:pointer;
}
.right-el-icon-copy-document{
    position: absolute;
    right: 20.3%;
    cursor:pointer;
}
.right-el-icon-delete{
    position: absolute;
    right: 8.3%;
    cursor:pointer;
}
.dataset-box-active{
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    padding: 7px 16px;
    gap: 103px;
    /* background: #A5C3F5; */
    width: 254px;
    height: 36px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    flex: none;
    order: 0;
    flex-grow: 0;
}
.dataset-box-content{
    width: 254px;
    min-height:150px;
    /* background: #A5C3F5; */
    flex: none;
    order: 4;
    flex-grow: 0;
    max-height:400px;
    overflow-y: auto;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding-left: 5px;
    padding-top: 3px;
}
.column-tag{
    max-width:150px;
    height: 30px;
    background: #f7bb61;
    border-radius: 2px;
    color: rgba(0, 0, 0, 0.6);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inline-block;
    padding: 0 10px;
    height: 32px;
    line-height: 30px;
    font-size: 12px;
    border-radius: 4px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border: 1px solid rgba(64,158,255,.2);
    font-weight: bold;
}
.center{
    flex: 1;
    // height: 99vh;
}
.right{
    width: 254px;
    height: 99vh;
    background: #FFFFFF;
    // box-shadow: 0px 4px 8px #D2E3FF;
}
.right-head{
    box-sizing: border-box;
    position: absolute;
    width: 254px;
    height: 32px;
    right: 0px;
    // top: 0px;
    background: #FFFFFF;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.right-head .el-icon-s-unfold{
    position: absolute;
    left: 5%;
    top: 18.75%;
    bottom: 18.66%;
}
.right-title{
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    padding: 6px 4px;
    gap: 4px;
    width: 254px;
    height: 30px;
    background: #E7E7E7;
    flex: none;
    order: 1;
    align-self: stretch;
    flex-grow: 0;
    // margin-top: 2px;
}
.cell-property{
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 3px 16px;
    gap: 4px;
    width: 50px;
    height: 22px;
    background: #FFFFFF;
    border-radius: 4px;
    flex: none;
    order: 0;
    flex-grow: 1;
    color:#45c5a9;
    font-size: 16px;
    cursor: pointer;
}
.cell-property-noactive{
    background: #E7E7E7;
    cursor: pointer;
    color:rgba(0, 0, 0, 0.6);;

}
.right-form{
    position: absolute;
    width: 254px;
    height: 95%;
    right: 0px;
    // top: 50px;
    background: #FFFFFF;
    overflow: auto;
}
.right-form::-webkit-scrollbar {
  width: 14px;
  height: 14px;
}
 
.right-form::-webkit-scrollbar-track,
::-webkit-scrollbar-thumb {
  border-radius: 999px;
  border: 5px solid transparent;
}
 
.right-form::-webkit-scrollbar-track {
  box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.2) inset;
}
 
.right-form::-webkit-scrollbar-thumb {
  min-height: 20px;
  background-clip: content-box;
  box-shadow: 0 0 0 5px rgba(0, 0, 0, 0.2) inset;
}
 
.right-form::-webkit-scrollbar-corner {
  background: transparent;
}
.demo-form-inline{
    padding-left: 8px;
}
.blockBtn{
    width: 230px;
    height: 30px;
    background: #45c5a9;
    border-radius: 4px;
    line-height: 5px;
}
.contentbox {
    display: flex;
}
.dataset-box-content::-webkit-scrollbar {
  width: 5px;
}
/*修改左侧垂直滚动条的样式*/
.left::-webkit-scrollbar {
  width: 5px;
}
/*修改左侧垂直滚动条的样式*/
.dataset-attr-active{
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    padding: 7px 16px;
    gap: 103px;
    background: #A5C3F5;
    width: 180px;
    height: 36px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    flex: none;
    order: 0;
    flex-grow: 0;
}
.dataset-attr{
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    padding: 7px 16px;
    gap: 103px;
    width: 180px;
    height: 36px;

    background: #FFFFFF;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    flex: none;
    order: 0;
    flex-grow: 0;
}
.tplname {
  padding: 0px 20px;
  background-color: rgba(208, 208, 208, 0);
  font-size: 19px;
  line-height: 30px;
  color: #45c5a9;
  font-weight: bold;
  margin: 5px 0;
}
._header {
  height: 45px !important;
  padding: 0px;
  background-color: #fff;
//   border-bottom: 1px solid #ccc;
  .headerRight {
    padding-right: 24px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.9);
    .role-name {
      margin-right: 8px;
    }
  }
  .el-dropdown-link {
    color: rgba(0, 0, 0, 0.9);
  }
}
::v-deep .el-avatar{
    background:#45c5a9 !important
}

::v-deep .el-dialog__wrapper {
   overflow: hidden;
//    z-index: 2005 !important;
   pointer-events: none !important;
}

::v-deep .el-dialog{
    pointer-events: auto !important;
    /* background:#d9ebf0 !important; */
} 
 ::v-deep .authdialog{
    margin-top: 50px !important;
    margin-left: 0px !important;
    flex-direction: column !important;
    // overflow: hidden !important;
    max-height: calc(100% - 90px) !important;
    top:0 !important;
    left:0px!important;
    bottom: 0;
    pointer-events: auto !important;
    /* background:#d9ebf0 !important; */
} 
.authdialog ::v-deep .el-dialog__body{
    height: calc(100% - 90px) !important;
    overflow: auto;
}
.authdialog ::v-deep .el-dialog-div{
     max-height: 60vh;
     overflow: auto;
     margin-left: 10px;
}
.authdialog ::v-deep .el-dialog-div::-webkit-scrollbar {
    display: none; /*隐藏滚动条*/
}
.authdialog ::v-deep .el-dialog__title{
    font-weight: bold;
}
.el-divider--horizontal{
    margin: 10px 0
}
</style>
