<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="文件名称" prop="fileName">
        <el-input
          v-model="queryParams.fileName"
          placeholder="请输入文件名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="上传人" prop="uploadUser">
        <el-input
          v-model="queryParams.uploadUser"
          placeholder="请输入上传人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="业务ID" prop="businessId">
        <el-input
          v-model="queryParams.businessId"
          placeholder="请输入业务ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="上传时间" prop="uploadTime">
        <el-date-picker
          v-model="queryParams.dateRange"
          style="width: 400px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="filter-item"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>


    <el-table v-loading="loading" :data="attachmentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="文件ID" width="200px" align="center" prop="fileId" />
      <el-table-column label="文件名称" width="300px" align="center" prop="fileName">
        <template slot-scope="scope">
          <el-link type="primary" @click="handleDownload(scope.row)">{{ scope.row.fileName }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="文件后缀" align="center" prop="fileSuffix" />
      <el-table-column label="文件大小" align="center" prop="fileSize" />
      <el-table-column label="上传人" align="center" prop="uploadUser" />
      <el-table-column label="业务ID" align="center" prop="businessId" />
      <el-table-column label="绝对路径" width="300px" align="center" prop="absolutePath" />
      <el-table-column label="相对路径" width="300px" align="center" prop="relativePath" />
      <el-table-column label="上传时间" align="center" prop="uploadTime" width="180"/>
      <el-table-column label="业务类型" align="center" prop="businessType" />
      <el-table-column label="附件状态" align="center" prop="status" />
<!--      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">-->
<!--        <template slot-scope="scope">-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-edit"-->
<!--            @click="handleUpdate(scope.row)"-->
<!--            v-hasPermi="['file:attachment:edit']"-->
<!--          >修改</el-button>-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-delete"-->
<!--            @click="handleDelete(scope.row)"-->
<!--            v-hasPermi="['file:attachment:remove']"-->
<!--          >删除</el-button>-->
<!--        </template>-->
<!--      </el-table-column>-->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改附件管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="文件ID" prop="fileId">
          <el-input v-model="form.fileId" placeholder="请输入文件ID" />
        </el-form-item>
        <el-form-item label="文件名称" prop="fileName">
          <el-input v-model="form.fileName" placeholder="请输入文件名称" />
        </el-form-item>
        <el-form-item label="文件后缀" prop="fileSuffix">
          <el-input v-model="form.fileSuffix" placeholder="请输入文件后缀" />
        </el-form-item>
        <el-form-item label="文件大小" prop="fileSize">
          <el-input v-model="form.fileSize" placeholder="请输入文件大小" />
        </el-form-item>
        <el-form-item label="上传人" prop="uploadUser">
          <el-input v-model="form.uploadUser" placeholder="请输入上传人" />
        </el-form-item>
        <el-form-item label="业务ID" prop="businessId">
          <el-input v-model="form.businessId" placeholder="请输入业务ID" />
        </el-form-item>
        <el-form-item label="绝对路径" prop="absolutePath">
          <el-input v-model="form.absolutePath" placeholder="请输入绝对路径" />
        </el-form-item>
        <el-form-item label="相对路径" prop="relativePath">
          <el-input v-model="form.relativePath" placeholder="请输入相对路径" />
        </el-form-item>
        <el-form-item label="上传时间" prop="uploadTime">
          <el-date-picker clearable
                          v-model="form.uploadTime"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择上传时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAttachment, getAttachment, delAttachment, addAttachment, updateAttachment } from "@/api/attachment/api";

export default {
  name: "Attachment",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 附件管理表格数据
      attachmentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        fileId: null,
        fileName: null,
        fileSuffix: null,
        fileSize: null,
        uploadUser: null,
        businessId: null,
        absolutePath: null,
        relativePath: null,
        uploadTime: null,
        businessType: null,
        status: null,
        dateRange: []
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询附件管理列表 */
    getList() {
      this.loading = true;
      if (this.queryParams.dateRange) {
        this.queryParams.startTime = this.queryParams.dateRange[0]
        this.queryParams.endTime = this.queryParams.dateRange[1]
      } else {
        this.queryParams.startTime = null
        this.queryParams.endTime = null
      }
      listAttachment(this.queryParams).then(response => {
        this.attachmentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        fileId: null,
        fileName: null,
        fileSuffix: null,
        fileSize: null,
        uploadUser: null,
        businessId: null,
        absolutePath: null,
        relativePath: null,
        uploadTime: null,
        businessType: null,
        status: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加附件管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getAttachment(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改附件管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateAttachment(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAttachment(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除附件管理编号为"' + ids + '"的数据项？').then(function() {
        return delAttachment(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('file/attachment/export', {
        ...this.queryParams
      }, `attachment_${new Date().getTime()}.xlsx`)
    },
    handleDownload(row) {
      const fileName = row.fileName
      const relativePath = row.absolutePath
      const downloadUrl = `${window.location.origin}${"/opsyndex-file"}${relativePath}`
      this.downloadFile(downloadUrl, fileName)
    },
    downloadFile(fileUrl, fileName) {
      const link = document.createElement("a");
      link.href = fileUrl;
      link.download = fileName; // 可选，浏览器会自动解析文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

  }
};
</script>
