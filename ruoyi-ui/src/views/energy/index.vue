<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <eHeader :dict="dict" :permission="permission" />
      <!-- <crudOperation :permission="permission" /> -->
    </div>
    <!--表格渲染-->
    <el-table ref="table" v-loading="crud.loading" :data="crud.data" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
      <el-table-column type="selection" width="55" />
      <el-table-column prop="dateTime" label="日期" />
      <el-table-column  label="数据名称">
        <template  slot-scope="scope">
          {{ getReleaseStatus(scope.row.dateName)}}
        </template>

      </el-table-column>
      <!-- <el-table-column prop="auditState" label="是否审核" /> -->
      <el-table-column   label="是否审核">
        <template  slot-scope="scope">
          {{ getAuditState(scope.row.auditState)}}
        </template>
      </el-table-column>
      <el-table-column prop="auditName" label="审核人" />
      <el-table-column label="审核日期">
        <template slot-scope="scope">
          {{ formatAuditTime(scope.row.auditTime) }}
        </template>
      </el-table-column>

      <el-table-column   label="是否已上报">
        <template  slot-scope="scope">
          {{ getPutStatus(scope.row.putState)}}
        </template>
      </el-table-column>
      <el-table-column

        label="操作"
        width="190px"
        align="center"
        fixed="right"
      >
        <template slot-scope="scope" >
          <div class="button-container">
            <el-button
            size="mini"
            type="success"
            icon="el-icon-edit"
            v-if="scope.row.auditState!==1"
            @click="fromEditmethod(scope,'edit')"

          >
          编辑
          </el-button>
          <el-button
            size="mini"
            type="success"
            icon="el-icon-edit"
            v-if="scope.row.auditState===1"
            @click="fromEditmethod(scope,'look')"

          >
          查看
          </el-button>
          <el-button
            size="mini"
            type="warning"
            icon="el-icon-s-check"
            v-if="scope.row.auditState!==1"
             @click="confirmDelete(scope)"
          >
          审核
          </el-button>


        </div>
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination />
    <!--表单渲染-->
    <!-- <formCO2 :job-status="dict.energy" v-if="visible" :visible.sync="visible" :formData="formData" /> -->
    <!-- todo: 以后就这样写了 -->
    <formCO2 :job-status="dict.energy" v-if="visibleCO2" :visible.sync="visibleCO2" :formData="formData" />
    <formElectricity :job-status="dict.energy" v-if="visibleElectricity" :visible.sync="visibleElectricity" :formData="formData" />
    <formWater :job-status="dict.energy" v-if="visibleWater" :visible.sync="visibleWater" :formData="formData" />
    <formSteam :job-status="dict.energy" v-if="visibleSteam" :visible.sync="visibleSteam" :formData="formData" />
    <formLineYield :job-status="dict.energy" v-if="visibleLineYield" :visible.sync="visibleLineYield" :formData="formData" />
    <formPackSkuDayYield :job-status="dict.energy" v-if="visiblePackSkuDayYield" :visible.sync="visiblePackSkuDayYield" :formData="formData" />
    <formPackSkuEquipDowntimeRecord :job-status="dict.energy" v-if="visiblePackSkuEquipDowntimeRecord" :visible.sync="visiblePackSkuEquipDowntimeRecord" :formData="formData" />
    <formCategoryPlan :job-status="dict.energy" v-if="visibleCategoryPlan" :visible.sync="visibleCategoryPlan" :formData="formData" />
    <formDayPlanMasterData :job-status="dict.energy" v-if="visibleDayPlanMaster" :visible.sync="visibleDayPlanMaster" :formData="formData" />
    <formAdsPackKpiIndicatorsSkuFull :job-status="dict.energy" v-if="visibleKpiSku" :visible.sync="visibleKpiSku" :formData="formData" />
    <formPackLossVelocityRecord :job-status="dict.energy" v-if="visiblePackLose" :visible.sync="visiblePackLose" :formData="formData" />
    <fromPackEquipDowntimeRecordFull :job-status="dict.energy" v-if="visiblePackEquipDowntimeRecordFull" :visible.sync="visiblePackEquipDowntimeRecordFull" :formData="formData" />
    <formAdsPackKpiIndicatorsFull :job-status="dict.energy" v-if="visibleKpi" :visible.sync="visibleKpi" :formData="formData" />
    <formAdsPackDayYieldFull :job-status="dict.energy" v-if="packDayYield" :visible.sync="packDayYield" :formData="formData" />
  </div>
</template>

<script>
import crudJob from '@/api/system/job'
import {editTsingtaoAuditStatus} from '@/api/energy/energyTsingtaoMesDataPlatFormAudit.js'
import {getAdsSteamFById} from '@/api/energy/energyAdsEnergyBaseDataSteamFull.js'
import {getAdsCo2ById} from '@/api/energy/energyAdsEnergyBaseDataCo2Full.js'
import {getAdsElectricityById} from '@/api/energy/energyAdsEnergyBaseDataElectricityFull.js'
import {getAdsWaterById} from '@/api/energy/energyAdsEnergyBaseDataWaterFull.js'

import {getLineYieldById} from '@/api/energy/energyAdsLineYieldFull.js'
import {getPackSkuDayYieldById} from '@/api/energy/energyAdsPackSkuDayYieldFull.js'
import {getPackSkuEquipDowntimeRecordId} from '@/api/energy/energyAdsPackSkuEquipDowntimeRecordFull.js'
import {getCategoryPlanById} from '@/api/energy/energyAdsCategoryPlanFull.js'
import {getDayPlanMasterById} from '@/api/energy/energyAdsDayPlanMasterDataFull.js'
import {getKpiSkuById} from '@/api/energy/energyAdsPackKpiIndicatorsSkuFull.js'

import {getPackLossVelocityRecord} from '@/api/energy/energyAdsPackLossVelocityRecord.js'
import {getAdsPackequipDowntimeRecord} from '@/api/energy/energyPackEquipDowntimeRecordFull.js'
import {getKpiById} from '@/api/energy/energyAdsPackKpiIndicatorsFull.js'
import {getPackDayYieldId} from '@/api/energy/energyAdsPackDayYieldFull.js'
import eHeader from './module/header'
import formCO2 from './module/formCO2'
import formElectricity from './module/formElectricity'
import formWater from './module/formWater'
import formSteam from './module/formSteam'
import formLineYield from './module/formLineYield'
import formPackSkuDayYield from './module/formPackSkuDayYield.vue'
import formPackSkuEquipDowntimeRecord from './module/formPackSkuEquipDowntimeRecord.vue'
import formCategoryPlan from './module/formCategoryPlan.vue'
import formDayPlanMasterData from './module/formDayPlanMasterData.vue'
import formAdsPackKpiIndicatorsSkuFull from './module/formAdsPackKpiIndicatorsSkuFull.vue'
import formPackLossVelocityRecord from './module/formPackLossVelocityRecord.vue'
import fromPackEquipDowntimeRecordFull from './module/formPackEquipDowntimeRecord.vue'
import formAdsPackKpiIndicatorsFull from './module/formAdsPackKpiIndicatorsFull.vue'
import formAdsPackDayYieldFull from './module/formAdsPackDayYieldFull.vue'
import CRUD, { presenter} from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import udOperation from '@crud/UD.operation'
import moment from 'moment';
// todo: 字典
import { getDicts as getCategoryDictData } from "@/api/system/dict/data";
import { get as getDictDetail } from '@/api/system/dictDetail'
import { log } from '@visactor/vchart/esm/util'

export default {

  name: 'Job',
  components: { eHeader, formCO2,formElectricity,formWater,formSteam, crudOperation,
                pagination, udOperation, formLineYield, formPackSkuDayYield, formPackSkuEquipDowntimeRecord, 
                  formCategoryPlan, formDayPlanMasterData, formAdsPackKpiIndicatorsSkuFull,formPackLossVelocityRecord,fromPackEquipDowntimeRecordFull,
                  formAdsPackKpiIndicatorsFull, formAdsPackDayYieldFull },
  cruds() {
    return CRUD({
      title: '岗位',
      url: '/energyAudit/energy/audit',
      // sort: ['jobSort,asc', 'id,desc'],
      // crudMethod: { ...crudEnergy }
    })
  },
  mixins: [presenter()],
  // 数据字典
  dicts: ['audit_energy','put_status','audit_state'],
  data() {

    return {
      workflow_exec_status: [],
      workflow_put_status_status: [],
      workflow_audit_state_status: [],
      visible:false,
      visibleCO2:false,
      visibleElectricity:false,
      visibleWater:false,
      visibleSteam:false,
      visibleLineYield:false,
      visiblePackSkuDayYield:false,
      visiblePackSkuEquipDowntimeRecord:false,
      visibleCategoryPlan:false,
      visibleDayPlanMaster:false,
      visibleKpiSku:false,
      visiblePackLose:false,
      visiblePackEquipDowntimeRecordFull:false,
      visibleKpi:false,
      packDayYield:false,
      formData:{
        type:Object,
        dateTime:'',
        cFactory:''
      },
      temp: {
        id: undefined,
        flowName: '',
        flowCode: '',
        flowVersion: null,
        releaseStatus: null,
        flowInfo: null
      },
      permission: {
        add: ['admin', 'job:add'],
        edit: ['admin', 'job:edit'],
        del: ['admin', 'job:del']
      }
    }
  },
  created() {
    this.getDictDetailInfo()

    this.getDicts('audit_energy').then(response => {
      console.log("字典audit_energy-response:" , response.data);
      if (response.code === 200) {
        this.workflow_exec_status = response.data
      }
    })
    this.getDicts('put_status').then(response => {
      if (response.code === 200) {
        this.workflow_put_status_status = response.data
      }
    })
    this.getDicts('audit_state').then(response => {
      if (response.code === 200) {
        this.workflow_audit_state_status = response.data
      }
    })

  },
  methods: {
    formatAuditTime(date) {
      // Check if the date is valid and not null
      if (!date || moment(date).isValid() === false) {
        return '';  // Return an empty string or any default value for invalid dates
      }
      return moment(date).format('YYYY-MM-DD HH:mm:ss');
    },
    getDictDetailInfo() {
      getCategoryDictData("audit_energy").then((res) => {
        this.workflow_exec_status = res.data;
      });
      getCategoryDictData("put_status").then((res) => {
        this.workflow_put_status_status = res.data;
      });
      getCategoryDictData("audit_state").then((res) => {
        this.workflow_audit_state_status = res.data;
      });
      // getDictDetail('energy').then(res => {
      //   this.workflow_exec_status = res.content
      // }),
      // getDictDetail('put_status').then(res => {
      //   this.workflow_put_status_status = res.content
      // }),
      // getDictDetail('audit_state').then(res => {
      //   this.workflow_audit_state_status = res.content
      // })

    },
    //匹配发布状态的字段label 每个字段赋值一次
    getReleaseStatus(status) {


      const found = this.workflow_exec_status.filter(item => item.dictValue  == status)
      if(found && found.length){

        return found[0].dictLabel
      }

    },
    getPutStatus(status) {
      const found = this.workflow_put_status_status.filter(item => item.dictValue == status)
      if(found && found.length){
        return found[0].dictLabel
      }
    },
    getAuditState(status) {
      const found = this.workflow_audit_state_status.filter(item => item.dictValue == status)
      if(found && found.length){
        return found[0].dictLabel
      }
    },
    confirmDelete(row) {
      this.$confirm('是否通过审核？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 用户点击确定按钮时执行删除操作
          this.changeAuditStateById(row)
        })
        .catch(() => {
          // 用户点击取消按钮时的操作，可以不做任何处理或关闭模态框
        })
    },


    changeAuditStateById(data){
      editTsingtaoAuditStatus(data.row.id).then(res=>{
        this.crud.notify('更新成功:','success')
        this.crud.refresh();
      })
      .catch(err => {
      // 错误处理
      console.error('更新失败:', err);
      this.crud.notify('操作失败', 'error');
    });
    },
    fromEditmethod(data,type){
      this.formData.type = type
      console.log("data.row.dateName:",data.row.dateName)
      // if else 的逻辑 如果是1 就是打开formCO2 的form的表单 如果是2就打开 formStream的表单
      console.log(data.row.dateName===2)
      switch (data.row.dateName) {
        case 1:
            this.visibleWater = true;
            getAdsWaterById(data.row.hiveTableId).then(res=>{
              console.log('visibleWater:',res);
              this.formData = res || {}
            })
            break;

        case 2:
            this.visibleCO2 = true;
            getAdsCo2ById(data.row.hiveTableId).then(res=>{
              console.log('visibleCO2:',res);
              this.formData = res || {}
            })
            break;

        case 3:
            this.visibleElectricity = true;
            getAdsElectricityById(data.row.hiveTableId).then(res=>{
              console.log('visibleElectricity:',res);
              this.formData = res || {}
            })
            break;

        case 4:
            this.visibleSteam = true;
            getAdsSteamFById(data.row.hiveTableId).then(res=>{
              console.log('visibleSteam:',res);
              this.formData = res || {}
            })
            break;

        // 各产线产量表
        case 5:
        this.visibleLineYield = true;
        getLineYieldById(data.row.hiveTableId).then(res=>{
          console.log('各产线产量表getLineYieldById:',res);
          this.formData = res || {}
        })
        break;

        // 包装SKU、分瓶型日产量表
        case 6:
        this.visiblePackSkuDayYield = true;
        getPackSkuDayYieldById(data.row.hiveTableId).then(res=>{
          console.log('包装SKU、分瓶型日产量表getPackSkuDayYieldById:',res);
          this.formData = res || {}
        })
        break;

        // getPackSkuEquipDowntimeRecordId 包装SKU停机记录表
        case 7:
        this.visiblePackSkuEquipDowntimeRecord = true;
        getPackSkuEquipDowntimeRecordId(data.row.hiveTableId).then(res=>{
          console.log('包装SKU停机记录表getPackSkuEquipDowntimeRecordId:',res);
          this.formData = res || {}
        })
        
        break;

        // 品种计划表
        case 8:
        this.visibleCategoryPlan = true;
        getCategoryPlanById(data.row.hiveTableId).then(res=>{
          console.log('品种计划表getCategoryPlanById:',res);
          this.formData = res || {}
        })
        break;

        // 日计划主数据表
        case 9:
        this.visibleDayPlanMaster = true;
        getDayPlanMasterById(data.row.hiveTableId).then(res=>{
          console.log('日计划主数据表getDayPlanMasterById:',res);
          this.formData = res || {}
        })
        break;

        // kpi 包装KPI指标SKU数据表
        case 10:
        this.visibleKpiSku = true;
        getKpiSkuById(data.row.hiveTableId).then(res=>{
          console.log('kpiSku表getKpiSkuById:',res);
          this.formData = res || {}
        })
        break;
        
        // 计划 包装-速度损失记录
        case 11:
        this.visiblePackLose = true;
        getPackLossVelocityRecord(data.row.hiveTableId).then(res=>{
          console.log('kpiSku表getPackLossVelocityRecord:',res);
          this.formData = res || {}
        })
        break;

        // 计划 包装停机记录
        case 12:
        this.visiblePackEquipDowntimeRecordFull = true;
        getAdsPackequipDowntimeRecord(data.row.hiveTableId).then(res=>{
          console.log('getPackEquipDowntimeRecord:',res);
          this.formData = res || {}
        })
        break;

        // kpi 包装KPI指标数据表
        case 13:
        this.visibleKpi = true;
        getKpiById(data.row.hiveTableId).then(res=>{
          console.log('getPackEquipDowntimeRecord:',res);
          this.formData = res || {}
        })
        break;

        // 计划 包装日产量表
        case 14:
        this.packDayYield = true;
        getPackDayYieldId(data.row.hiveTableId).then(res=>{
          console.log('getPackDayYieldId:',res);
          this.formData = res || {}
        })
        break;

        default:
            console.warn("未知的 dateName:", data.row.dateName);
    }


      // getAdsCo2ById(data.row.hiveTableId).then(res=>{
      //   console.log('res:',res);
      //   this.formData = res || {}
      // })

    }

  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
 ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }

.button-container {
  display: flex;
  flex-direction: row;
  gap: 5px; /* 设置按钮之间的间距 */
}
.filter-item {
  margin: 0; /* 确保没有额外的上下间距 */
}

</style>
