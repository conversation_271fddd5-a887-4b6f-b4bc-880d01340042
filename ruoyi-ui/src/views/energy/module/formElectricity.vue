<template>
  <el-dialog
    append-to-body
    :close-on-click-modal="false"
    :visible="visible"
    title="编辑"
    width="600px"
    @close ="cancleMethod"
  >
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      size="small"
      label-width="160px"
    >
      <el-form-item label="日期" prop="dateTime">
        <el-input v-model="formData.dateTime" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="全厂" prop="eFactory">
        <el-input v-model="formData.eFactory" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装" prop="ePackage">
        <el-input v-model="formData.ePackage" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="酿造" prop="eBrew">
        <el-input v-model="formData.eBrew" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="非生产" prop="eNonproductive">
        <el-input v-model="formData.eNonproductive" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="动力" prop="ePower">
        <el-input v-model="formData.ePower" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="线损" prop="eLineLoss">
        <el-input v-model="formData.eLineLoss" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="瓶装线" prop="eBottledLine">
        <el-input v-model="formData.eBottledLine" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="听装线" prop="eCannedLine">
        <el-input v-model="formData.eCannedLine" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="桶装" prop="eBarrelLine">
        <el-input v-model="formData.eBarrelLine" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="制冷" prop="eRefrigeration">
        <el-input v-model="formData.eRefrigeration" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="空压" prop="eAirPressure">
        <el-input v-model="formData.eAirPressure" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="锅炉" prop="eBoiler">
        <el-input v-model="formData.eBoiler" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="二氧化碳" prop="eCo2">
        <el-input v-model="formData.eCo2" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="污水" prop="eSewage">
        <el-input v-model="formData.eSewage" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="1#包装产线消耗" prop="ePackageLine1">
        <el-input v-model="formData.ePackageLine1" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="2#包装产线消耗" prop="ePackageLine2">
        <el-input v-model="formData.ePackageLine2" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="3#包装产线消耗" prop="ePackageLine3">
        <el-input v-model="formData.ePackageLine3" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="4#包装产线消耗" prop="ePackageLine4">
        <el-input v-model="formData.ePackageLine4" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="5#包装产线消耗" prop="ePackageLine5">
        <el-input v-model="formData.ePackageLine5" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="6#包装产线消耗" prop="ePackageLine6">
        <el-input v-model="formData.ePackageLine6" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="7#包装产线消耗" prop="ePackageLine7">
        <el-input v-model="formData.ePackageLine7" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="8#包装产线消耗" prop="ePackageLine8">
        <el-input v-model="formData.ePackageLine8" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="9#包装产线消耗" prop="ePackageLine9">
        <el-input v-model="formData.ePackageLine9" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="制冷机1" prop="eRefrigeration1">
        <el-input v-model="formData.eRefrigeration1" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="制冷机2" prop="eRefrigeration2">
        <el-input v-model="formData.eRefrigeration2" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="制冷机3" prop="eRefrigeration3">
        <el-input v-model="formData.eRefrigeration3" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="制冷机4" prop="eRefrigeration4">
        <el-input v-model="formData.eRefrigeration4" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="制冷机5" prop="eRefrigeration5">
        <el-input v-model="formData.eRefrigeration5" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="制冷机6" prop="eRefrigeration6">
        <el-input v-model="formData.eRefrigeration6" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="制冷机7" prop="eRefrigeration7">
        <el-input v-model="formData.eRefrigeration7" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="制冷机8" prop="eRefrigeration8">
        <el-input v-model="formData.eRefrigeration8" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="制冷机9" prop="eRefrigeration9">
        <el-input v-model="formData.eRefrigeration9" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="空压机1" prop="eAirPressure1">
        <el-input v-model="formData.eAirPressure1" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="空压机2" prop="eAirPressure2">
        <el-input v-model="formData.eAirPressure2" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="空压机3" prop="eAirPressure3">
        <el-input v-model="formData.eAirPressure3" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="空压机4" prop="eAirPressure4">
        <el-input v-model="formData.eAirPressure4" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="空压机5" prop="eAirPressure5">
        <el-input v-model="formData.eAirPressure5" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="空压机6" prop="eAirPressure6">
        <el-input v-model="formData.eAirPressure6" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="空压机7" prop="eAirPressure7">
        <el-input v-model="formData.eAirPressure7" :disabled="disabled" style="width: 370px;" />
      </el-form-item>

      <!-- <el-form-item label="ads 创建时间" prop="cTime">
          <el-input v-model="form.cTime" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="ads 更新时间" prop="uTime">
          <el-input v-model="form.uTime" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="是否删除 N 否 Y 是" prop="delFlag">
          <el-input v-model="form.delFlag" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="年月" prop="pYearMonth">
          <el-input v-model="form.pYearMonth" style="width: 370px;" />
      </el-form-item> -->

    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        type="text"
        @click="cancleMethod"
      >
        取消
      </el-button>

      <!-- :loading="crud.status.cu === 2" -->
      <el-button
        v-if="!disabled"
        type="primary"
        @click="saveMethod"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>

import { form } from '@crud/crud'
import {edit} from '@/api/energy/energyAdsEnergyBaseDataCo2Full.js'
export default {
  props: {
    jobStatus: {
      type: Array,
      required: true
    },
    visible:{
      type:Boolean,
      default:false
    },
    formData:{
      type:Object,
      default:()=>{}
    }
  },
  data() {
    return {
      disabled:false,
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        jobSort: [
          { required: true, message: '请输入序号', trigger: 'blur', type: 'number' }
        ]
      }
    }
  },
  methods:{
    cancleMethod(){
      this.$emit('update:visible',false)
    },
    // 确认按钮
    saveMethod(){
      edit(this.formData).then(res=>{
        // todo: 加上修改成功的反应...
        if(res.code==200){
          this.crud.notify( '保存成功', 'success')
        }
        this.cancleMethod();
      })

    }
    },
    mounted(){
      if(this.formData.type){
        this.disabled = this.formData.type === 'look'
      }
    }
  }

</script>

<style rel="stylesheet/scss" lang="scss" scoped>
 ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
</style>
