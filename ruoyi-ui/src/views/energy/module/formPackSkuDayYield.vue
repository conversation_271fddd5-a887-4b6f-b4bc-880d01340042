<template>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible="visible"
      title="编辑"
      width="600px"
      @close="cancleMethod"
    >
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        size="small"
        label-width="160px"
      >
        <!-- <el-form-item label="产线id" prop="skuLineOpenid">
          <el-input v-model="formData.skuLineOpenid" style="width: 370px;" />
        </el-form-item> -->
        <el-form-item label="产线" prop="skuLineName">
          <el-input v-model="formData.skuLineName" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="生产日期" prop="skuProduceT">
          <el-input v-model="formData.skuProduceT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="品种编码" prop="skuTypeCode">
          <el-input v-model="formData.skuTypeCode" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="瓶型" prop="skuBottleType">
          <el-input v-model="formData.skuBottleType" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="产量(KL)" prop="skuYield">
          <el-input v-model="formData.skuYield" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <!-- 标志(0为当天计划品种、1为非当天计划品种) -->
        <el-form-item label="标志" prop="skuMark">
          <el-input v-model="formData.skuMark" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="产量(瓶数)" prop="skuBottle">
          <el-input v-model="formData.skuBottle" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <!-- <el-form-item label="ads创建时间" prop="cTime">
          <el-input v-model="formData.cTime" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="ads更新时间" prop="uTime">
          <el-input v-model="formData.uTime" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="是否删除 N否Y是" prop="delFlag">
          <el-input v-model="formData.delFlag" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="分区字段" prop="pYearMonth">
          <el-input v-model="formData.pYearMonth" style="width: 370px;" />
        </el-form-item> -->
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="text"
          @click="cancleMethod"
        >
          取消
        </el-button>
        <!-- :loading="crud.status.cu === 2" -->
        <el-button
          v-if="!disabled"
          type="primary"
          @click="saveMethod"
        >
          确认
        </el-button>
      </div>
    </el-dialog>
  </template>

  <script>
  import { form } from '@crud/crud'
  import { edit } from '@/api/energy/energyAdsPackSkuDayYieldFull.js'

  export default {
    props: {
      jobStatus: {
        type: Array,
        required: true
      },
      visible: {
        type: Boolean,
        default: false
      },
      formData: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        disabled:false,
        rules: {
          // 根据实际需求添加表单验证规则，如果需要的话
        }
      }
    },
    methods: {
      cancleMethod() {
        this.$emit('update:visible', false)
      },
      // 确认按钮
      saveMethod() {
        edit(this.formData).then(res => {
          // todo: 加上修改成功的反应...
          if (res.code === 200) {
            this.crud.notify('保存成功', 'success')
          }
          this.cancleMethod()
        })
      }
    },
    mounted(){
      if(this.formData.type){
        this.disabled = this.formData.type === 'look'
      }
    }
  }
  </script>

  <style rel="stylesheet/scss" lang="scss" scoped>
  ::v-deep.el-input-number.el-input__inner {
    text-align: left;
  }
  </style>
