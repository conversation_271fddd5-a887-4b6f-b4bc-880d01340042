<template>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible="visible"
      title="编辑"
      width="600px"
      @close="cancleMethod"
    >
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        size="small"
        label-width="160px"
      >
        <el-form-item label="日期" prop="lossD">
          <el-input v-model="formData.lossD" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
  
        <el-form-item label="日期-小时" prop="lossDHour">
          <el-input v-model="formData.lossDHour" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
  
        <el-form-item label="产线" prop="lossLineName">
          <el-input v-model="formData.lossLineName" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
  
        <el-form-item label="速度损失原因" prop="lossVelocityReason">
          <el-input v-model="formData.lossVelocityReason" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
  
        <el-form-item label="速度损失时间" prop="lossVelocityDuration">
          <el-input v-model="formData.lossVelocityDuration" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
  
        <el-form-item label="小停机时间" prop="lossSmallDowntimeT">
          <el-input v-model="formData.lossSmallDowntimeT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
  
        <el-form-item label="小停机次数" prop="lossDowntimeCount">
          <el-input v-model="formData.lossDowntimeCount" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
  
        <el-form-item label="损失产量(瓶)" prop="lossYield">
          <el-input v-model="formData.lossYield" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
  
        <!-- <el-form-item label="小停机分类1" prop="lossType1">
          <el-input v-model="formData.lossType1" :disabled="disabled" style="width: 370px;" />
        </el-form-item> -->
  
        <el-form-item label="小停机分类1" prop="lossType1Name">
          <el-input v-model="formData.lossType1Name" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
  
        <!-- <el-form-item label="小停机分类2" prop="lossType2">
          <el-input v-model="formData.lossType2" :disabled="disabled" style="width: 370px;" />
        </el-form-item> -->
  
        <el-form-item label="小停机分类2" prop="lossType2Name">
          <el-input v-model="formData.lossType2Name" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
  
        <!-- <el-form-item label="小停机分类3" prop="lossType3">
          <el-input v-model="formData.lossType3" :disabled="disabled" style="width: 370px;" />
        </el-form-item> -->
  
        <el-form-item label="小停机分类3" prop="lossType3Name">
          <el-input v-model="formData.lossType3Name" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
  
        <!-- <el-form-item label="小停机分类4" prop="lossType4">
          <el-input v-model="formData.lossType4" :disabled="disabled" style="width: 370px;" />
        </el-form-item> -->
  
        <el-form-item label="小停机分类4" prop="lossType4Name">
          <el-input v-model="formData.lossType4Name" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
  
        <el-form-item label="ads创建时间" prop="cTime">
          <el-input v-model="formData.cTime" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
  
        <el-form-item label="ads更新时间" prop="uTime">
          <el-input v-model="formData.uTime" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
  
        <el-form-item label="年月" prop="pYearMonth">
          <el-input v-model="formData.pYearMonth" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
  
        <el-form-item label="是否删除" prop="delFlag">
          <el-input v-model="formData.delFlag" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
  
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="cancleMethod">取消</el-button>
        <el-button v-if="!disabled" type="primary" @click="saveMethod">确认</el-button>
      </div>
    </el-dialog>
  </template>
  <script>
  import { edit } from '@/api/energy/energyAdsPackLossVelocityRecord.js'
  
  export default {
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      formData: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        disabled: false,
        rules: {
          // 你可以根据需求添加字段验证规则
          lossD: [
            { required: true, message: '请输入日期', trigger: 'blur' }
          ],
          lossDHour: [
            { required: true, message: '请输入日期-小时', trigger: 'blur' }
          ]
          // 其他字段验证规则...
        }
      }
    },
    methods: {
      cancleMethod() {
        this.$emit('update:visible', false)
      },
      // 确认按钮
      saveMethod() {
        edit(this.formData).then(res => {
          if (res.code === 200) {
            this.$notify({
              title: '成功',
              message: '保存成功',
              type: 'success'
            })
          }
          this.cancleMethod()
        }).catch(error => {
          this.$notify({
            title: '错误',
            message: '保存失败',
            type: 'error'
          })
        })
      }
    },
    mounted() {
      if (this.formData.type) {
        this.disabled = this.formData.type === 'look'
      }
    }
  }
  </script>
  <style scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
</style>
