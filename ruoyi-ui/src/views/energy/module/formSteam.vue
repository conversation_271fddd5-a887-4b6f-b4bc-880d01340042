<template>
  <el-dialog
    append-to-body
    :close-on-click-modal="false"
    :visible="visible"
    title="编辑"
    width="600px"
    @close ="cancleMethod"
  >
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      size="small"
      label-width="160px"
    >
    <el-form-item label="日期" prop="dateTime">
      <el-input v-model="formData.dateTime" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="全厂" prop="gFactory">
      <el-input v-model="formData.gFactory" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="冷凝水回水率" prop="gReturnWaterRate">
      <el-input v-model="formData.gReturnWaterRate" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="天然气消耗量(m³)" prop="gNaturalgasConsumption">
      <el-input v-model="formData.gNaturalgasConsumption" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="工厂锅炉产蒸汽量(t)" prop="gBoilerProducesSteam">
      <el-input v-model="formData.gBoilerProducesSteam" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="酿造" prop="gBrew">
      <el-input v-model="formData.gBrew" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="包装" prop="gPackage">
      <el-input v-model="formData.gPackage" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="动力" prop="gPower">
      <el-input v-model="formData.gPower" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="非生产" prop="gNonproductive">
      <el-input v-model="formData.gNonproductive" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="线损" prop="gLineLoss">
      <el-input v-model="formData.gLineLoss" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="糖化" prop="gSaccharify">
      <el-input v-model="formData.gSaccharify" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="发酵过滤" prop="gFermentationFiltration">
      <el-input v-model="formData.gFermentationFiltration" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="瓶装线" prop="gBottledLine">
      <el-input v-model="formData.gBottledLine" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="听装线" prop="gCannedLine">
      <el-input v-model="formData.gCannedLine" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="桶装" prop="gBarrelLine">
      <el-input v-model="formData.gBarrelLine" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="包装产线1" prop="gPackageLine1">
      <el-input v-model="formData.gPackageLine1" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="包装产线2" prop="gPackageLine2">
      <el-input v-model="formData.gPackageLine2" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="包装产线3" prop="gPackageLine3">
      <el-input v-model="formData.gPackageLine3" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="包装产线4" prop="gPackageLine4">
      <el-input v-model="formData.gPackageLine4" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="包装产线5" prop="gPackageLine5">
      <el-input v-model="formData.gPackageLine5" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="包装产线6" prop="gPackageLine6">
      <el-input v-model="formData.gPackageLine6" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="包装产线7" prop="gPackageLine7">
      <el-input v-model="formData.gPackageLine7" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="包装产线8" prop="gPackageLine8">
      <el-input v-model="formData.gPackageLine8" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="包装产线9" prop="gPackageLine9">
      <el-input v-model="formData.gPackageLine9" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="包装产线1洗瓶机" prop="gBottleWash1">
      <el-input v-model="formData.gBottleWash1" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="包装产线3洗瓶机" prop="gBottleWash3">
      <el-input v-model="formData.gBottleWash3" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="包装产线8洗瓶机" prop="gBottleWash8">
      <el-input v-model="formData.gBottleWash8" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="杀菌机消耗1" prop="gDisinfect1">
      <el-input v-model="formData.gDisinfect1" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="杀菌机消耗3" prop="gDisinfect3">
      <el-input v-model="formData.gDisinfect3" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="杀菌机消耗6" prop="gDisinfect6">
      <el-input v-model="formData.gDisinfect6" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="杀菌机消耗8" prop="gDisinfect8">
      <el-input v-model="formData.gDisinfect8" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="杀菌机消耗9" prop="gDisinfect9">
      <el-input v-model="formData.gDisinfect9" :disabled="disabled" style="width: 370px;" />
    </el-form-item>
    <!-- <el-form-item label="ads 创建时间" prop="cTime">
        <el-input v-model="form.cTime" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="ads 更新时间" prop="uTime">
        <el-input v-model="form.uTime" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="是否删除 N 否 Y 是" prop="delFlag">
        <el-input v-model="form.delFlag" style="width: 370px;" />
    </el-form-item>
    <el-form-item label="年月" prop="pYearMonth">
        <el-input v-model="form.pYearMonth" style="width: 370px;" />
    </el-form-item> -->
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        type="text"
        @click="cancleMethod"
      >
        取消
      </el-button>

      <!-- :loading="crud.status.cu === 2" -->
      <el-button
        v-if="!disabled"
        type="primary"
        @click="saveMethod"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>

import { form } from '@crud/crud'
import {edit} from '@/api/energy/energyAdsEnergyBaseDataCo2Full.js'
export default {
  props: {
    jobStatus: {
      type: Array,
      required: true
    },
    visible:{
      type:Boolean,
      default:false
    },
    formData:{
      type:Object,
      default:()=>{}
    }
  },
  data() {
    return {
      disabled:false,
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        jobSort: [
          { required: true, message: '请输入序号', trigger: 'blur', type: 'number' }
        ]
      }
    }
  },
  methods:{
    cancleMethod(){
      this.$emit('update:visible',false)
    },
    // 确认按钮
    saveMethod(){
      edit(this.formData).then(res=>{
        // todo: 加上修改成功的反应...
        if(res.code==200){
          this.crud.notify( '保存成功', 'success')
        }
        this.cancleMethod();
      })

    }
    },
    mounted(){
      if(this.formData.type){
        this.disabled = this.formData.type === 'look'
      }
    }
  }

</script>

<style rel="stylesheet/scss" lang="scss" scoped>
 ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
</style>
