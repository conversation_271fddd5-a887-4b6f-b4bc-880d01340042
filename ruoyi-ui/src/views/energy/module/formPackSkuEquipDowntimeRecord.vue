<template>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible="visible"
      title="编辑"
      width="600px"
      @close="cancleMethod"
    >
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        size="small"
        label-width="160px"
      >
        <!-- <el-form-item label="产线id" prop="sdLineOpenid">
          <el-input v-model="formData.sdLineOpenid" style="width: 370px;" />
        </el-form-item> -->
        <el-form-item label="日期" prop="sdDate">
          <el-input v-model="formData.sdDate" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="品种编码" prop="sdTypeCode">
          <el-input v-model="formData.sdTypeCode" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="停机开始时间" prop="sdStartTime">
          <el-input v-model="formData.sdStartTime" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="停机结束时间" prop="sdEndTime">
          <el-input v-model="formData.sdEndTime" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="停机时长" prop="sdDowntimeDuration">
          <el-input v-model="formData.sdDowntimeDuration" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="停机分类1" prop="sdType1">
          <el-input v-model="formData.sdType1" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="停机分类2" prop="sdType2">
          <el-input v-model="formData.sdType2" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="停机分类3" prop="sdType3">
          <el-input v-model="formData.sdType3" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="停机分类4" prop="sdType4">
          <el-input v-model="formData.sdType4" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="停机原因说明" prop="sdReason">
          <el-input v-model="formData.sdReason" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="停机次数" prop="sdDowntimeDegree">
          <el-input v-model="formData.sdDowntimeDegree" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <!-- 是否是20分钟以上故障停机(1是0否) -->
        <el-form-item label="是否是20分钟以上故障停机(1是0否)" prop="sdDowntimeBig">
          <el-input v-model="formData.sdDowntimeBig" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="班组名称code" prop="sdWorkgroupCode">
          <el-input v-model="formData.sdWorkgroupCode" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="班组名称" prop="sdWorkgroupName">
          <el-input v-model="formData.sdWorkgroupName" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="班次名称code" prop="sdShiftCode">
          <el-input v-model="formData.sdShiftCode" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="班次名称" prop="sdShiftName">
          <el-input v-model="formData.sdShiftName" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="数据来源标志(0自动采集、1手工录入)" prop="sdMark">
          <el-input v-model="formData.sdMark" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <!-- <el-form-item label="ads创建时间" prop="cTime">
          <el-input v-model="formData.cTime" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="ads更新时间" prop="uTime">
          <el-input v-model="formData.uTime" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="是否删除 N否Y是" prop="delFlag">
          <el-input v-model="formData.delFlag" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="分区字段-按年" prop="pYearMonth">
          <el-input v-model="formData.pYearMonth" style="width: 370px;" />
        </el-form-item> -->
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="text"
          @click="cancleMethod"
        >
          取消
        </el-button>
        <!-- :loading="crud.status.cu === 2" -->
        <el-button
          v-if="!disabled"
          type="primary"
          @click="saveMethod"
        >
          确认
        </el-button>
      </div>
    </el-dialog>
  </template>

  <script>
  import { form } from '@crud/crud'
  import { edit } from '@/api/energy/energyAdsPackSkuEquipDowntimeRecordFull.js' // 假设编辑接口路径正确，需根据实际情况调整

  export default {
    props: {
      jobStatus: {
        type: Array,
        required: true
      },
      visible: {
        type: Boolean,
        default: false
      },
      formData: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        disabled:false,
        rules: {
          // 根据实际需求添加表单验证规则，如果需要的话
        }
      }
    },
    methods: {
      cancleMethod() {
        this.$emit('update:visible', false)
      },
      // 确认按钮
      saveMethod() {
        edit(this.formData).then(res => {
          // todo: 加上修改成功的反应...
          if (res.code === 200) {
            this.crud.notify('保存成功', 'success')
          }
          this.cancleMethod()
        })
      }
    },
    mounted(){
      if(this.formData.type){
        this.disabled = this.formData.type === 'look'
      }
    }

  }
  </script>

  <style rel="stylesheet/scss" lang="scss" scoped>
  ::v-deep.el-input-number.el-input__inner {
    text-align: left;
  }
  </style>
