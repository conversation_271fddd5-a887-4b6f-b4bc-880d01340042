<template>
  <el-dialog
    append-to-body
    :close-on-click-modal="false"
    :visible="visible"
    title="编辑"
    width="600px"
    @close ="cancleMethod"
  >
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      size="small"
      label-width="160px"
    >
      <el-form-item label="日期" prop="dateTime">
        <el-input v-model="formData.dateTime" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="全厂" prop="wFactory">
        <el-input v-model="formData.wFactory" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装" prop="wPackage">
        <el-input v-model="formData.wPackage" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="酿造" prop="wBrew">
        <el-input v-model="formData.wBrew" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="非生产" prop="wNonproductive">
        <el-input v-model="formData.wNonproductive" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="动力" prop="wPower">
        <el-input v-model="formData.wPower" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="线损" prop="wLineLoss">
        <el-input v-model="formData.wLineLoss" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="过滤" prop="wFilterate">
        <el-input v-model="formData.wFilterate" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="发酵" prop="wFerment">
        <el-input v-model="formData.wFerment" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="糖化" prop="wSaccharify">
        <el-input v-model="formData.wSaccharify" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="瓶装线" prop="wBottledLine">
        <el-input v-model="formData.wBottledLine" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="听装线" prop="wCannedLine">
        <el-input v-model="formData.wCannedLine" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="桶装" prop="wBarrelLine">
        <el-input v-model="formData.wBarrelLine" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="链道润滑" prop="wChainLubrication">
        <el-input v-model="formData.wChainLubrication" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="制冷间" prop="wCoolingRoom">
        <el-input v-model="formData.wCoolingRoom" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="水处理" prop="wWaterTreatment">
        <el-input v-model="formData.wWaterTreatment" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="锅炉" prop="wBoiler">
        <el-input v-model="formData.wBoiler" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="瓶装杀菌机" prop="wBottledSterilization">
        <el-input v-model="formData.wBottledSterilization" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="瓶装洗瓶机" prop="wBottleWashing">
        <el-input v-model="formData.wBottleWashing" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="真空泵" prop="wVacuumPump">
        <el-input v-model="formData.wVacuumPump" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="听装杀菌机" prop="wCannedSterilization">
        <el-input v-model="formData.wCannedSterilization" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装产线1消耗" prop="wPackage1">
        <el-input v-model="formData.wPackage1" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装产线3消耗" prop="wPackage3">
        <el-input v-model="formData.wPackage3" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装产线4消耗" prop="wPackage4">
        <el-input v-model="formData.wPackage4" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装产线5消耗" prop="wPackage5">
        <el-input v-model="formData.wPackage5" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装产线6消耗" prop="wPackage6">
        <el-input v-model="formData.wPackage6" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装产线7消耗" prop="wPackage7">
        <el-input v-model="formData.wPackage7" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装产线8消耗" prop="wPackage8">
        <el-input v-model="formData.wPackage8" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装产线9消耗" prop="wPackage9">
        <el-input v-model="formData.wPackage9" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装产线1洗瓶机" prop="wBottleWash1">
        <el-input v-model="formData.wBottleWash1" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装产线3洗瓶机" prop="wBottleWash3">
        <el-input v-model="formData.wBottleWash3" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装产线8洗瓶机" prop="wBottleWash8">
        <el-input v-model="formData.wBottleWash8" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装产线1杀菌机" prop="wDisinfect1">
        <el-input v-model="formData.wDisinfect1" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装产线3杀菌机" prop="wDisinfect3">
        <el-input v-model="formData.wDisinfect3" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装产线6杀菌机" prop="wDisinfect6">
        <el-input v-model="formData.wDisinfect6" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装产线8杀菌机" prop="wDisinfect8">
        <el-input v-model="formData.wDisinfect8" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装产线9杀菌机" prop="wDisinfect9">
        <el-input v-model="formData.wDisinfect9" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <!-- <el-form-item label="ads 创建时间" prop="cTime">
          <el-input v-model="form.cTime" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="ads 更新时间" prop="uTime">
          <el-input v-model="form.uTime" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="是否删除 N 否 Y 是" prop="delFlag">
          <el-input v-model="form.delFlag" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="年月" prop="pYearMonth">
          <el-input v-model="form.pYearMonth" style="width: 370px;" />
      </el-form-item> -->

    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        type="text"
        @click="cancleMethod"
      >
        取消
      </el-button>

      <!-- :loading="crud.status.cu === 2" -->
      <el-button
        v-if="!disabled"
        type="primary"
        @click="saveMethod"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>

import { form } from '@crud/crud'
import {edit} from '@/api/energy/energyAdsEnergyBaseDataSteamFull.js'
export default {
  props: {
    jobStatus: {
      type: Array,
      required: true
    },
    visible:{
      type:Boolean,
      default:false
    },
    formData:{
      type:Object,
      default:()=>{}
    }
  },
  data() {
    return {
      rules: {
        disabled:false,
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        jobSort: [
          { required: true, message: '请输入序号', trigger: 'blur', type: 'number' }
        ]
      }
    }
  },
  methods:{
    cancleMethod(){
      this.$emit('update:visible',false)
    },
    // 确认按钮
    saveMethod(){
      edit(this.formData).then(res=>{
        // todo: 加上修改成功的反应...
        if(res.code==200){
          this.crud.notify( '保存成功', 'success')
        }
        this.cancleMethod();
      })

    }
    },
    mounted(){
      if(this.formData.type){
        this.disabled = this.formData.type === 'look'
      }
    }
  }

</script>

<style rel="stylesheet/scss" lang="scss" scoped>
 ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
</style>
