<template>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible="visible"
      title="编辑"
      width="600px"
      @close="cancleMethod"
    >
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        size="small"
        label-width="160px"
      >
        <!-- <el-form-item label="产线id" prop="packLineOpenid">
          <el-input v-model="formData.packLineOpenid" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="产线openid工厂运营平台版本" prop="packLineOpenidPro">
          <el-input v-model="formData.packLineOpenidPro" :disabled="disabled" style="width: 370px;" />
        </el-form-item> -->
        <!-- <el-form-item label="工厂编码" prop="packFactoryCode">
          <el-input v-model="formData.packFactoryCode" :disabled="disabled" style="width: 370px;" />
        </el-form-item> -->
        <el-form-item label="产线" prop="packLineName">
          <el-input v-model="formData.packLineName" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="生产日期" prop="packProduceT">
          <el-input v-model="formData.packProduceT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <!-- todo:挨着翻译 -->
        <el-form-item label="指标编码" prop="packCode">
          <el-input v-model="formData.packCode" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="指标值" prop="packValue">
          <el-input v-model="formData.packValue" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="text"
          @click="cancleMethod"
        >
          取消
        </el-button>
        <el-button
          v-if="!disabled"
          type="primary"
          @click="saveMethod"
        >
          确认
        </el-button>
      </div>
    </el-dialog>
  </template>
  
  <script>
  import { form } from '@crud/crud'
  import { edit } from '@/api/energy/energyAdsPackDayYieldFull.js'  // 这里需要替换成实际的接口文件路径
  
  export default {
    props: {
      jobStatus: {
        type: Array,
        required: true
      },
      visible: {
        type: Boolean,
        default: false
      },
      formData: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        disabled: false,
        rules: {
          // 这里可以根据实际需求补充具体的校验规则，以下只是示例占位
          someField: [
            { required: true, message: '请输入内容', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      cancleMethod() {
        this.$emit('update:visible', false)
      },
      saveMethod() {
        edit(this.formData).then(res => {
          if (res.code === 200) {
            this.crud.notify('保存成功', 'success')
          }
          this.cancleMethod()
        })
      },
      mounted() {
        if (this.formData.type) {
          this.disabled = this.formData.type === 'look'
        }
      }
    }
  }
  </script>
  
  <style rel="stylesheet/scss" lang="scss" scoped>
   ::v-deep.el-input-number.el-input__inner {
      text-align: left;
    }
  </style>