<template>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible="visible"
      title="编辑"
      width="600px"
      @close="cancleMethod"
    >
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        size="small"
        label-width="160px"
      >
        <el-form-item label="班次" prop="yldShiftName">
          <el-input v-model="formData.yldShiftName" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="日期" prop="yldDate">
          <el-input v-model="formData.yldDate" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="产线名称" prop="yldLineName">
          <el-input v-model="formData.yldLineName" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="各包装产线产量" prop="yldLineAmount">
          <el-input v-model="formData.yldLineAmount" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="麦汁量" prop="yldWortAmount">
          <el-input v-model="formData.yldWortAmount" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="过滤清酒量" prop="yldFilterSakeAmount">
          <el-input v-model="formData.yldFilterSakeAmount" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="原浆产量" prop="yldOriginalBeerAmount">
          <el-input v-model="formData.yldOriginalBeerAmount" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="鲜啤产量" prop="yldFreshBeerAmount">
          <el-input v-model="formData.yldFreshBeerAmount" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="扎啤产量" prop="yldDraftBeerAmount">
          <el-input v-model="formData.yldDraftBeerAmount" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="发酵液供出混合量" prop="yldMixFermentationBrothAmount">
          <el-input v-model="formData.yldMixFermentationBrothAmount" :disabled="disabled" style="width: 370px;" />
        </el-form-item>

        <el-form-item label="果饮产量" prop="yldFruitDrinkAmount">
          <el-input v-model="formData.yldFruitDrinkAmount" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="听装产量" prop="yldCannedAmount">
          <el-input v-model="formData.yldCannedAmount" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="瓶装产量" prop="yldBottledAmount">
          <el-input v-model="formData.yldBottledAmount" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="桶装产量" prop="yldBarrelAmount">
          <el-input v-model="formData.yldBarrelAmount" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <!-- <el-form-item label="ads创建时间" prop="cTime">
          <el-input v-model="formData.cTime" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="ads更新时间" prop="uTime">
          <el-input v-model="formData.uTime" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="是否删除 N否Y是" prop="delFlag">
          <el-input v-model="formData.delFlag" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="分区字段" prop="pYearMonth">
          <el-input v-model="formData.pYearMonth" style="width: 370px;" />
        </el-form-item> -->
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="text"
          @click="cancleMethod"
        >
          取消
        </el-button>
        <!-- :loading="crud.status.cu === 2" -->
        <el-button
          v-if="!disabled"
          type="primary"
          @click="saveMethod"
        >
          确认
        </el-button>
      </div>
    </el-dialog>
  </template>

  <script>
  import { form } from '@crud/crud'
  import { edit } from '@/api/energy/energyAdsLineYieldFull.js'

  export default {
    props: {
      jobStatus: {
        type: Array,
        required: true
      },
      visible: {
        type: Boolean,
        default: false
      },
      formData: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        disabled:false,
        rules: {
          // 这里根据实际需求添加表单验证规则，如果需要的话
        }
      }
    },
    methods: {
      cancleMethod() {
        this.$emit('update:visible', false)
      },
      // 确认按钮
      saveMethod() {
        edit(this.formData).then(res => {
          // todo: 加上修改成功的反应...
          if (res.code === 200) {
            this.crud.notify('保存成功', 'success')
          }
          this.cancleMethod()
        })
      }
    },
    mounted(){
      if(this.formData.type){
        this.disabled = this.formData.type === 'look'
      }
    }
  }
  </script>

  <style rel="stylesheet/scss" lang="scss" scoped>
  ::v-deep.el-input-number.el-input__inner {
    text-align: left;
  }
  </style>
