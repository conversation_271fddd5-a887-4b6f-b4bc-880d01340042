<template>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible="visible"
      title="编辑"
      width="600px"
      @close="cancleMethod"
    >
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        size="small"
        label-width="160px"
      >
        <el-form-item label="产线" prop="kpiLineOpenid">
          <el-input v-model="formData.kpiLineOpenid" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="生产日期" prop="kpiProductionD">
          <el-input v-model="formData.kpiProductionD" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="品种编码" prop="kpiProductCode">
          <el-input v-model="formData.kpiProductCode" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="计划生产时间" prop="kpiPlanProductionT">
          <el-input v-model="formData.kpiPlanProductionT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="运行时间" prop="kpiRunT">
          <el-input v-model="formData.kpiRunT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="实际运行时间" prop="kpiActualRunT">
          <el-input v-model="formData.kpiActualRunT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="产量标准时间" prop="kpiYieldStandardT">
          <el-input v-model="formData.kpiYieldStandardT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="速度损失时间" prop="kpiSpeedLossT">
          <el-input v-model="formData.kpiSpeedLossT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="停机时间" prop="kpiDowntime">
          <el-input v-model="formData.kpiDowntime" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="设备故障停机时间" prop="kpiDeviceFailureDowntime">
          <el-input v-model="formData.kpiDeviceFailureDowntime" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="设备调整停机时间" prop="kpiDeviceModulationDowntime">
          <el-input v-model="formData.kpiDeviceModulationDowntime" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="包装外因停机时间" prop="kpiDeviceExternalCauseDowntime">
          <el-input v-model="formData.kpiDeviceExternalCauseDowntime" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="包装工艺要求停机时间" prop="kpiPackageTechnologyDowntime">
          <el-input v-model="formData.kpiPackageTechnologyDowntime" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="其他停机时间" prop="kpiOtherDowntime">
          <el-input v-model="formData.kpiOtherDowntime" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="可扣除时间" prop="kpiDeductibleT">
          <el-input v-model="formData.kpiDeductibleT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="可扣除的换瓶型时间" prop="kpiChangeBottleTypeDeductibleT">
          <el-input v-model="formData.kpiChangeBottleTypeDeductibleT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="可扣除的换酒液时间" prop="kpiChangeOfLiquorDeductibleT">
          <el-input v-model="formData.kpiChangeOfLiquorDeductibleT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="可扣除(工艺)刷洗时间" prop="kpiProcessScrubDeductibleT">
          <el-input v-model="formData.kpiProcessScrubDeductibleT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="可扣除计划停机时间" prop="kpiPlanDowntimeDeductibleT">
          <el-input v-model="formData.kpiPlanDowntimeDeductibleT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="不可扣除计划停机时间" prop="kpiPlanDowntimeDeductibleNotT">
          <el-input v-model="formData.kpiPlanDowntimeDeductibleNotT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="可扣除品种转换时间" prop="kpiConversionOfVarietiesDeductibleT">
          <el-input v-model="formData.kpiConversionOfVarietiesDeductibleT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="刷洗时间" prop="kpiScrubT">
          <el-input v-model="formData.kpiScrubT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="品种转换时间" prop="kpiConversionOfVarietiesT">
          <el-input v-model="formData.kpiConversionOfVarietiesT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="可扣除的换瓶盖时间" prop="kpiChangeBottleCapDeductibleT">
          <el-input v-model="formData.kpiChangeBottleCapDeductibleT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="可扣除的换射码时间" prop="kpiChangeCommutativeCodeDeductibleT">
          <el-input v-model="formData.kpiChangeCommutativeCodeDeductibleT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="可扣除的换商标时间" prop="kpiChangeTrademarkDeductibleT">
          <el-input v-model="formData.kpiChangeTrademarkDeductibleT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="可扣除的换标版时间" prop="kpiChangeTargetDeductibleT">
          <el-input v-model="formData.kpiChangeTargetDeductibleT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="可扣除的换纸箱时间" prop="kpiChangeCartonDeductibleT">
          <el-input v-model="formData.kpiChangeCartonDeductibleT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="可扣除的换内包装时间" prop="kpiChangeInnerPackingDeductibleT">
          <el-input v-model="formData.kpiChangeInnerPackingDeductibleT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="未确认原因小停机时间" prop="kpiWithoutConfirmedReasonSmallShutdownT">
          <el-input v-model="formData.kpiWithoutConfirmedReasonSmallShutdownT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="未确认原因小停机时间" prop="kpiProductFetchNum">
          <el-input v-model="formData.kpiProductFetchNum" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
  
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="cancleMethod">
          取消
        </el-button>
        
        <el-button v-if="!disabled" type="primary" @click="saveMethod">
          确认
        </el-button>
      </div>
    </el-dialog>
  </template>
  
  <script>
  import { edit } from '@/api/energy/energyAdsPackKpiIndicatorsSkuFull.js';
  
  export default {
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      formData: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        disabled: false,
        rules: {
          kpiLineOpenid: [
            { required: true, message: '请输入产线', trigger: 'blur' }
          ],
          kpiProductionD: [
            { required: true, message: '请输入生产日期', trigger: 'blur' }
          ]
        }
      };
    },
    methods: {
      cancleMethod() {
        this.$emit('update:visible', false);
      },
      saveMethod() {
        edit(this.formData).then(res => {
          if (res.code === 200) {
            this.crud.notify('保存成功', 'success');
          }
          this.cancleMethod();
        });
      }
    },
    mounted() {
      if (this.formData.type) {
        this.disabled = this.formData.type === 'look';
      }
    }
  };
  </script>
  
  <style scoped lang="scss">
    ::v-deep .el-input-number .el-input__inner {
      text-align: left;
    }
  </style>
  