<template>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible="visible"
      title="编辑"
      width="600px"
      @close="cancleMethod"
    >
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        size="small"
        label-width="160px"
      >
        <el-form-item label="日计划主表ID" prop="spOpenid">
          <el-input v-model="formData.spOpenid" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <!-- <el-form-item label="产线id" prop="spLineOpenid">
          <el-input v-model="formData.spLineOpenid" :disabled="disabled" style="width: 370px;" />
        </el-form-item> -->
        <el-form-item label="产线" prop="spLineName">
          <el-input v-model="formData.spLineName" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="日期" prop="spDate">
          <el-input v-model="formData.spDate" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="品种编码、" prop="spCode">
          <el-input v-model="formData.spCode" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="计划量(瓶)" prop="spPlanAmount">
          <el-input v-model="formData.spPlanAmount" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="清酒量(KL)" prop="spSakeAmount">
          <el-input v-model="formData.spSakeAmount" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="酒液品种编码" prop="spSakeCode">
          <el-input v-model="formData.spSakeCode" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="品种（包装）转换类型" prop="spTransformType">
          <el-input v-model="formData.spTransformType" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="品种转换时间" prop="spTransformTime">
          <el-input v-model="formData.spTransformTime" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="备注" prop="spRemark">
          <el-input v-model="formData.spRemark" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <!-- <el-form-item label="ads创建时间" prop="cTime">
          <el-input v-model="formData.cTime" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="ads更新时间" prop="uTime">
          <el-input v-model="formData.uTime" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="是否删除 N否Y是" prop="delFlag">
          <el-input v-model="formData.delFlag" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="分区字段-按年" prop="pYearMonth">
          <el-input v-model="formData.pYearMonth" style="width: 370px;" />
        </el-form-item> -->
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="text"
          @click="cancleMethod"
        >
          取消
        </el-button>
        <!-- :loading="crud.status.cu === 2" -->
        <el-button
          v-if="!disabled"
          type="primary"
          @click="saveMethod"
        >
          确认
        </el-button>
      </div>
    </el-dialog>
  </template>

  <script>
  import { form } from '@crud/crud'
  import { edit } from '@/api/energy/energyAdsCategoryPlanFull.js'

  export default {
    props: {
      jobStatus: {
        type: Array,
        required: true
      },
      visible: {
        type: Boolean,
        default: false
      },
      formData: {
        type: Object,
        default: () => {}
      }
    },
    data() {

      return {
        rules: {
          disabled:false,
          // 根据实际需求添加表单验证规则，如果需要的话
        }
      }
    },
    methods: {
      cancleMethod() {
        this.$emit('update:visible', false)
      },
      // 确认按钮
      saveMethod() {
        edit(this.formData).then(res => {
          // todo: 加上修改成功的反应...
          if (res.code === 200) {
            this.crud.notify('保存成功', 'success')
          }
          this.cancleMethod()
        })
      }
    },
    mounted(){
      if(this.formData.type){
        this.disabled = this.formData.type === 'look'
      }
    }

  }
  </script>

  <style rel="stylesheet/scss" lang="scss" scoped>
  ::v-deep.el-input-number.el-input__inner {
    text-align: left;
  }
  </style>
