<template>
  <el-dialog
    append-to-body
    :close-on-click-modal="false"
    :visible="visible"
    title="编辑"
    width="600px"
    @close ="cancleMethod"
  >
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      size="small"
      label-width="160px"
    >
      <!-- <el-form-item
        label="日期"
        prop="dateTime"
      >
        <el-input
          v-model="formData.dateTime"
          style="width: 370px;"
        />
      </el-form-item> -->
      <el-form-item label="日期" prop="dateTime">
        <el-input v-model="formData.dateTime" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="全厂" prop="cFactory">
        <el-input v-model="formData.cFactory" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="CO2采购量" prop="cCo2Procure">
        <el-input v-model="formData.cCo2Procure" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="CO2回收量" prop="cCo2Recovery">
        <el-input v-model="formData.cCo2Recovery" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="酿造" prop="cBrew">
        <el-input v-model="formData.cBrew" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装" prop="cPackage">
        <el-input v-model="formData.cPackage" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="瓶装线" prop="cBottledLine">
        <el-input v-model="formData.cBottledLine" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="听装线" prop="cCannedLine">
        <el-input v-model="formData.cCannedLine" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="桶装" prop="cBarrelLine">
        <el-input v-model="formData.cBarrelLine" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装1产线消耗" prop="cPackageLine1">
        <el-input v-model="formData.cPackageLine1" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装3产线消耗" prop="cPackageLine3">
        <el-input v-model="formData.cPackageLine3" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装4产线消耗" prop="cPackageLine4">
        <el-input v-model="formData.cPackageLine4" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装5产线消耗" prop="cPackageLine5">
        <el-input v-model="formData.cPackageLine5" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装6产线消耗" prop="cPackageLine6">
        <el-input v-model="formData.cPackageLine6" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装7产线消耗" prop="cPackageLine7">
        <el-input v-model="formData.cPackageLine7" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装8产线消耗" prop="cPackageLine8">
        <el-input v-model="formData.cPackageLine8" :disabled="disabled" style="width: 370px;" />
      </el-form-item>
      <el-form-item label="包装9产线消耗" prop="cPackageLine9">
        <el-input v-model="formData.cPackageLine9" :disabled="disabled" style="width: 370px;" />
      </el-form-item>


    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        type="text"
        @click="cancleMethod"
      >
        取消
      </el-button>

      <!-- :loading="crud.status.cu === 2" -->
      <el-button
        v-if="!disabled"
        type="primary"
        @click="saveMethod"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>

import { form } from '@crud/crud'
import {edit} from '@/api/energy/energyAdsEnergyBaseDataCo2Full.js'
export default {
  props: {
    jobStatus: {
      type: Array,
      required: true
    },
    visible:{
      type:Boolean,
      default:false
    },
    formData:{
      type:Object,
      default:()=>{}
    }
  },
  data() {
    return {
      disabled:false,
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        jobSort: [
          { required: true, message: '请输入序号', trigger: 'blur', type: 'number' }
        ]
      }
    }
  },
  methods:{
    cancleMethod(){
      this.$emit('update:visible',false)
    },
    // 确认按钮
    saveMethod(){
      edit(this.formData).then(res=>{
        // todo: 加上修改成功的反应...
        if(res.code==200){
          this.crud.notify( '保存成功', 'success')
        }
        this.cancleMethod();
      })

    }
    },
    mounted(){
      if(this.formData.type){
        this.disabled = this.formData.type === 'look'
      }
    }
  }

</script>

<style rel="stylesheet/scss" lang="scss" scoped>
 ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
</style>
