<template>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible="visible"
      title="编辑"
      width="600px"
      @close="cancleMethod"
    >
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        size="small"
        label-width="180px"
      >
        <el-form-item label="日计划主数据OPENID" prop="mainOpenid">
          <el-input v-model="formData.mainOpenid" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="产线" prop="mainLineName">
          <el-input v-model="formData.mainLineName" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        
        <el-form-item label="日期" prop="mainDate">
          <el-input v-model="formData.mainDate" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="计划开始时间" prop="mainPlanStartT">
          <el-input v-model="formData.mainPlanStartT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="计划结束时间" prop="mainPlanEndT">
          <el-input v-model="formData.mainPlanEndT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="计划生产时长(分)" prop="mainPlanProductionT">
          <el-input v-model="formData.mainPlanProductionT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="更新时间" prop="mainUpdateTime">
          <el-input v-model="formData.mainUpdateTime" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="录入人" prop="mainStaffName">
          <el-input v-model="formData.mainStaffName" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="审核人" prop="mainAuditor">
          <el-input v-model="formData.mainAuditor" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="审核状态" prop="mainAuditStatus">
          <el-input v-model="formData.mainAuditStatus" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="变更状态" prop="mainChangeAuditStatus">
          <el-input v-model="formData.mainChangeAuditStatus" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="计划检修时间（时长）" prop="mainPlanOverhaulT">
          <el-input v-model="formData.mainPlanOverhaulT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="计划大刷洗时间（时长）" prop="mainPlanBigScrubT">
          <el-input v-model="formData.mainPlanBigScrubT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="计划过程刷洗时间（时长）" prop="mainPlanProcessScrubT">
          <el-input v-model="formData.mainPlanProcessScrubT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="计划小刷洗时间（时长）" prop="mainPlanSmallScrubT">
          <el-input v-model="formData.mainPlanSmallScrubT" :disabled="disabled" style="width: 370px;" />
        </el-form-item>
        <!-- <el-form-item label="ads创建时间" prop="cTime">
          <el-input v-model="formData.cTime" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="ads更新时间" prop="uTime">
          <el-input v-model="formData.uTime" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="是否删除 N否Y是" prop="delFlag">
          <el-input v-model="formData.delFlag" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="分区字段-按年" prop="pYearMonth">
          <el-input v-model="formData.pYearMonth" style="width: 370px;" />
        </el-form-item> -->
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          v-if="!disabled"
          type="text"
          @click="cancleMethod"
        >
          取消
        </el-button>
        <!-- :loading="crud.status.cu === 2" -->
        <el-button
          type="primary"
          @click="saveMethod"
        >
          确认
        </el-button>
      </div>
    </el-dialog>
  </template>

  <script>
  import { form } from '@crud/crud'
  import { edit } from '@/api/energy/energyAdsDayPlanMasterDataFull.js'

  export default {
    props: {
      jobStatus: {
        type: Array,
        required: true
      },
      visible: {
        type: Boolean,
        default: false
      },
      formData: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        disabled:false,
        rules: {
          // 根据实际需求添加表单验证规则，如果需要的话
        }
      }
    },
    methods: {
      cancleMethod() {
        this.$emit('update:visible', false)
      },
      // 确认按钮
      saveMethod() {
        edit(this.formData).then(res => {
          // todo: 加上修改成功的反应...
          if (res.code === 200) {
            this.crud.notify('保存成功', 'success')
          }
          this.cancleMethod()
        })
      }
    },
    mounted(){
      if(this.formData.type){
        this.disabled = this.formData.type === 'look'
      }
    }

  }
  </script>

  <style rel="stylesheet/scss" lang="scss" scoped>
  ::v-deep.el-input-number.el-input__inner {
    text-align: left;
  }
  </style>
