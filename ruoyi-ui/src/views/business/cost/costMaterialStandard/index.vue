<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="财年" prop="fiscalYear">
        <el-input
          v-model="queryParams.fiscalYear"
          placeholder="请输入财年"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="期间" prop="period">
        <el-date-picker clearable
          v-model="queryParams.period"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择期间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="物料代码" prop="materialCode">
        <el-input
          v-model="queryParams.materialCode"
          placeholder="请输入物料代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物料名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入物料名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="SAP品牌" prop="sapBrand">
        <el-input
          v-model="queryParams.sapBrand"
          placeholder="请输入SAP品牌"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="费用性质" prop="natureOfExpenses">
        <el-input
          v-model="queryParams.natureOfExpenses"
          placeholder="请输入费用性质"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="材料三级" prop="materialLevel3">
        <el-input
          v-model="queryParams.materialLevel3"
          placeholder="请输入材料三级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="材料四级" prop="materialLevel4">
        <el-input
          v-model="queryParams.materialLevel4"
          placeholder="请输入材料四级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="成本项目代码" prop="costProjectCode">
        <el-input
          v-model="queryParams.costProjectCode"
          placeholder="请输入成本项目代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="成本项目名称" prop="costProjectName">
        <el-input
          v-model="queryParams.costProjectName"
          placeholder="请输入成本项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标准单价" prop="standardUnitPrice">
        <el-input
          v-model="queryParams.standardUnitPrice"
          placeholder="请输入标准单价"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标准耗用量" prop="standardConsumption">
        <el-input
          v-model="queryParams.standardConsumption"
          placeholder="请输入标准耗用量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标准金额" prop="standardAmount">
        <el-input
          v-model="queryParams.standardAmount"
          placeholder="请输入标准金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标准损耗率(%)" prop="standardLossRate">
        <el-input
          v-model="queryParams.standardLossRate"
          placeholder="请输入标准损耗率(%)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标准损耗金额" prop="standardLossAmount">
        <el-input
          v-model="queryParams.standardLossAmount"
          placeholder="请输入标准损耗金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标准合计金额" prop="standardAmountSum">
        <el-input
          v-model="queryParams.standardAmountSum"
          placeholder="请输入标准合计金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="预处理牛奶标准损耗率%" prop="processedLossRate">
        <el-input
          v-model="queryParams.processedLossRate"
          placeholder="请输入预处理牛奶标准损耗率%"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="灌包装牛奶标准损耗率%" prop="packagedLossRate">
        <el-input
          v-model="queryParams.packagedLossRate"
          placeholder="请输入灌包装牛奶标准损耗率%"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remarks">
        <el-input
          v-model="queryParams.remarks"
          placeholder="请输入备注"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="修改人" prop="updateUser">
        <el-input
          v-model="queryParams.updateUser"
          placeholder="请输入修改人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建人" prop="createUser">
        <el-input
          v-model="queryParams.createUser"
          placeholder="请输入创建人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['cost:costMaterialStandard:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['cost:costMaterialStandard:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['cost:costMaterialStandard:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['cost:costMaterialStandard:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="costMaterialStandardList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="大区编码" align="center" prop="regionCode" />
      <el-table-column label="大区名称" align="center" prop="regionName" />
      <el-table-column label="工厂代码" align="center" prop="factoryCode" />
      <el-table-column label="工厂名称" align="center" prop="factoryName" />
      <el-table-column label="财年" align="center" prop="fiscalYear" />
      <el-table-column label="期间" align="center" prop="period" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.period, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="物料代码" align="center" prop="materialCode" />
      <el-table-column label="物料名称" align="center" prop="materialName" />
      <el-table-column label="SAP品牌" align="center" prop="sapBrand" />
      <el-table-column label="费用性质" align="center" prop="natureOfExpenses" />
      <el-table-column label="材料三级" align="center" prop="materialLevel3" />
      <el-table-column label="材料四级" align="center" prop="materialLevel4" />
      <el-table-column label="成本项目代码" align="center" prop="costProjectCode" />
      <el-table-column label="成本项目名称" align="center" prop="costProjectName" />
      <el-table-column label="标准单价" align="center" prop="standardUnitPrice" />
      <el-table-column label="标准耗用量" align="center" prop="standardConsumption" />
      <el-table-column label="标准金额" align="center" prop="standardAmount" />
      <el-table-column label="标准损耗率(%)" align="center" prop="standardLossRate" />
      <el-table-column label="标准损耗金额" align="center" prop="standardLossAmount" />
      <el-table-column label="标准合计金额" align="center" prop="standardAmountSum" />
      <el-table-column label="预处理牛奶标准损耗率%" align="center" prop="processedLossRate" />
      <el-table-column label="灌包装牛奶标准损耗率%" align="center" prop="packagedLossRate" />
      <el-table-column label="备注" align="center" prop="remarks" />
      <el-table-column label="修改人" align="center" prop="updateUser" />
      <el-table-column label="创建人" align="center" prop="createUser" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['cost:costMaterialStandard:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['cost:costMaterialStandard:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改材料标准导入对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="财年" prop="fiscalYear">
          <el-input v-model="form.fiscalYear" placeholder="请输入财年" />
        </el-form-item>
        <el-form-item label="期间" prop="period">
          <el-date-picker clearable
            v-model="form.period"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择期间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="物料代码" prop="materialCode">
          <el-input v-model="form.materialCode" placeholder="请输入物料代码" />
        </el-form-item>
        <el-form-item label="物料名称" prop="materialName">
          <el-input v-model="form.materialName" placeholder="请输入物料名称" />
        </el-form-item>
        <el-form-item label="SAP品牌" prop="sapBrand">
          <el-input v-model="form.sapBrand" placeholder="请输入SAP品牌" />
        </el-form-item>
        <el-form-item label="费用性质" prop="natureOfExpenses">
          <el-input v-model="form.natureOfExpenses" placeholder="请输入费用性质" />
        </el-form-item>
        <el-form-item label="材料三级" prop="materialLevel3">
          <el-input v-model="form.materialLevel3" placeholder="请输入材料三级" />
        </el-form-item>
        <el-form-item label="材料四级" prop="materialLevel4">
          <el-input v-model="form.materialLevel4" placeholder="请输入材料四级" />
        </el-form-item>
        <el-form-item label="成本项目代码" prop="costProjectCode">
          <el-input v-model="form.costProjectCode" placeholder="请输入成本项目代码" />
        </el-form-item>
        <el-form-item label="成本项目名称" prop="costProjectName">
          <el-input v-model="form.costProjectName" placeholder="请输入成本项目名称" />
        </el-form-item>
        <el-form-item label="标准单价" prop="standardUnitPrice">
          <el-input v-model="form.standardUnitPrice" placeholder="请输入标准单价" />
        </el-form-item>
        <el-form-item label="标准耗用量" prop="standardConsumption">
          <el-input v-model="form.standardConsumption" placeholder="请输入标准耗用量" />
        </el-form-item>
        <el-form-item label="标准金额" prop="standardAmount">
          <el-input v-model="form.standardAmount" placeholder="请输入标准金额" />
        </el-form-item>
        <el-form-item label="标准损耗率(%)" prop="standardLossRate">
          <el-input v-model="form.standardLossRate" placeholder="请输入标准损耗率(%)" />
        </el-form-item>
        <el-form-item label="标准损耗金额" prop="standardLossAmount">
          <el-input v-model="form.standardLossAmount" placeholder="请输入标准损耗金额" />
        </el-form-item>
        <el-form-item label="标准合计金额" prop="standardAmountSum">
          <el-input v-model="form.standardAmountSum" placeholder="请输入标准合计金额" />
        </el-form-item>
        <el-form-item label="预处理牛奶标准损耗率%" prop="processedLossRate">
          <el-input v-model="form.processedLossRate" placeholder="请输入预处理牛奶标准损耗率%" />
        </el-form-item>
        <el-form-item label="灌包装牛奶标准损耗率%" prop="packagedLossRate">
          <el-input v-model="form.packagedLossRate" placeholder="请输入灌包装牛奶标准损耗率%" />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="form.remarks" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="修改人" prop="updateUser">
          <el-input v-model="form.updateUser" placeholder="请输入修改人" />
        </el-form-item>
        <el-form-item label="创建人" prop="createUser">
          <el-input v-model="form.createUser" placeholder="请输入创建人" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCostMaterialStandard, getCostMaterialStandard, delCostMaterialStandard, addCostMaterialStandard, updateCostMaterialStandard } from "@/api/service/cost/costMaterialStandard";

export default {
  name: "CostMaterialStandard",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 材料标准导入表格数据
      costMaterialStandardList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        regionCode: null,
        regionName: null,
        factoryCode: null,
        factoryName: null,
        fiscalYear: null,
        period: null,
        materialCode: null,
        materialName: null,
        sapBrand: null,
        natureOfExpenses: null,
        materialLevel3: null,
        materialLevel4: null,
        costProjectCode: null,
        costProjectName: null,
        standardUnitPrice: null,
        standardConsumption: null,
        standardAmount: null,
        standardLossRate: null,
        standardLossAmount: null,
        standardAmountSum: null,
        processedLossRate: null,
        packagedLossRate: null,
        remarks: null,
        updateUser: null,
        createUser: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询材料标准导入列表 */
    getList() {
      this.loading = true;
      listCostMaterialStandard(this.queryParams).then(response => {
        this.costMaterialStandardList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        regionCode: null,
        regionName: null,
        factoryCode: null,
        factoryName: null,
        fiscalYear: null,
        period: null,
        materialCode: null,
        materialName: null,
        sapBrand: null,
        natureOfExpenses: null,
        materialLevel3: null,
        materialLevel4: null,
        costProjectCode: null,
        costProjectName: null,
        standardUnitPrice: null,
        standardConsumption: null,
        standardAmount: null,
        standardLossRate: null,
        standardLossAmount: null,
        standardAmountSum: null,
        processedLossRate: null,
        packagedLossRate: null,
        remarks: null,
        updateTime: null,
        updateUser: null,
        createTime: null,
        createUser: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加材料标准导入";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getCostMaterialStandard(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改材料标准导入";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCostMaterialStandard(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCostMaterialStandard(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除材料标准导入编号为"' + ids + '"的数据项？').then(function() {
        return delCostMaterialStandard(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('cost/costMaterialStandard/export', {
        ...this.queryParams
      }, `costMaterialStandard_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
