<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="大区" prop="region">
        <el-select v-model="queryParams.region" filterable :filter-method="filterValueRegion" @change="changeRegion" placeholder="请选择大区">
          <el-option
            v-for="item in optionsSwitch.region"
            :key="item.key"
            :label="item.label"
            :value="item.key"
          >
            <span style="float: left">{{ item.value }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="工厂" prop="factoryCode">
        <el-select v-model="queryParams.factoryCode" :filter-method="filterValueFactory" filterable  placeholder="请选择工厂">
          <el-option
            v-for="item in optionsSwitch.factory"
            :key="item.key"
            :label="item.label"
            :value="item.key">
            <span style="float: left">{{ item.value }}</span>
          </el-option>
        </el-select>
      </el-form-item>

<!--      <span v-if="advanced">-->
<!--        <el-form-item label="一级代码" prop="level1Code">-->
<!--        <el-select v-model="queryParams.factoryCode" :filter-method="filterValueFactory" filterable-->
<!--                   placeholder="请选择工厂">-->
<!--          <el-option-->
<!--            v-for="item in optionsSwitch.factory"-->
<!--            :key="item.key"-->
<!--            :label="item.label"-->
<!--            :value="item.key">-->
<!--            <span style="float: left">{{ item.value }}</span>-->
<!--          </el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--        <el-form-item label="工厂" prop="factoryCode">-->
<!--        <el-select v-model="queryParams.factoryCode" :filter-method="filterValueFactory" filterable-->
<!--                   placeholder="请选择工厂">-->
<!--          <el-option-->
<!--            v-for="item in optionsSwitch.factory"-->
<!--            :key="item.key"-->
<!--            :label="item.label"-->
<!--            :value="item.key">-->
<!--            <span style="float: left">{{ item.value }}</span>-->
<!--          </el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--        <el-form-item label="工厂" prop="factoryCode">-->
<!--        <el-select v-model="queryParams.factoryCode" :filter-method="filterValueFactory" filterable-->
<!--                   placeholder="请选择工厂">-->
<!--          <el-option-->
<!--            v-for="item in optionsSwitch.factory"-->
<!--            :key="item.key"-->
<!--            :label="item.label"-->
<!--            :value="item.key">-->
<!--            <span style="float: left">{{ item.value }}</span>-->
<!--          </el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--        <el-form-item label="工厂" prop="factoryCode">-->
<!--        <el-select v-model="queryParams.factoryCode" :filter-method="filterValueFactory" filterable-->
<!--                   placeholder="请选择工厂">-->
<!--          <el-option-->
<!--            v-for="item in optionsSwitch.factory"-->
<!--            :key="item.key"-->
<!--            :label="item.label"-->
<!--            :value="item.key">-->
<!--            <span style="float: left">{{ item.value }}</span>-->
<!--          </el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--        <el-form-item label="工厂" prop="factoryCode">-->
<!--        <el-select v-model="queryParams.factoryCode" :filter-method="filterValueFactory" filterable-->
<!--                   placeholder="请选择工厂">-->
<!--          <el-option-->
<!--            v-for="item in optionsSwitch.factory"-->
<!--            :key="item.key"-->
<!--            :label="item.label"-->
<!--            :value="item.key">-->
<!--            <span style="float: left">{{ item.value }}</span>-->
<!--          </el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--                <el-form-item label="工厂" prop="factoryCode">-->
<!--        <el-select v-model="queryParams.factoryCode" :filter-method="filterValueFactory" filterable-->
<!--                   placeholder="请选择工厂">-->
<!--          <el-option-->
<!--            v-for="item in optionsSwitch.factory"-->
<!--            :key="item.key"-->
<!--            :label="item.label"-->
<!--            :value="item.key">-->
<!--            <span style="float: left">{{ item.value }}</span>-->
<!--          </el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--                <el-form-item label="工厂" prop="factoryCode">-->
<!--        <el-select v-model="queryParams.factoryCode" :filter-method="filterValueFactory" filterable-->
<!--                   placeholder="请选择工厂">-->
<!--          <el-option-->
<!--            v-for="item in optionsSwitch.factory"-->
<!--            :key="item.key"-->
<!--            :label="item.label"-->
<!--            :value="item.key">-->
<!--            <span style="float: left">{{ item.value }}</span>-->
<!--          </el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      </span>-->


<!--      <el-form-item label="一级代码" prop="level1Code">-->
<!--        <el-input-->
<!--          v-model="queryParams.level1Code"-->
<!--          placeholder="请输入一级代码"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="一级名称" prop="level1Name">-->
<!--        <el-input-->
<!--          v-model="queryParams.level1Name"-->
<!--          placeholder="请输入一级名称"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="二级代码" prop="level2Code">-->
<!--        <el-input-->
<!--          v-model="queryParams.level2Code"-->
<!--          placeholder="请输入二级代码"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="二级名称" prop="level2Name">-->
<!--        <el-input-->
<!--          v-model="queryParams.level2Name"-->
<!--          placeholder="请输入二级名称"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="三级代码" prop="level3Code">-->
<!--        <el-input-->
<!--          v-model="queryParams.level3Code"-->
<!--          placeholder="请输入三级代码"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="三级名称" prop="level3Name">-->
<!--        <el-input-->
<!--          v-model="queryParams.level3Name"-->
<!--          placeholder="请输入三级名称"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="四级代码" prop="level4Code">-->
<!--        <el-input-->
<!--          v-model="queryParams.level4Code"-->
<!--          placeholder="请输入四级代码"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="三级报警" prop="error">-->
<!--        <el-input-->
<!--          v-model="queryParams.error"-->
<!--          placeholder="请输入三级报警"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="四级名称" prop="level4Name">-->
<!--        <el-input-->
<!--          v-model="queryParams.level4Name"-->
<!--          placeholder="请输入四级名称"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="五级代码" prop="level5Code">-->
<!--        <el-input-->
<!--          v-model="queryParams.level5Code"-->
<!--          placeholder="请输入五级代码"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="五级名称" prop="level5Name">-->
<!--        <el-input-->
<!--          v-model="queryParams.level5Name"-->
<!--          placeholder="请输入五级名称"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="六级代码" prop="level6Code">-->
<!--        <el-input-->
<!--          v-model="queryParams.level6Code"-->
<!--          placeholder="请输入六级代码"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="六级名称" prop="level6Name">-->
<!--        <el-input-->
<!--          v-model="queryParams.level6Name"-->
<!--          placeholder="请输入六级名称"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="修改人" prop="updateUser">-->
<!--        <el-input-->
<!--          v-model="queryParams.updateUser"-->
<!--          placeholder="请输入修改人"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="创建人" prop="createUser">-->
<!--        <el-input-->
<!--          v-model="queryParams.createUser"-->
<!--          placeholder="请输入创建人"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="备注" prop="remarks">-->
<!--        <el-input-->
<!--          v-model="queryParams.remarks"-->
<!--          placeholder="请输入备注"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button :icon="advanced ? 'up-outlined' : 'down-outlined'" size="mini" @click="unfold">{{ advanced ? '收起' : '展开' }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['efficiency:loss:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['efficiency:loss:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['efficiency:loss:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['efficiency:loss:export']"
        >导出</el-button>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="warning"-->
<!--          plain-->
<!--          icon="el-icon-download"-->
<!--          size="mini"-->
<!--          @click="templateDownload"-->
<!--          v-hasPermi="['efficiency:loss:templateDownload']"-->
<!--        >下载导入模板</el-button>-->
<!--      </el-col>-->
      <el-col :span="1.5">
      <el-button
        type="info"
        icon="el-icon-upload2"
        size="mini"
        @click="handleImport"
      >导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      border
      style="width: 100%;"
      height="480px"
      v-loading="loading"
      :data="lossList"
      :row-style="{height:'10px'}"
      size = "mini"
      @selection-change="handleSelectionChange"
      table-layout="auto" >

      <el-table-column type="selection" align="center"/>

      <el-table-column label="工厂编码" align="center" prop="factoryCode" />
      <el-table-column label="工厂名称" min-width="150" align="center" prop="factoryName" />
      <el-table-column label="一级代码" align="center" prop="level1Code" />
      <el-table-column label="一级名称" min-width="150" align="center" prop="level1Name" />
      <el-table-column label="二级代码" align="center" prop="level2Code" />
      <el-table-column label="二级名称" min-width="150" align="center" prop="level2Name" />
      <el-table-column label="三级代码" align="center" prop="level3Code" />
      <el-table-column label="三级名称" min-width="150" align="center" prop="level3Name" />
      <el-table-column label="四级代码" align="center" prop="level4Code" />
      <el-table-column label="四级名称" min-width="280" align="center" prop="level4Name" />
      <el-table-column label="五级代码" align="center" prop="level5Code" />
      <el-table-column label="五级名称" min-width="300" align="center" prop="level5Name" />
      <el-table-column label="六级代码" align="center" prop="level6Code" />
      <el-table-column label="六级名称" min-width="150" align="center" prop="level6Name" />
      <el-table-column label="创建人" align="center" prop="createBy" />
      <el-table-column label="备注" align="center" prop="remark" />

      <el-table-column label="操作" min-width="120px" fixed="right" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['efficiency:loss:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['efficiency:loss:remove']"
          >删除</el-button>
        </template>
      </el-table-column>

    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改损失代码对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form label-position="left" ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col v-if = displayId :span="12">
            <el-form-item label="id" prop="id">
              <el-input v-model="form.id" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="大区" prop="region">
              <el-select v-model="form.region" filterable @change="changeRegion" placeholder="请选择大区">
                <el-option
                  v-for="item in optionsSwitch.region"
                  :key="item.key"
                  :label="item.label"
                  :value="item.key"
                ><span style="float: left">{{ item.value }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工厂" prop="factoryCode">
              <el-select v-model="form.factoryCode" @change="chooseFactoryCode" filterable placeholder="请选择工厂">
                <el-option
                  v-for="item in options.factory"
                  :key="item.key"
                  :label="item.label"
                  :value="item.key">
                  <span style="float: left">{{ item.value }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="一级代码" prop="level1Name">
              <el-input v-model="form.level1Code" placeholder="请输入一级名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="一级名称" prop="level2Code">
              <el-input v-model="form.level1Name" placeholder="请输入二级代码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="二级代码" prop="level2Name">
              <el-input v-model="form.level2Code" placeholder="请输入二级名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="二级名称" prop="level2Name">
              <el-input v-model="form.level2Name" placeholder="请输入二级名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="三级代码" prop="level3Code">
              <el-input v-model="form.level3Code" placeholder="请输入三级代码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="三级名称" prop="level3Name">
              <el-input v-model="form.level3Name" placeholder="请输入三级名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="四级代码" prop="level4Code">
              <el-input v-model="form.level4Code" placeholder="请输入四级代码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="四级名称" prop="level4Name">
              <el-input v-model="form.level4Name" placeholder="请输入四级名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="五级代码" prop="level5Code">
              <el-input v-model="form.level5Code" placeholder="请输入五级代码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="五级名称" prop="level5Name">
              <el-input v-model="form.level5Name" placeholder="请输入五级名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="六级代码" prop="level6Code">
              <el-input v-model="form.level6Code" placeholder="请输入六级代码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="六级名称" prop="level6Name">
              <el-input v-model="form.level6Name" placeholder="请输入六级名称" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>


    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px">
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
<!--          <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据-->
          <el-link type="info" style="font-size:12px" @click="importTemplate">下载模板</el-link>
        </div>
        <div class="el-upload__tip" style="color:red" slot="tip">提示：仅允许导入“xls”或“xlsx”格式文件！</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listLoss, getLoss, delLoss, addLoss, updateLoss } from "@/api/service/efficiency/loss";
import { getToken } from "@/utils/auth";
import {getFactory, getRegion} from "@/api/service/foundation/organization"
import {left} from "core-js/internals/array-reduce";

export default {
  name: "Loss",
  data() {
    return {
      advanced:false,
      displayId:false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 损失代码表格数据
      lossList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        factoryCode: null,
        factoryName: null,
        effCode: null,
        level1Code: null,
        level1Name: null,
        level2Code: null,
        level2Name: null,
        level3Code: null,
        level3Name: null,
        level4Code: null,
        error: null,
        level4Name: null,
        level5Code: null,
        level5Name: null,
        level6Code: null,
        level6Name: null,
        updateUser: null,
        createUser: null,
        remarks: null,
        region: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      // 导入参数
      upload: {
        // 是否显示弹出层（导入）
        open: false,
        // 弹出层标题（导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "business/efficiency/loss/importData"
      },
      options:{
        //大区 options
        region : [],
        // 工厂 options
        factory : []
      },
      optionsSwitch:{
        //大区 options
        region : [],
        // 工厂 options
        factory : []
      },


    };
  },
  created() {
    this.getList();
    this.selectRegion();
  },
  methods: {
    /** 查询损失代码列表 */
    getList() {
      this.loading = true;
      listLoss(this.queryParams).then(response => {
        this.lossList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        factoryCode: null,
        factoryName: null,
        effCode: null,
        level1Code: null,
        level1Name: null,
        level2Code: null,
        level2Name: null,
        level3Code: null,
        level3Name: null,
        level4Code: null,
        error: null,
        level4Name: null,
        level5Code: null,
        level5Name: null,
        level6Code: null,
        level6Name: null,
        updateTime: null,
        updateUser: null,
        createTime: null,
        createUser: null,
        remarks: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 展开收起按钮操作 */
    unfold() {
      this.advanced = !this.advanced;
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.displayId = false;
      this.reset();
      this.open = true;
      this.title = "添加损失代码";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.displayId = true;
      this.reset();
      const id = row.id || this.ids
      getLoss(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改损失代码";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateLoss(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addLoss(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除损失代码编号为"' + ids + '"的数据项？').then(function() {
        return delLoss(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('business/efficiency/loss/export', {
        ...this.queryParams
      }, `损失代码_${new Date().getTime()}.xlsx`)
    },
    /** 导入模板下载 */
    templateDownload() {
      this.download('business/efficiency/loss/templateDownload', {
        ...this.queryParams
      }, `损失代码数据库-模板.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "损失代码导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.templateDownload();
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 查询大区*/
    selectRegion() {
      this.options.region =[];
      getRegion().then(response => {
        this.options.region = response.data
        this.optionsSwitch.region =response.data

      })
    },
    /** 查询工厂*/
    selectFactory(regionCode) {
      this.options.factory =[];
      getFactory(regionCode).then(response => {
        this.options.factory = response.data
        this.optionsSwitch.factory =response.data
      })
    },
    /** 切换大区方法*/
    changeRegion(row) {
      this.queryParams.factoryCode = null;
      this.form.factoryCode = null;
      this.selectFactory(row);
    },
    filterValueRegion(query) {
      if (query !== "") {
        this.optionsSwitch.region = this.options.region.filter((item) => {
          // 这里是用的value选项筛选，默认是label
          return item.value.toLowerCase().indexOf(query.toLowerCase()) > -1;
        });
      } else {
        this.optionsSwitch.region = this.options.region;
      }
    },
    filterValueFactory(query) {
      if (query !== "") {
        this.optionsSwitch.factory = this.options.factory.filter((item) => {
          // 这里是用的value选项筛选，默认是label
          return item.value.toLowerCase().indexOf(query.toLowerCase()) > -1;
        });
      } else {
        this.optionsSwitch.factory = this.options.factory;
      }
    },
    chooseFactoryCode(row) {
      for (let i = 0; i < this.options.factory.length; i++) {
        if(this.options.factory[i].key === row){
          this.form.factoryName = this.options.factory[i].label;
          break;
        }
      }
    }

}
};
</script>
