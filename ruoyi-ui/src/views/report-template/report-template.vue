<template>
  <div class="container">
    <div class="left">
      <el-tree :data="pageData.treeData" :props="pageData.defaultProps" @node-click="handleNodeClick"
        default-expand-all></el-tree>
    </div>
    <div class="_tablepage">
      <!-- 搜索头  -->
      <searchForm ref="searchRef" :searchForm="pageData.searchForm" :searchData="pageData.queryData"
        :searchHandle="pageData.searchHandle"></searchForm>
        <!-- table -->
      <cus-table ref="custable" :isSelection="true" :isIndex="true" :isPagination="true" :isHandle="true"
        :tableCols="pageData.tableCols" :tableHandles="pageData.tableHandles" :tableData="pageData.tableData"
        :tablePage="pageData.tablePage" @handleCurrentChange="searchtablelist()" @selectChange="selectChange">
      </cus-table>

      <modal ref="modalRef" :modalConfig="pageData.modalConfig" :modalForm="pageData.modalForm"
        :modalData="pageData.modalData" :modalHandles="pageData.modalHandles" @closeModal="closeModal()"></modal>

      <modal ref="copyModalRef" :modalConfig="pageData.copyModalConfig" :modalForm="pageData.copyModalForm"
        :modalData="pageData.copyModalData" :modalHandles="pageData.copyModalHandles" @closeModal="closeCopyModal()">
      </modal>
      
      <modal ref="changePwd" :modalConfig="pageData.changePwdConfig" :modalForm="pageData.changePwdForm"
        :modalData="pageData.changePwdModalData" :modalHandles="pageData.changePwdModalHandles"
        @closeModal="closePwdModal()"></modal>

      <modal ref="shareReport" :modalConfig="pageData.shareReportConfig" :modalForm="pageData.shareReportForm"
        :modalData="pageData.shareReportModalData" :modalHandles="pageData.shareReportModalHandles"
        @closeModal="closeShareReportModal()"></modal>
        
      <textarea id="clipboradInput" style="opacity:0;position:absolute" />
    </div>
  </div>
</template>
<script src="./report-template.js"></script>
<style scoped>
.container {
  display: flex;
}

.left {
  box-sizing: border-box;
  width: 232px;
  flex-shrink: 0;
  background: #ffffff;
  border-radius: 6px;
  margin-right: 16px;
  padding: 6px;
}

._tablepage {
  flex: 1;
}
</style>