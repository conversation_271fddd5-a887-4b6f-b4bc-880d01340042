<template>
    <div class="app-container">
      <el-form :model="queryParams" ref="queryFormRef" size="mini" :inline="true" v-show="showSearch" label-width="100px">
        <el-form-item label="任务名称" prop="nodeName">
          <el-input
            v-model="queryParams.nodeName"
            placeholder="请输入任务名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="流程状态" prop="flowStatus">
          <el-select v-model="queryParams.nodeType" placeholder="请选择流程状态" clearable>
            <el-option
              v-for="dict in dict.type.flow_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="抄送人" prop="flowName">
          <el-input
            v-model="queryParams.flowName"
            placeholder="请输入任抄送人"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table ref="fullHeightTableRef" :height="tableHeight" v-loading="loading" :data="instanceList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" fixed />
        <el-table-column label="序号" width="55" align="center" key="id" prop="id">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="流程实例id" width="100" align="center" prop="instanceId" :show-overflow-tooltip="true"/>
        <el-table-column label="流程名称" align="center" prop="flowName" :show-overflow-tooltip="true"/>
        <el-table-column label="任务名称" align="center" prop="nodeName" :show-overflow-tooltip="true"/>
        <el-table-column label="抄送人" align="center" prop="createBy" :show-overflow-tooltip="true"/>
        <el-table-column label="流程状态" align="center" prop="flowStatus" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.flow_status" :value="scope.row.flowStatus"/>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="160" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="240" fixed="right" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="dataDetail(scope.row)">业务数据</el-button>
            <el-button
              size="mini"
              type="text"
              @click="toFlowImage(scope.row)"
              v-hasPermi="['codeDev:flowTodo:chart']"
            >流程图</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <component v-bind:is="approve" v-model="businessId" :taskId="taskId" @refresh="getList"></component>
      <el-dialog title="流程图" :visible.sync="flowChart" width="80%">
        <div style="position: relative;" v-if="imgUrl">
          <div class="status-container">
            <div class="status-ract not-done">未办理</div>
            <div class="status-ract pending">待办理</div>
            <div class="status-ract done">已办理</div>
          </div>
        <img :src="imgUrl" width="100%" style="margin:0 auto"/>
        <flow-table v-if="flowChart" :flow-code="flowCode" :intance-id="intanceId"></flow-table>
        </div>
      </el-dialog>

      <el-dialog title="数据详情" :visible.sync="handleShow" width="80%">
        <!-- <el-table v-loading="listLoading" :data="tableData" element-loading-text="Loading"
          :header-cell-style="{ 'background': '#f5f7fa', 'color': '#666' }" stripe border fit highlight-current-row>
          <el-table-column v-for="item in tableColumns" :key="item.fieldCode" :label="item.fieldName"
            :prop="item.fieldName" show-overflow-tooltip :min-width="item.minWidth" :width="item.width" align="center">
            <template slot-scope="{row}">
              {{ row[item.fieldCode] }}
            </template>
          </el-table-column>
        </el-table> -->
        <form-data-table :tableData="tableData" :tableColumns="tableColumns"> </form-data-table>
        <el-pagination @size-change="handlePageSizeChange" @current-change="handlePageNumChange"
                       :current-page="queryDataParams.pageNum" :page-sizes="[10, 20, 30, 40]" :page-size="queryDataParams.pageSize"
                       layout="total, sizes, prev, pager, next, jumper" :total="queryDataParams.total"></el-pagination>
      </el-dialog>

      <!-- 权限标识：选择用户 -->
      <el-dialog :title="`${dialogTitle}用户选择`" v-if="userVisible" :visible.sync="userVisible" width="80%" append-to-body>
        <selectUser :postParams="postParams" :type="dialogTitle" :selectUser.sync="form.assigneePermission" :userVisible.sync="userVisible" @handleUserSelect="handleUserSelect"></selectUser>
      </el-dialog>
    </div>
  </template>

  <script>
import tableFullHeight from '@/utils/tableFullHeight'
  import * as api from "@/api/flow/execute";
  import { flowImage } from "@/api/flow/definition";
  import selectUser from "@/views/components/selectUser";
  import {getTableCodeAndBusinessId} from "@/api/flow/instance";
  import {getFormDesignList, getSearchConfigList} from "@/api/codeDev/formDesign/formDesign";
  import FormDataTable from "@/views/codeDev/formData/formDataTable.vue";
  import {getFormDataList} from "@/api/codeDev/formData/formData";
import flowTable from "@/views/flow/components/flowTable.vue";

  export default {
  mixins: [tableFullHeight],
    name: "Todo",
    dicts: ['flow_status'],
    components: {
      flowTable,
      FormDataTable,
      selectUser
    },
    data() {
      return {
        // 遮罩层
        loading: true,
        imgUrl: "",
        flowChart: false,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 流程实例表格数据
        instanceList: [],
        // 业务审批页面
        approve: null,
        taskId: "",
        businessId: "",
        handleShow:false,
        listLoading: false,
        tableData: [],
        tableColumns: [],
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          nodeName: null,
          flowStatus: null,
          approver: null,
          flowName: null,
          createTime: null,
        },
        // 数据查询参数
        queryDataParams: {
          searchFormParams: {},
          pageNum: 1,
          pageSize: 10,
          tableName: null,
          tableCode: null,
          // formJsonInfo: null,
          columnStr: '',
          columnArray: [],
          labelArray: [],
          templateId: null,
          formDesignId: null,
          total: null
        },
        formRender: {
          formJson: {},
          formData: {},
          optionData: {}
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {
          assigneePermission: [
            { required: true, message: "请选择", trigger: "change" }
          ],
        },
        dialogTitle: "",
        postParams: {},
        userVisible: false,
        flowCode: '',
        intanceId: '',
      };
    },
    created() {
      this.getList();
    },
    methods: {
      /** 查询流程实例列表 */
      getList() {
        this.loading = true;
        api.copyPage(this.queryParams).then(response => {
          this.instanceList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryFormRef");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id)
        this.single = selection.length!==1
        this.multiple = !selection.length
      },
      /** 办理按钮操作 */
      handle(row) {
        this.taskId = row.id
        this.businessId = row.businessId
        if (row.fromCustom == 'N' && row.fromPath) {
          // 实际情况是，不同条件对应不同的页面，所以用动态组件
          this.approve = (resolve) => require([`@/views/${row.fromPath}`], resolve)
        }
      },
      toFlowImage(row) {
        flowImage(row.instanceId).then(response => {
          this.flowChart = true
          this.imgUrl = "data:image/gif;base64," + response.data;
          this.flowCode = row.flowCode
          this.intanceId = row.instanceId
        });
      },
      /** 转办|加签|委派|减签弹框显示按钮操作 */
      transferShow(row, title) {
        this.form.assigneePermission = null;
        this.taskId = row.id;
        this.dialogTitle = title;
        api.getTaskById(this.taskId).then(res => {
          this.form.assigneePermission = res.data.assigneePermission ? res.data.assigneePermission.split(",") : [];
        });
        this.userVisible = true;
        let operatorTypeObj = {
          "转办": "1",
          "加签": "6",
          "委派": "3",
          "减签": "7"
        };
        this.postParams = {
          url: "interactiveTypeSysUser",
          taskId: row.id,
          operatorType: operatorTypeObj[title]
        };
      },
      // 获取选中用户数据
      handleUserSelect(checkedItemList) {
        this.form.assigneePermission = checkedItemList.map(e => {
          return e.userId;
        });
        let assigneePermission = JSON.parse(JSON.stringify(this.form.assigneePermission));
        let operatorTypeObj = {
          "转办": "2",
          "加签": "6",
          "委派": "3",
          "减签": "7"
        };
        api.interactiveType(this.taskId, Array.isArray(assigneePermission) ? assigneePermission.join(',') : assigneePermission,  operatorTypeObj[this.dialogTitle])
        .then(() => {
          this.$modal.msgSuccess(`${this.dialogTitle}成功`);
          this.getList();
        });
      },
      async dataDetail(row) {
        this.handleShow = true;
        //获取表单数据
        this.taskId = row.id
        this.nodeCode = row.nodeCode
        this.instanceId = row.instanceId
        // 查询表编码和业务数据id
        const resData = await getTableCodeAndBusinessId(row.businessId)
        if (resData.code === 200) {
          //获取表单
          getFormDesignList({ tableCode: resData.data.tableCode }).then(response => {
            this.handleShow = true
            this.rowData = response[0]
            this.queryDataParams.tableCode = this.rowData.tableCode + "_modify"
            this.queryDataParams.formDesignId = this.rowData.id
            this.queryDataParams.mongoIds = resData.data.dataIds
            this.queryDataParams.updateStatus = row.flowStatus
            this.queryDataParams.searchFormParams = {}
            // listFormDesignFieldConfig({ formDesignId: response[0].id, pageSize: 1000, pageNum: 1 }).then(res => {
            //   this.rowData.columnInfos = res.rows
            //   this.getTableColumns()
            // })
            getSearchConfigList(this.queryDataParams.formDesignId).then(result => {
              if (result.code === 200) {
                this.tableColumns = result.data.listConfiguration
              }
            })
            this.formRender.formJson = JSON.parse(this.rowData.formJsonInfo)
            // this.formColumnJson = JSON.parse(this.rowData.formJsonInfo)

            this.getFormDataList()
          })
        } else {
          this.$message.error('查询失败')
        }
      },
      handlePageSizeChange(val) {
        this.queryDataParams.pageSize = val
        this.getFormDataList()
      },
      handlePageNumChange(val) {
        this.queryDataParams.pageNum = val
        this.getFormDataList()
      },
      /**
       * 获取列表数据
       */
      async getFormDataList() {
        const res = await getFormDataList(this.queryDataParams).finally(() => {
          this.listLoading = false
        })
        if (res.code === 200) {
          this.tableData = res.data.dataList
          this.queryDataParams.total = res.data.total
        } else {
          this.$message.error('查询失败')
        }
      },
    }
  };
  </script>
