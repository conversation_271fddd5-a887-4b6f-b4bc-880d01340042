<template>
  <el-table class="flow-table"  v-loading="flowTableDataLoading"
            :data="flowTableData">
    <el-table-column label="节点名称" align="center" prop="nodeName" width="120"
                     show-overflow-tooltip></el-table-column>
    <el-table-column label="协作方式" align="center" prop="handleMethod" width="120"></el-table-column>
    <el-table-column label="节点状态" align="center" prop="doneUserName" width="120" show-overflow-tooltip>
      <template slot-scope="scope">
        <el-tag v-if="scope.row.nodeStatus == 0" type="success">已办理</el-tag>
        <el-tag v-else-if="scope.row.nodeStatus == 1" type="danger">待办理</el-tag>
        <el-tag v-else-if="scope.row.nodeStatus == 2" type="info">未办理</el-tag>
      </template>
    </el-table-column>
    <el-table-column label="已办理" align="center" prop="doneName" show-overflow-tooltip>
      <!-- <template slot-scope="scope">
          <el-tag v-if="scope.row.doneName">{{ scope.row.doneName }}</el-tag>
      </template> -->
    </el-table-column>
    <el-table-column label="未办理" align="center" prop="notDoneName" show-overflow-tooltip>
      <!-- <template slot-scope="scope">
          <el-tag v-if="scope.row.notDoneName">{{ scope.row.notDoneName }}</el-tag>
      </template> -->
    </el-table-column>

  </el-table>
</template>

<script>
import * as api from '@/api/flow/execute'
export default {
  data() {
    return {
      flowTableDataLoading: true,
      flowTableData: [],
    }
  },
  props: {
    flowCode: {
      type: String,
      default: ''
    },
    intanceId: {
      type: String,
      default: ''
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    tableRowClassName({ row, rowIndex }) {
      if (row.nodeStatus == 0) {
        // 已办理
        return 'processed-row';

      } else if (row.nodeStatus == 1) {
        // 待办理
        return 'tobeprocessed-row';
      }
    },
    getList() {
      if (!this.flowCode) return
      this.flowTableDataLoading = true
      api.getNodesByDefCode1(this.flowCode, this.intanceId).then(response => {
        this.flowTableData = response.data
        this.flowTableDataLoading = false

      })
    }
  }
}
</script>
<style lang="scss">
.flow-table {
  .tobeprocessed-row {
    background: rgb(243, 178, 177);
  }

  .processed-row {
    background: rgb(117, 251, 76);
  }
}
</style>
