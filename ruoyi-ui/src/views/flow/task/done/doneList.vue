<template>
  <div class="app-container">
    <div style="display: flex;justify-content: end;">
      <!--      <el-button type="primary" size="mini" @click="handleExport">导出</el-button>-->
      <el-button size="mini" @click="$router.go(-1)">返回</el-button>
    </div>
    <el-table :data="taskList">
      <el-table-column label="序号" width="50" align="center" key="id" prop="id">
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="流程实例id" align="center" prop="instanceId" :show-overflow-tooltip="true" />
      <el-table-column label="开始节点名称" align="center" prop="nodeName" :show-overflow-tooltip="true" />
      <el-table-column label="结束节点名称" align="center" prop="targetNodeName" :show-overflow-tooltip="true" />
      <el-table-column label="审批人" align="center" prop="approver" :show-overflow-tooltip="true" />
      <el-table-column label="部门" align="center" prop="deptName" :show-overflow-tooltip="true" />
      <el-table-column label="协作类型" align="center" prop="cooperateType" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.cooperate_type" :value="scope.row.cooperateType" />
        </template>
      </el-table-column>
      <el-table-column label="协作人" align="center" prop="collaborator" :show-overflow-tooltip="true" />
      <el-table-column label="流程状态" align="center" prop="flowStatus" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.flow_status" :value="scope.row.flowStatus" />
        </template>
      </el-table-column>
      <el-table-column label="审批意见" align="center" prop="message" :show-overflow-tooltip="true" />
      <el-table-column label="审批时间" align="center" prop="createTime" width="160" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <!--      <el-table-column label="业务详情" align="center" width="100">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-button size="mini" v-if="scope.row.ext" @click="handle(scope.row)">查看</el-button>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column label="操作" align="center" width="110" fixed="right" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="openDialog(scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 审批弹窗 -->
    <el-dialog :title="'审批'" ref="formApproveDialog" :visible.sync="detailVisible" width="40%">
      <el-form ref="form" :model="formApprove" label-width="70" style="padding: 0 50px " v-loading="dialogLoading"
        v-if="detailVisible">
        <el-form-item label="审批意见" prop="flowCode">
          <el-input v-model="formApprove.message" disabled maxlength="40" type="textarea" :rows="7" show-word-limit />
        </el-form-item>
        <el-form-item label="图片" prop="favicon">
          <img v-for="(item, index) in formApprove.imgList" :key="index" :src="baseUrl + item.path"
            style="width: 100px; height: 100px;margin: 0 20px" @click="handlePictureCardPreview(item)" alt="" />

        </el-form-item>
        <el-form-item label="附件" prop="favicon">
          <el-upload :headers="headers" :action="uploadUrl" class="upload-demo" multiple disabled :on-preview="downLoad"
            :file-list="formApprove.fileList">
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailVisible = false">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="baseUrl + dialogImageUrl" alt="">
    </el-dialog>
    <component v-if="approve" v-bind:is="approve" :businessId="businessId" :taskId="taskId" :testLeaveVo="testLeaveVo"
      @refresh="fetchTaskList"></component>
  </div>
</template>

<script>
import { doneList1 } from '@/api/flow/execute';
import { exportFormDataApproval } from "@/api/codeDev/formData/formData";
import { getToken } from "@/utils/auth";

export default {
  name: "DoneList",
  dicts: ['flow_status', 'cooperate_type'],
  data() {
    return {
      formApprove: {
        message: "",
        imgList: [],
        fileList: [],
      },
      dialogLoading: false,
      dialogVisible: false,
      dialogImageUrl: '',
      uploadUrl: process.env.VUE_APP_BASE_API + '/file/attachments/upload',
      baseUrl: window.location.origin + "/opsyndex-file",
      detailVisible: false,
      instanceIds: "",
      fromPath: "",
      // 历史任务记录表格数据
      taskList: [],
      taskId: "",
      businessId: "",
      approve: null,
      testLeaveVo: null
    };
  },
  computed: {
    headers() {
      return {
        Authorization: 'Bearer ' + getToken()
      }
    }
  },
  created() {
    this.instanceIds = this.$route.params.instanceIds;
    this.fromPath = this.$route.query.fromPath;
    this.testLeaveVo = this.$route.query.testLeaveVo; // 接收传递过来的testLeaveVo
    this.fetchTaskList();
  },
  methods: {
    // async handleExport() {
    //   const formDesignId = this.$route.query.formDesignId
    //   const tableCode = this.$route.query.tableCode
    //   const tableName = this.$route.query.tableName
    //   const exportFormDataVo = {
    //     bizFormData: {
    //       formDesignId,
    //       instanceId: this.instanceId,
    //       tableCode
    //     },
    //     flowHisTasks: this.taskList
    //   }
    //   const result = await exportFormDataApproval(exportFormDataVo)
    //   const blob = new Blob([result])
    //   const url = window.URL.createObjectURL(blob)
    //   const a = document.createElement('a')
    //   a.href = url
    //   a.download = `${tableName}${Date.now()}.xlsx`
    //   a.click()
    //   URL.revokeObjectURL(url)

    // },
    fetchTaskList() {
      doneList1(this.instanceIds).then(response => {
        this.taskList = response.data;
      });
    },
    handle(row) {
      // 设置任务ID和业务ID
      this.taskId = row.id;
      this.businessId = row.businessId;

      // 获取或设置testLeaveVo数据
      // 解析ext字段的JSON数据
      if (row.ext) {
        try {
          const parsedExt = JSON.parse(row.ext);
          this.testLeaveVo = { ...parsedExt };
        } catch (e) {
          console.error('Invalid ext JSON format:', row.ext);
        }
      }
      // 获取或设置fromPath路径
      const fromPath = row.fromPath || this.fromPath;

      // 如果存在fromPath，则动态加载组件
      if (fromPath) {
        // 通过require动态加载组件
        this.approve = (resolve) => require([`@/views/${fromPath}`], resolve);
      } else {
        this.approve = null;
      }
    },
    openDialog(row) {
      this.dialogLoading = true
      this.formApprove.imgList = []
      this.formApprove.fileList = []
      this.formApprove.message = row.message
      if (row.ext !== null) {
        const parsedObject = JSON.parse(row.ext);
        parsedObject.forEach(item => {
          if (item.type === 'image') {
            this.formApprove.imgList.push(item);
          } else if (item.type === 'file') {
            this.formApprove.fileList.push(item);
          } else {
            console.warn(`未知类型的项目: ${item.type}`, item);
          }
        });
        this.dialogLoading = false
      } else {
        this.formApprove.imgList = []
        this.formApprove.fileList = []
        this.dialogLoading = false
      }
      this.detailVisible = true

    },
    downLoad(file) {
      const downloadUrl = `${window.location.origin}${"/opsyndex-file"}${file.path}`
      this.downloadFile(downloadUrl, file.name)
    },
    downloadFile(fileUrl, fileName) {
      const link = document.createElement("a");
      link.href = fileUrl;
      link.download = fileName; // 可选，浏览器会自动解析文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    handlePictureCardPreview(file) {
      console.log(file)
      this.dialogImageUrl = file.path;
      this.dialogVisible = true;
    },
  }
};
</script>
