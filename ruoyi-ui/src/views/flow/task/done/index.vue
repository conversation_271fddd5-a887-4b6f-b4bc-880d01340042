<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryFormRef" size="mini" :inline="true" v-show="showSearch" label-width="100px">
      <!-- 流程名称输入框 -->
      <el-form-item label="流程名称" prop="nodeName">
        <el-input v-model="queryParams.flowName" placeholder="请输入流程名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- 任务名称输入框 -->
      <el-form-item label="任务名称" prop="nodeName">
        <el-input v-model="queryParams.nodeName" placeholder="请输入任务名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- 流程状态选择框 -->
      <el-form-item label="流程状态" prop="flowStatus">
        <el-select v-model="queryParams.flowStatus" placeholder="请选择流程状态" clearable>
          <el-option v-for="dict in dict.type.flow_status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <!-- 审批时间选择器 -->
      <el-form-item label="发起时间" prop="createTime">
        <el-date-picker v-model="queryDate" type="daterange" value-format="yyyy-MM-dd" range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>

      <!-- 搜索和重置按钮 -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <!-- 流程实例表格 -->
    <el-table ref="fullHeightTableRef" :height="tableHeight" :data="instanceList">
      <!-- 表格列定义 -->
      <el-table-column type="selection" width="55" align="center" fixed />
      <el-table-column label="序号" width="55" align="center" key="id" prop="id">
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="流程实例id" align="center" prop="instanceId" :show-overflow-tooltip="true" />
      <el-table-column label="流程名称" align="center" prop="flowName" :show-overflow-tooltip="true" />
      <el-table-column label="任务名称" align="center" prop="nodeName" :show-overflow-tooltip="true" />
      <el-table-column label="审批人" align="center" prop="approver" :show-overflow-tooltip="true" />
      <el-table-column label="当前审批人" align="center" prop="nextApproveName" :show-overflow-tooltip="true" />
      <el-table-column label="协作类型" align="center" prop="cooperateType" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.cooperate_type" :value="scope.row.cooperateType" />
        </template>
      </el-table-column>
      <el-table-column label="协作人" align="center" prop="collaborator" :show-overflow-tooltip="true" />
      <el-table-column label="流程状态" align="center" prop="flowStatus" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.flow_status" :value="scope.row.flowStatus" />
        </template>
      </el-table-column>
      <el-table-column label="审批时间" align="center" prop="createTime" width="160" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="110" fixed="right" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text"
                     v-hasPermi="['codeDev:flowTodo:record']"
            @click="showDoneList(scope.row.instanceId, scope.row.fromPath, scope.row.testLeaveVo)">审批记录</el-button>
          <el-button size="mini" type="text" @click="toFlowImage(scope.row)" v-hasPermi="['codeDev:flowTodo:chart']">流程图</el-button>
          <el-button size="mini" type="text" @click="dataDetail(scope.row)">业务数据</el-button>
          <el-button size="mini" type="text" @click="withDraw(scope.row)">撤回</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />


    <el-dialog title="流程图" :visible.sync="flowChart" width="80%">
      <div style="position: relative;" v-if="imgUrl">
        <div class="status-container">
          <div class="status-ract not-done">未办理</div>
          <div class="status-ract pending">待办理</div>
          <div class="status-ract done">已办理</div>
        </div>
      <img :src="imgUrl" width="100%" style="margin:0 auto" />
      <flow-table v-if="flowChart" :flow-code="flowCode" :intance-id="intanceId"></flow-table>
      </div>
    </el-dialog>




    <el-dialog title="数据详情" :visible.sync="handleShow" width="80%">
      <!-- <el-table v-loading="listLoading" :data="tableData" element-loading-text="Loading"
        :header-cell-style="{ 'background': '#f5f7fa', 'color': '#666' }" stripe border fit highlight-current-row>
        <el-table-column v-for="item in tableColumns" :key="item.fieldCode" :label="item.fieldName"
          :prop="item.fieldName" show-overflow-tooltip :min-width="item.minWidth" :width="item.width" align="center">
          <template slot-scope="{row}">
            {{ row[item.fieldCode] }}
          </template>
        </el-table-column>
      </el-table> -->
      <form-data-table :tableData="tableData" :tableColumns="tableColumns"> </form-data-table>
      <el-pagination @size-change="handlePageSizeChange" @current-change="handlePageNumChange"
        :current-page="queryDataParams.pageNum" :page-sizes="[10, 20, 30, 40]" :page-size="queryDataParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="queryDataParams.total"></el-pagination>
    </el-dialog>


    <component v-if="approve" v-bind:is="approve" :businessId="businessId" :taskId="taskId" :testLeaveVo="testLeaveVo"
      @refresh="getList"></component>
    <!-- 动态加载的组件 -->
    <el-dialog title="审批详情" :visible.sync="approveDialogVisible" width="80%">
      <component :is="dynamicComponent" v-if="dynamicComponent" :testLeaveVo="testLeaveVo" :taskId="taskId"
        :disabled="false" />
    </el-dialog>
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import { donePage } from "@/api/flow/execute";
import { flowImage } from "@/api/flow/definition";
import { getFormDataList } from '@/api/codeDev/formData/formData'
import { getTableCodeAndBusinessId, withDraw } from '@/api/flow/instance'
import { getFormDesignList } from '@/api/codeDev/formDesign/formDesign'
// import { listFormDesignFieldConfig } from '@/api/codeDev/formDesignFieldConfig/formDesignFieldConfig'
import FormDataTable from "../../../codeDev/formData/formDataTable.vue";
import { getSearchConfigList } from "@/api/codeDev/formDesign/formDesign";
import flowTable from '../../components/flowTable.vue'
export default {
  mixins: [tableFullHeight],
  name: "Done",
  dicts: ['flow_status', 'cooperate_type'],
  data() {
    return {
      flowCode:'',
      intanceId:'',
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      flowChart: false,
      imgUrl: "",
      // 总条数
      total: 0,
      // 流程实例表格数据
      instanceList: [],
      todoDetail: null,
      formRender: {
        formJson: {},
        formData: {},
        optionData: {}
      },
      // formColumnJson: {},
      rowData: {
        tableName: '',
        tableCode: '',
        id: '',
        formJsonInfo: '',
        columnInfo: '',
        columnInfos: []
      },
      queryDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nodeName: null,
        flowStatus: null,
        createBy: null,
        createTime: null,
        fireStart: null,
        fireEnd: null,
      },
      // 数据查询参数
      queryDataParams: {
        searchFormParams: {},
        pageNum: 1,
        pageSize: 10,
        tableName: null,
        tableCode: null,
        formJsonInfo: null,
        columnStr: '',
        columnArray: [],
        labelArray: [],
        templateId: null,
        formDesignId: null,
        total: null
      },
      // 动态加载的组件相关
      approve: null,
      dynamicComponent: null,
      approveDialogVisible: false,
      testLeaveVo: null,
      taskId: null,
      handleShow: false,
      listLoading: false,
      tableData: [],
      tableColumns: [],
      // JsonTableColumns: [],
    };
  },
  created() {
    this.getList();
  },
  components: {
    flowTable,
    FormDataTable
  },
  methods: {
    /**
     * 查询流程实例列表
     */
    getList() {
      // 处理queryParam
      if (this.queryDate) {
        this.queryParams.fireStart = this.queryDate[0];
        this.queryParams.fireEnd = this.queryDate[1];
      }
      donePage(this.queryParams).then(response => {
        this.instanceList = response.rows;
        this.total = response.total;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryFormRef");
      this.handleQuery();
    },
    /** 审批记录 */
    showDoneList(instanceId, fromPath, testLeaveVo) {
      const params = { disabled: false, pageNum: this.queryParams.pageNum, fromPath, testLeaveVo };
      this.$router.push({ path: `/done/doneList/index/${instanceId}`, query: params });
    },
    toFlowImage(row) {
      flowImage(row.instanceId).then(response => {
        this.flowChart = true;
        this.imgUrl = "data:image/gif;base64," + response.data;
        this.flowCode = row.flowCode
        this.intanceId = row.instanceId
      });
    },
    async dataDetail(row) {
      this.handleShow = true;
      //获取表单数据
      this.taskId = row.id
      this.nodeCode = row.nodeCode
      this.instanceId = row.instanceId
      // 查询表编码和业务数据id
      const resData = await getTableCodeAndBusinessId(row.businessId)
      if (resData.code === 200) {
        //获取表单
        getFormDesignList({ tableCode: resData.data.tableCode }).then(response => {
          this.handleShow = true
          this.rowData = response[0]
          this.queryDataParams.tableCode = this.rowData.tableCode + "_modify"
          this.queryDataParams.formDesignId = this.rowData.id
          this.queryDataParams.mongoIds = resData.data.dataIds
          this.queryDataParams.updateStatus = row.flowStatus
          this.queryDataParams.searchFormParams = {}
          // listFormDesignFieldConfig({ formDesignId: response[0].id, pageSize: 1000, pageNum: 1 }).then(res => {
          //   this.rowData.columnInfos = res.rows
          //   this.getTableColumns()
          // })
          getSearchConfigList(this.queryDataParams.formDesignId).then(result => {
            if (result.code === 200) {
              // 过滤掉name为is_valid、del_flag或approvalStatus的元素
              this.tableColumns = result.data.listConfiguration.filter(item =>
                item.name !== 'is_valid' &&
                item.name !== 'del_flag' &&
                item.name !== 'approvalStatus'
              );
            }
          })
          this.formRender.formJson = JSON.parse(this.rowData.formJsonInfo)
          // this.formColumnJson = JSON.parse(this.rowData.formJsonInfo)

          this.getFormDataList()
        })
      } else {
        this.$message.error('查询失败')
      }
    },
    // getTableColumns() {
    //   this.tableColumns = []
    //   this.rowData.columnInfos.sort((a, b) => a.sort - b.sort)
    //   this.rowData.columnInfos = this.rowData.columnInfos.filter(item => item.displayStatus === '1')
    //   let sourceColumn = this.rowData.columnInfos
    //   let sourceJson = JSON.parse(this.rowData.columnInfo)
    //   this.tableColumns.push(...sourceColumn)
    //   // this.JsonTableColumns.push(...sourceJson)
    // },
    handlePageSizeChange(val) {
      this.queryDataParams.pageSize = val
      this.getFormDataList()
    },
    handlePageNumChange(val) {
      this.queryDataParams.pageNum = val
      this.getFormDataList()
    },
    /**
     * 获取列表数据
     */
    async getFormDataList() {
      const res = await getFormDataList(this.queryDataParams).finally(() => {
        this.listLoading = false
      })
      if (res.code === 200) {
        this.tableData = res.data.dataList
        this.queryDataParams.total = res.data.total
      } else {
        this.$message.error('查询失败')
      }
    },
    withDraw(row) {
      let commitParam = { instanceId: row.instanceId, nodeCode: row.nodeCode, skipType: "WITHDRAW", taskId: row.taskId, businessId: row.businessId }
      withDraw(commitParam).then(response => {
        this.$modal.msgSuccess('撤回成功')
        //刷新列表
        this.getList()
      })
    },
  }
};
</script>
<style lang="scss">
.status-container {
  display: flex;
  gap: 20px;
  font-family: Arial, sans-serif;
  position: absolute;
  right: 0;
  top: 0;

  .status-ract {
    padding: 5px 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    margin-bottom: 8px;
  }

  .not-done {
    border: 2px solid rgb(0, 0, 0);
  }

  .pending {
    border: 2px solid rgb(255, 175, 175);
  }

  .done {
    border: 2px solid rgb(0, 255, 0);
  }
}
</style>
