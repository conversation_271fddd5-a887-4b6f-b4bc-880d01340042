<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryFormRef" size="mini" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="流程名称" prop="nodeName">
        <el-input v-model="queryParams.flowName" placeholder="请输入流程名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="任务名称" prop="nodeName">
        <el-input v-model="queryParams.nodeName" placeholder="请输入任务名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker clearable size="small" v-model="queryParams.createTime" type="date" value-format="yyyy-MM-dd"
          placeholder="选择创建时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <!-- <el-button type="primary" icon="el-icon" size="mini" @click="batchHandle">批量通过</el-button> -->
      </el-form-item>
    </el-form>
    <el-table ref="fullHeightTableRef" :height="tableHeight" v-loading="loading" :data="instanceList"
      @selection-change="handleSelectionChange">
      <el-table-column type="expand">
        <template #default="props">
          <el-form style="padding-left: 115px;">
            <el-form-item label="协作方式">{{ props.row.handleMethod }}</el-form-item>
            <el-form-item label="未办理人">
              <el-tag v-if="props.row.noNickName && props.row.noNickName !== ''"
                v-for="(name, index) in props.row.noNickName.split(',').map(name => name.trim())" :key="index"
                type="danger" style="margin: 0 5px;">
                {{ name }}
              </el-tag>
            </el-form-item>
            <el-form-item label="已办理人">
              <el-tag v-if="props.row.doneNickName && props.row.doneNickName !== ''"
                v-for="(name, index) in props.row.doneNickName.split(',').map(name => name.trim())" :key="index"
                type="success" style="margin: 0 5px;">
                {{ name }}
              </el-tag>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column type="selection" width="55" align="center" fixed />
      <el-table-column label="id" width="100" align="center" prop="id" :show-overflow-tooltip="true" />
      <el-table-column label="流程名称" align="center" prop="flowName" :show-overflow-tooltip="true" />
      <el-table-column label="任务名称" align="center" prop="nodeName" :show-overflow-tooltip="true" />
      <el-table-column label="审批人" align="center" prop="approver" :show-overflow-tooltip="true" />
      <el-table-column label="转办人" align="center" prop="transferredBy" :show-overflow-tooltip="true" />
      <el-table-column label="委派人" align="center" prop="delegate" :show-overflow-tooltip="true" />
      <el-table-column label="流程状态" align="center" prop="flowStatus" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.flow_status" :value="scope.row.flowStatus" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="160" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="240" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handle(scope.row)">办理</el-button>
<!--          <el-button size="mini" type="text" @click="transferShow(scope.row, '转办')">转办</el-button>-->
<!--          <el-button size="mini" type="text" @click="transferShow(scope.row, '加签')">加签</el-button>-->
          <!--          <el-button size="mini" type="text" @click="transferShow(scope.row, '委派')">委派-->
          <!--          </el-button>-->
          <!--          <el-button size="mini" type="text" @click="transferShow(scope.row, '减签')">减签-->
          <!--          </el-button>-->
          <el-button size="mini" type="text" v-hasPermi="['codeDev:flowTodo:chart']" @click="toFlowImage(scope.row)">流程图</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <component v-bind:is="approve" v-model="businessId" :taskId="taskId" @refresh="getList"></component>

    <el-dialog title="流程图" :visible.sync="flowChart" width="80%">
      <div style="position: relative;" v-if="flowChartImgUrl">
        <div class="status-container">
          <div class="status-ract not-done">未办理</div>
          <div class="status-ract pending">待办理</div>
          <div class="status-ract done">已办理</div>
        </div>
        <img :src="flowChartImgUrl" width="100%" style="margin:0 auto" />
        <flow-table v-if="flowChart" :flow-code="flowCode" :intance-id="intanceId"></flow-table>
      </div>
    </el-dialog>

    <!-- 权限标识：选择用户 -->
    <el-dialog :title="`${dialogTitle}用户选择`" :visible.sync="userVisible" width="80%" append-to-body>
      <selectUser v-if="userVisible" :postParams="postParams" :type="dialogTitle"
        :selectUser.sync="form.assigneePermission" :userVisible.sync="userVisible" @handleUserSelect="handleUserSelect">
      </selectUser>
    </el-dialog>
    <!-- 办理-->
    <el-dialog title="办理" :visible.sync="handleShow" width="80%">
      <form-data-table :tableData="tableData" :tableColumns="tableColumns" :approvalStatuOptions="approvalStatuOptions"
                       :validOptions="validOptions" :delFlagOptions="delFlagOptions">
        <el-table-column slot="handler-column" label="操作" align="center" fixed="right" width="160">
          <template slot-scope="{ row }">
            <el-button type="text" @click="handleViewFormData(row)">
              查看
            </el-button>
            <el-button v-if="updateType === 'Y'" type="text" @click="handleUpdateFormData(row)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </form-data-table>
      <el-pagination @size-change="handlePageSizeChange" @current-change="handlePageNumChange"
        :current-page="queryDataParams.pageNum" :page-sizes="[10, 20, 30, 40]" :page-size="queryDataParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="queryDataParams.total">
      </el-pagination>

      <el-alert title="审批用户" style="margin: 10px 0;" type="info" :closable="false"
        v-if="approvalMode === '3'"></el-alert>

      <el-form label-width="auto">
        <el-form-item :label="processNode.nodeName" v-for="(processNode, index) in processNodeList" :key="index">
          <el-tag type="success" style="margin: 0 5px;" v-for="(userName, userIndex) in processNode.userName"
            @close="handleDeleteSelectedUser(index, userIndex)" :key="userName" :closable=true>
            {{ userName }}
          </el-tag>
          <el-button type="primary" @click="initUser(processNode)" size="mini">选择用户</el-button>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleBtn()">
          {{ passName }}
        </el-button>
        <el-button v-if="isReturn === 'Y'" type="danger" @click="reject()">
          {{ returnName }}
        </el-button>
        <el-button v-if="isCountersign === 'Y'" type="primary" @click="transferShow(row, '加签')">
          加签
        </el-button>
        <el-button v-if="isTransfer === 'Y'" type="primary" @click="transferShow(row, '转办')">
          转办
        </el-button>
        <el-button @click="handleShow = false">
          返回
        </el-button>
      </div>
    </el-dialog>


    <el-dialog :title="'修改数据'" :visible.sync="formRenderShow" width="80%">
      <v-form-render v-if="formRenderShow" :handle-type="handleType" :form-json="formRender.formJson"
        :form-data="formRender.formData" :downUploadFileCallback="downLoadUploadFile" ref="vFormRef">
      </v-form-render>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveFormData">{{ formRenderButtonName }}</el-button>
      </div>
    </el-dialog>
    <!-- 权限标识：会签票签选择用户 -->
    <el-dialog title="用户选择" :visible.sync="userVisibleNew" width="80%" append-to-body>
      <select-user v-if="userVisibleNew" :selectUser.sync="form.permissionFlag" :userVisible.sync="userVisibleNew"
        @handleUserSelect="handleUserSelect"></select-user>
    </el-dialog>
    <!-- 权限标识：会签票签选择用户 -->
    <el-dialog title="用户选择" :visible.sync="selectUserDialogVisible" width="80%" append-to-body>
      <select-user v-if="selectUserDialogVisible" ref="selectUserRef" :userIds="userIds"
        :userVisible.sync="selectUserDialogVisible" @handleUserSelect="userSelect">
      </select-user>
    </el-dialog>
    <!-- 审批弹窗 -->
    <el-dialog :title="'审批'" :visible.sync="formApproveVisible" width="40%">
      <el-form ref="form" :model="formApprove" :rules="rules" label-width="70" style="padding: 0 50px"
        v-loading="dialogLoading" v-if="handleShow">
        <el-form-item label="审批意见" prop="flowCode">
          <el-input v-model="formApprove.message" placeholder="请输入审批意见" maxlength="40" type="textarea" :rows="7"
            show-word-limit />
        </el-form-item>
        <el-form-item v-if="uploadFiles === 'Y'" label="图片" prop="favicon">
          <el-upload :headers="headers" :action="uploadUrl" list-type="picture-card"
            :on-success="handleUploadImgSuccess" :before-upload="handleBeforeImgUpload"
            :on-preview="handlePictureCardPreview" :on-remove="handleImgRemove">
            <i class="el-icon-plus"></i>
          </el-upload>

        </el-form-item>
        <el-form-item v-if="uploadFiles === 'Y'" label="附件" prop="favicon">
          <el-upload class="upload-demo" :headers="headers" :action="uploadUrl" :on-success="handleUploadSuccess"
            :before-upload="handleBeforeUpload" :on-remove="handleRemove" :before-remove="beforeRemove" multiple
            :file-list="fileList">
            <el-button size="small" type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleApprove">提交</el-button>
        <el-button @click="formApproveVisible = false">取消</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import * as api from '@/api/flow/execute'
import { getNodesByDefCode, getTaskById, updateFormData } from '@/api/flow/execute'
import { flowImage } from '@/api/flow/definition'
import SelectUser from '@/views/components/selectUser'
import {getFormDesign, getFormDesignList} from '@/api/codeDev/formDesign/formDesign'
// import { listFormDesignFieldConfig } from '@/api/codeDev/formDesignFieldConfig/formDesignFieldConfig'
import { getSearchConfigList } from "@/api/codeDev/formDesign/formDesign";
import { getFormDataList } from '@/api/codeDev/formData/formData'
import { batchHandle, getTableCodeAndBusinessId, handleCommit, reject } from '@/api/flow/instance'
import FormDataTable from "../../../codeDev/formData/formDataTable.vue";
import { getToken } from '@/utils/auth'
import {batchUpdateAttachment, listAttachment} from "@/api/file/attachment";
import flowTable from '../../components/flowTable.vue'
export default {
  mixins: [tableFullHeight],
  name: 'Todo',
  dicts: ['flow_status'],
  components: {
    SelectUser,
    FormDataTable,
    flowTable
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      dialogLoading: false,
      flowChartImgUrl: '',
      flowCode: '',
      intanceId: '',
      flowChart: false,
      flowFormData: {},
      // 上传图片附件属性
      imageUrl: '',
      dialogImageUrl: '',
      uploadUrl: process.env.VUE_APP_BASE_API + '/file/attachments/upload',
      baseUrl: window.location.origin + "/opsyndex-file",
      imgFileId: '',
      imgPath: '',
      imgList: [],
      dialogVisible: false,
      disabled: false,
      fileList: [],
      handleStatus: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 流程实例表格数据
      instanceList: [],
      selection: [],
      // 流程节点
      processNodeList: [],
      nowNodeList: [],
      uploadFiles: '',
      isReturn: '',
      isCountersign: '',
      isTransfer: '',
      approvalMode: '',
      userIds: '',
      clickItem: {}, // 点击框框数据
      // 业务审批页面
      approve: null,
      row: {},
      taskId: '',
      nodeCode: '',
      instanceId: '',
      businessId: '',
      message: '',
      // 审批通过按钮名称
      passName: '审批通过',
      // 退回按钮名称
      returnName: '退回',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nodeName: null,
        flowStatus: null,
        approver: null,
        createTime: null,
        mongoIds: []
      },
      formRenderShow: false,
      formRenderButtonName: "",
      handleType: 'view',
      updateType: 'N',
      // 表单参数
      form: { message: '同意' },
      formApprove: {
        message: '同意',
      },
      // 表单校验
      rules: {
        assigneePermission: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      },
      dialogTitle: '',
      postParams: {},
      userVisible: false,
      handleShow: false,
      permissionFlag: null,
      permissionFlagName: null,
      userVisibleNew: false,
      formApproveVisible: false,
      selectUserDialogVisible: false,
      formRender: {
        formJson: {},
        formData: {},
        optionData: {},
        fileFromData:{},
        copyFileFormData:{}
      },
      columnInfoStr:'',
      // formColumnJson: {},
      rowData: {
        tableName: '',
        tableCode: '',
        id: '',
        formJsonInfo: '',
        columnInfo: '',
        columnInfos: []
      },
      listLoading: false,
      tableData: [],
      tableColumns: [],
      approvalStatuOptions: [
        { label: '未发起', value: "-1" },
        { label: '未开始', value: "0" },
        { label: '审批中', value: "1" },
        { label: '审批结束', value: "2" },
        { label: '驳回', value: "3" },
        { label: '撤回', value: "4" }
      ],
      validOptions: [
        {
          label: "无效",
          value: "0"
        }, {
          label: "有效",
          value: "1",
        }
        , {
          label: "锁定",
          value: "2",
        }
      ],
      delFlagOptions: [
        {
          value: "0",
          label: "正常"
        },
        {
          value: "1",
          label: "删除"
        }
      ],
      // 数据查询参数
      queryDataParams: {
        searchFormParams: {},
        pageNum: 1,
        pageSize: 10,
        tableName: null,
        tableCode: null,
        // formJsonInfo: null,
        columnStr: '',
        columnArray: [],
        labelArray: [],
        templateId: null,
        formDesignId: null,
        total: null
      },
      selectedRowId:''
    }
  },
  computed: {
    headers() {
      return {
        Authorization: 'Bearer ' + getToken()
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /**
     * 查询流程实例列表
     */
    getList() {
      this.loading = true
      api.toDoPage(this.queryParams).then(response => {
        this.instanceList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /**
     * 搜索按钮操作
     */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /**
     * 重置按钮操作
     */
    resetQuery() {
      this.resetForm('queryFormRef')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selection = selection
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /**
     * 办理按钮操作
     */
    async handle(row) {
      // 获取表单数据
      this.row = row
      this.taskId = row.id
      this.nodeCode = row.nodeCode
      this.instanceId = row.instanceId
      this.updateType = row.updateType
      // 按钮名称赋值
      this.passName = row.passName != null && row.passName !== '' ? row.passName : '审批通过';
      this.returnName = row.returnName != null && row.returnName !== '' ? row.returnName : '退回';
      // 清空附件信息
      this.imgList = []
      this.fileList = []
      this.dialogImageUrl = ''
      // 查询表编码和业务数据id
      const resData = await getTableCodeAndBusinessId(row.businessId)
      if (resData.code === 200) {
        await getFormDesignList({ tableCode: resData.data.tableCode }).then(response => {
          this.handleShow = true
          // this.form.message = '同意'
          this.rowData = response[0]
          this.queryDataParams.tableCode = this.rowData.tableCode
          this.queryDataParams.formDesignId = this.rowData.id
          this.queryDataParams.mongoIds = resData.data.dataIds
          this.queryDataParams.updateStatus = '1' // 1为审批中
          this.queryDataParams.searchFormParams = {}
          // listFormDesignFieldConfig({ formDesignId: response[0].id, pageSize: 1000, pageNum: 1 }).then(res => {
          //   this.rowData.columnInfos = res.rows
          //   this.getTableColumns()
          // })
          this.formRender.formJson = JSON.parse(this.rowData.formJsonInfo)
          // this.formColumnJson = JSON.parse(this.rowData.formJsonInfo)
          // 解析表单json 遍历出列表的头
          // this.listLoading = false
          getSearchConfigList(this.queryDataParams.formDesignId).then(result => {
            if (result.code === 200) {
              console.log('result.data.listConfiguration', result.data.listConfiguration)
              // 过滤掉name为is_valid、del_flag或approvalStatus的元素
              this.tableColumns = result.data.listConfiguration.filter(item =>
                item.name !== 'is_valid' &&
                item.name !== 'del_flag' &&
                item.name !== 'approvalStatus'
              );
            }
          })
          this.getFormDataList()
        })
        //查询节点信息
        const res = await getNodesByDefCode(row.flowCode)
        if (res.code === 200) {
          //筛选出审批模式为3的下一节点信息
          this.processNodeList = res.data.filter(item => item.nodeCode === row.nextNodeCode && item.nodeType === 1 && item.approvalMode === '3')
          this.nowNodeList = res.data.filter(item => item.nodeCode === row.nodeCode)
          this.uploadFiles = this.nowNodeList[0]?.uploadFiles ?? 'N';
          this.isReturn = this.nowNodeList[0]?.isReturn ?? 'N';
          this.isCountersign = this.nowNodeList[0]?.isCountersign ?? 'N';
          this.isTransfer = this.nowNodeList[0]?.isTransfer ?? 'N';
          if (this.processNodeList.length !== 0) {
            this.approvalMode = this.processNodeList[0].approvalMode
          } else {
            this.approvalMode = ''
          }

        }
      } else {
        this.$message.error('查询失败')
      }
      this.getFormDesignInfo()
    },
    /**
     * 批量通过按钮
     */
    batchHandle() {
      this.$confirm(`是否要执行批量通过`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (this.selection.length === 0) {
          this.$alert('请选择至少一条数据')
        } else {
          this.loading = true
          let params = this.selection.map(s => {
            return {
              instanceId: s.instanceId,
              taskId: s.id,
              nodeCode: s.nodeCode,
              skipType: 'PASS',
              message: '通过'
            }
          })
          batchHandle(params).then(response => {
            this.$modal.msgSuccess('办理成功')
            this.$emit('refresh')
            this.loading = false
            //刷新列表
            this.getList()
          }).catch((e) => {
            this.loading = false
          })
        }
      })
    },
    /**
     * 审核通过按钮
     */
    handleBtn() {
      if (this.processNodeList && this.processNodeList.length !== 0)
        for (let i = 0; i < this.processNodeList.length; i++) {
          if (!this.processNodeList[i].ids || this.processNodeList[i].ids.length === 0) {
            this.$message.error(`${this.processNodeList[i].nodeName} 未选择审批人请选择！`)
            return
          }
        }
      this.formApprove.message = '同意'
      this.formApproveVisible = true
      this.handleStatus = true

      const defaultValue = '同意'
      // this.$prompt(`请输入意见`, '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   // 默认值
      //   inputValue: defaultValue
      // }).then(({ value }) => {

      // let commitParam = {
      //   instanceId: this.instanceId,
      //   taskId: this.taskId,
      //   nodeCode: this.nodeCode,
      //   skipType: 'PASS',
      //   message: defaultValue,
      //   nameOrLabelList: this.rowData.columnInfos,
      //   nodePermissionFlag: this.processNodeList.map(item => {
      //     return {
      //       id: item.ids?.join(",") || "", // 使用可选链操作符和空值合并运算符
      //       code: item.nodeCode
      //     }
      //   })
      // }
      // handleCommit(commitParam).then(response => {
      //   this.$modal.msgSuccess('办理成功')
      //   this.$emit('refresh')
      //   this.handleShow = false
      //   //刷新列表
      //   this.getList()
      // })


      // })
    },
    /**
     * 退回按钮
     */
    reject() {
      this.formApprove.message = '不同意'
      this.formApproveVisible = true
      this.handleStatus = false
      // const defaultValue = '不同意'
      // this.$prompt(`请输入意见`, '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   // 默认值
      //   inputValue: defaultValue
      // }).then(({ value }) => {
      // let msg = skipType === 'PASS' ? '同意' : '不同意'

      // })
    },
    // getTableColumns() {
    //   this.tableColumns = []
    //   // this.rowData.columnInfos.sort((a, b) => a.sort - b.sort)
    //   this.rowData.columnInfos = this.rowData.columnInfos.filter(item => item.displayStatus === '1')
    //   let sourceColumn = this.rowData.columnInfos
    //   let sourceJson = JSON.parse(this.rowData.columnInfo)
    //   this.tableColumns.push(...sourceColumn)
    // },
    handlePageSizeChange(val) {
      this.queryDataParams.pageNum = 1
      this.queryDataParams.pageSize = val
      this.getFormDataList()
    },
    handlePageNumChange(val) {
      this.queryDataParams.pageNum = val
      this.getFormDataList()
    },
    // 获取表单设计详情
    getFormDesignInfo(){
      getFormDesign(this.queryDataParams.formDesignId).then(res => {
        if (res.code === 200) {
          this.columnInfoStr = res.data.columnInfo
        } else {
          this.$message.error('查询失败')
        }
      })
    },
    //获取列表数据
    async getFormDataList() {
      const query = this.queryDataParams
      query.tableCode = this.rowData.tableCode + "_modify"
      // 查询数据
      getFormDataList(query).then(res => {
        if (res.code === 200) {
          this.tableData = res.data.dataList
          this.queryDataParams.total = res.data.total
        } else {
          this.$message.error('查询失败')
        }
      }).finally(() => {
        this.listLoading = false
      })
    },
    toFlowImage(row) {
      flowImage(row.instanceId).then(response => {
        this.flowChart = true
        this.flowChartImgUrl = 'data:image/gif;base64,' + response.data
        this.flowCode = row.flowCode
        this.intanceId = row.instanceId
        this.flowFormData = row
      })
    },
    /** 转办|加签|委派|减签弹框显示按钮操作 */
    transferShow(row, title) {
      this.form.assigneePermission = null
      this.taskId = row.id
      this.dialogTitle = title
      getTaskById(this.taskId).then(res => {
        this.form.assigneePermission = res.data.assigneePermission ? res.data.assigneePermission.split(',') : []
      })
      this.userVisible = true
      let operatorTypeObj = {
        '转办': '1',
        '加签': '6',
        '委派': '3',
        '减签': '7'
      }
      this.postParams = {
        url: 'interactiveTypeSysUser',
        taskId: row.id,
        operatorType: operatorTypeObj[title]
      }
    },
    // 获取选中用户数据
    handleUserSelect(checkedItemList) {
      this.form.assigneePermission = checkedItemList.map(e => {
        this.form.permissionFlag = checkedItemList.map(e => {
          return e.userId
        }).join(',')
        this.form.permissionFlagName = checkedItemList.map(e => {
          return e.userName + ':' + e.nickName
        }).join(',')
        return e.userId
      })
      let assigneePermission = JSON.parse(JSON.stringify(this.form.assigneePermission))
      let operatorTypeObj = {
        '转办': '2',
        '加签': '6',
        '委派': '3',
        '减签': '7'
      }
      api.interactiveType(this.taskId, Array.isArray(assigneePermission) ? assigneePermission.join(',') : assigneePermission, operatorTypeObj[this.dialogTitle])
        .then(() => {
          // this.$modal.msgSuccess(`${this.dialogTitle}成功`)
          this.handleShow = false
          this.$modal.msgSuccess('操作成功')
          this.getList()
        })
    },
    /**
     * 获取选中用户数据
     * @param checkedItemList
     */
    userSelect(checkedItemList) {

      // this.permissionFlag = checkedItemList.map(e => {
      //   return e.userId
      // }).join(',')

      const userName = checkedItemList.map(e => {
        return e.nickName
      })//.join(',')

      const ids = checkedItemList.map(e => {
        return e.userId
      })//.join(',')

      this.processNodeList.forEach(item => {
        if (item.id === this.clickItem.id) {
          item.userName = userName
          item.ids = ids
        }
      })
    },
    async saveFormData() {
      if (this.handleType == "view") {
        this.formRenderShow = false
      } else {
        const formData = await this.$refs.vFormRef.getFormData()
        console.log('zsq22',formData)
        this.$refs.vFormRef
          .getFormData().then((formData) => {
            const insertOrUpdateQuery = {
              tableCode: this.queryDataParams.tableCode,
              updateColumnAndDataObject: Object.assign({}, formData),
              columnAndDataObject: Object.assign({}, this.formRender.formData),
              mongoId: this.formRender.formData._id,
              formDesignId: this.queryDataParams.formDesignId,
              nodeCode: this.nodeCode,
              instanceId: this.instanceId,
              nameOrLabelList: this.rowData.columnInfos,
              columnInfoStr:this.columnInfoStr
            }
            updateFormData(insertOrUpdateQuery).then((res) => {
              if (res.code === 200) {
                this.formRender.formData = JSON.parse(JSON.stringify(formData))
                this.handleBindRelation("edit", this.selectedRowId, this.queryDataParams.formDesignId)
                this.getFormDataList()
              } else {
                this.$message.error('修改失败')
              }
            })
            this.formRenderShow = false
          })

      }

    },
    // 批量绑定关联关系
    async handleBindRelation(handleType, businessId, businessType, fileStatus) {
      console.log('434242',this.tableColumns)
      const fileTypes = ['picture-upload', 'file-upload']
      if (handleType === "add") {
        const uploadResult = this.tableData.find(item => item._id === businessId)
        this.tableColumns.forEach(item => {
          if (fileTypes.includes(item.type)) {
            const uploadList = uploadResult[item.name]
            if (uploadList && uploadList.length > 0) {
              const bindingParams = uploadList.map(item => {
                const fileId = item.fileId
                if (fileId) {
                  const bindingParams = {
                    businessId,
                    businessType,
                    fileStatus,
                    fileId
                  }
                  return bindingParams
                }
              }).filter(_ => _)
              batchUpdateAttachment(bindingParams).then(res => {
                console.log("新建批量关联", res);
              })
            }
          }
        })
      } else {
        this.tableColumns.forEach(item => {
          if (fileTypes.includes(item.type)) {
            const newUploadList = this.formRender.formData[item.name]
            const oldUploadList = this.formRender.copyFileFormData[item.name]
            console.log("file",newUploadList,oldUploadList)
            if (JSON.stringify(oldUploadList) !== JSON.stringify(newUploadList)) {
              if (oldUploadList && oldUploadList.length > 0) {
                // 取消关联
                const bindingParams = oldUploadList.map(item => {
                  const fileId = item.fileId
                  if (fileId) {
                    const bindingParams = {
                      businessId,
                      businessType,
                      fileStatus: "UNBOUND",
                      fileId
                    }
                    return bindingParams
                  }
                }).filter(_ => _)
                batchUpdateAttachment(bindingParams).then(res => {
                  console.log("编辑批量取消关联", res);
                })
              }
              if (newUploadList && newUploadList.length > 0) {
                const bindingParams = newUploadList.map(fileOption => {
                  const bindingParams = {
                    businessId,
                    businessType,
                    fileStatus: "BOUND",
                    fileId: fileOption.fileId
                  }
                  if ("response" in fileOption) {
                    bindingParams.fileId = fileOption.response.data.fileId
                  }
                  return bindingParams
                }).filter(_ => _)
                batchUpdateAttachment(bindingParams).then(res => {
                  console.log("编辑批量关联", res);
                })
              }
            }
          }
        })
      }
    },
    /**
     * 查看表单
     * @param row
     */
    handleViewFormData(row) {
      this.handleType = "view"
      this.formRenderButtonName = "关闭"
      this.formRender.formJson = JSON.parse(this.rowData.formJsonInfo)
      this.formRender.formData = this.handleFileUrl(JSON.parse(JSON.stringify(row)))
      this.formRenderShow = true
      this.$nextTick(() => {
        this.$refs.vFormRef.disableForm()
      })
    },
    /**
     * 更新表单
     * @param row
     */
    handleUpdateFormData(row) {
      console.log(row)
      this.selectedRowId = row.original_id
      this.handleType = "edit"
      this.formRenderButtonName = "保存"
      this.formRender.formJson = JSON.parse(this.rowData.formJsonInfo)
      this.formRender.formData = this.handleFileUrl(JSON.parse(JSON.stringify(row)))
      // this.formRender.fileFromData = this.handleFileUrl(JSON.parse(JSON.stringify(row)))
      this.formRender.copyFileFormData = this.handleFileUrl(JSON.parse(JSON.stringify(row)))
      this.formRenderShow = true
    },
    handleDeleteSelectedUser(index, userIndex) {
      this.processNodeList[index].userName.splice(userIndex, 1)
      this.processNodeList[index].ids.splice(userIndex, 1)
      this.processNodeList = JSON.parse(JSON.stringify(this.processNodeList))
    },
    /**
     * 打开用户选择弹窗
     * @param processNode
     */
    initUser(processNode) {
      // 发起流程 反显用
      this.clickItem = processNode
      processNode.ids = processNode.ids ? processNode.ids : []
      this.selectUserDialogVisible = true
      if (!processNode.ids || !processNode.ids.length) {
        this.$nextTick(() => {
          this.$refs.selectUserRef.$refs.tableRef.clearSelection()
        })
      }
      this.userIds = processNode.ids.join(",")
    },
    /**
     * 图片操作
     */
    handleBeforeImgUpload(file) {
      // 通常会校验文件格式 校验通过 返回 true  校验失败返回 false
      // 只能上传jpeg/jpg/png文件，且不超过20M
      const type = ['image/jpeg', 'image/jpg', 'image/png']
      const isPic = type.includes(file.type)
      if (!isPic) {
        this.$message.error('只能上传jpeg/jpg/png文件')
        return false
      }
      const isLt20M = file.size / 1024 / 1024 < 20
      if (!isLt20M) {
        this.$message.error('上传文件大小不能超过20M!')
        return false
      }
      return true
    },
    handleUploadImgSuccess(file) {
      // 把新上传的图片信息添加到 imgList 数组
      this.imgList.push({
        fileId: file.data.fileId,
        path: file.data.relativePath,
        name: file.data.name
      });

    },
    handleImgRemove(file) {
      let fileId = file.response?.data?.fileId;
      if (fileId) {
        this.imgList = this.imgList.filter(item => item.fileId !== fileId);
      } else {

      }
    },
    handlePictureCardPreview(file) {
      // this.dialogImageUrl = file.response.data.relativePath;
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    /**
     * 附件操作
     */
    handleBeforeUpload(file) {
      // 通常会校验文件格式 校验通过 返回 true  校验失败返回 false
      // 只能上传jpeg/jpg/png文件，且不超过20M
      // const type = ['image/jpeg', 'image/jpg', 'image/png']
      // const isPic = type.includes(file.type)
      // if (!isPic) {
      //   this.$message.error('只能上传jpeg/jpg/png文件')
      //   return false
      // }
      const isLt100M = file.size / 1024 / 1024 < 100
      if (!isLt100M) {
        this.$message.error('上传文件大小不能超过100M!')
        return false
      }
      return true
    },
    handleUploadSuccess(file) {
      // 把新上传的附件信息添加到 fileList 数组
      this.fileList.push({
        fileId: file.data.fileId,
        path: file.data.relativePath,
        name: file.data.name
      });
    },
    handleRemove(file) {
      let fileId = file.response?.data?.fileId;
      if (fileId) {
        this.fileList = this.fileList.filter(item => item.fileId !== fileId);
      } else {

      }
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    // 审批确定
    handleApprove() {
      this.dialogLoading = true
      const attachments = [
        ...this.imgList.map(item => ({
          ...item,
          type: 'image', // 标记为图片类型
        })),
        ...this.fileList.map(item => ({
          ...item,
          type: 'file',  // 标记为文件类型
        })),
      ];

      // true 走审批通过 否则 走退回
      if (this.handleStatus) {
        let commitParam = {
          instanceId: this.instanceId,
          taskId: this.taskId,
          nodeCode: this.nodeCode,
          skipType: 'PASS',
          message: this.formApprove.message,
          fileList: JSON.stringify(attachments, null),
          nameOrLabelList: this.rowData.columnInfos,
          nodePermissionFlag: this.processNodeList.map(item => {
            return {
              id: item.ids?.join(",") || "", // 使用可选链操作符和空值合并运算符
              code: item.nodeCode
            }
          })
        }
        handleCommit(commitParam).then(response => {
          this.batchBinding(attachments)
          this.$modal.msgSuccess('办理成功')
          this.$emit('refresh')
          this.dialogLoading = false
          this.formApproveVisible = false
          this.handleShow = false
          this.fileList = []
          this.imgList = []
          //刷新列表
          this.getList()
        }).catch(() => {
          this.formApproveVisible = false
        }).finally(() => {
          this.dialogLoading = false
        })
      } else {
        let commitParam = {
          instanceId: this.instanceId,
          taskId: this.taskId,
          nodeCode: this.nodeCode,
          skipType: 'REJECT',
          message: this.formApprove.message,
          fileList: JSON.stringify(attachments, null),
          nameOrLabelList: this.rowData.columnInfos,
          nodePermissionFlag: this.processNodeList.map(item => {
            return {
              id: item.ids?.join(",") || "", // 使用可选链操作符和空值合并运算符
              code: item.nodeCode
            }
          })
        }
        handleCommit(commitParam).then(response => {
          this.batchBinding(attachments)
          this.$modal.msgSuccess('退回成功')
          this.$emit('refresh')
          this.dialogLoading = false
          this.formApproveVisible = false
          this.handleShow = false
          this.fileList = []
          this.imgList = []
          //刷新列表
          this.getList()
        }).catch(() => {
          this.formApproveVisible = false
        }).finally(() => {
          this.dialogLoading = false
        })
      }
      const bindingParams = []

    },
    //附件绑定
    batchBinding(parm) {
      if (parm.length !== 0 && Array.isArray(parm)) {
        const fileParams = parm.map(item => ({
          fileId: item.fileId,
          businessId: this.taskId,
          fileStatus: "BOUND",
          businessType: null
        }));
        batchUpdateAttachment(fileParams).then(res => {
        })
      }
    },
    async downLoadUploadFile(fileList, fileName) {
      if (this.handleType === "add") {
        this.$message.warning("新建不可下载文件")
        return
      }
      let currentFile = null
      fileList.forEach(fileOption => {
        if ("response" in fileOption) {
          if (fileName === fileOption.response.data.name) { }
          currentFile = fileOption.response.data
        } else {
          if (fileOption.name === fileName) {
            currentFile = fileOption
          }
        }
      })
      if (!currentFile) return
      const businessId = this.selectedRowId
      const businessType = this.queryDataParams.formDesignId
      const fileId = currentFile.fileId
      const result = await listAttachment({
        businessId,
        businessType,
        fileId
      })
      if (result.code == 200) {
        const rows = result.rows
        if (rows && rows.length > 0) {
          const fileOption = rows[0]
          const relativePath = fileOption.relativePath
          const fileName = fileOption.fileName
          // const downloadUrl = `${process.env.VUE_APP_BASE_API}${relativePath}`
          const downloadUrl = `${window.location.origin}${"/opsyndex-file"}${relativePath}`
          this.downloadFile(downloadUrl, fileName)
        }
      }
    },
    downloadFile(fileUrl, fileName) {
      const link = document.createElement("a");
      link.href = fileUrl;
      link.download = fileName; // 可选，浏览器会自动解析文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    handleFileUrl(formData) {
      const fileTypes = ['picture-upload'] // 'file-upload'
      this.tableColumns.forEach(item => {
        if (fileTypes.includes(item.type)) {
          const fileList = formData[item.name]
          if (fileList && fileList.length) {
            fileList.forEach(i => {
              i.url = `${window.location.origin}${"/opsyndex-file"}${i.relativePath}`
            })
          }
        }
      })
      return formData
    },
    handleDownload(file) {
    }
  }
}
</script>
<style lang="scss">
.status-container {
  display: flex;
  gap: 20px;
  font-family: Arial, sans-serif;
  position: absolute;
  right: 0;
  top: 0;

  .status-ract {
    padding: 5px 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    margin-bottom: 8px;
  }

  .not-done {
    border: 2px solid rgb(0, 0, 0);
  }

  .pending {
    border: 2px solid rgb(255, 175, 175);
  }

  .done {
    border: 2px solid rgb(0, 255, 0);
  }
}
</style>
