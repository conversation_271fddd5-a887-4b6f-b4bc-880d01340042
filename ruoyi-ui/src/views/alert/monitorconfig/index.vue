<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryFormRef" size="small" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item label="指标类型" prop="targetType">
        <el-select v-model="queryParams.targetType" filterable clearable>
          <el-option
            v-for="dict in options.targetTypeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="指标名称" prop="targetName">
        <el-input
          v-model="queryParams.targetName"
          placeholder="请输入指标名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['alert:monitorconfig:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['alert:monitorconfig:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['alert:monitorconfig:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['alert:monitorconfig:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table ref="fullHeightTableRef" :height="tableHeight" v-loading="loading" :data="monitorconfigList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="指标分类" align="center" prop="targetType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.warning_target_type" :value="scope.row.targetType"/>
        </template>
      </el-table-column>
      <el-table-column label="指标名称" align="center" width="180px" prop="targetName" />
      <el-table-column label="数据源" align="center" width="120px" prop="dataSourceId">
        <template slot-scope="scope">
          {{ getDisplayName(options.dataSourceOptions,scope.row.dataSourceId,'id','name') }}
        </template>
      </el-table-column>
      <el-table-column label="执行方式" align="center" prop="executeWay">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.warning_execute_way" :value="scope.row.executeWay"/>
        </template>
      </el-table-column>
      <!-- <el-table-column label="数据库/架构" align="center" prop="warehouse" /> -->
      <el-table-column label="监控类型" align="center" prop="monitorType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.warning_monitor_type" :value="scope.row.monitorType"/>
        </template>
      </el-table-column>
<!--      <el-table-column label="指标监控" align="center" width="200px" >-->
<!--        <template slot-scope="scope">-->
<!--           <div>{{ scope.row.tableName}}</div>-->
<!--           <div>{{ scope.row.columnName }}</div>-->
<!--           <div>-->
<!--            <dict-tag :options="dict.type.warning_statistics" :value="scope.row.statFunction"/>-->
<!--           </div>-->
<!--           <div>-->
<!--            {{ scope.row.grepColumnName }}-->
<!--           </div>-->
<!--           <div>-->
<!--            {{  scope.row.filterCondition }}-->
<!--           </div>-->
<!--           <div>-->
<!--            {{ scope.row.statSql }}-->
<!--           </div>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="下次执行时间" align="center" prop="nextDateTime" width="200px"/>
<!--      <el-table-column label="统计函数" align="center" prop="statFunction" >-->
<!--        <template slot-scope="scope">-->
<!--          <dict-tag :options="dict.type.warning_statistics" :value="scope.row.statFunction"/>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="时间列名" align="center" prop="timeColumnName" />-->
      <!-- <el-table-column label="分组列名" align="center" prop="grepColumnName" /> -->
      <!-- <el-table-column label="过滤条件" align="center" prop="filterCondition" /> -->
<!--      <el-table-column label="启用状态" align="center" prop="taskStatus">-->
<!--        <template slot-scope="scope">-->
<!--          <dict-tag :options="dict.type.warning_task_status" :value="scope.row.taskStatus"/>-->
<!--        </template>-->
<!--      </el-table-column>-->

      <el-table-column label="启用状态" align="center" width="150">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.taskStatus"
            active-color="#00A854"
            active-text="启用"
            :active-value="'INVOCATION'"
            inactive-color="#F04134"
            inactive-text="停用"
            :inactive-value="'DEACTIVATE'"
            @change="changeSwitch(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="下次触发时间" align="center" width="120">
        <template slot-scope="scope">
          <el-popover
            placement="bottom"
            width="300"
            @show="nextTriggerTime(scope.row)"
          >
            <h5 v-html="triggerNextTimes" />
            <el-button slot="reference" size="small">查看</el-button>
          </el-popover>
        </template>
      </el-table-column>

      <el-table-column label="cron" width="150px" align="center" prop="cron" />

      <el-table-column label="重跑类型" align="center" prop="rerunType" />
      <!-- <el-table-column label="统计SQL" align="center" prop="statSql" /> -->
      <el-table-column label="备注" align="center" width="180px" prop="remark" />
      <el-table-column label="操作" align="center" width="300px" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="handleAddRule(scope.row)"
          >绑定规则</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="triggerTask(scope.row)"
          >执行一次</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-link"
            @click="logJump(scope.row)"
          >日志</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['alert:monitorconfig:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['alert:monitorconfig:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改数据监控配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-position="left" label-width="110px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="指标名称" prop="targetName">
              <el-input v-model="form.targetName" placeholder="请输入指标名称"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-dialog
              title="提示"
              :visible.sync="showCronBox"
              width="60%"
              append-to-body
            >
              <cron v-model="form.cron" />
              <span slot="footer" class="dialog-footer">
                <el-button @click="showCronBox = false;">关闭</el-button>
                <el-button type="primary" @click="showCronBox = false">确 定</el-button>
              </span>
            </el-dialog>
            <el-form-item label="Cron" prop="cron">
              <el-input v-model="form.cron" auto-complete="off" placeholder="请输入Cron表达式">
                <el-button v-if="!showCronBox" slot="append" icon="el-icon-turn-off" title="打开图形配置" @click="showCronBox = true" />
                <el-button v-else slot="append" icon="el-icon-open" title="关闭图形配置" @click="showCronBox = false" />
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="指标类型" prop="targetType">
              <el-select v-model="form.targetType" clearable>
                <el-option
                  v-for="dict in options.targetTypeOptions"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="执行方式" prop="executeWay">
              <el-select v-model="form.executeWay" clearable>
                <el-option
                  v-for="dict in options.executeOptions"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- <el-form-item label="数据源" prop="dataSourceId">
              <el-input v-model="form.dataSourceId" placeholder="请输入数据源ID" />
            </el-form-item> -->
            <el-form-item label="数据源" prop="dataSourceId">
              <el-select v-model="form.dataSourceId" @change="getTableNameList(form.dataSourceId)" clearable filterable>
                <el-option
                  v-for="dict in options.dataSourceOptions"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="监控类型" prop="monitorType">
                  <el-select v-model="form.monitorType"  clearable >
                <el-option
                  v-for="dict in options.monitorTypeOptions"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="数据库/架构" prop="warehouse">
              <el-input v-model="form.warehouse" placeholder="请输入数据库/架构" />
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item label="表名" prop="tableName" v-if="form.monitorType === 'TABLE' || form.monitorType === 'COLUMN'">
              <el-select v-model="form.tableName" @change="changeTableName" filterable clearable>
                <el-option
                  v-for="dict in options.tableNameOptions"
                  :key="dict.tableName"
                  :label="dict.tableName"
                  :value="dict.tableName"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="统计周期" prop="statisticsCycle" v-if="form.monitorType === 'TABLE' || form.monitorType === 'COLUMN'">
              <el-col :span="12">
                <el-input-number v-model="form.statisticsCycleNumber" :min="1" :max="1000" size="mini"></el-input-number>
              </el-col>
              <el-col :span="12">
                <el-select v-model="form.statisticsCycle" clearable>
                <el-option
                  v-for="dict in options.cycleOptions"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                />
              </el-select>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="列名" prop="columnName" v-if="form.monitorType === 'COLUMN'">
              <el-select v-model="form.columnName" clearable>
                <el-option
                  v-for="dict in options.columnNameOptions"
                  :key="dict.columnName"
                  :label="dict.columnName"
                  :value="dict.columnName"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="统计函数" prop="statFunction" v-if="form.monitorType === 'TABLE' || form.monitorType === 'COLUMN'">
              <el-select v-model="form.statFunction" clearable>
                <el-option
                  v-for="dict in options.statisticsOptions"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="时间列名" prop="timeColumnName" v-if="form.monitorType === 'TABLE' || form.monitorType === 'COLUMN'">
              <el-select v-model="form.timeColumnName" clearable>
                <el-option
                  v-for="dict in options.columnNameOptions"
                  :key="dict.columnName"
                  :label="dict.columnName"
                  :value="dict.columnName"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分组列名" prop="grepColumnName" v-if="form.monitorType === 'TABLE' || form.monitorType === 'COLUMN'">
              <el-select v-model="form.grepColumnName" clearable>
                <el-option
                  v-for="dict in options.columnNameOptions"
                  :key="dict.columnName"
                  :label="dict.columnName"
                  :value="dict.columnName"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="过滤条件" prop="filterCondition" v-if="form.monitorType === 'TABLE' || form.monitorType === 'COLUMN'">
              <el-input v-model="form.filterCondition" placeholder="请输入过滤条件" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="重跑类型" prop="rerunType">
              <el-select v-model="form.rerunType" clearable>
                <el-option
                  v-for="dict in options.rerunTypeOptions"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="启动状态" prop="taskStatus">
              <el-select v-model="form.taskStatus" clearable>
                <el-option
                  v-for="dict in options.taskStatusOptions"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="统计SQL" prop="statSql" v-if="form.monitorType === 'SQL_LANGUAGE'">
          <sql-editor ref="sqlEditor" v-model="statSql" @changeTextarea="handleSQLChange" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 新增规则弹窗 -->
     <rule-list-modal v-if="openRuleDialog" :openRuleDialog.sync="openRuleDialog" :configId="configId"></rule-list-modal>
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import {
  addMonitorconfig,
  delMonitorconfig,
  getMonitorconfig,
  listMonitorconfig,
  taskTrigger,
  updateMonitorconfig,
  updateStatus
} from "@/api/alert/monitorconfig/api";
import Cron from "@/components/Cron/index.vue";
import SqlEditor from "@/components/SqlEditor/index.vue";
import {listDataSource} from "@/api/metadata/datasource"
import {listDataTable} from "@/api/metadata/datatable"
import {listDataColumn} from "@/api/metadata/datacolumn"
import ruleListModal from "../common/ruleListModal.vue";
import * as job from "@/api/datax/datax-job-info";
import {encodeSqlToEncryptedBase64} from "@/utils/sqlHandle";

export default {
  mixins: [tableFullHeight],
  name: "Monitorconfig",
  dicts: ['warning_execute_way','warning_rerun_type','warning_target_type','warning_monitor_type','warning_task_status','warning_statistics','warning_statistics_cycle'],
  components: {SqlEditor, Cron,ruleListModal},
  data() {
    return {
      configId:'',//配置id
      openRuleDialog: false,
      // cron 选择器
      showCronBox: false,
      // 遮罩层
      loading: true,
      // 下次执行时间
      triggerNextTimes:'',
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数据监控配置表格数据
      monitorconfigList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenantId: null,
        revision: null,
        dataSourceId: null,
        executeWay: null,
        warehouse: null,
        statSql: '',
        monitorType: null,
        tableName: null,
        columnName: null,
        statFunction: null,
        timeColumnName: null,
        grepColumnName: null,
        filterCondition: null,
        taskStatus: null,
        cron: null,
        targetName: null,
        targetType: null,
        rerunType: null
      },
      statSql: '',
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      // 下拉选择
      options:{
        // 监控执行方式字典
        executeOptions : [],
        // 启用状态字典
        taskStatusOptions : [],
        // 指标类型字典
        targetTypeOptions : [],
        // 监控类型字典
        monitorTypeOptions : [],
        // 重跑类型
        rerunTypeOptions : [],
        // 数据源
        dataSourceOptions : [],
        // 表名
        tableNameOptions : [],
        // 列名
        columnNameOptions : [],
        // 统计函数
        statisticsOptions: [],
        // 统计周期
        cycleOptions: [],
      },

    };
  },
  created() {
    this.getDataSourceList();
    this.getList();
    // 执行方式
    this.getDicts("warning_execute_way").then(response => {
      this.options.executeOptions = response.data;
    });
    // 启动状态
    this.getDicts("warning_task_status").then(response => {
      this.options.taskStatusOptions = response.data;
    });
    // 监控类型
    this.getDicts("warning_monitor_type").then(response => {
      this.options.monitorTypeOptions = response.data;
    });
    // 指标类型
    this.getDicts("warning_target_type").then(response => {
      this.options.targetTypeOptions = response.data;
    });
    // 重跑类型
    this.getDicts("warning_rerun_type").then(response => {
      this.options.rerunTypeOptions = response.data;
    });
    // 统计函数
    this.getDicts("warning_statistics").then(response => {
      this.options.statisticsOptions = response.data;
    });
    // 统计函数
    this.getDicts("warning_statistics_cycle").then(response => {
      this.options.cycleOptions = response.data;
    });

  },
  methods: {
    handleAddRule(row){
      this.openRuleDialog = true;
      this.configId = row.id
    },
    handleSQLChange(sql) {
      this.form.statSql = sql
    },
    changeSwitch(row) {
      const data = {
        id: row.id,
        taskStatus: row.taskStatus
      }
      updateStatus(data).then(response => {
        console.log(response)
        if (response.code === 200) {
          this.$modal.msgSuccess(response.msg);
        } else {
          this.$modal.msgError(response.msg);
        }
      });
    },

    /** 查询数据监控配置列表 */
    getList() {
      this.loading = true;
      listMonitorconfig(this.queryParams).then(response => {
        this.monitorconfigList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.statSql = ''
      this.form = {
        tenantId: null,
        revision: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        id: null,
        remark: null,
        delFlag: null,
        dataSourceId: null,
        executeWay: null,
        warehouse: null,
        statSql: null,
        monitorType: null,
        tableName: null,
        columnName: null,
        statFunction: null,
        timeColumnName: null,
        grepColumnName: null,
        filterCondition: null,
        taskStatus: null,
        cron: null,
        targetName: null,
        targetType: null,
        rerunType: null,
        statisticsCycle: null,
        statisticsCycleNumber: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryFormRef");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加数据监控配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getMonitorconfig(id).then(response => {
        this.form = response.data;
        this.statSql = this.form.statSql
        this.open = true;
        this.title = "修改数据监控配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(async valid => {
        if (valid) {
          this.form.statSql = await encodeSqlToEncryptedBase64(this.statSql);
          if (this.form.id != null) {
            updateMonitorconfig(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMonitorconfig(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除数据监控配置编号为"' + ids + '"的数据项？').then(function() {
        return delMonitorconfig(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('alert/monitorconfig/export', {
        ...this.queryParams
      }, `monitorconfig_${new Date().getTime()}.xlsx`)
    },
    // 获取数据源列表
    getDataSourceList() {
      this.options.dataSourceOptions = [];
      listDataSource().then(response => {
        if(response.code === 200) {
          response.data.forEach(element => {
            this.options.dataSourceOptions.push({'id' : element.id, 'name' : element.sourceName})
          });
        }
      });
    },
    // 获取表名列表
    getTableNameList(sourceId) {
      this.form.tableName = '';
      this.options.tableNameOptions = [];
      if(sourceId) {
        listDataTable({'sourceId' : sourceId}).then(response => {
          if(response.code === 200) {
            response.data.forEach(element => {
              this.options.tableNameOptions.push({'id' : element.id, 'tableName' : element.tableName})
           });
          }
        });
      }
    },
    // 获取列名列表
    getColumnNameList(tableId) {
      this.options.columnNameOptions = [];
      listDataColumn({'tableId': tableId}).then(response => {
        if(response.code === 200) {
          response.data.forEach(element => {
              this.options.columnNameOptions.push({'id' : element.id, 'columnName' : element.columnName})
           });
        }
      });
    },
    // 获取字典值方法
    getDisplayName(options,key,keyName,labelName) {
      // 查找 options 中匹配的项
      const item = options.find(option => option[keyName] === key);
      // 如果找到，返回 labelName 对应的值；否则返回 key
      return item ? item[labelName] : key;
    },
    // 表名切换
    changeTableName(row) {
      var table = this.options.tableNameOptions.filter((item) => {
        return item.tableName == row;
      });
      this.getColumnNameList(table[0].id);
    },
    triggerTask(row) {
      console.log(row)
      var that = this
      this.$modal.confirm('是否确认执行"' + row.id + '"的数据项？').then(function () {
        return taskTrigger(row.id).then(response => {
          if(response.code === 200) {
            that.$modal.msgSuccess("执行成功");
          } else {
            that.$modal.msgError("执行失败");
          }
        });
      })

    },
    nextTriggerTime(row) {
      job.nextTriggerTime(row.cron).then(response => {
        const { content } = response
        this.triggerNextTimes = content.join('<br>')
      })
    },
    // 日志点击
    logJump(row) {
      this.$router.push({
        path: './history',
        query: { dataMonitorId : row.id }
      })
    }
  }
};
</script>
