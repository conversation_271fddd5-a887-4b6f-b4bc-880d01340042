<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryFormRef" size="small" :inline="true" v-show="showSearch" label-width="68px">

<!--      <el-form-item>-->
<!--        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>-->
<!--        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>-->
<!--      </el-form-item>-->
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['alert:historicaldetails:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['alert:historicaldetails:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table ref="fullHeightTableRef" :height="tableHeight" v-loading="loading" :data="historicaldetailsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="预警规则" align="center" prop="alertRuleId" />-->
      <el-table-column label="预警规则" align="center" prop="alertRuleId">
        <template slot-scope="scope">
          {{ getDisplayName(options.alertRuleOption,scope.row.alertRuleId,'id','ruleName') }}
        </template>
      </el-table-column>
      <el-table-column label="预警结果" align="center" prop="alertResults">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.warning_monition_results" :value="scope.row.alertResults"/>
        </template>
      </el-table-column>
<!--      <el-table-column label="动作执行结果" align="center" prop="actionResults" />-->
<!--      <el-table-column label="监控历史" align="center" prop="monitorHistoryId" />-->
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
<!--        <template slot-scope="scope">-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-edit"-->
<!--            @click="handleUpdate(scope.row)"-->
<!--            v-hasPermi="['alert:historicaldetails:edit']"-->
<!--          >修改</el-button>-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-delete"-->
<!--            @click="handleDelete(scope.row)"-->
<!--            v-hasPermi="['alert:historicaldetails:remove']"-->
<!--          >删除</el-button>-->
<!--        </template>-->
        <template slot-scope="scope">
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['alert:history:edit']"
          >修改</el-button> -->
          <el-tooltip class="item" effect="dark" :content=scope.row.actionResults placement="top-start">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-s-order"
            >动作执行结果</el-button>
          </el-tooltip>
        </template>

      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改监控历史明细对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="租户号" prop="tenantId">
          <el-input v-model="form.tenantId" placeholder="请输入租户号" />
        </el-form-item>
        <el-form-item label="乐观锁" prop="revision">
          <el-input v-model="form.revision" placeholder="请输入乐观锁" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="删除标记" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标记" />
        </el-form-item>
        <el-form-item label="预警规则" prop="alertRuleId">
          <el-input v-model="form.alertRuleId" placeholder="请输入预警规则" />
        </el-form-item>
        <el-form-item label="预警结果@warning_monition_results@" prop="alertResults">
          <el-input v-model="form.alertResults" placeholder="请输入预警结果@warning_monition_results@" />
        </el-form-item>
        <el-form-item label="监控历史id" prop="monitorHistoryId">
          <el-input v-model="form.monitorHistoryId" placeholder="请输入监控历史id" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import { listHistoricaldetails, getHistoricaldetails, delHistoricaldetails, addHistoricaldetails, updateHistoricaldetails } from "@/api/alert/historicaldetails/api";
import {getRuleOptionList} from "@/api/alert/rule/api";

export default {
  mixins: [tableFullHeight],
  name: "Historicaldetails",
  dicts: ['warning_monition_results'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 监控历史明细表格数据
      historicaldetailsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenantId: null,
        revision: null,
        alertRuleId: null,
        alertResults: null,
        monitorHistoryId: this.$route.query.monitorHistoryId ? Number(this.$route.query.monitorHistoryId) : null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      options :{
        /** 预警规则*/
        alertRuleOption : [],
      },
    };
  },
  created() {
    this.getList();
    this.getRuleOption();
  },
  methods: {
    /** 查询监控历史明细列表 */
    getList() {
      this.loading = true;
      listHistoricaldetails(this.queryParams).then(response => {
        this.historicaldetailsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        tenantId: null,
        revision: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        id: null,
        remark: null,
        delFlag: null,
        alertRuleId: null,
        alertResults: null,
        monitorHistoryId: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryFormRef");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加监控历史明细";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getHistoricaldetails(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改监控历史明细";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateHistoricaldetails(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addHistoricaldetails(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除监控历史明细编号为"' + ids + '"的数据项？').then(function() {
        return delHistoricaldetails(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('alert/historicaldetails/export', {
        ...this.queryParams
      }, `historicaldetails_${new Date().getTime()}.xlsx`)
    },
    // 获取字典值方法
    getDisplayName(options,key,keyName,labelName) {
      // 查找 options 中匹配的项
      const item = options.find(option => option[keyName] === key);
      // 如果找到，返回 labelName 对应的值；否则返回 key
      return item ? item[labelName] : key;
    },
    /** 获取预警规则*/
    getRuleOption() {
      getRuleOptionList().then(response => {
        this.options.alertRuleOption = response.data;
      });
    },
  }
};
</script>
