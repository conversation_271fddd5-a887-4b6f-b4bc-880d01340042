<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="监控配置" prop="dataMonitorId">
        <el-input
          v-model="queryParams.dataMonitorId"
          placeholder="请输入监控配置"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['alert:rule:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['alert:rule:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['alert:rule:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['alert:rule:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="ruleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />

      <el-table-column label="监控配置" align="center" prop="dataMonitorId" >
        <template slot-scope="scope">
          {{ getDisplayName(options.dataMonitorOption,scope.row.dataMonitorId,'id','targetName') }}
        </template>
      </el-table-column>
      <el-table-column label="规则名称" align="center" prop="ruleName" />
      <el-table-column label="规则类型" align="center" prop="ruleType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.warning_rule_type" :value="scope.row.ruleType"/>
        </template>
      </el-table-column>
      <el-table-column label="比较方式" align="center" prop="compareMethod">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.warning_compare_method" :value="scope.row.compareMethod"/>
        </template>
      </el-table-column>
      <el-table-column label="取值周期" align="center" prop="valueTakingCycle" />
      <el-table-column label="取值周期数" align="center" prop="valueTakingCycleNumber" />

      <el-table-column label="比较值" align="center" prop="compareValue" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" width="150px" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['alert:rule:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['alert:rule:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改预警规则对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="监控配置" prop="dataMonitorId">
            <el-select v-model="form.dataMonitorId" clearable>
              <el-option
                v-for="dict in options.dataMonitorOption"
                :key="dict.id"
                :label="dict.targetName"
                :value="dict.id"
              />
            </el-select>
        </el-form-item>
        <el-form-item label="规则名称" prop="ruleName">
          <el-input v-model="form.ruleName" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="取值周期" prop="valueTakingCycle">
          <el-input v-model="form.valueTakingCycle" placeholder="请输入取值周期" />
        </el-form-item>
        <el-form-item label="比较方式" prop="compareMethod">
              <el-select v-model="form.compareMethod" clearable>
                <el-option
                  v-for="dict in options.compareMethodOption"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                />
              </el-select>
        </el-form-item>
        <el-form-item label="周期数" prop="valueTakingCycleNumber">
          <el-input v-model="form.valueTakingCycleNumber" placeholder="请输入取值周期数" />
        </el-form-item>
        <el-form-item label="规则类型" prop="ruleType">
              <el-select v-model="form.ruleType" clearable>
                <el-option
                  v-for="dict in options.ruleTypeOption"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                />
              </el-select>
        </el-form-item>
        <el-form-item label="比较值" prop="compareValue">
          <el-input v-model="form.compareValue" placeholder="请输入比较值" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRule, getRule, delRule, addRule, updateRule } from "@/api/alert/rule/api";
import { getOptionList } from "@/api/alert/monitorconfig/api"

export default {
  name: "Rule",
  dicts: ['warning_rule_type','warning_compare_method'],

  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 预警规则表格数据
      ruleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenantId: null,
        revision: null,
        ruleType: null,
        compareMethod: null,
        valueTakingCycle: null,
        valueTakingCycleNumber: null,
        valueTakingAlgorithm: null,
        dataMonitorId: null,
        compareValue: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      options :{
        // 数据监控选择列表
        dataMonitorOption : [],
        // 规则类型字典
        ruleTypeOption : [],
        // 比较方式字典
        compareMethodOption : [],
      }
    };
  },
  created() {
    this.getDataMonitor();
    this.getList();
    // 取值算法字典
    this.getDicts("warning_rule_type").then(response => {
      this.options.ruleTypeOption = response.data;
    });
    // 比较方式字典
    this.getDicts("warning_compare_method").then(response => {
      this.options.compareMethodOption = response.data;
    });
  },
  methods: {
    /** 查询预警规则列表 */
    getList() {
      this.loading = true;
      listRule(this.queryParams).then(response => {
        this.ruleList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        tenantId: null,
        revision: null,
        id: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
        ruleType: null,
        compareMethod: null,
        valueTakingCycle: null,
        valueTakingCycleNumber: null,
        valueTakingAlgorithm: null,
        dataMonitorId: null,
        compareValue: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加预警规则";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getRule(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改预警规则";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateRule(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRule(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除预警规则编号为"' + ids + '"的数据项？').then(function() {
        return delRule(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('alert/rule/export', {
        ...this.queryParams
      }, `rule_${new Date().getTime()}.xlsx`)
    },
    /** 获取数据监控信息 */
    getDataMonitor() {
      getOptionList().then(res => {
        this.options.dataMonitorOption = res.data;
      })
    },
    // 获取字典值方法
    getDisplayName(options,key,keyName,labelName) {
      // 查找 options 中匹配的项
      const item = options.find(option => option[keyName] === key);
      // 如果找到，返回 labelName 对应的值；否则返回 key
      return item ? item[labelName] : key;
    },
  }
};
</script>
