<template>
  <!-- 添加或修改预警规则对话框 -->
  <el-dialog :title="title" :visible="openAddDialog" width="500px" append-to-body @before-close="cancel"
             @close="cancel">
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="监控配置" prop="dataMonitorId">
        <el-select v-model="form.dataMonitorId" clearable :disabled="true">
          <el-option
            v-for="dict in options.dataMonitorOption"
            :key="dict.id"
            :label="dict.targetName"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="规则名称" prop="ruleName">
        <el-input v-model="form.ruleName" placeholder="请输入规则名称"/>
      </el-form-item>
      <el-form-item label="规则类型" prop="ruleType">
        <el-select v-model="form.ruleType" @change="changeRuleType" clearable>
          <el-option
            v-for="dict in options.ruleTypeOption"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>



      <el-form-item label="取值周期" prop="valueTakingCycle" v-if="form.ruleType === 'history'">
        <el-select v-model="form.valueTakingCycle" clearable>
          <el-option
            v-for="dict in options.valueTakingCycleOption"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="周期数" prop="valueTakingCycleNumber" v-if="form.ruleType === 'history'">
        <el-input v-model="form.valueTakingCycleNumber" placeholder="请输入取值周期数" />
      </el-form-item>
      <el-form-item label="比较方式" prop="compareMethod" v-if="form.ruleType === 'fixed' ">
        <el-select v-model="form.compareMethod" clearable>
          <el-option
            v-for="dict in options.compareMethodOption"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="比较方式" prop="compareMethod" v-if="form.ruleType === 'history' ">
        <el-select v-model="form.compareMethod" clearable>
          <el-option
            v-for="dict in options.historyCompareMethodOption"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="比较" prop="compareMethod" v-if="form.compareMethod === 'fixed' ">
        <el-select v-model="form.historyComparisonMethod" clearable>
          <el-option
            v-for="dict in options.compareMethodOption"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="比较值" prop="compareValue" v-if="form.ruleType === 'fixed' || form.compareMethod === 'fixed'" >
        <el-input v-model="form.compareValue" placeholder="请输入比较值"/>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" placeholder="请输入备注"/>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {addRule, getRule, updateRule} from "@/api/alert/rule/api";
import {getOptionList} from "@/api/alert/monitorconfig/api"
import {mount} from "sortablejs";
import {log} from "@visactor/vchart/esm/util";

export default {
  name: "AddRuleModal",
  props: {
    openAddDialog: {
      type: Boolean,
      default: false
    },
    configId: {
      type: [Number, String],
      default: ''
    },
    ruleId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      title: "添加预警规则",
      form: {
        dataMonitorId: "",
        ruleName: "",
        compareMethod: "",
        ruleType: "",
        compareValue: "",
        remark: "",
        valueTakingCycle:null,
        valueTakingCycleNumber:null,
        historyComparisonMethod: null
      },
      options: {
        // 数据监控选择列表
        dataMonitorOption: [],
        // 规则类型字典
        ruleTypeOption: [],
        // 比较方式字典
        compareMethodOption: [],
        // 历史值比较方式字典
        historyCompareMethodOption: [],
        // 取值周期字典
        valueTakingCycleOption: [],
      },
      rules: {}
    };
  },
  methods: {
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 判断新增还是修改
          if (this.form.id) {
            updateRule(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.$emit("update:openAddDialog", false);
              this.$emit('getDataList')
            });
          } else {
            addRule(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.$emit("update:openAddDialog", false);
              this.$emit('getDataList')
            });
          }
        }
      });
    },
    reset() {
      this.form = {
        tenantId: null,
        revision: null,
        id: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
        ruleType: null,
        compareMethod: null,
        valueTakingAlgorithm: null,
        dataMonitorId: null,
        compareValue: null,
        valueTakingCycle:null,
        valueTakingCycleNumber:null,
        historyComparisonMethod: null
      };
      this.resetForm("form");
    },
    changeRuleType() {
      this.form.compareMethod = '';
      this.form.compareValue = '';
      this.form.remark = '';
      this.form.valueTakingCycle = '';
      this.form.valueTakingCycleNumber = null;
      this.form.historyComparisonMethod = null;
    },
    cancel() {
      this.$emit("update:openAddDialog", false);
      this.reset();
    },
    /** 获取数据监控信息 */
    getDataMonitor() {
      getOptionList().then(res => {
        this.title = "添加预警规则"
        this.options.dataMonitorOption = res.data;
        if (this.configId) {
          this.form.dataMonitorId = this.configId;
        }
      })
    },
  },
  created() {
    this.reset();
    this.getDataMonitor()
    if (this.ruleId) {
      getRule(this.ruleId).then(response => {
        this.form = response.data;
        this.title = "修改预警规则";
        if (this.configId) {
          this.form.dataMonitorId = this.configId;
        }
      });
    }
    // 取值算法字典
    this.getDicts("warning_rule_type").then(response => {
      this.options.ruleTypeOption = response.data;
    });
    // 比较方式字典
    this.getDicts("warning_compare_method").then(response => {
      this.options.compareMethodOption = response.data;
    });
    // 历史值比较方式字典
    this.getDicts("warning_history_compare_method").then(response => {
      this.options.historyCompareMethodOption = response.data;
    });
    // 取值周期字典
    this.getDicts("warning_value_taking_cycle").then(response => {
      this.options.valueTakingCycleOption = response.data;
    });
  }
}
</script>
<style></style>
