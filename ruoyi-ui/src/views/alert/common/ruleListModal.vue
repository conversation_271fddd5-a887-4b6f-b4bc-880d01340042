<template>
  <el-dialog title="绑定预警规则" :visible="openRuleDialog" width="1200px" append-to-body @before-close="cancel"
             @close="cancel">
    <div class="app-container">
      <h3>预警规则</h3>
      <el-row :gutter="1" class="mb8">
        <el-col :span="2">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
          >新增
          </el-button>
        </el-col>
        <el-col :span="2">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="!single"
            @click="handleUpdate"
          >修改
          </el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
      <el-row class="mb8">
        <el-table v-loading="loading" :data="ruleList" highlight-current-row
                  @current-change="handleSelectionChange">

          <el-table-column label="监控配置" align="center" prop="dataMonitorId">
            <template slot-scope="scope">
              {{ getDisplayName(options.dataMonitorOption, scope.row.dataMonitorId, 'id', 'targetName') }}
            </template>
          </el-table-column>
          <el-table-column label="规则名称" align="center" prop="ruleName"/>
          <el-table-column label="规则类型" align="center" prop="ruleType">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.warning_rule_type" :value="scope.row.ruleType"/>
            </template>
          </el-table-column>
          <el-table-column label="启用状态" align="center" width="150">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.ruleStatus"
                active-color="#00A854"
                active-text="启用"
                :active-value="'INVOCATION'"
                inactive-color="#F04134"
                inactive-text="停用"
                :inactive-value="'DEACTIVATE'"
                @change="changeSwitch(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column label="比较方式" align="center" prop="compareMethod">
            <template slot-scope="scope">
              {{ getDisplayName(options.historyCompareMethodOption.concat(options.compareMethodOption), scope.row.compareMethod, 'dictValue', 'dictLabel') }}
            </template>
          </el-table-column>
          <el-table-column label="取值周期" align="center" prop="valueTakingCycle">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.warning_value_taking_cycle" :value="scope.row.valueTakingCycle"/>
            </template>
          </el-table-column>
          <el-table-column label="取值周期数" align="center" prop="valueTakingCycleNumber"/>

          <el-table-column label="比较值" align="center" prop="compareValue"/>
          <el-table-column label="备注" align="center" prop="remark"/>
          <el-table-column label="操作" align="center" width="150px" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['alert:rule:edit']"
              >修改
              </el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['alert:rule:remove']"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-row>
      <el-row>
        <action-list ref="actionListRef" @closeParentPage="cancel" :parentIds="ids"></action-list>
      </el-row>
      <!-- 添加或修改预警规则对话框 -->
      <addRuleModal ref="addRuleModalRef" v-if="openAddDialog" :title="title" :openAddDialog.sync="openAddDialog" :configId="configId"
                   @getDataList="getList" :ruleId="ruleId"/>
    </div>
  </el-dialog>
</template>

<script>
import {listRule, delRule, addRule, updateRule, updateRulStatus} from "@/api/alert/rule/api";
import {getOptionList} from "@/api/alert/monitorconfig/api"
import addRuleModal from "./addRuleModal.vue";
import actionList from "./actionList.vue";

export default {
  name: "Rule",
  dicts: ['warning_rule_type', 'warning_compare_method','warning_value_taking_cycle'],
  components: {addRuleModal, actionList},
  props: {
    openRuleDialog: {
      type: Boolean,
      default: false
    },
    configId: {
      type: [Number, String],
      default: ''
    },
  },
  data() {
    return {
      ruleId: '',
      openAddDialog: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: '',
      // 非单个禁用
      single: false,

      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 预警规则表格数据
      ruleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenantId: null,
        revision: null,
        ruleType: null,
        compareMethod: null,
        valueTakingCycle: null,
        valueTakingCycleNumber: null,
        valueTakingAlgorithm: null,
        dataMonitorId: null,
        compareValue: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      options: {
        // 数据监控选择列表
        dataMonitorOption: [],
        // 规则类型字典
        ruleTypeOption: [],
        // 比较方式字典
        compareMethodOption: [],
        // 历史值比较方式字典
        historyCompareMethodOption: [],
      }
    };
  },
  created() {
    this.getDataMonitor();
    this.getList();
    // 取值算法字典
    this.getDicts("warning_rule_type").then(response => {
      this.options.ruleTypeOption = response.data;
    });
    // 比较方式字典
    this.getDicts("warning_compare_method").then(response => {
      this.options.compareMethodOption = response.data;
    });
    // 历史值比较方式字典
    this.getDicts("warning_history_compare_method").then(response => {
      this.options.historyCompareMethodOption = response.data;
    });
  },
  methods: {
    /** 查询预警规则列表 */
    getList() {
      this.queryParams.dataMonitorId = this.configId;
      this.loading = true;
      listRule(this.queryParams).then(response => {
        this.ruleList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      console.log("取消按钮")
      this.$emit("update:openRuleDialog", false);
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        tenantId: null,
        revision: null,
        id: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
        ruleType: null,
        compareMethod: null,
        valueTakingCycle: null,
        valueTakingCycleNumber: null,
        valueTakingAlgorithm: null,
        dataMonitorId: null,
        compareValue: null,
        ruleStatus : null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.id
      this.single = !!selection.id
      this.$nextTick(() => {
        this.$refs.actionListRef.getList()
      })
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.ruleId = null
      this.openAddDialog = true;
      this.title = "添加预警规则";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.ruleId = row.id || this.ids
      this.openAddDialog = true;
      this.title = "修改预警规则";

    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateRule(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRule(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除预警规则编号为"' + ids + '"的数据项？').then(function () {
        return delRule(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 获取数据监控信息 */
    getDataMonitor() {
      getOptionList().then(res => {
        this.options.dataMonitorOption = res.data;
      })
    },
    // 获取字典值方法
    getDisplayName(options, key, keyName, labelName) {
      // 查找 options 中匹配的项
      const item = options.find(option => option[keyName] === key);
      // 如果找到，返回 labelName 对应的值；否则返回 key
      return item ? item[labelName] : key;
    },
    // 规则状态
    changeSwitch(row) {
      updateRulStatus(row).then(response => {
        if (response.code === 200) {
          this.$modal.msgSuccess(response.msg);
        } else {
          this.$modal.msgError(response.msg);
        }
      });
    }
  }
};
</script>
