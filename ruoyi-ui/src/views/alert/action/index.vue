<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['alert:action:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['alert:action:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['alert:action:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['alert:action:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="actionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />

      <el-table-column label="预警规则" align="center" prop="alertRuleId">
        <template slot-scope="scope">
          {{ getDisplayName(options.alertRuleOption,scope.row.alertRuleId,'id','ruleName') }}
        </template>
      </el-table-column>
      <el-table-column label="通知提醒组" align="center" prop="userGroups">
        <template slot-scope="scope">
          {{ getDisplayName(options.msgGroupOption,scope.row.userGroups,'number','name') }}
        </template>
      </el-table-column>
      <el-table-column label="动作类型" align="center" prop="actionType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.warning_action_type" :value="scope.row.actionType"/>
        </template>
      </el-table-column>
      <el-table-column label="工作流" align="center" prop="workflowId" />
      <el-table-column label="目标对象" align="center" prop="target" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" width="150px" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['alert:action:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['alert:action:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改预警动作对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="预警规则" prop="alertRuleId">
          <el-select v-model="form.alertRuleId" clearable>
              <el-option
                v-for="dict in options.alertRuleOption"
                :key="dict.id"
                :label="dict.ruleName"
                :value="dict.id"
              />
            </el-select>
        </el-form-item>
        <el-form-item label="动作类型" prop="actionType">
            <el-select v-model="form.actionType" clearable filterable>
              <el-option
                v-for="dict in options.actionTypeOption"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
        </el-form-item>
        <el-form-item label="提醒组" prop="actionType">
          <el-select v-model="form.userGroups" clearable filterable>
            <el-option
              v-for="dict in options.msgGroupOption"
              :key="dict.number"
              :label="dict.name"
              :value="dict.number"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAction, getAction, delAction, addAction, updateAction } from "@/api/alert/action/api";
import {selectMsgGroup} from "@/api/message/group/group"
import {getRuleOptionList} from "@/api/alert/rule/api"

export default {
  name: "Action",
  dicts: ['warning_action_type'],

  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 预警动作表格数据
      actionList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenantId: null,
        revision: null,
        alertRuleId: null,
        actionType: null,
        workflowId: null,
        target: null,
        userGroups: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      options :{
        /** 动作类型*/
        actionTypeOption : [],
        /** 消息提醒组选择*/
        msgGroupOption : [],
        /** 预警规则*/
        alertRuleOption : [],
      },
    };
  },
  created() {
    this.getRuleOption();
    this.getMsgGroup();
    this.getList();
    // 动作类型字典
    this.getDicts("warning_action_type").then(response => {
      this.options.actionTypeOption = response.data;
    });
  },
  methods: {
    /** 查询预警动作列表 */
    getList() {
      this.loading = true;
      listAction(this.queryParams).then(response => {
        this.actionList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        tenantId: null,
        revision: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        id: null,
        remark: null,
        delFlag: null,
        alertRuleId: null,
        actionType: null,
        workflowId: null,
        target: null,
        userGroups: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加预警动作";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getAction(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改预警动作";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateAction(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAction(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除预警动作编号为"' + ids + '"的数据项？').then(function() {
        return delAction(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('alert/action/export', {
        ...this.queryParams
      }, `action_${new Date().getTime()}.xlsx`)
    },
    /** 查询消息提醒组*/
    getMsgGroup() {
      selectMsgGroup().then(response => {
        this.options.msgGroupOption = response.data;
      });
    },
    /** 获取预警规则*/
    getRuleOption() {
      getRuleOptionList().then(response => {
        this.options.alertRuleOption = response.data;
      });
    },
    // 获取字典值方法
    getDisplayName(options,key,keyName,labelName) {
      // 查找 options 中匹配的项
      const item = options.find(option => option[keyName] === key);
      // 如果找到，返回 labelName 对应的值；否则返回 key
      return item ? item[labelName] : key;
    },
  }
};
</script>
