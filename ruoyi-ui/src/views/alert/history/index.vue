<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryFormRef" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="预警监控" prop="dataMonitorId">
        <el-select v-model="queryParams.dataMonitorId" filterable clearable>
          <el-option
            v-for="dict in options.dataMonitorOption"
            :key="dict.id"
            :label="dict.targetName"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="监控结果" prop="monitorResults">
        <el-select v-model="queryParams.monitorResults">
          <el-option :label="option.label" :value="option.value" :key = "option.value" v-for="option in dict.type.warning_monition_results"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" icon="el-icon-delete" size="mini" @click="handleClear">清除</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['alert:history:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['alert:history:edit']"
        >修改</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['alert:history:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['alert:history:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table ref="fullHeightTableRef" :height="tableHeight" v-loading="loading" :data="historyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="监控时间" align="center" prop="monitoringTime" width="180"/>
      <el-table-column label="预警监控" align="center" prop="dataMonitorId">
        <template slot-scope="scope">
          {{ getDisplayName(options.dataMonitorOption,scope.row.dataMonitorId,'id','targetName') }}
        </template>
      </el-table-column>
      <el-table-column label="监控结果" align="center" prop="monitorResults">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.warning_monition_results" :value="scope.row.monitorResults"/>
        </template>
      </el-table-column>
      <el-table-column label="监控值" align="center" prop="monitorValue" />
<!--      <el-table-column label="监控数据" align="center" prop="monitorData" />-->

      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['alert:history:edit']"
          >修改</el-button> -->
          <el-tooltip class="item" effect="dark" :content=scope.row.monitorData placement="top-start">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-s-order"
            >详情</el-button>
          </el-tooltip>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleAlertDetail(scope.row)"
            v-hasPermi="['alert:history:remove']"
          >预警详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['alert:history:remove']"
          >删除</el-button>


        </template>

      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog title="clear" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="清理范围" prop="clearType">
          <el-select v-model="form.clearType" clearable>
          <el-option
            v-for="dict in deleteTypeList"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>

        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import { listHistory, getHistory, delHistory, addHistory, updateHistory ,clearHistory} from "@/api/alert/history/api";
import { getOptionList } from "@/api/alert/monitorconfig/api"

export default {
  mixins: [tableFullHeight],
  name: "History",
  dicts: ['warning_monition_results'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 监控历史数据表格数据
      historyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenantId: null,
        revision: null,
        monitoringTime: null,
        dataMonitorId: this.$route.query.dataMonitorId ? Number(this.$route.query.dataMonitorId) : null,
        monitorResults: null,
        monitorValue: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      deleteTypeList: [
        { value: 1, label: '清理一个月之前日志数据' },
        { value: 2, label: '清理三个月之前日志数据' },
        { value: 3, label: '清理六个月之前日志数据' },
        { value: 4, label: '清理一年之前日志数据' },
        { value: 5, label: '清理所有日志数据' }
      ],
      options :{
        dataMonitorOption : [],
      }
    };
  },
  async created() {
    await this.getDataMonitor()
    this.getList();
  },
  methods: {
    /** 查询监控历史数据列表 */
    getList() {
      this.loading = true;
      listHistory(this.queryParams).then(response => {
        this.historyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        clearType: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryFormRef");
      this.handleQuery();
    },
    /** 清除日志按钮操作 */
    handleClear() {
      this.reset();
      this.open = true;
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getHistory(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改监控历史数据";
      });
    },
    /** 提交按钮 */
    submitForm() {
      const type = this.form.clearType;
      clearHistory(type).then(response => {
        this.$message.info(response.msg)
        this.getList();
        this.open = false;
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除监控历史数据编号为"' + ids + '"的数据项？').then(function() {
        return delHistory(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 查看详情按钮操作 */
    handleAlertDetail(row) {
      this.$router.push({
        path: './historicaldetails',
        query: { monitorHistoryId : row.id }
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('alert/history/export', {
        ...this.queryParams
      }, `history_${new Date().getTime()}.xlsx`)
    },
    /** 获取数据监控信息 */
    getDataMonitor() {
      getOptionList().then(res => {
        this.options.dataMonitorOption = res.data;
      })
    },
    // 获取字典值方法
    getDisplayName(options,key,keyName,labelName) {
      // 查找 options 中匹配的项
      const item = options.find(option => option[keyName] === key);
      // 如果找到，返回 labelName 对应的值；否则返回 key
      return item ? item[labelName] : key;
    },
  }
};
</script>
