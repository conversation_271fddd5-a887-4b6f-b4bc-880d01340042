<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="接口编码" prop="apiCode">
        <el-input
          v-model="queryParams.apiCode"
          placeholder="请输入接口编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="对端系统" prop="oppositeCode">
        <el-input
          v-model="queryParams.oppositeCode"
          placeholder="请输入对端系统编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="请求地址" prop="callHost">
        <el-input
          v-model="queryParams.callHost"
          placeholder="请输入接口请求地址"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="请求路径" prop="callPath">
        <el-input
          v-model="queryParams.callPath"
          placeholder="请输入接口请求路径"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['mengniu-api:cfg:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['mengniu-api:cfg:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['mengniu-api:cfg:remove']"
        >删除
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="cfgList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="接口编码" align="center" prop="apiCode"/>
      <el-table-column label="对端系统编码" align="center" prop="oppositeCode"/>
      <el-table-column label="接口请求地址" align="center" prop="callHost"/>
      <el-table-column label="接口请求路径" align="center" prop="callPath"/>
      <el-table-column label="请求头" align="center" prop="apiHeader"/>
      <el-table-column label="请求参数" align="center" prop="apiQuery"/>
      <el-table-column label="请求体" align="center" prop="apiBody"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['mengniu-api:cfg:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="execute(scope.row)"
          >执行一次
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >上线
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['mengniu-api:cfg:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改请求配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="接口编码" prop="apiCode">
              <el-input v-model="form.apiCode" placeholder="请输入接口编码"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="对端系统" prop="oppositeCode">
              <el-input v-model="form.oppositeCode" placeholder="请输入对端系统编码"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="接口地址" prop="callHost">
              <el-input v-model="form.callHost" placeholder="请输入接口请求地址"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="接口路径" prop="callPath">
              <el-input v-model="form.callPath" placeholder="请输入接口请求路径"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="请求头" prop="apiHeader">
<!--              <el-input :rows="5" v-model="form.apiHader" type="textarea" placeholder="请输入内容"/>-->
              <json-editor
                v-model="form.apiHeader"
                ref="jsonEditor"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请求参数" prop="apiQuery">
<!--              <el-input :rows="5" v-model="form.apiQuery" type="textarea" placeholder="请输入内容"/>-->
              <json-editor
                v-model="form.apiQuery"
                ref="jsonEditor"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="请求体" prop="apiBody">
<!--              <el-input :rows="5" v-model="form.apiBody" type="textarea" placeholder="请输入内容"/>-->
              <json-editor
                v-model="form.apiBody"
                ref="jsonEditor"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {addCfg, delCfg, getCfg, listCfg, sendRequest, updateCfg} from "@/api/apimodule/apirequestscfg";
import JsonEditor from "@/components/JsonEditor/index.vue";

export default {
  name: "Cfg",
  components: {JsonEditor},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 请求配置表格数据
      cfgList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenantId: null,
        revision: null,
        apiCode: null,
        oppositeCode: null,
        callHost: null,
        callPath: null,
        apiHeader: null,
        apiQuery: null,
        apiBody: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询请求配置列表 */
    getList() {
      this.loading = true;
      listCfg(this.queryParams).then(response => {
        this.cfgList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        apiCode: null,
        oppositeCode: null,
        callHost: null,
        callPath: null,
        apiHeader: {},
        apiQuery: {},
        apiBody: {},
        tenantId: null,
        revision: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加请求配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getCfg(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改请求配置";
      });
    },
    /** 执行一次按钮*/
    execute(row) {
      this.$modal.confirm('是否确认执行接口编码为"' + row.apiCode + '"的接口？').then(function() {
        return sendRequest(row.apiCode);
      }).then(() => {
        this.$modal.msgSuccess("执行成功");
      }).catch(() => {
        this.$modal.msgError("接口异常");
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCfg(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCfg(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除接口编号为"' + ids + '"的数据项？').then(function() {
        return delCfg(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
}
;
</script>
