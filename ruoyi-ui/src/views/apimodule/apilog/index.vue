<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryFormRef" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="对端接口" prop="apiCode">
        <el-input
          v-model="queryParams.apiCode"
          placeholder="请输入对端接口"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="请求路径" prop="callUrl">
        <el-input
          v-model="queryParams.callUrl"
          placeholder="请输入请求路径"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="执行结果"  class="filter-item" clearable>
          <el-option
            v-for="item in dict.type.api_res_status"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="批次号" prop="batchCode">
        <el-input
          v-model="queryParams.batchCode"
          placeholder="请输入批次号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="请求内容">
        <el-input
          v-model="queryParams.reqBody"
          placeholder="请输入请求内容"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="请求时间">
        <el-date-picker v-model="dateRange" style="width: 400px" value-format="yyyy-MM-dd HH:mm:ss"
                        type="datetimerange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" size="small"
        ></el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button icon="el-icon-delete" size="mini" @click="cleanLog">清除</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="danger"-->
<!--          plain-->
<!--          icon="el-icon-delete"-->
<!--          size="mini"-->
<!--          :disabled="multiple"-->
<!--          @click="handleDelete"-->
<!--          v-hasPermi="['system:log:remove']"-->
<!--        >删除</el-button>-->
<!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:log:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      border
      ref="fullHeightTableRef" :height="tableHeight"
      v-loading="loading" :data="logList" @selection-change="handleSelectionChange">
<!--      <el-table-column type="selection" width="55" align="center" />-->
      <el-table-column label="对端系统名称" align="center" prop="desSys"  width="100"/>
      <el-table-column label="对端接口编码" align="center" prop="apiCode" width="100"/>
<!--      <el-table-column label="请求路径" align="center" prop="callUrl" />-->
<!--      <el-table-column label="请求的url参数" align="center" prop="reqUrlParam" width="110" />-->
      <el-table-column label="批次号" align="center" prop="batchCode" width="200"/>
<!--      <el-table-column label="请求内容" align="center" prop="reqBody"   width="200"  :show-overflow-tooltip="true" />-->
<!--      <el-table-column label="响应内容" align="center" prop="resBody"  width="200"     :show-overflow-tooltip="true"/>-->
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.api_res_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
<!--      <el-table-column label="类型" align="center" prop="apiType" />-->
      <el-table-column label="请求时间" align="center" prop="reqTime" width="180">
      </el-table-column>
      <el-table-column label="响应时间" align="center" prop="resTime" width="180">
      </el-table-column>
      <el-table-column label="耗时(ms)" align="center" prop="requestDuration" width="180" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:log:edit']"
          >详情</el-button>
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-delete"-->
<!--            @click="handleDelete(scope.row)"-->
<!--            v-hasPermi="['system:log:remove']"-->
<!--          >删除</el-button>-->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改API接口日志对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="200px">
        <el-form-item label="对端系统名称" prop="desSys">
          <el-input v-model="form.desSys" readonly />
        </el-form-item>
        <el-form-item label="对端接口编码" prop="apiCode">
          <el-input v-model="form.apiCode" readonly />
        </el-form-item>
        <el-form-item label="请求路径" prop="callUrl">
          <el-input v-model="form.callUrl" readonly />
        </el-form-item>
        <el-form-item label="请求的url参数" prop="reqUrlParam">
          <el-input v-model="form.reqUrlParam" readonly />
        </el-form-item>
        <el-form-item label="请求内容" prop="reqBody">
          <el-input v-model="form.reqBody" type="textarea" :rows="4" readonly />
        </el-form-item>
        <el-form-item label="响应内容" prop="resBody">
          <el-input v-model="form.resBody" type="textarea" :rows="2" readonly />
        </el-form-item>
        <el-form-item label="请求时间" prop="reqTime">
          <el-date-picker clearable
                          v-model="form.reqTime"
                          type="datetime"

                          readonly>
          </el-date-picker>
        </el-form-item>
        <el-form-item label="响应时间" prop="resTime">
          <el-date-picker clearable
                          v-model="form.resTime"
                          type="datetime"
                          readonly>
          </el-date-picker>
        </el-form-item>
        <el-form-item label="批次号" prop="batchCode">
          <el-input v-model="form.batchCode" readonly />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>



    <el-dialog title="clear" :visible.sync="cleanOpen" width="500px" append-to-body>
      <el-form ref="form" :model="cleanForm" :rules="cleanRules" label-width="80px">
        <el-form-item label="清理范围" prop="clearType">
          <el-select v-model="cleanForm.clearType" clearable>
            <el-option
              v-for="dict in deleteTypeList"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>

        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="cleanSubmitForm">确 定</el-button>
        <el-button @click="cleanCancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import {listLog, getLog, delLog, addLog, updateLog, cleanLog} from "@/api/apimodule/apilog/api";
import { deepClone } from '@/utils'

export default {
  mixins: [tableFullHeight],
  name: "Log",
  dicts: ['api_res_status'],

  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // API接口日志表格数据
      logList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        desSys: null,
        apiCode: this.$route.query.apiCode ? this.$route.query.apiCode : null,
        callUrl: null,
        reqUrlParam: null,
        reqBody: null,
        resBody: null,
        reqTime: null,
        resTime: null,
        status: null,
        apiType: null,
        batchCode: null
      },
      deleteTypeList: [
        { value: 1, label: '清理一个月之前日志数据' },
        { value: 2, label: '清理三个月之前日志数据' },
        { value: 3, label: '清理六个月之前日志数据' },
        { value: 4, label: '清理一年之前日志数据' },
        { value: 5, label: '清理所有日志数据' }
      ],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        status: [
          { required: true, message: "0:请求中 1:已响应 2: 请求失败  3: 异常不能为空", trigger: "change" }
        ],
      },
      cleanOpen: false,
      cleanForm: {
        clearType: null
      },
      cleanRules: {
        clearType: [
          { required: true, message: "清理范围不能为空", trigger: "change" }
        ],
      },
      // 日期范围
      dateRange: []
    };
  },
  created() {
    this.getList();
  },
  methods: {
    cleanSubmitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          cleanLog(this.cleanForm.clearType).then(response => {
            this.$message.info(response.msg)
            this.getList();
            this.cleanOpen = false;
            this.cleanReset();
          })
        }
      })
    },
    cleanCancel() {
      this.cleanOpen = false;
      this.cleanReset();
    },

    cleanLog() {
      this.cleanOpen = true;
      this.cleanReset();
    },


    /** 查询API接口日志列表 */
    getList() {
      this.loading = true;
      const params = deepClone(this.queryParams);

      if (this.dateRange) {
        params.startDateTime = this.dateRange[0]
        params.endDateTime = this.dateRange[1]
      }

      listLog(params).then(response => {
        this.logList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        desSys: null,
        apiCode: null,
        callUrl: null,
        reqUrlParam: null,
        reqBody: null,
        resBody: null,
        reqTime: null,
        resTime: null,
        status: null,
        apiType: null,
        batchCode: null
      };
      this.resetForm("form");
    },
    cleanReset() {
      this.cleanForm = {
        clearType: null
      };
      this.resetForm("cleanForm");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryFormRef");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加API接口日志";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getLog(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改API接口日志";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateLog(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addLog(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除API接口日志编号为"' + ids + '"的数据项？').then(function() {
        return delLog(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('mengniu-api/callApiLog/export', {
        ...this.queryParams
      }, `log_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
