<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryFormRef" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="api编码" prop="apiCode">
        <el-input
          v-model="queryParams.apiCode"
          placeholder="请输入api编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="来源" prop="apiService">
        <el-input
          v-model="queryParams.apiService"
          placeholder="请输入接口来源服务"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['apimodule:dataSaveCfg:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['apimodule:dataSaveCfg:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['apimodule:dataSaveCfg:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['apimodule:dataSaveCfg:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table ref="fullHeightTableRef" :height="tableHeight" v-loading="loading" :data="dataSaveCfgList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="api名称" width="120px" align="center" prop="apiName" />
      <el-table-column label="api编码" width="120px" align="center" prop="apiCode" />
      <el-table-column label="接口地址" width="300px" align="center" prop="apiCode">
        <template slot-scope="scope">
          <span>{{baseURL + scope.row.apiCode}}</span>
        </template>
      </el-table-column>
      <el-table-column label="接口对应主表名" width="120px" align="center" prop="mainTableName" />
      <el-table-column label="主表Json路径" width="120px" align="center" prop="mainTableJsonPath" />
      <el-table-column label="主表业务唯一键" width="120px" align="center" prop="mainTableUniqueKey" />
      <el-table-column label="接口对应子表名" width="120px" align="center" prop="detailsTableName" />
      <el-table-column label="子表Json路径" width="120px" align="center" prop="detailsTableJsonPath" />
      <el-table-column label="子表业务唯一键" width="120px" align="center" prop="detailsTableUniqueKey" />
      <el-table-column label="接口来源服务" width="120px" align="center" prop="apiService" />

      <el-table-column label="启用状态" align="center" width="150" prop="apiStatus">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.apiStatus"
            active-color="#00A854"
            active-text="启用"
            :active-value="'INVOCATION'"
            inactive-color="#F04134"
            inactive-text="停用"
            :inactive-value="'DEACTIVATE'"
            @change="changeSwitch(scope.row)"
          />
        </template>
      </el-table-column>

      <el-table-column label="创建人" align="center" prop="createBy" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新人" align="center" prop="updateBy" />
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />

      <el-table-column label="操作" width="360px" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['apimodule:dataSaveCfg:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-s-operation"
            @click="handleLogView(scope.row)"
            v-hasPermi="['apimodule:dataSaveCfg:edit']"
          >日志</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['apimodule:dataSaveCfg:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改数据保存接口配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

        <el-form-item label="api名称" prop="apiName">
          <el-input v-model="form.apiName" placeholder="请输入api名称" />
        </el-form-item>
        <el-form-item label="api编码" prop="apiCode">
          <el-input v-model="form.apiCode" placeholder="请输入api编码" />
        </el-form-item>
        <el-form-item label="主表名" prop="mainTableName">
          <el-input v-model="form.mainTableName" placeholder="请输入接口对应主表名" />
        </el-form-item>
        <el-form-item label="主表路径" prop="mainTableJsonPath">
          <el-input v-model="form.mainTableJsonPath" placeholder="请输入主表对接json路径默认item" />
        </el-form-item>
        <el-form-item label="主表唯一" prop="mainTableUniqueKey">
          <el-input v-model="form.mainTableUniqueKey" placeholder="请输入接口对应主表业务唯一键" />
        </el-form-item>
        <el-form-item label="子表名" prop="detailsTableName">
          <el-input v-model="form.detailsTableName" placeholder="请输入接口对应子表名" />
        </el-form-item>
        <el-form-item label="子表路径" prop="detailsTableJsonPath">
          <el-input v-model="form.detailsTableJsonPath" placeholder="请输入子表对接json路径默认itemDetails" />
        </el-form-item>
        <el-form-item label="子表唯一" prop="detailsTableUniqueKey">
          <el-input v-model="form.detailsTableUniqueKey" placeholder="请输入接口对应子表业务唯一键" />
        </el-form-item>
        <el-form-item label="接口来源" prop="apiService">
          <el-input v-model="form.apiService" placeholder="请输入接口来源服务" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>


      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import { listDataSaveCfg, getDataSaveCfg, delDataSaveCfg, addDataSaveCfg, updateDataSaveCfg, updateApiStatus } from "@/api/apimodule/dataSaveCfg/api";

export default {
  mixins: [tableFullHeight],
  name: "DataSaveCfg",
  data() {
    return {
      baseURL: window.location.protocol + "//" + window.location.host +  process.env.VUE_APP_BASE_API  + '/mengniu-api/save/saveDataInfo/',
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数据保存接口配置表格数据
      dataSaveCfgList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenantId: null,
        revision: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlg: null,
        apiCode: null,
        apiName: null,
        mainTableName: null,
        detailsTableName: null,
        apiService: null,
        apiStatus: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        apiCode: [
          { required: true, message: "api编码不能为空", trigger: "blur" }
        ],
        apiName: [
          { required: true, message: "api名称不能为空", trigger: "blur" }
        ],
        mainTableName: [
          { required: true, message: "主表表名不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询数据保存接口配置列表 */
    getList() {
      this.loading = true;
      listDataSaveCfg(this.queryParams).then(response => {
        this.dataSaveCfgList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        tenantId: null,
        revision: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        id: null,
        remark: null,
        delFlg: null,
        apiCode: null,
        apiName: null,
        mainTableName: null,
        detailsTableName: null,
        apiService: null,
        detailsTableUniqueKey: null,
        mainTableUniqueKey: null,
        mainTableJsonPath: null,
        detailsTableJsonPath: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryFormRef");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加数据保存接口配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getDataSaveCfg(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改数据保存接口配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDataSaveCfg(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDataSaveCfg(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除数据保存接口配置编号为"' + ids + '"的数据项？').then(function() {
        return delDataSaveCfg(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('apimodule/dataSaveCfg/export', {
        ...this.queryParams
      }, `dataSaveCfg_${new Date().getTime()}.xlsx`)
    },
    changeSwitch(row) {
      updateApiStatus(row).then(response => {
        console.log(response)
        if (response.code === 200) {
          this.$modal.msgSuccess(response.msg);
        } else {
          this.$modal.msgError(response.msg);
        }
      });
    },
    handleLogView(row) {
      this.$router.push({ path: './apilog', query: { apiCode: row.apiCode } })
    }
  }
};
</script>
