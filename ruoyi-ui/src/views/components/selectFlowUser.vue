<template>
  <!-- 选择用户 -->
  <div class="selectUser">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input v-model="deptName" placeholder="请输入部门名称" clearable size="small" prefix-icon="el-icon-search"
            style="margin-bottom: 20px" />
        </div>
        <div class="head-container">
          <el-tree :data="deptOptions" :props="defaultProps" :expand-on-click-node="false"
            :filter-node-method="filterNode" ref="tree" node-key="id" default-expand-all highlight-current
            @node-click="handleNodeClick" />
        </div>
      </el-col>
      <!--用户数据-->
      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
          label-width="68px">
          <el-form-item label="用户名" prop="userName">
            <el-input v-model="queryParams.userName" placeholder="用户名" clearable style="width: 240px"
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="姓名" prop="nickName">
            <el-input v-model="queryParams.nickName" placeholder="请输入姓名" clearable style="width: 240px"
              @keyup.enter.native="handleQuery" />
          </el-form-item>
<!--          <el-form-item label="岗位" prop="postName">-->
<!--            <el-select v-model="queryParams.postName" clearable filterable placeholder="请输入岗位" class="filter-item"-->
<!--              @keyup.enter.native="handleQuery">-->
<!--              <el-option v-for="item in postListOptions" :key="item.id" :label="item.postName" :value="item.postName" />-->
<!--            </el-select>-->
<!--          </el-form-item>-->
          <div style="text-align: right">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button type="primary" size="mini" :disabled="checkedUserData.length === 0" @click="submitForm">
              确定
            </el-button>
          </div>
        </el-form>
        <el-row style="display: flex;flex-wrap: nowrap;">
          <el-col :span="12" style="margin-right: 10px; border:1px solid #ccc">
            <el-table ref="tableRef" v-loading="loading" :data="userList" row-key="userId"
              @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="50" align="center" :reserve-selection="true"></el-table-column>
              <el-table-column label="用户名" align="center" key="userName" prop="userName" v-if="columns[1].visible"
                :show-overflow-tooltip="true" />
              <el-table-column label="姓名" align="center" key="nickName" prop="nickName" v-if="columns[2].visible"
                :show-overflow-tooltip="true" />
              <el-table-column label="部门" align="center" key="deptName" prop="dept.deptName" v-if="columns[3].visible"
                :show-overflow-tooltip="true" />
              <el-table-column label="岗位" align="center" key="postName" prop="postName" :show-overflow-tooltip="true" />
            </el-table>
            <el-pagination style="margin-bottom: 35px;" v-show="total > 0" @size-change="handlePageSizeChange"
              @current-change="handlePageNumChange" :current-page="queryParams.pageNum" :page-sizes="[10, 20, 30, 40]"
              :page-size="queryParams.pageSize" layout=" sizes, prev, pager, next" :total="total">
            </el-pagination>
          </el-col>
          <el-col :span="12" style="border:1px solid #ccc">
            <el-table ref="tableChecked" :data="checkedUserData" v-loading="checkedloading">
              <el-table-column type="index" label="序号" width="50"></el-table-column>
              <el-table-column label="用户名称" align="center" key="userName" prop="userName"
                :show-overflow-tooltip="true" />
              <el-table-column label="用户昵称" align="center" key="nickName" prop="nickName"
                :show-overflow-tooltip="true" />
              <el-table-column label="部门" align="center" key="deptName" prop="dept.deptName"
                :show-overflow-tooltip="true" />
              <el-table-column label="岗位" align="center" key="postName" prop="postName" :show-overflow-tooltip="true" />
              <el-table-column label="操作">
                <template slot-scope="scope">
                  <el-button size="mini" type="danger" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination style="margin-bottom: 35px;" v-show="seletedTotal > 0" @size-change="handleSelectdSizeChange"
              @current-change="handleSelectdPageNumChange" :current-page="selectedQueryParams.pageNum"
              :page-sizes="[10, 20, 30, 40]" :page-size="selectedQueryParams.pageSize" layout="sizes, prev, pager, next"
              :total="total">
            </el-pagination>
            <!-- <pagination v-show="seletedTotal > 0" style="margin-bottom: 35px;" :total="seletedTotal"
              :page.sync="selectedQueryParams.pageNum" :limit.sync="selectedQueryParams.pageSize"
              @pagination="getSelectList" /> -->
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as api from "@/api/system/user";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { listPostAll } from "../../api/system/post";
export default {
  name: "User",
  dicts: ['sys_normal_disable', 'sys_user_sex'],
  components: { Treeselect },
  props: ["userVisible", "selectUser", "postParams", "type", 'userIds'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: [],
      checkedUserData: [],//选中的用户信息列表
      checkedloading: false,//选中的用户信息列表loading
      copyData: [],
      seletedTotal: 0,
      selectedQueryParams: {
        pageNum: 1,
        pageSize: 10
      },
      selectedRow: [],
      userMap: {},
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,
      // 默认密码
      initPassword: undefined,
      // 日期范围
      dateRange: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      postListOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        phonenumber: undefined,
        status: "0",
        deptId: undefined,
        postName: '',
      },
      // 列信息
      columns: [
        { key: 0, label: `用户编号`, visible: true },
        { key: 1, label: `用户名称`, visible: true },
        { key: 2, label: `用户昵称`, visible: true },
        { key: 3, label: `部门`, visible: true },
        { key: 4, label: `手机号码`, visible: true },
        { key: 5, label: `状态`, visible: true },
        { key: 6, label: `创建时间`, visible: true }
      ],
      checkedItemList: [], // 已选的itemList
      checkAllInfo: {
        isIndeterminate: false,
        isChecked: false,
      }
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    },
    userVisible: {
      handler() {
        this.initForm();
      },
      immediate: true
    }
  },
  created() {
    this.getList();
    this.getDeptTree();
    this.getPostList();
  },
  methods: {
    handlePageNumChange(val) {
      this.queryParams.pageNum = val
      this.getList();
    },
    handlePageSizeChange(val) {
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = val
      this.getList();
    },
    handleSelectdPageNumChange(val) {
      this.selectedQueryParams.pageNum = val
      this.getSelectList()
    },
    handleSelectdSizeChange(val) {
      this.selectedQueryParams.pageNum = 1
      this.selectedQueryParams.pageSize = val
      this.getSelectList()
    },
    // 删除功能方法
    handleDelete: function (index, row) {
      this.checkedUserData = this.checkedUserData.filter(item => item.userId !== row.userId)
      let indexD = this.userList.findIndex(item => item.userId === row.userId)
      this.$nextTick(() => {
        this.$refs.tableRef.toggleRowSelection(this.userList[indexD], false);
      })
    },
    // 左边表的选中事件
    handleSelectionChange(selection) {
      console.trace("handleSelectionChange");

      this.selectedRow = selection
      const { pageNum, pageSize } = this.selectedQueryParams;
      // 计算当前页的数据
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;

      this.checkedUserData = selection.slice(start, end);
      this.copyData = JSON.parse(JSON.stringify(selection))
      this.seletedTotal = this.copyData.length

    },
    getSelectList() {
      this.checkedloading = true
      const { pageNum, pageSize } = this.selectedQueryParams;
      // 计算当前页的数据
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;
      const paginatedData = this.copyData.slice(start, end);

      // 更新数据
      this.checkedUserData = paginatedData;
      this.seletedTotal = this.copyData.length
      this.checkedloading = false;
    },
    initForm: function () {
      this.$nextTick(() => {
        if (this.userIds) {
          this.$refs.tableRef.clearSelection();
          const selectUser = this.userIds.split(',')
          this.userList.forEach(one => {
            selectUser.forEach(item => {
              if (item == one.userId) {
                this.$nextTick(() => {
                  this.$refs.tableRef.toggleRowSelection(one, true);
                })
              } else {
                const sign = `${one.userName}:${one.nickName}`
                if (item == sign) {
                  this.$nextTick(() => {
                    this.$refs.tableRef.toggleRowSelection(one, true);
                  })
                }
              }
            })
          });
        }
      })
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      let params = this.addDateRange(this.queryParams, this.dateRange);
      let url = "listUser"
      if (this.type) {
        let postParams = JSON.parse(JSON.stringify(this.postParams));
        url = postParams.url;
        delete postParams.url;
        params = { ...postParams, deptId: this.queryParams.deptId };
      }
      api[url](params).then(response => {
        this.total = response.total;
        this.loading = false;
        response.rows.forEach(item => {
          item.isChecked = this.checkedItemList.findIndex(e => e.userId === item.userId) !== -1;
          this.userMap[item.userId] = item;
        })
        this.userList = response.rows;
        this.initForm()
        // this.isCheckedAll();
        // this.initForm();
      }
      );
    },
    getPostList() {
      listPostAll().then(response => {
        this.postListOptions = response.data
      })
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      api.deptTreeSelect().then(response => {
        this.deptOptions = response.data;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id;
      this.handleQuery();
    },
    /**
     * 搜索按钮操作
     */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /**
     * 重置按钮操作
     */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.deptId = undefined;
      this.$refs.tree.setCurrentKey(null);
      this.handleQuery();
    },
    // 是否全选中
    isCheckedAll() {
      const len = this.userList.length;
      let count = 0;
      this.userList.map(item => {
        if (item.isChecked) count += 1;
      });
      this.checkAllInfo.isChecked = len === count && len > 0;
      this.checkAllInfo.isIndeterminate = count > 0 && count < len;
    },
    // 全选
    handleCheckAll() {
      const checkedItemList = this.checkedItemList;
      this.checkAllInfo.isIndeterminate = false;
      if (this.checkAllInfo.isChecked) {
        this.userList = this.userList.map(item => {
          item.isChecked = true;
          if (this.checkedItemList.findIndex(e => e.userId === item.userId) === -1) {
            checkedItemList.push({ userId: item.userId, nickName: row.nickName });
          }
          return item;
        });
      } else {
        this.userList = this.userList.map(item => {
          item.isChecked = false;
          let index = checkedItemList.findIndex(e => e.userId === item.userId);
          if (index !== -1) checkedItemList.splice(index, 1);
          return item;
        });
      }
      this.checkedItemList = checkedItemList
    },
    // 单选
    handleCheck(row) {
      // 转办|委派仅支持单选
      if (['转办', '委派'].includes(this.type)) {
        this.userList.forEach(e => {
          if (e.userId === row.userId) e.isChecked = true;
          else e.isChecked = false;
        });
        this.checkedItemList = [{ userId: row.userId, userName: row.userName, nickName: row.nickName }];
      } else {
        if (this.userMap[row.userId]) {
          var isChecked = this.userMap[row.userId]['isChecked'];
          this.userMap[row.userId]['isChecked'] = !isChecked;

          const checkedItemList = this.checkedItemList;
          if (!isChecked) {
            checkedItemList.push({
              userId: row.userId,
              userName: row.userName,
              nickName: row.nickName,
              deptName: row.dept.deptName
            });
          } else {
            const index = checkedItemList.findIndex(n => n.userId == row.userId);
            if (index !== -1) checkedItemList.splice(index, 1);
          }
          this.checkedItemList = checkedItemList;
          this.checkedUserData = checkedItemList;
        }


        this.isCheckedAll();
      }
    },
    // 删除标签
    handleClose(userId) {
      this.userList.forEach(e => {
        if (e.userId === userId) e.isChecked = !e.isChecked;
      });
      const checkedItemList = this.checkedItemList
      const index = checkedItemList.findIndex(n => n.userId === userId);
      if (index !== -1) checkedItemList.splice(index, 1);
      this.checkedItemList = checkedItemList;
      this.isCheckedAll();
    },
    // 取消按钮
    cancel() {
      this.$emit("update:userVisible", false);
    },
    // 提交按钮
    submitForm() {
      this.$emit("handleUserSelect", this.copyData);
      this.cancel();
    }
  }
};
</script>
