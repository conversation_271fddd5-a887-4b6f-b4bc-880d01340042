<template>
  <div class="app-container">
    <div ref="queryFormRef" class="filter-container">
      <el-input
        v-model="listQuery.jobName"
        clearable
        placeholder="任务名称"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter.native="fetchData"
      />
      <el-select v-model="listQuery.projectId" clearable placeholder="所属项目" class="filter-item">
        <el-option v-for="item in jobProjectList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="fetchData">
        搜索
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;margin-bottom: 1vh" type="primary" icon="el-icon-edit" @click="handleCreate">
        添加
      </el-button>
    </div>
    <el-table
      ref="fullHeightTableRef"
      :height="tableHeight"
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="所属项目" width="95">
        <template slot-scope="scope">
          {{ getProjectName(scope.row.projectId) }}
        </template>
      </el-table-column>
      <el-table-column label="任务名称" width="400" align="center">
        <template slot-scope="scope">{{ scope.row.jobName }}</template>
      </el-table-column>
      <el-table-column label="所属系统_建模层" width="120" align="center">
        <template slot-scope="scope">{{ scope.row.affSystem }}
        </template>
      </el-table-column>

      <el-table-column label="读数据源名称" width="250" align="center">
        <template slot-scope="scope">
          {{ getDatabaseName(scope.row.rDatabaseId) }}
        </template>
      </el-table-column>
      <el-table-column label="读数据库类型" width="120" align="center">
        <template slot-scope="scope">{{ scope.row.rDatabaseType }}
        </template>
      </el-table-column>
      <el-table-column label="读数据库库名" width="200" align="center">
        <template slot-scope="scope">{{ scope.row.tableSchema }}
        </template>
      </el-table-column>

      <el-table-column label="是否全库同步" width="120" align="center">
        <template slot-scope="scope">
          {{ getDataBaseShowRules(scope.row.databaseShowStatus) }}
        </template>
      </el-table-column>
      <el-table-column label="数据库表名" width="450" align="center">
        <template slot-scope="scope">
          {{ scope.row.tableName }}
        </template>
      </el-table-column>

      <el-table-column label="写数据库源名称" width="250" align="center">
        <template slot-scope="scope">
          {{ getDatabaseName(scope.row.wDatabaseId) }}
        </template>
      </el-table-column>
      <el-table-column label="写数据库类型" width="120" align="center">
        <template slot-scope="scope">{{ scope.row.wDatabaseType }}
        </template>
      </el-table-column>
      <!--      <el-table-column label="状态" align="center">-->
      <!--        <template slot-scope="scope">-->
      <!--          {{ getStatusText(scope.row.status)}}-->
      <!--        </template>-->
      <!--      </el-table-column>-->

      <el-table-column label="是否开启驼峰映射" width="120" align="center">
        <template slot-scope="scope">
          {{ getFieldRuleLabel(scope.row.changeHump) }}
        </template>
      </el-table-column>

      <el-table-column label="创建时间" width="200" align="center">
        <template slot-scope="scope">{{ scope.row.createTime }}</template>
      </el-table-column>

      <el-table-column style="color: #00a0e9" label="操作" align="center" fixed="right">
        <template slot-scope="{row}">
          <el-dropdown trigger="click">
            <span class="el-dropdown-link">
              操作<i class="el-icon-arrow-down el-icon--right" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item style="color: #7a8b9a" @click.native="handlerViewLog(row)">日志</el-dropdown-item>
              <el-dropdown-item style="color: #7a8b9a" @click.native="handleUpdate(row)">编辑</el-dropdown-item>
              <el-dropdown-item style="color: red" @click.native="confirmDelete(row)">删除</el-dropdown-item>

              <el-dropdown-item style="color: #13ce66" divided @click.native="confirmExecute(row)">执行</el-dropdown-item>
<!--              <el-dropdown-item style="color: blue" @click.native="openCreateQualityJob(row)">构建质检任务</el-dropdown-item>-->
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="fetchData"
    />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="800px" @close="resetTemp">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="150px">
        <el-form-item label="所属项目：" prop="projectId">
          <el-select v-model="temp.projectId" placeholder="所属项目" class="filter-item">
            <el-option v-for="item in jobProjectList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="任务名称：" prop="jobName">
          <el-input v-model="temp.jobName" placeholder="任务名称" style="width: 40%" />
        </el-form-item>
        <el-form-item label="所属系统_建模层：" prop="affSystem">
          <el-input v-model="temp.affSystem" placeholder="所属系统 (用于建表时拼接前缀)" style="width: 40%" />
        </el-form-item>
        <el-form-item label="读数据库类型：" prop="rDatabaseType">
          <el-select v-model="temp.rDatabaseType" placeholder="源数据库类型" tyle="width: 40%" @change="changeRDatabaseType(temp.rDatabaseType)">
            <el-option
              v-for="item in databaseTypeList"
              :key="item.id"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <!--          <el-input :disabled="true" v-model="temp.rDatabaseType" placeholder="源数据库类型" style="width: 40%" />-->
        </el-form-item>
        <el-form-item label="读数据库源：" prop="rDatabaseId">
          <el-select v-model="temp.rDatabaseId" filterable style="width: 300px" @change="rDsChange">
            <el-option
              v-for="item in rDsList"
              :key="item.id"
              :label="item.datasourceName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="字段获取方式" prop="way" v-if="temp.rDatabaseType==='mongodb'">
          <el-radio-group v-model="temp.way">
            <el-radio label="1">从MongoDB获取文档及字段</el-radio>
            <el-radio label="2">从表单设计器获取文档及字段</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-show="temp.rDatabaseType==='postgresql' || temp.rDatabaseType==='oracle' || temp.rDatabaseType==='sqlserver'" label="数据库" prop="tableSchema">
          <el-select v-model="temp.tableSchema" default-first-option filterable style="width: 300px" @change="schemaChange">
            <el-option
              v-for="item in schemaList"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="写数据库类型：" prop="wDatabaseType">
          <el-select v-model="temp.wDatabaseType" placeholder="写数据库类型" tyle="width: 40%" @change="changeWDatabaseType(temp.wDatabaseType)">
            <el-option
              v-for="item in mysqlTypeList"
              :key="item.id"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="写数据库：" prop="wDatabaseId">
          <el-select v-model="temp.wDatabaseId" filterable style="width: 300px" @change="wDsChange">
            <el-option
              v-for="item in wDsList"
              :key="item.id"
              :label="item.datasourceName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="是否全库同步：" prop="databaseShowStatus" v-if="temp.wDatabaseType!=='doris'">
          <el-select v-model="temp.databaseShowStatus" placeholder="请选择 (默认全库同步)" tyle="width: 40%" @change="dataBaseShowChange" >
            <el-option
              v-for="item in dataBaseShowRules"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-show="1 === this.temp.databaseShowStatus" label="数据库表名：" prop="tableName">
         <el-select
            v-model="temp.tableName"
            multiple
            collapse-tags
            clearable
            filterable
            reserve-keyword
            style="width: 550px;"
            @change="handleSelectionChange">
           <el-option v-for="item in tableList" :key="item" :label="item" :value="item" />
         </el-select>
        </el-form-item>

        <el-form-item v-show="temp.wDatabaseType==='doris'" label="数据库表名：" prop="tableName">
          <el-select
            v-model="temp.tableName"
            collapse-tags
            clearable
            filterable
            reserve-keyword
            style="width: 550px;"
          >
            <el-option v-for="item in tableList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>


        <el-form-item v-show="temp.wDatabaseType==='doris'" label="模型：" prop="model">
          <el-select
            v-model="temp.model"
            collapse-tags
            clearable
            filterable
            reserve-keyword
            style="width: 550px;"
            @change="getFieldsChange">
            <el-option    v-for="item in models"
                          :key="item.id"
                          :label="item.label"
                          :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item v-show="temp.model" :label="temp.model+'：'" prop="dorisKeys">
          <el-select
            v-model="temp.dorisKeys"
            collapse-tags
            clearable
            filterable
            multiple
            reserve-keyword
            style="width: 550px;">
                        <el-option v-for="item in keys" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>




        <el-form-item label="开启字段驼峰映射：" prop="changeHump" v-show="temp.wDatabaseType==='mysql'">
          <el-select v-model="temp.changeHump" placeholder="请选择 (默认否)" tyle="width: 40%" >
            <el-option
              v-for="item in fieldRules"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确认
        </el-button>
      </div>
    </el-dialog>

<!--    <el-dialog title="创建质检任务" :visible.sync="qualityJobShow" width="800px">-->
<!--      <el-form ref="qualityJobForm" :rules="qualityRules" :model="qualityJobForm" label-position="left" label-width="150px">-->

<!--        <el-form-item label="所属项目" prop="projectId">-->
<!--          <el-select v-model="qualityJobForm.projectId" :disabled="true" placeholder="所属项目" class="filter-item">-->
<!--            <el-option v-for="item in jobProjectList" :key="item.id" :label="item.name" :value="item.id" />-->
<!--          </el-select>-->
<!--        </el-form-item>-->

<!--        <el-form-item label="所属主任务" prop="jobName">-->
<!--          <el-input v-model="qualityJobForm.jobName" :disabled="true" placeholder="所属主任务" style="width: 40%" />-->
<!--        </el-form-item>-->

<!--        <el-form-item v-show="false" label="所属主任务id" prop="jobId">-->
<!--          <el-input v-model="qualityJobForm.jobId" placeholder="所属主任务id" style="width: 40%" />-->
<!--        </el-form-item>-->

<!--        <el-form-item label="质检任务名称" prop="qualityJobName">-->
<!--          <el-input v-model="qualityJobForm.qualityJobName" placeholder="质检任务名称" style="width: 40%" />-->
<!--        </el-form-item>-->

<!--        <el-dialog-->
<!--          title="提示"-->
<!--          :visible.sync="showCronBox"-->
<!--          width="60%"-->
<!--          append-to-body-->
<!--        >-->
<!--          <cron v-model="qualityJobForm.cron" />-->
<!--          <span slot="footer" class="dialog-footer">-->
<!--            <el-button @click="showCronBox = false;">关闭</el-button>-->
<!--            <el-button type="primary" @click="showCronBox = false">确 定</el-button>-->
<!--          </span>-->
<!--        </el-dialog>-->
<!--        <el-form-item label="Cron表达式" prop="cron">-->
<!--          <el-input v-model="qualityJobForm.cron" auto-complete="off" placeholder="请输入Cron表达式">-->
<!--            <el-button v-if="!showCronBox" slot="append" icon="el-icon-turn-off" title="打开图形配置" @click="showCronBox = true" />-->
<!--            <el-button v-else slot="append" icon="el-icon-open" title="关闭图形配置" @click="showCronBox = false" />-->
<!--          </el-input>-->
<!--        </el-form-item>-->
<!--      </el-form>-->
<!--      <div slot="footer" class="dialog-footer">-->
<!--        <el-button @click="qualityJobShow = false">-->
<!--          取消-->
<!--        </el-button>-->
<!--        <el-button type="primary" @click="createQualityJob">-->
<!--          确认-->
<!--        </el-button>-->
<!--      </div>-->

<!--    </el-dialog>-->
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import { get as getDictDetail } from '@/api/system/dictDetail'

// import { created as createQualityJob } from '@/api/qualityJob/qualityJobInfo'
import { list as jdbcDsList } from '@/api/datax/datax-jdbcDatasource'
import * as dsQueryApi from '@/api/dts/metadata-query'
import * as createTableJobInfoApi from '@/api/datax/datax-createTableJobInfo'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import Cron from '@/components/Cron/index.vue'
import * as jobProjectApi from '@/api/datax/datax-job-project'

export default {
  mixins: [tableFullHeight],
  name: 'JobProject',
  components: { Cron, Pagination },
  directives: { waves },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'gray',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      jobProjectList: [],
      databaseTypeList: [
        { id: 1, value: 'mysql', label: 'mysql' },
        { id: 2, value: 'oracle', label: 'oracle' },
        { id: 3, value: 'sqlserver', label: 'sqlserver' },
        { id: 4, value: 'mongodb', label: 'mongodb' }
      ],
      // 是否采取驼峰映射
      fieldRules: [
        { value: 'HUMP', label: '是' },
        { value: 'ORIGIN', label: '否' }
      ],
      dataBaseShowRules: [
        { value: 0, label: '全库同步' },
        { value: 1, label: '选表同步' }
      ],
      // 写数据库目前只支持mysql类型
      mysqlTypeList: [
        { id: 1, value: 'mysql', label: 'mysql' },
         { id: 2, value: 'doris', label: 'doris' },
      ],

      models: [
        { id: 1, value: 'DuplicateKey', label: '明细模型' },
        { id: 2, value: 'UniqueKey', label: '主键模型' },
      ],
      databaseList: [],
      qualityJobForm: {
        projectId: '',
        cron: '',
        jobName: '',
        qualityJobName: '',
        jobId: ''

      },
      showCronBox: false,
      qualityJobShow: false,
      statusList: [],
      schemaList: [],
      wDsList: [],
      rDsList: [],
      tableList: [],
      list: null,
      listLoading: true,
      total: 0,
      listQuery: {
        projectId: '',
        pageNo: 1,
        pageSize: 10,
        jobName: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: 'Edit',
        create: 'Create'
      },
      rules: {
        jobName: [{ required: true, message: 'this is required', trigger: 'blur' }],
        rDatabaseId: [{ required: true, message: 'this is required', trigger: 'blur' }],
        rDatabaseType: [{ required: true, message: 'this is required', trigger: 'blur' }],
        wDatabaseId: [{ required: true, message: 'this is required', trigger: 'blur' }],
        wDatabaseType: [{ required: true, message: 'this is required', trigger: 'blur' }],
        affSystem: [{ required: false, message: 'this is required', trigger: 'blur' }],
        way:[{required: true, message: 'this is required', trigger: 'blur' }]
      },
      qualityRules: {
        jobName: [{ required: true, message: 'this is required', trigger: 'blur' }],
        qualityJobName: [{ required: true, message: 'this is required', trigger: 'blur' }],
        cron: [{ required: true, message: 'this is required', trigger: 'change' }]

      },
      temp: {
        projectId: '',
        tableName: '',
        columns: [],
        affSystem: '',
        jobName: '',
        rDatabaseId: '',
        rDatabaseType: '',
        tableSchema: '',
        wDatabaseId: '',
        wDatabaseType: '',
        changeHump:'',
        databaseShowStatus:'',
        way:'',
      },
      visible: true,
      keys:[],
    }
  },
  watch: {
    'temp.rDatabaseId': function(oldVal, newVal) {
      if (this.temp.rDatabaseType === 'postgresql' || this.temp.rDatabaseType === 'oracle' || this.temp.rDatabaseType === 'sqlserver') {
        this.getSchema()
      }
      if (this.temp.rDatabaseType==='mongodb'){

      }else {
        this.getTables('JobProject')
      }
    }
  },
  created() {
    this.getDictDetailInfo()
    // 获取数据源信息
    this.getJdbcDs()
    // 获取项目列表
    this.getJobProject()
    // 获取任务列表
    this.fetchData()
    this.getDatebaseList()
  },
  methods: {
    getProjectName(projectId) {
      const project = this.jobProjectList.filter(item => item.id == projectId)
      if (project && project.length) {
        return project[0].name
      }
    },
    // 获取表名
    getTables(type) {
        if (type === 'JobProject') {
          let obj = {}
          if (this.temp.rDatabaseType === 'postgresql' || this.temp.rDatabaseType === 'oracle' || this.temp.rDatabaseType === 'sqlserver') {
            obj = {
                    datasourceId: this.temp.rDatabaseId,
                    tableSchema: this.temp.tableSchema
                  }
          }
          else if (this.temp.rDatabaseType === 'mongodb'){
            dsQueryApi.getMongoTables().then(response => {
              if (response) {
                this.tableList = response.data
              }
            })
            return;
          }
          else {
            obj = {datasourceId: this.temp.rDatabaseId}
          }
          // 组装
          dsQueryApi.getTables(obj).then(response => {
            if (response) {
              this.tableList = response.data
            }
          })
        }
      },
    getFieldRuleLabel(value) {
      // 查找 fieldRules 中匹配的标签
      const rule = this.fieldRules.find(rule => rule.value === value);
      // 如果找到对应的标签，返回标签；否则返回原值
      return rule ? rule.label : value;
      },
    getDataBaseShowRules(valueShow) {
    // 查找 fieldRules 中匹配的标签
    const ruleShow = this.dataBaseShowRules.find(ruleShow => ruleShow.value === valueShow);
    // 如果找到对应的标签，返回标签；否则返回原值
    return ruleShow ? ruleShow.label : ruleShow;
    },
    getJobProject() {
      jobProjectApi.getJobProjectList().then(response => {
        this.jobProjectList = response.data
      })
    },
    changeRDatabaseType(databaseType) {
      this.temp.rDatabaseId = ''
      this.temp.tableSchema = ''
      const param = {
        datasource: databaseType
      }
      jdbcDsList(param).then(res => {
        this.rDsList = res.data.records
      })
    },

    changeWDatabaseType(databaseType) {
      this.temp.wDatabaseId = ''
      const param = {
        datasource: databaseType
      }
      jdbcDsList(param).then(res => {
        this.wDsList = res.data.records
      })
    },
    // 删除按钮提示模态框
    confirmDelete(row) {
      this.$confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 用户点击确定按钮时执行删除操作
        this.handleDelete(row)
      }).catch(() => {
        // 用户点击取消按钮时的操作，可以不做任何处理或关闭模态框
      })
    },

    handleSelectionChange(selectedValues) {
      // 对tableList进行重新排序，将选中的值移到前面
      this.tableList = [
        ...selectedValues,
        ...this.tableList.filter(item => !selectedValues.includes(item))
      ];
    },
    // 根据数据源id和表名获取字段信息
    getFieldsChange(selectedValues) {
      const obj = {
        datasourceId: this.temp.rDatabaseId,
        tableName: this.temp.tableName
      }

      if (this.temp.rDatabaseType === 'mongodb') {
        createTableJobInfoApi.getFields(obj).then(response => {
          this.keys = response.data
        })
      }

      if (this.temp.rDatabaseType === 'mysql') {
        createTableJobInfoApi.getColumns(obj).then(response => {
          this.keys = response.data
        })
      }
    },
    remoteMethod(query) {
      // 这里是你的远程查询方法的实现
    },

    // 执行按钮提示模态框
    confirmExecute(row) {
      this.$confirm('确定要执行吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 用户点击确定按钮时,才确定执行操作
        this.handlerExecute(row)
      }).catch(() => {
        // 用户点击取消按钮时的操作，可以不做任何处理或关闭模态框
      })
    },
    getDatebaseList() {
      const param = {}
      jdbcDsList(param).then(res => {
        this.databaseList = res.data.records
      })
    },
    getDatabaseName(id) {
      const found = this.databaseList.filter(item => item.id == id)
      if (found && found.length) {
        return found[0].datasourceName
      } else {
        return ''
      }
    },
    getDictDetailInfo() {
      this.getDicts('create_table_job_status').then(res => {
        this.statusList = res.content
      })
    },
    openCreateQualityJob(row) {
      this.qualityJobShow = true
      this.qualityJobForm.jobId = row.id
      this.qualityJobForm.jobName = row.jobName
      this.qualityJobForm.projectId = row.projectId
    },
    createQualityJob(row) {
      this.$refs['qualityJobForm'].validate((valid) => {
        if (valid) {
          createQualityJob(this.qualityJobForm).then(res => {
            if (res.code === 0) {
              this.qualityJobShow = false
              this.$message.success('质检任务创建成功')
            }
          })
        } else {
          this.$message.error('请检查必填项')
        }
        this.fetchData()
      })
    },
    getStatusText(status) {
      const found = this.statusList.filter(item => item.dictValue == status)
      return found[0].dictLabel
    },
    getSchema() {
      const obj = {
        datasourceId: this.temp.rDatabaseId
      }
      dsQueryApi.getTableSchema(obj).then(response => {
        this.schemaList = response.data
      })
    },
    // schema 切换
    schemaChange(e) {
      // 拿到表之前先clean一下
      this.temp.tableName = ''
      this.temp.tableSchema = e
      // 获取可用表
      this.getTables('JobProject')
    },

    // 是否全库同步状态值赋值
    dataBaseShowChange(e) {
      this.temp.databaseShowStatus = e

    },

    // 获取可用数据源
    getJdbcDs(type) {
      this.loading = true
      jdbcDsList(this.jdbcDsQuery).then(response => {
        const { records } = response.data
        this.rDsList = records
        this.wDsList = records
        this.loading = false
      })
    },
    // 执行任务按钮
    handlerExecute(row) {
      dsQueryApi.createTargetTable(row).then(res => {
        if (res.code === 0) {
          this.$message.success('任务执行成功')
        } else {
          this.$message.error('任务执行失败')
        }
      })
    },
    // writer 数据源切换
    wDsChange(e) {
      // 清空
      this.temp.wDatasourceId = e
      this.wDsList.find((item) => {
        if (item.id === e) {
          this.temp.wDatabaseType = item.datasource
        }
      })
    },
    // reader 数据源切换
    rDsChange(e) {
      // 拿到表之前先clean一下
      this.temp.tableName = ''

      // 获取可用表
      this.getTables('JobProject')
      this.temp.rDatabaseId = e
      this.rDsList.find((item) => {
        if (item.id === e) {
          this.temp.rDatabaseType = item.datasource
        }
      })
      this.$emit('selectDataSource', this.temp.rDatabaseType)
    },
    fetchData() {
      this.listLoading = true
      createTableJobInfoApi.list(this.listQuery).then(response => {
        const { records } = response.data
        const { total } = response.data
        this.total = total
        this.list = records
        this.listLoading = false
      })
    },
    resetTemp() {
      this.temp = {
        jobName: '',
        rDatabaseId: '',
        rDatabaseType: '',
        tableSchema: '',
        wDatabaseId: '',
        wDatabaseType: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.rDsList=[]
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    convertTableNameArrayToString() {
    if (Array.isArray(this.temp.tableName)) {
      this.temp.tableName = this.temp.tableName.join(',');
     }
      if (Array.isArray(this.temp.dorisKeys)) {
        this.temp.dorisKeys = this.temp.dorisKeys.join(',');
      }

    },
    // 新增
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // 调用转换函数
          this.convertTableNameArrayToString();
          createTableJobInfoApi.created(this.temp).then(() => {
            this.fetchData()
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Created Successfully',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // 调用转换函数
          this.convertTableNameArrayToString();
          const tempData = Object.assign({}, this.temp)
          createTableJobInfoApi.updated(tempData).then(() => {
            this.fetchData()
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    // handleUpdate(row) {
    //   this.temp = Object.assign({}, row) // copy obj
    //   this.dialogStatus = 'update'
    //   this.dialogFormVisible = true
    //   this.$nextTick(() => {
    //     this.$refs['dataForm'].clearValidate()
    //   })
    // },
    // 查看日志
    handlerViewLog(row) {
      this.$router.push({ path: './createTableJobLog', query: { createTableJobName: row.jobName } })
    },
        // 编辑按钮
    handleUpdate(row) {
      this.temp = Object.assign({}, row) // copy obj
      if (this.temp.tableName && this.temp.tableName !== ''){
        this.temp.tableName = this.temp.tableName.split(',')
      }
      if (this.temp.dorisKeys && this.temp.dorisKeys !== ''){
        this.temp.dorisKeys = this.temp.dorisKeys.split(',')
      }
      this.getTables('JobProject')

      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleDelete(row) {
      console.log('删除')
      const idList = []
      idList.push(row.id)
      createTableJobInfoApi.deleted({ idList: row.id }).then(response => {
        this.fetchData()
        this.$notify({
          title: 'Success',
          message: 'Delete Successfully',
          type: 'success',
          duration: 2000
        })
      })
    }
  }
}
</script>
