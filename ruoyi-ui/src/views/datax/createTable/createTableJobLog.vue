<template>
  <div class="app-container">
    <div ref="queryFormRef" class="filter-container">
      <el-input
        v-model="listQuery.createTableJobName"
        clearable
        placeholder="建表任务名称"
        style="width: 200px;"
        class="filter-item"
      />
      <el-select v-model="listQuery.status" placeholder="执行任务状态" clearable>
        <el-option
          v-for="item in excuteStatusDict"
          :key="item.id"
          :label="item.dictLabel"
          :value="item.dictValue"
        />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="fetchData">
        搜索
      </el-button>
      <el-button class="filter-item"  icon="el-icon-refresh"  type="primary"  @click="resetQuery">重置</el-button>
      <el-button class="filter-item"   icon="el-icon-edit"  type="primary"  @click="handlerDelete">清除</el-button>
    </div>
    <el-table
      ref="fullHeightTableRef"
      :height="tableHeight"
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column label="建表任务名称" align="center">
        <template slot-scope="scope">{{ scope.row.createTableJobName }}
        </template>
      </el-table-column>
      <el-table-column label="任务状态" width="150" align="center">
        <template slot-scope="scope">
          {{ getStatusText2(scope.row.status)}}
        </template>
      </el-table-column>
      <el-table-column label="任务日志详情"  width="200" align="center">
        <template slot-scope="scope">
          <el-button @click="openResultDialog(scope.row.result)"  size="small">查看</el-button>

        </template>
      </el-table-column>
      <el-table-column label="执行时间" width="200" align="center">
        <template slot-scope="scope">{{ scope.row.createTime }}</template>
      </el-table-column>
      <el-table-column label="结束时间" width="200" align="center">
        <template slot-scope="scope">{{ scope.row.endTime }}</template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="230" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button v-if="row.status!=='deleted'" size="mini" type="danger" @click="confirmDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="fetchData"
    />
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="600px">
      <el-form ref="dataForm" :rules="rules" :model="tempDelete" label-position="center" label-width="100px">
        <el-row>
          <el-col :span="14" :offset="5">
            <el-form-item label="执行器">
              <el-select v-model="tempDelete.deleteType" placeholder="请选择执行器" style="width: 230px">
                <el-option v-for="item in deleteTypeList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="deleteLog">
          确定
        </el-button>
      </div>
    </el-dialog>

    <el-dialog title="建表日志详情" :visible.sync="openResultShow" width="800px">
<!--      <div class="log-container">-->
<!--        <pre  v-text="resultInfoText" />-->
<!--      </div>-->
      <div style="font-size: large ">{{resultInfoText}}</div>
    </el-dialog>
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import * as createTableJobLog from '@/api/datax/datax-createTableJobLog'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'

export default {
  mixins: [tableFullHeight],
  name: 'JobProject',
  components: { Pagination },
  directives: { waves },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'gray',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      resultInfoText:"",
      openResultShow: false,
      excuteStatusDict:[],
      list: null,
      listLoading: true,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        createTableJobName: '',
        status:'',
      },
      dialogPluginVisible: false,
      pluginData: [],
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: 'Edit',
        create: 'Create'
      },
      rules: {
      },
      temp: {
        id: undefined,
        name: '',
        description: ''
      },
      tempDelete: {
        deleteType: 1,
      },
      visible: true,
      deleteTypeList: [
        { value: 1, label: '清理一个月之前日志数据' },
        { value: 2, label: '清理三个月之前日志数据' },
        { value: 3, label: '清理六个月之前日志数据' },
        { value: 4, label: '清理一年之前日志数据' },
        { value: 5, label: '清理一千条以前日志数据' },
        { value: 6, label: '清理一万条以前日志数据' },
        { value: 7, label: '清理三万条以前日志数据' },
        { value: 8, label: '清理十万条以前日志数据' },
        { value: 9, label: '清理所有日志数据' }
      ],
    }
  },
  created() {
    this.getDictDetailInfo();
    this.fetchData()
  },
  methods: {
    openResultDialog(text){
      this.resultInfoText = text
      this.openResultShow = true
    },
    getStatusText2(status) {
      return this.mySelectDictLabel(this.excuteStatusDict, status);
    },
    getDictDetailInfo(){
      this.getDicts("job_excute_status").then(response =>{
        if (response.code === 200) {
          this.excuteStatusDict = response.data
        }
      })
    },
    fetchData() {
      this.listLoading = true
      const param = Object.assign({}, this.listQuery)
      const urlJobName = this.$route.query.createTableJobName
      if (urlJobName && !param.createTableJobName) {
        param.createTableJobName = urlJobName
      }

      createTableJobLog.list(param).then(response => {
        const { records } = response.data
        const { total } = response.data
        this.total = total
        this.list = records
        this.listLoading = false
      })
    },
    resetQuery(){
      this.listQuery.createTableJobName = ''
      this.listQuery.status = ''
      this.fetchData()
    },
    deleteLog(){
      createTableJobLog.clearLog(this.tempDelete.deleteType).then(response => {
        this.fetchData()
        this.dialogFormVisible = false
        this.$notify({
          title: 'Success',
          message: 'Delete Successfully',
          type: 'success',
          duration: 2000
        })
      })
    },
    handlerDelete(){
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        name: '',
        description: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createTableJobLog.created(this.temp).then(() => {
            this.fetchData()
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Created Successfully',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row) // copy obj
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          createTableJobLog.updated(tempData).then(() => {
            this.fetchData()
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    //删除按钮提示模态框
    confirmDelete(row) {
      this.$confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 用户点击确定按钮时执行删除操作
        this.handleDelete(row);
      }).catch(() => {
        // 用户点击取消按钮时的操作，可以不做任何处理或关闭模态框
      });
    },
    handleDelete(row) {
      console.log('删除')
      const idList = []
      idList.push(row.id)
      createTableJobLog.deleted({ idList: row.id }).then(response => {
        this.fetchData()
        this.$notify({
          title: 'Success',
          message: 'Delete Successfully',
          type: 'success',
          duration: 2000
        })
      })
    }
  }
}
</script>

<style>
.log-container {
  font-size: larger;
  margin-bottom: 20px;
  background: #f5f5f5;
  width: 100%;
  height: 500px;
  overflow: scroll;
  pre {
    display: block;
    padding: 10px;
    margin: 0 0 10.5px;
    word-break: break-all;
    word-wrap: break-word;
    color: #334851;
    background-color: #f5f5f5;
    border-radius: 1px;
  }
}
</style>
