<template>
  <div class="app-container">
    <el-form label-position="right" label-width="120px" ref="incrementForm" :model="incrementForm" :rules="rules">
      <el-form-item label="增量方式" prop="incrementType">
        <el-select v-model="incrementForm.incrementType" placeholder="请选择参数类型" clearable filterable
          style="width: 500px">
          <el-option v-for="item in incrementTypes" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="增量时间格式" prop="replaceParamType" v-if="incrementForm.incrementType === 2">
        <el-select v-model="incrementForm.replaceParamType" placeholder="增量时间格式" clearable filterable
          style="width: 500px">
          <el-option v-for="item in replaceFormatTypes" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="增量开始时间" prop="incStartTime" v-if="incrementForm.incrementType === 2">
        <el-date-picker v-model="incrementForm.incStartTime" type="datetime" placeholder="首次增量时使用"
          format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" style="width: 50%" />
      </el-form-item>
      <el-form-item label="增量时间字段" prop="tableTimeParam" v-if="incrementForm.incrementType === 2">
        <!-- <el-input v-model="incrementForm.tableTimeParam" placeholder="请填写数据表中增量时间字段" style="width: 50%" /> -->
        <el-select filterable clearable v-model="incrementForm.tableTimeParam"
          placeholder="增量时间字段">
          <el-option v-for="item in tableTimeParamOptions" :key="item.source" :label="item.source" :value="item.source">
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>

export default {
  name: 'Increment',
  data() {
    return {
      incrementForm: {
        incrementType: 0,
        replaceParamType: 'Timestamp',
        incStartTime: '',
        tableTimeParam: ''
      },
      incrementTypes: [
        { value: 0, label: '无' },
        // { value: 1, label: '主键自增' },
        { value: 2, label: '时间自增' }
        // { value: 3, label: 'HIVE分区' }
      ],
      replaceFormatTypes: [
        // { value: 'yyyy/MM/dd', label: 'yyyy/MM/dd' },
        // { value: 'yyyy-MM-dd', label: 'yyyy-MM-dd' },
        // { value: 'HH:mm:ss', label: 'HH:mm:ss' },
        { value: 'yyyy/MM/dd HH:mm:ss', label: 'yyyy/MM/dd HH:mm:ss' },
        { value: 'yyyy-MM-dd HH:mm:ss', label: 'yyyy-MM-dd HH:mm:ss' },
        { value: 'Timestamp', label: '时间戳' }
      ],
      rules: {
        incrementType: [{ required: true, message: 'this is required', trigger: 'change' }],
        replaceParamType: [{ required: true, message: 'this is required', trigger: 'change' }],
        incStartTime: [{ required: true, message: 'this is required', trigger: 'blur' }],
        tableTimeParam: [{ required: true, message: 'this is required', trigger: 'blur' }]
      },
      tableTimeParamOptions:[]
    }
  },
  methods: {
    getFormValid() {
      return this.$refs['incrementForm'].validate()
    },
    getData() {
      return this.incrementForm
    },
    
  }
}
</script>
