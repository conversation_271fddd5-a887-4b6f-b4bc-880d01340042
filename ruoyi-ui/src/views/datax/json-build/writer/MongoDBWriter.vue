<template>
  <div>
    <el-form label-position="left" label-width="105px" ref="writerForm" :model="writerForm" :rules="rules">
      <el-form-item label="数据源" prop="datasourceId">
        <el-select
          v-model="writerForm.datasourceId"
          filterable
          @change="wDsChange"
        >
          <el-option
            v-for="item in wDsList"
            :key="item.id"
            :label="item.datasourceName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="获取方式" prop="way">
        <el-radio-group v-model="writerForm.way">
          <el-radio :label="1">从MongoDB获取文档及字段</el-radio>
          <el-radio :label="2">从表单设计器获取文档及字段</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="文档" prop="fromTableName">
            <el-select
              v-model="writerForm.fromTableName"
              :disabled="!this.writerForm.way"
              filterable
              @change="wTbChange"
              style="width: 42%"
            >
              <el-option
                v-for="item in wTbList"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="是否更新" prop="isUpsert">
        <el-select v-model="writerForm.isUpsert" placeholder="是否更新" :disabled="!this.writerForm.way">
          <el-option v-for="item in upsertType" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="更新模式" prop="upsertMode">
        <el-select v-model="writerForm.upsertMode" placeholder="更新模式" :disabled="!this.writerForm.way">
          <el-option v-for="item in upsertMode" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="业务主键" prop="upsertKey">
        <el-input v-model="writerForm.upsertKey" placeholder="被抽取业务表的主键" style="width: 42%" :disabled="!this.writerForm.way" />
      </el-form-item>

      <el-form-item label="前置sql语句：">
        <div style="display: flex; align-items: center; gap: 10px; width: 100%;">
          <!-- 下拉框 -->
          <el-select
            v-model="writerForm.preSqlType"
            placeholder="请选择类型"
            style="width: 200px"
          >
            <el-option label="清空集合（remove）" value="remove" />
            <el-option label="删除集合（drop）" value="drop" />
            <el-option label="自定义" value="custom" />
          </el-select>

          <!-- 输入框（仅在自定义时显示） -->
          <el-input
            v-if="writerForm.preSqlType === 'custom'"
            v-model="writerForm.preSql"
            placeholder="请输入完整 preSql JSON"
            type="textarea"
            :rows="2"
            style="flex: 1"
          />
        </div>
      </el-form-item>


      <el-form-item label="字段">
        <el-checkbox v-model="writerForm.checkAll" :indeterminate="writerForm.isIndeterminate" @change="wHandleCheckAllChange">全选</el-checkbox>
        <div style="margin: 15px 0;" />
        <el-checkbox-group v-model="writerForm.columns" @change="wHandleCheckedChange">
          <el-checkbox v-for="c in fromColumnList" :key="c" :label="c">{{ c }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import * as dsQueryApi from '@/api/dts/metadata-query.js'
import { getAllTableCodeFromFormDesign as getTableCodeList } from '@/api/codeDev/formDesign/formDesign'
import { list as jdbcDsList } from '@/api/datax/datax-jdbcDatasource'
import Bus from '../busWriter'
export default {
  name: 'MongoDBWriter',
  data() {
    return {
      radio: '',
      step1Data:{},
      jdbcDsQuery: {
        current: 1,
        size: 200
      },
      wDsList: [],
      fromColumnList: [],
      wTbList: [],
      dataSource: '',
      writerForm: {
        datasourceId: undefined,
        columns: [],
        tableName: '',
        checkAll: false,
        isIndeterminate: true,
        ifCreateTable: false,
        isUpsert: '',
        upsertMode: '',
        upsertKey: '',
        fromTableName: '',
        preSql:'',
        preSqlType: ''
      },
      upsertType: [
        { value: true, label: '针对相同的upsertKey做更新' },
        { value: false, label: '不做更新' }
      ],
      upsertMode: [
        { value: 'isReplace', label: 'replace模式' },
        { value: 'isUpsert', label: 'upsert模式' }
      ],
      rules: {
        mode: [{ required: true, message: 'this is required', trigger: 'change' }],
        datasourceId: [{ required: true, message: 'this is required', trigger: 'change' }],
        fromTableName: [{ required: true, message: 'this is required', trigger: 'change' }],
        way: [{ required: true, message: 'this is required', trigger: 'blur' }],
        isUpsert: [{ required: true, message: 'this is required', trigger: 'change' }],
        upsertKey: [{ required: true, message: 'this is required', trigger: 'blur' }],
        upsertMode: [{ required: true, message: 'this is required', trigger: 'blur' }]
      },
      readerForm: this.getReaderData()
    }
  },
  watch: {
    // 'writerForm.datasourceId': function(oldVal, newVal) {
    //   this.getTables('mongodbWriter')
    // }
    // 根据获取方式获取数据表和表字段
    'writerForm.way'(way) {
      // 清空已选文档
      this.writerForm.fromTableName = ''
      this.writerForm.isUpsert = ''
      this.writerForm.upsertKey = ''
      this.writerForm.upsertMode = ''
      // 清空已选文档对应的字段
      this.fromColumnList = []
      this.getTables('mongodbWriter', way);
    }
  },
  created() {
    this.getJdbcDs()
  },
  methods: {
    // 获取可用数据源
    getJdbcDs(type) {
      this.loading = true
      jdbcDsList(this.jdbcDsQuery).then(response => {
        const { records } = response.data
        this.wDsList = records
        this.loading = false
      })
    },
    // 获取表名
    getTables(type, way) {
      if (type === 'mongodbWriter'  && way == 1) {
        // 从mongo获取
        const obj = {
          datasourceId: this.writerForm.datasourceId
        }
        // 组装
        dsQueryApi.getTables(obj).then(response => {
          this.wTbList = response.data
        })
      } else if (type === 'mongodbWriter'  && way == 2) {
        // 从表单设计获取表
        getTableCodeList().then((result) => {
          this.wTbList = result.data
        })
      }
    },
    wDsChange(e) {
      // 清空
      this.writerForm.tableName = ''
      this.writerForm.datasourceId = e
      this.wDsList.find((item) => {
        if (item.id === e) {
          this.dataSource = item.datasource
        }
      })
      Bus.dataSourceId = e
      this.$emit('selectDataSource', this.dataSource)
      // 获取可用表
      this.getTables()
    },
    // 获取表字段
    getColumnsByType(type) {
      if (type === 'writer') {
        if (this.writerForm.way == '2') {
          //从表单设计器获取
          this.getTableColumnsFromFormDesign()
        } else {
          this.getColumns()
        }
      }
    },
    // 获取表字段
    getColumns() {
      const obj = {
        datasourceId: this.writerForm.datasourceId,
        tableName: this.writerForm.tableName
      }
      dsQueryApi.getColumns(obj).then(response => {
        this.fromColumnList = response.data
        this.writerForm.columns = response.data
        this.writerForm.checkAll = true
        this.writerForm.isIndeterminate = false
      })
    },
    getTableColumnsFromFormDesign() {
      const obj = {
        datasourceId: this.writerForm.datasourceId,
        tableName: this.writerForm.tableName
      }
      dsQueryApi.getColumnsFromFormDesign(obj).then(response => {
        this.fromColumnList = response.data
        this.writerForm.columns = response.data
        this.writerForm.checkAll = true
        this.writerForm.isIndeterminate = false
      })
    },
    // 表切换
    wTbChange(t) {
      this.writerForm.tableName = t
      this.fromColumnList = []
      this.writerForm.columns = []
      if (this.writerForm.way == '2') {
        this.collectionExists()
      }
      this.getColumnsByType('writer')
    },
    wHandleCheckAllChange(val) {
      this.writerForm.columns = val ? this.fromColumnList : []
      this.writerForm.isIndeterminate = false
    },
    wHandleCheckedChange(value) {
      const checkedCount = value.length
      this.writerForm.checkAll = checkedCount === this.fromColumnList.length
      this.writerForm.isIndeterminate = checkedCount > 0 && checkedCount < this.fromColumnList.length
    },
    createTableCheckedChange(val) {
      this.writerForm.tableName = val ? this.readerForm.tableName : ''
      this.fromColumnList = this.readerForm.columns
      this.writerForm.columns = this.readerForm.columns
      this.writerForm.checkAll = true
      this.writerForm.isIndeterminate = false
    },
    getData() {
      if (Bus.dataSourceId) {
        this.writerForm.datasourceId = Bus.dataSourceId
      }
      return this.writerForm
    },
    getReaderData() {
      return this.$parent.getReaderData()
    },
    getTableName() {
      return this.fromTableName
    },
    getFormValid() {
      return this.$refs['writerForm'].validate()
    },
    collectionExists() {
      const param = this.writerForm.tableName
      dsQueryApi.findCollection(param).then(result => {
        if ((result.msg !== '')) {
          this.$notify({
            title: 'Warn',
            message: result.msg,
            type: 'warning',
            duration: 4000
          })
        }
      })
    }
  }
}
</script>
