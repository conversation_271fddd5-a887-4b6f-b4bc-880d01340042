<template>
  <div class="app-container">
    <div style="color: #ff8833; font-weight: bold; font-size: 16px; margin-top: 10px;">
      Tips: 源端字段与目标字段    ( 1. 映射数量请保持一致；  2. 映射字段标识请保持一致 )
    </div>
    <br>
    <el-table :data="mapColumns" style="width: 100%">
      <el-table-column prop="index" label="序号" width="80"></el-table-column>

      <el-table-column prop="source" label="源端字段">
        <template #default="{ row }">
          <el-select
            v-model="row.source"
            placeholder="选择源端字段"
            clearable
            @change="updateTargetColumn(row)"
          >
            <el-option
              v-for="c in fromColumnsList"
              :key="c"
              :label="c"
              :value="c">
            </el-option>
          </el-select>
        </template>
      </el-table-column>

      <el-table-column prop="target" label="目标字段">
        <template #default="{ row }">
          <el-select
            v-model="row.target"
            placeholder="选择目标字段"
            :disabled="!row.source"
            filterable
            clearable
          >
            <el-option
              v-for="field in toColumnsList"
              :key="field"
              :label="field"
              :value="field">
            </el-option>
          </el-select>
        </template>
      </el-table-column>
    </el-table>
    <br>
    <el-button @click="checkField">校验</el-button>
  </div>
</template>

<script>
export default {
  name: 'FieldMapper',
  data() {
    return {
      mapperJson: {},
      fromColumnsList: [],
      toColumnsList: [],
      copyFromColumnsList: [],
      copyToColumnsList: [],
      sourceLength: 0,
      targetLength: 0,
      readerForm: {
        lcolumns: [],
        rcolumns: [],
        lcheckAll: false,
        rcheckAll: false,
        isIndeterminate: true
      }
    };
  },
  computed: {
    mapColumns() {
      // 深拷贝 fromColumnsList
      this.copyFromColumnsList = JSON.parse(JSON.stringify(this.fromColumnsList));
      // 创建一个空的 copyToColumnsList
      this.copyToColumnsList = Array(this.copyFromColumnsList.length).fill('');
      // 遍历 copyFromColumnsList
      for (let i = 0; i < this.copyFromColumnsList.length; i++) {
        const fromElement = this.copyFromColumnsList[i];
        // 遍历 toColumnsList
        for (let j = 0; j < this.toColumnsList.length; j++) {
          const toElement = this.toColumnsList[j];
          // 如果对应元素相等，存入 copyToColumnsList
          if (fromElement == toElement || fromElement.split(':')[0] == toElement || fromElement == toElement.split(':')[0] ||
            // read:Oracle => write hive
            fromElement.toLowerCase() == toElement.split(':')[1] ||
            // read:hive => write Oracle
            fromElement.split(':')[1] == toElement.toLowerCase()
          ) {
            this.copyToColumnsList[i] = toElement;
            break; // 找到匹配后，跳出内层循环
          }
        }
      }
      const maxRows = this.copyFromColumnsList.length;
      return Array.from({ length: maxRows }, (v, i) => ({
        index: i + 1,
        source: this.copyFromColumnsList[i] || '', // 使用 copyFromColumnsList
        target: this.copyToColumnsList[i] || ''    // 使用 copyToColumnsList
      }));
    }
  },
  methods: {
    updateTargetColumn(row) {
      // 如果源端字段不在目标字段列表中，目标字段清空
      row.target = ''; // 将目标字段清空
      // 更新左侧字段的数组
      this.getLColumns();
    },
    getLColumns() {
      return this.readerForm.lcolumns = this.mapColumns.map(item => item.source).filter(Boolean);
    },
    getRColumns() {
      return this.readerForm.rcolumns = this.mapColumns.map(item => item.target).filter(Boolean);
    },
    checkField() {
      this.sourceLength = this.getLColumns().length
      this.targetLength = this.getRColumns().length
      if (this.sourceLength !== this.targetLength) {
        this.$message.error("字段数量不一致，请检查")
        return false
      } else {
        this.$message.success('字段数量检查成功');
        return true
      }
    }
  }
}
</script>
