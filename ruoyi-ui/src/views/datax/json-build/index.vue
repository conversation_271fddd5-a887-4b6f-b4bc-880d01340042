<template>
  <div class="app-container">
    <div class="build-container">
      <el-steps :active="active" finish-status="success">
        <el-step title="步骤 1" description="构建reader">1</el-step>
        <el-step title="步骤 2" description="构建writer">2</el-step>
        <el-step title="步骤 3" description="字段映射">3</el-step>
        <el-step title="步骤 4" description="增量配置">4</el-step>
        <el-step title="步骤 5" description="构建">5</el-step>
      </el-steps>

      <div v-show="active === 1" class="step1">
        <Reader ref="reader" />
      </div>
      <div v-show="active === 2" class="step2">
        <Writer ref="writer" />
      </div>
      <div v-show="active === 3" class="step3">
        <Mapper ref="mapper" />
      </div>
      <div v-show="active === 4" class="step4">
        <Increment ref="increment" />
      </div>
      <div v-show="active === 5" class="step5">
        <el-button type="primary" @click="buildJson">1.构建</el-button>
        <el-button type="primary" @click="handleJobTemplateSelectDrawer">{{ jobTemplate ? jobTemplate : "2.选择模板"
          }}</el-button>
        <el-button type="info" @click="handleCopy(inputData, $event)">复制json</el-button>

        <label for="mission-select" style="margin-left: 10px;color: #0d13aa;font-weight: normal">指定所属任务：</label>
        <el-select id="mission-select" v-model="temp.missionId" placeholder="指定所属任务" filterable clearable
          style="margin-right: 10px;">
          <el-option v-for="item in jobSummaryList" :key="item.id" :label="item.jobDesc" :value="item.id">
          </el-option>
        </el-select>
        (步骤：构建->选择模板->指定所属任务(可选)->下一步)
        <el-drawer ref="jobTemplateSelectDrawer" title="选择模板" :visible.sync="jobTemplateSelectDrawer" direction="rtl"
          size="50%">
          <el-table v-loading="listLoading" :data="list" element-loading-text="Loading" border fit highlight-current-row
            destroy-on-close="true" @current-change="handleCurrentChange">
            <el-table-column align="center" label="任务ID" width="80">
              <template slot-scope="scope">{{ scope.row.id }}</template>
            </el-table-column>
            <el-table-column label="任务描述" align="center">
              <template slot-scope="scope">{{ scope.row.jobDesc }}</template>
            </el-table-column>
            <el-table-column label="所属项目" align="center" width="120">
              <template slot-scope="scope">{{ scope.row.projectName }}</template>
            </el-table-column>
            <el-table-column label="Cron" align="center">
              <template slot-scope="scope"><span>{{ scope.row.jobCron }}</span></template>
            </el-table-column>
            <el-table-column label="路由策略" align="center">
              <template slot-scope="scope"> {{routeStrategies.find(t => t.value ===
                scope.row.executorRouteStrategy).label }}</template>
            </el-table-column>
          </el-table>
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size"
            @pagination="fetchData" />
        </el-drawer>
        <div style="margin-bottom: 20px;" />
        <json-editor v-show="active === 5" ref="jsonEditor" v-model="configJson" />
      </div>

      <el-button :disabled="active === 1" style="margin-top: 12px;" @click="last">上一步</el-button>
      <el-button type="primary" style="margin-top: 12px;margin-bottom: 12px;" @click="next">下一步</el-button>
    </div>
  </div>
</template>

<script>
import * as dataxJsonApi from '@/api/datax/datax-json'
import * as jobTemplate from '@/api/datax/datax-job-template'
import * as jobSummary from '@/api/datax/datax-job-summary'
import Pagination from '@/components/Pagination'
import JsonEditor from '@/components/JsonEditor'
import Reader from './reader'
import Writer from './writer'
import clip from '@/utils/clipboard'
import Mapper from './mapper'
import Increment from './increment.vue'

export default {
  name: 'JsonBuild',
  components: { Reader, Writer, Pagination, JsonEditor, Mapper, Increment },
  data() {
    return {
      jobSummaryList: [],
      configJson: '',
      active: 1,
      jobTemplate: '',
      jobTemplateSelectDrawer: false,
      list: null,
      currentRow: null,
      listLoading: true,
      total: 0,
      listQuery: {
        current: 1,
        size: 10,
        jobGroup: 0,
        triggerStatus: -1,
        jobDesc: '',
        executorHandler: '',
        userId: 0
      },
      blockStrategies: [
        { value: 'SERIAL_EXECUTION', label: '单机串行' },
        { value: 'DISCARD_LATER', label: '丢弃后续调度' },
        { value: 'COVER_EARLY', label: '覆盖之前调度' }
      ],
      routeStrategies: [
        { value: 'FIRST', label: '第一个' },
        { value: 'LAST', label: '最后一个' },
        { value: 'ROUND', label: '轮询' },
        { value: 'RANDOM', label: '随机' },
        { value: 'CONSISTENT_HASH', label: '一致性HASH' },
        { value: 'LEAST_FREQUENTLY_USED', label: '最不经常使用' },
        { value: 'LEAST_RECENTLY_USED', label: '最近最久未使用' },
        { value: 'FAILOVER', label: '故障转移' },
        { value: 'BUSYOVER', label: '忙碌转移' }
        // { value: 'SHARDING_BROADCAST', label: '分片广播' }
      ],
      triggerNextTimes: '',
      registerNode: [],
      jobJson: '',
      temp: {
        id: undefined,
        jobGroup: '',
        jobCron: '',
        jobDesc: '',
        executorRouteStrategy: '',
        executorBlockStrategy: '',
        childJobId: '',
        executorFailRetryCount: '',
        alarmEmail: '',
        executorTimeout: '',
        userId: 0,
        jobConfigId: '',
        executorHandler: 'executorJobHandler',
        glueType: 'BEAN',
        jobJson: '',
        executorParam: '',
        replaceParam: '',
        jvmParam: '',
        incStartTime: '',
        writeMode: 'update',
        missionId: undefined,
        jobMulDesc: '',
        readerDatasourceId: '',
        writerDatasourceId: '',
        incrementType: 0,
        replaceParamType: 'Timestamp',
        tableTimeParam: ''
      }
    }
  },
  created() {
    // this.getJdbcDs()
    this.getAllJobMulti()
  },
  methods: {
    async next() {
      const toColumnsList = this.$refs.writer.getData().columns
      const fromColumnList = this.$refs.reader.getData().columns
      const fromTableName = this.$refs.reader.getData().tableName
      // const dataSourceId = this.$refs.reader.getData().datasourceId
      // 第一步 reader 判断是否已选字段
      if (this.active === 1) {
        //判断是哪个子页面
        if (this.$refs.reader.dataSource == 'mongodb') {
          const valid = await this.$refs.reader.$refs.mongodbreader.getFormValid().catch(() => false)
          if (valid) {
            this.active++
          } else {
            this.$message.error('请填写必填字段')
          }
        } else {
          this.active++
        }

      } else {
        // 将第一步和第二步得到的字段名字发送到第三步
        if (this.active === 2) {
          if (this.$refs.writer.dataSource == 'mongodb') {
            const valid = await this.$refs.writer.$refs.mongodbwriter.getFormValid().catch(() => false)
            if (valid) {
              this.$refs.mapper.sendColumns(fromColumnList, toColumnsList)
            } else {
              this.$message.error('请填写必填字段')
              return
            }
          } else {
            this.$refs.mapper.sendColumns(fromColumnList, toColumnsList)
          }

        }
        if (this.active === 3) {
          const valid = await this.$refs.mapper.getCheckedField()
          if (!valid) {
            return
          }
          // todo
          // 将第三步的数据发送到第四步
          const mapColumns = this.$refs.mapper.$refs.mapper.mapColumns

          const tableTimeParamOptions = mapColumns.filter(i => i.source).map(item => {
            const source = item.source.split(':')[0]

            return {
              ...item,
              source,
            }
          })
          this.$refs.increment.tableTimeParamOptions = tableTimeParamOptions
          const hasUpdateTime = tableTimeParamOptions.some(item => item.source === 'update_time')
          if (hasUpdateTime) {
            this.$refs.increment.incrementForm.tableTimeParam = 'update_time'
          }

        }
        if (this.active === 4) {
          const valid = await this.$refs.increment.getFormValid().catch(() => false)
          if (!valid) {
            this.$message.error('请填写必填字段')
            return
          }
        }
        if (this.active === 5) {
          this.temp.jobJson = this.configJson
          const data = this.$refs.writer.getData()
          const mode = data.writeMode
          if (mode !== null && mode !== '' && mode !== undefined) {
            debugger
            this.temp.writeMode = mode
          }
          this.temp.jobDesc = this.getReaderData().jobDesc
          this.temp.jobMulDesc = this.getReaderData().jobMulDesc
          this.temp.readerDatasourceId = this.$refs.reader.getData().datasourceId
          this.temp.writerDatasourceId = this.$refs.writer.getData().datasourceId
          this.temp.incrementType = this.$refs.increment.getData().incrementType
          this.temp.incStartTime = this.$refs.increment.getData().incStartTime
          this.temp.replaceParamType = this.$refs.increment.getData().replaceParamType
          this.temp.tableTimeParam = this.$refs.increment.getData().tableTimeParam
          jobSummary.createJob(this.temp).then(() => {
            this.$notify({
              title: 'Success',
              message: 'Created Successfully',
              type: 'success',
              duration: 2000
            })
            // 切回第一步
            this.active = 1
            this.$router.push({ path: '/DataDev/dataxJob/dataxJobSummary' })
          })
        } else {
          this.active++
        }
      }
    },
    last() {
      if (this.active > 1) {
        this.active--
      }
    },
    // 构建json
    buildJson() {
      const readerData = this.$refs.reader.getData()
      const writeData = this.$refs.writer.getData()
      const readerColumns = this.$refs.mapper.getLColumns()
      const writerColumns = this.$refs.mapper.getRColumns()
      const incrementData = this.$refs.increment.getData()
      const hiveReader = {
        readerPath: readerData.path,
        readerDefaultFS: readerData.defaultFS,
        readerFileType: readerData.fileType,
        readerFieldDelimiter: readerData.fieldDelimiter,
        readerSkipHeader: readerData.skipHeader
      }
      const hiveWriter = {
        writerDefaultFS: writeData.defaultFS,
        writerFileType: writeData.fileType,
        writerPath: writeData.path,
        writerFileName: writeData.fileName,
        writeMode: writeData.writeMode,
        writeFieldDelimiter: writeData.fieldDelimiter
      }
      const hbaseReader = {
        readerMode: readerData.mode,
        readerMaxVersion: readerData.maxVersion,
        readerRange: readerData.range
      }
      const hbaseWriter = {
        writerMode: writeData.mode,
        writerRowkeyColumn: writeData.rowkeyColumn,
        writerVersionColumn: writeData.versionColumn,
        writeNullMode: writeData.nullMode
      }
      const mongoDBReader = {}
      const mongoDBWriter = {
        preSql: writeData.preSql,
        preSqlType: writeData.preSqlType,
        isUpsert: writeData.isUpsert,
        upsertKey: writeData.upsertKey,
        upsertMode: writeData.upsertMode
      }
      const rdbmsReader = {
        readerSplitPk: readerData.splitPk,
        whereParams: readerData.where,
        querySql: readerData.querySql
      }
      const rdbmsWriter = {
        preSql: writeData.preSql,
        postSql: writeData.postSql
      }
      const obj = {
        readerDatasourceId: readerData.datasourceId,
        readerTables: [readerData.tableName],
        readerColumns: readerColumns,
        writerDatasourceId: writeData.datasourceId,
        writerTables: [writeData.tableName],
        writerColumns: writerColumns,
        hiveReader: hiveReader,
        hiveWriter: hiveWriter,
        rdbmsReader: rdbmsReader,
        rdbmsWriter: rdbmsWriter,
        hbaseReader: hbaseReader,
        hbaseWriter: hbaseWriter,
        mongoDBReader: mongoDBReader,
        mongoDBWriter: mongoDBWriter,
        increment: incrementData
      }
      // 调api
      dataxJsonApi.buildJobJson(obj).then(response => {
        this.configJson = JSON.parse(response.data)
      })
    },
    handleCopy(text, event) {
      clip(this.configJson, event)
      this.$message({
        message: 'copy success',
        type: 'success'
      })
    },
    handleJobTemplateSelectDrawer() {
      this.jobTemplateSelectDrawer = !this.jobTemplateSelectDrawer
      if (this.jobTemplateSelectDrawer) {
        this.fetchData()
        this.getExecutor()
      }
    },
    getReaderData() {
      return this.$refs.reader.getData()
    },
    getExecutor() {
      jobTemplate.getExecutorList().then(response => {
        const { content } = response
        this.executorList = content
      })
    },
    fetchData() {
      this.listLoading = true
      jobTemplate.getList(this.listQuery).then(response => {
        const { content } = response
        this.total = content.recordsTotal
        this.list = content.data
        this.listLoading = false
      })
    },
    handleCurrentChange(val) {
      this.temp = Object.assign({}, val)
      this.temp.id = undefined
      this.$refs.jobTemplateSelectDrawer.closeDrawer()
      this.jobTemplate = val.id + '(' + val.jobDesc + ')'
    },
    /** 获取任务列表所有数据 */
    getAllJobMulti() {
      jobSummary.getJobIdList().then(res => {
        this.jobSummaryList = res.content
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
