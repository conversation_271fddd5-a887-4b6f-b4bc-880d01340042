<template>
  <div class="app-container">
    {{ dataSource }}
    <RDBMSReader v-show="dataSource!=='hive' && dataSource!=='hbase' && dataSource!=='mongodb'" ref="rdbmsreader" @selectDataSource="showDataSource" />
    <HiveReader v-show="dataSource==='hive'" ref="hivereader" @selectDataSource="showDataSource" />
    <HBaseReader v-show="dataSource==='hbase'" ref="hbasereader" @selectDataSource="showDataSource" />
    <MongoDBReader v-show="dataSource==='mongodb'" ref="mongodbreader" @selectDataSource="showDataSource" />
  </div>
</template>

<script>
import RDBMSReader from './reader/RDBMSReader'
import HiveReader from './reader/HiveReader'
import HBaseReader from './reader/HBaseReader'
import MongoDBReader from './reader/MongoDBReader'
export default  {
  name: 'Reader',
  components: { R<PERSON><PERSON><PERSON>eader, Hive<PERSON>eader, HBaseReader, MongoDBReader },
  data() {
    return {
      dataSource: ''
    }
  },
  mounted(){
    this.dataSource = ""
    this.$refs.rdbmsreader.readerForm.datasourceId = undefined
  },
  methods: {
    getData() {
      if (this.dataSource === 'hive') {
        return this.$refs.hivereader.getData()
      } else if (this.dataSource === 'hbase') {
        return this.$refs.hbasereader.getData()
      } else if (this.dataSource === 'mongodb') {
        return this.$refs.mongodbreader.getData()
      } else {
        return this.$refs.rdbmsreader.getData()
      }
    },
    showDataSource(data) {
      console.log("Data-data",data);
      
      this.dataSource = data
      this.getData()
    },
    getReaderVerify() {
      return this.$refs.mongodbreader.getFormValid();
    }
  }
}
</script>
