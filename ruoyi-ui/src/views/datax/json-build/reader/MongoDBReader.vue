<template>
  <div class="app-container">
    <el-form label-position="left" label-width="105px" ref="readerForm" :model="readerForm" :rules="rules">
      <el-form-item label="数据源" prop="datasourceId">
        <el-select v-model="readerForm.datasourceId" filterable @change="rDsChange">
          <el-option
            v-for="item in rDsList"
            :key="item.id"
            :label="item.datasourceName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="获取方式" prop="way">
        <el-radio-group v-model="readerForm.way">
          <el-radio :label="1">从MongoDB获取文档及字段</el-radio>
          <el-radio :label="2">从表单设计器获取文档及字段</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="文档" prop="tableName">
        <el-select v-model="readerForm.tableName" filterable @change="rTbChange" :disabled="!this.readerForm.way" style="width: 42%">
          <el-option v-for="item in rTbList" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="字段">
        <el-checkbox
          v-model="readerForm.checkAll"
          :indeterminate="readerForm.isIndeterminate"
          @change="rHandleCheckAllChange"
        >全选</el-checkbox>
        <div style="margin: 15px 0;" />
        <el-checkbox-group v-model="readerForm.columns" @change="rHandleCheckedChange">
          <el-checkbox v-for="c in rColumnList" :key="c" :label="c">{{ c }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="任务分组名称：" prop="jobMulDesc">
        <el-input v-model="readerForm.jobMulDesc" placeholder="请填写任务分组名称" style="width: 42%" />
      </el-form-item>
      <el-form-item label="任务明细名称：" prop="jobDesc">
        <el-input v-model="readerForm.jobDesc" placeholder="请填写任务明细名称" style="width: 42%" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import * as dsQueryApi from '@/api/dts/metadata-query.js'
import { getAllTableCodeFromFormDesign as getTableCodeList } from '@/api/codeDev/formDesign/formDesign'
import { list as jdbcDsList } from '@/api/datax/datax-jdbcDatasource'
import Bus from '../busReader'

export default {
  name: 'MongoDBReader',
  data() {
    return {
      radio: '',
      jdbcDsQuery: {
        current: 1,
        size: 200
      },
      rDsList: [],
      rTbList: [],
      rColumnList: [],
      loading: false,
      active: 1,
      customFields: '',
      customType: '',
      customValue: '',
      dataSource: '',
      readerForm: {
        datasourceId: undefined,
        tableName: '',
        columns: [],
        checkAll: false,
        isIndeterminate: true,
        jobMulDesc: '',
        jobDesc: ''
      },
      rules: {
        mode: [{ required: true, message: 'this is required', trigger: 'change' }],
        datasourceId: [{ required: true, message: 'this is required', trigger: 'change' }],
        tableName: [{ required: true, message: 'this is required', trigger: 'change' }],
        way: [{ required: true, message: 'this is required', trigger: 'change' }],
        jobMulDesc: [{ required: true, message: 'this is required', trigger: 'blur' }],
        jobDesc: [{ required: true, message: 'this is required', trigger: 'blur' }]
      }
    }
  },
  watch: {
    // 'readerForm.datasourceId': function(oldVal, newVal) {
    //   this.getTables('mongodbReader')
    // },
    // 获取方式发生变化时获取表  数据源--从数据源获取   表单设计--从表单设计获取
    'readerForm.way'(way) {
      // 清空已选文档
      this.readerForm.tableName = ''
      // 清空已选文档对应的字段
      this.rColumnList = []
      this.getTables('mongodbReader', way);
    }
  },
  created() {
    this.getJdbcDs()
  },
  methods: {
    // 获取可用数据源
    getJdbcDs() {
      this.loading = true
      jdbcDsList(this.jdbcDsQuery).then(response => {
        const { records } = response.data
        this.rDsList = records
        this.loading = false
      })
    },
    // 获取表名
    getTables(type, way) {
      if (type === 'mongodbReader' && way == 1) {
        const obj = {
          datasourceId: this.readerForm.datasourceId
        }
        // 组装
        dsQueryApi.getTables(obj).then(response => {
         this.rTbList = response.data
        })
      } else if (type === 'mongodbReader' && way == 2) {
        // 从表单设计获取表
        getTableCodeList().then((result) => {
          this.rTbList = result.data
        })
      }
    },
    // reader 数据源切换
    rDsChange(e) {
      // 清空
      this.readerForm.tableName = ''
      this.readerForm.datasourceId = e
      this.rDsList.find((item) => {
        if (item.id === e) {
          this.dataSource = item.datasource
        }
      })
      Bus.dataSourceId = e
      this.$emit('selectDataSource', this.dataSource)
      // 获取可用表
      this.getTables('reader')
    },
    getTableColumns() {
      const obj = {
        datasourceId: this.readerForm.datasourceId,
        tableName: this.readerForm.tableName
      }
      dsQueryApi.getColumns(obj).then(response => {
        this.rColumnList = response.data
        this.readerForm.columns = response.data
        this.readerForm.checkAll = true
        this.readerForm.isIndeterminate = false
      })
    },
    getTableColumnsFromFormDesign() {
      const obj = {
        datasourceId: this.readerForm.datasourceId,
        tableName: this.readerForm.tableName
      }
      dsQueryApi.getColumnsFromFormDesign(obj).then(response => {
        this.rColumnList = response.data
        this.readerForm.columns = response.data
        this.readerForm.checkAll = true
        this.readerForm.isIndeterminate = false
      })
    },
    getColumnsByQuerySql() {
      const obj = {
        datasourceId: this.readerForm.datasourceId,
        querySql: this.readerForm.querySql
      }
      dsQueryApi.getColumnsByQuerySql(obj).then(response => {
        this.rColumnList = response.data
        this.readerForm.columns = response.data
        this.readerForm.checkAll = true
        this.readerForm.isIndeterminate = false
      })
    },
    // 获取表字段
    getColumns(type) {
      if (type === 'reader') {
        console.log('this.readerForm.way' + this.readerForm.way)
        if (this.readerForm.way == '2') {
          //从表单设计器获取
          this.getTableColumnsFromFormDesign()
        } else {
          this.getTableColumns()
        }
      }
    },
    // 表切换
    rTbChange(t) {
      this.readerForm.tableName = t
      this.rColumnList = []
      this.readerForm.columns = []
      // 调用mongo查询collection是否存在的接口
      if (this.readerForm.way == '2') {
        this.collectionExists()
      }
      this.getColumns('reader')
    },
    rHandleCheckAllChange(val) {
      this.readerForm.columns = val ? this.rColumnList : []
      this.readerForm.isIndeterminate = false
    },
    rHandleCheckedChange(value) {
      const checkedCount = value.length
      this.readerForm.checkAll = checkedCount === this.rColumnList.length
      this.readerForm.isIndeterminate = checkedCount > 0 && checkedCount < this.rColumnList.length
    },
    getData() {
      if (Bus.dataSourceId) {
        this.readerForm.datasourceId = Bus.dataSourceId
      }
      return this.readerForm
    },
    getFormValid() {
     return this.$refs['readerForm'].validate()
    },
    collectionExists() {
      const param = this.readerForm.tableName
      dsQueryApi.findCollection(param).then(result => {
        if ((result.msg !== '')) {
          this.$notify({
            title: 'Fail',
            message: result.msg,
            type: 'error',
            duration: 4000
          })
        }
      })
    }
  }
}
</script>
