<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button class="filter-item" style="margin-left: 10px" type="primary" icon="el-icon-edit" @click="fetchData">
        搜索
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px" type="primary" icon="el-icon-edit" @click="handleCreate">
        添加
      </el-button>
    </div>
    <el-table :height="tableHeight" ref="fullHeightTableRef" v-loading="listLoading" :data="list"
      element-loading-text="Loading" border fit highlight-current-row>
      <el-table-column label="DataX服务器名称" width="160" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope">{{ scope.row.serverName }}</template>
      </el-table-column>
      <el-table-column label="服务器IP" width="110" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope">{{ scope.row.serverIp }}</template>
      </el-table-column>
      <el-table-column label="端口号" width="80" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope">{{ scope.row.serverPort }}</template>
      </el-table-column>
      <el-table-column label="DataXBinHome" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope">{{ scope.row.dataxBinHome }}</template>
      </el-table-column>
      <el-table-column label="DataXJobPath" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope">{{ scope.row.dataxJobPath }}</template>
      </el-table-column>
      <el-table-column label="DataXLogHome" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope">{{ scope.row.dataxLogHome }}</template>
      </el-table-column>
      <el-table-column label="是否启用" align="center" width="150">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.enable" active-color="#00A854" active-text="启用" :active-value="0"
            inactive-color="#F04134" inactive-text="停用" :inactive-value="1" @change="changeSwitch(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="230" class-name="small-padding fixed-width">
        <template slot-scope="{ row }">
          <el-button type="primary" size="mini" style="width: 60px!important;" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button v-if="row.status != 'deleted'" size="mini" type="danger" @click="confirmDelete(row)"
            style="width: 60px!important;">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="listQuery.current" :limit.sync="listQuery.size"
      @pagination="fetchData" />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="200px"
        style=" margin-left: 50px">
        <el-form-item label="DataX服务器名称" prop="serverName">
          <el-input v-model="temp.serverName" placeholder="请输入DataX服务器名称" />
        </el-form-item>
        <el-form-item label="服务器IP" prop="serverIp">
          <el-input v-model="temp.serverIp" placeholder="请输入DataX所在服务器IP" />
        </el-form-item>
        <el-form-item label="端口号" prop="serverPort">
          <el-input v-model="temp.serverPort" placeholder="请输入服务器SSH端口号" />
        </el-form-item>
        <el-form-item label="账号" prop="serverAccount">
          <el-input v-model="temp.serverAccount" placeholder="请输入服务器账号" />
        </el-form-item>
        <el-form-item label="密码" prop="serverPwd">
          <el-input v-model="temp.serverPwd" type="password" placeholder="请输入服务器密码" />
        </el-form-item>
        <el-form-item label="DataXBinHome" prop="dataxBinHome">
          <el-input v-model="temp.dataxBinHome" placeholder="请输入DataXBinHome" />
        </el-form-item>
        <el-form-item label="DataXJobPath" prop="dataxJobPath">
          <el-input v-model="temp.dataxJobPath" placeholder="请输入DataXJobPath" />
        </el-form-item>
        <el-form-item label="DataXLogHome" prop="dataxLogHome">
          <el-input v-model="temp.dataxLogHome" placeholder="请输入DataXLogHome" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false"> 取消 </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import * as dataXMachine from '@/api/datax/datax-machine'
import waves from '@/directive/waves' // waves directive
import Pagination from '@/components/Pagination'

export default {
  mixins: [tableFullHeight],
  name: 'Executor',
  components: { Pagination },
  directives: { waves },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'gray',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      deleteDialogVisible: false,
      list: null,
      listLoading: true,
      total: 0,
      listQuery: {
        current: 1,
        size: 10,
        name: undefined,
        jobGroup: undefined
      },
      editJsonVisible: false,
      pluginData: [],
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: 'Edit',
        create: 'Create'
      },
      rules: {
        serverName: [
          { required: true, message: '请填写DataX服务器名称', trigger: 'blur' }
        ],
        serverIp: [
          { required: true, message: '请填写服务器IP', trigger: 'blur' }
        ],
        serverPort: [
          { required: true, message: '请填写端口号', trigger: 'blur' }
        ],
        serverAccount: [
          { required: true, message: '请填写用户名', trigger: 'blur' }
        ],
        serverPwd: [
          { required: true, message: '请填写密码', trigger: 'blur' }
        ],
        dataxBinHome: [
          { required: true, message: '请填写DataXBinHome', trigger: 'blur' }
        ],
        dataxJobPath: [
          { required: true, message: '请填写DataXJobPath', trigger: 'blur' }
        ],
        dataxLogHome: [
          { required: true, message: '请填写DataXLogHome', trigger: 'blur' }
        ]
      },
      temp: {
        serverName: '',
        serverIp: '',
        serverPort: '',
        serverAccount: '',
        serverPwd: '',
        dataxBinHome: '',
        dataxJobPath: '',
        dataxLogHome: ''
      },
      addressTypes: [
        { value: 0, label: '自动注册' },
        { value: 1, label: '手动录入' }
      ]
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      this.listLoading = true
      // 获取dataX配置的接口
      dataXMachine.getList().then((res) => {
        const { content } = res
        this.list = content
        this.listLoading = false
      })
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        appName: undefined,
        title: undefined,
        order: undefined,
        addressType: undefined,
        addressList: undefined
      }
    },
    // 删除按钮提示模态框
    confirmDelete(row) {
      this.$confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 用户点击确定按钮时执行删除操作
          this.handleDelete(row)
        })
        .catch(() => {
          // 用户点击取消按钮时的操作，可以不做任何处理或关闭模态框
        })
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          dataXMachine.created(this.temp).then((res) => {
            if (res.code === 200) {
              this.fetchData()
              this.dialogFormVisible = false
              this.$notify({
                title: 'Success',
                message: 'Created Successfully',
                type: 'success',
                duration: 2000
              })
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row) // copy obj
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          tempData.configJson = this.configJson
          dataXMachine.updated(tempData).then(() => {
            this.fetchData()
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handleDelete(row) {
      const idList = []
      idList.push(row.id)
      dataXMachine.deleted({ idList: row.id }).then((response) => {
        if (response.code === 500) {
          this.$notify({
            title: 'error',
            message: response.msg,
            type: 'error',
            duration: 2000
          })
        } else {
          this.$notify({
            title: 'Success',
            message: 'Delete Successfully',
            type: 'success',
            duration: 2000
          })
        }
        this.fetchData()
      })
    },
    changeSwitch(row) {
      this.updateStatus(row)
    },
    updateStatus(row) {
      const param = { id: row.id, enable: row.enable }
      dataXMachine.updateStatus(param).then(response => {
        this.$notify({
          title: 'Success',
          message: 'UpdateStatus Successfully',
          type: 'success',
          duration: 2000
        })
      })
    }
  }
}
</script>
