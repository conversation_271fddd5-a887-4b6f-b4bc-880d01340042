<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="ruleForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item label="流程名称" prop="flowName">
        <el-select v-model="queryParams.flowName" filterable clearable>
          <el-option
            v-for="item in formDataList"
            :key="item.id"
            :label="item.flowName"
            :value="item.flowName"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="流程编码" prop="flowCode">
        <el-select v-model="queryParams.flowCode" filterable clearable>
          <el-option
            v-for="item in formDataList"
            :key="item.id"
            :label="item.flowCode"
            :value="item.flowCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table ref="fullHeightTableRef" :height="tableHeight" v-loading="loading" :data="formDataList"
              @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="规则ID" align="center" width="180px" prop="flowId">
        <template slot-scope="scope">
          <span>{{ scope.row.flowId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="规则名称" align="center" width="120px" prop="flowName">
        <template slot-scope="scope">
          <span>{{ scope.row.flowName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="规则编码" align="center" prop="flowCode">
        <template slot-scope="scope">
          <span>{{ scope.row.flowCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="流程URL" align="center" prop="flowUrl">
        <template slot-scope="scope">
          <span>{{ scope.row.flowUrl }}</span>
        </template>
      </el-table-column>
      <!--      <el-table-column label="规则状态" align="center" width="150">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-switch-->
      <!--            v-model="scope.row.flowStatus"-->
      <!--            active-color="#00A854"-->
      <!--            active-text="启用"-->
      <!--            :active-value='`true`'-->
      <!--            inactive-color="#F04134"-->
      <!--            inactive-text="停用"-->
      <!--            :inactive-value='`false`'-->
      <!--            @change="changeSwitch(scope.row)"-->
      <!--          />-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column label="备注" align="center" width="180px" prop="remark">
        <template slot-scope="scope">
          <span>{{ scope.row.remark }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="300px" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="arrangeRule(scope.row)"
          >规则编排
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-position="left" label-width="110px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="规则ID" prop="flowId">
              <el-input v-model="form.flowId" disabled/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="规则名称" prop="flowName">
              <el-input v-model="form.flowName" placeholder="请输入规则名称" :disabled="!!form.flowId"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规则编码" prop="flowCode">
              <el-input v-model="form.flowCode" placeholder="请输入规则编码"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="流程URL" prop="flowUrl">
              <el-select
                v-model="form.flowUrl"
                style="width: 100%"
                filterable
                clearable
                :disabled="!!form.flowId"
              >
                <el-option
                  v-for="dict in dict.type.rule_flow_url"
                  :key="dict.value"
                  :label="dict.value"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" :autosize="{ minRows: 5, maxRows: 15 }" style="min-height: 120px;"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import tableFullHeight from '@/utils/tableFullHeight'
import * as rule from '@/api/rule/rule-engine'
import { getToken } from '@/utils/auth'

export default {
  mixins: [tableFullHeight],
  name: 'RuleList',
  dicts: ['rule_flow_url'],
  data() {
    return {
      configId: '',//配置id
      openRuleDialog: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数据监控配置表格数据
      formDataList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 判断新增或更新
      action: '',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenantId: null,
        revision: null,
        flowId: null,
        flowName: null,
        flowCode: null,
        flowUrl: null,
        flowStatus: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        flowName: [{ required: true, message: 'this is required', trigger: 'blur' }],
        flowCode: [{ required: true, message: 'this is required', trigger: 'blur' }],
        flowUrl: [{ required: true, message: 'this is required', trigger: 'change' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    changeSwitch(row) {
      rule.changeFlowStatus(row.id, row.flowStatus).then(response => {
        if (response.code === 200) {
          this.$modal.msgSuccess('规则启用成功')
        } else {
          this.$modal.msgError('规则启用失败')
        }
      })
    },

    /** 查询数据监控配置列表 */
    getList() {
      this.loading = true
      rule.getList(this.queryParams).then(response => {
        this.formDataList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = this.$options.data.call(this).form
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryFormRef')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '新增规则'
      this.action = 'add'
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.form = Object.assign({}, row)
      this.open = true
      this.title = '更新规则'
      this.action = 'update'
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          if (this.form.id != null) {
            rule.updateFlow(this.form.id, this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            rule.addFlow(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id ? [row.id] : this.ids
      this.$modal.confirm('是否确认删除数据项？').then(() => {
        return rule.deleteFlows(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },
    /** 规则编排按钮操作 */
    arrangeRule(row) {
      let url = row.flowUrl+'?bihua_token=' + getToken() + '#flow/' + row.flowId
      window.open(url, '_blank')
    }
  }
}
</script>
