<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container"
      @toggleClick="toggleSideBar" />

    <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!topNav" />
    <top-nav id="topmenu-container" class="topmenu-container" v-if="topNav" />
    <div class="topmenu-container" v-else></div>
    <div class="right-menu">
      <template v-if="device !== 'mobile'">
        <search id="header-search" class="right-menu-item" />
        <el-tooltip :content="noticeContent" effect="dark" placement="bottom">
          <el-badge :value="noticeCount" class="right-menu-item hover-effect">
            <i class="el-icon-message-solid" @click="toNoticePage"></i>
          </el-badge>
        </el-tooltip>
        <screenfull id="screenfull" class="right-menu-item hover-effect" />
        <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>
      </template>
      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">
          <h5 class="user-name" style="color:#025D7E;">{{ userName }}</h5>
          <img :src="avatar" class="user-avatar">
        </div>
        <el-dropdown-menu slot="dropdown">
          <router-link to="/user/profile">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link>
          <el-dropdown-item @click.native="setting = true">
            <span>布局设置</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import TopNav from '@/components/TopNav'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import Search from '@/components/HeaderSearch'
import { listMessage } from '@/api/system/notice'
import toastr from 'toastr'
import 'toastr/build/toastr.min.css'
import RuoYiGit from '@/components/RuoYi/Git'
import RuoYiDoc from '@/components/RuoYi/Doc'
import { getUserProfile } from "@/api/system/user";
import router from "@/router";
import OAuth2Login from '@/utils/oauth2-login'
export default {
  components: {
    Breadcrumb,
    TopNav,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search,
    RuoYiGit,
    RuoYiDoc
  },
  mixins: [OAuth2Login],
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'device'
    ]),
    setting: {
      get() {
        return this.$store.state.settings.showSettings
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: val
        })
      }
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav
      }
    }
  },
  data() {
    return {
      userName: '',
      noticeContent: '', // 通知内容
      noticeCount: 0, // 通知数量
      lastNoticeCount: 0, // 上一次通知数量
      intervalId: null
    }
  },
  mounted() {
    // 启动轮询
    this.startPolling();
  },
  created() {
    // 假设从后端获取用户名
    this.fetchUserName();
    this.poll();
  },
  beforeDestroy() {
    // 在组件销毁之前清除定时器，防止内存泄漏
    this.stopPolling();
  },
  methods: {
    async fetchUserName() {
      try {
        const response = await getUserProfile();
        this.userName = response.data.nickName;
      } catch (error) {
        console.error('获取用户名失败:', error);
      }
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
      const sidebarContainer = document.getElementsByClassName("sidebar-container")[0];
      let history_width = localStorage.getItem("sliderWidth")
      if (!history_width || parseInt(history_width) <= parseInt("54px")) {
        history_width = "310px";
      }
      if (this.$store.state.app.sidebar.opened) {
        // 展开
        sidebarContainer.style.width = history_width
      } else {
        sidebarContainer.style.width = "54px"
      }
    },
    async logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.handleLogout()
      }).catch(() => {
      });
    },
    toNoticePage() {
      // 前往通知公告管理页面
      this.$router.push("/message/list/");
    },
    startPolling() {
      // 每隔一定时间执行轮询任务
      this.intervalId = setInterval(() => {
        this.poll();
      }, 30000); // 5秒钟轮询一次
    },
    stopPolling() {
      // 清除定时器，停止轮询任务
      clearInterval(this.intervalId);
    },
    poll() {
      // 执行轮询任务
      listMessage().then(response => {
        this.noticeCount = response.total || 0; // 获取信息条数

        // 如果通知数量有变化，则提示用户
        if (this.noticeCount > this.lastNoticeCount) {
          toastr.success(`您有 ${this.noticeCount} 条未读消息`); // 显示 Toast 提示
        }

        this.lastNoticeCount = this.noticeCount; // 更新上一次的通知数量

        this.noticeContent = String(this.noticeCount); // 定制内容
      });
    }
  }
}


</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, .08);
  display: flex;

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    // float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }


  .topmenu-container {
    flex: 1;

    // position: absolute;
    // left: 50px;
    ::v-deep .el-submenu {
      position: absolute;
      right: 0;
    }
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    // float: right;
    position: relative;
    height: 100%;
    line-height: 50px;



    &:focus {
      outline: none;
    }

    // el-badge 的通知数量图标下移 左移
    ::v-deep .el-badge .el-badge__content {
      top: 10px !important;
      right: 15px !important;
    }


    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {
      margin-right: 10px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;
        display: flex;
        align-items: center;

        .user-name {
          margin: 0;
          margin-right: 10px;
        }

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
