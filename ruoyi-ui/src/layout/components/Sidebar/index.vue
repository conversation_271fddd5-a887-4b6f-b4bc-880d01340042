<template>
    <div :class="{ 'has-logo': showLogo }" class="sidebar-container"
        :style="{ backgroundColor: settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }">
        <logo v-if="showLogo" :collapse="isCollapse" />
        <div class="sidebar-menu-container" id="leftmenu">
            <!-- v-resize="MuneResize" -->
            <el-scrollbar :class="settings.sideTheme" wrap-class="scrollbar-wrapper">
                <el-menu :default-active="activeMenu" :collapse="isCollapse"
                    :background-color="settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground"
                    :text-color="settings.sideTheme === 'theme-dark' ? variables.menuColor : variables.menuLightColor"
                    :unique-opened="true" :active-text-color="settings.theme" :collapse-transition="false"
                    mode="vertical">
                    <sidebar-item v-for="(route, index) in sidebarRouters" :key="route.path + index" :item="route"
                        :base-path="route.path" />
                </el-menu>
            </el-scrollbar>
            <!-- 给个可以拖拽的标识 -->
            <div id="drap-meuline"></div>
            <!-- <div class="resize-handle" id="drap-meuline" @mousedown="startDrag"></div> -->
        </div>
    </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import Logo from "./Logo";
import SidebarItem from "./SidebarItem.vue";
import variables from "@/assets/styles/variables.scss";


export default {
    components: { SidebarItem, Logo },
    directives: {
        resize: {
            // 指令的名称
            bind(el, binding) {
                // el为绑定的元素，binding为绑定给指令的对象
                let _width = "";
                function isReize() {
                    const style = document.defaultView.getComputedStyle(el);
                    if (_width !== style.width) {
                        binding.value({ width: style.width, targetId: el.id });
                        _width = style.width;
                    }
                }
                el.__vueSetInterval__ = setInterval(isReize, 300);
            },
            unbind(el) {
                clearInterval(el.__vueSetInterval__);
            },
        },
    },
    mounted() {
        // 获取dom，对左菜单进行拖拽
        const drapLine = document.getElementById("drap-meuline");
        // 获取右侧内容Dom
        const mainContainer = document.getElementsByClassName("main-container")[0];
        // 获取左侧菜单Dom
        const menuleft = document.getElementById("leftmenu");
        // 获取左侧菜单Dom父元素，为了动态设置宽度
        const sidebarContainer = document.getElementsByClassName("sidebar-container")[0];

        const history_width = localStorage.getItem("sliderWidth") || "310px";
        if (this.$store.state.app.sidebar.opened) {
            sidebarContainer.style.width = history_width;
        } else {
            sidebarContainer.style.width = '54px';
        }


        drapLine.onmousedown = (e) => {
            e.preventDefault(); // 阻止默认事件
            // 设置最大/最小宽度
            const max_width = 600;
            const min_width = 54;
            let mouse_x = 0; // 记录鼠标相对left盒子x轴位置
            const _e = e || window.event;
            mouse_x = _e.clientX - menuleft.offsetWidth;
            document.onmousemove = (e_) => {
                const _e_ = e_ || window.event;
                let left_width = _e_.clientX - mouse_x;
                left_width = left_width < min_width ? min_width : left_width;
                left_width = left_width > max_width ? max_width : left_width;
                if (this.$store.state.app.sidebar.opened) {
                    sidebarContainer.style.width = left_width + "px";
                }
            };
            document.onmouseup = () => {
                document.onmousemove = null;
                document.onmouseup = null;
                // 本地保存
                localStorage.setItem("sliderWidth", sidebarContainer.style.width);
                if (parseInt(sidebarContainer.style.width) <= parseInt("54px")) {
                    this.$store.dispatch('app/closeSideBar', { withoutAnimation: true })
                }
            };
        };

    },

    //拖拽宽度的改变
    methods: {
        // 动态获取左侧菜单的宽度
        MuneResize(data) {
            // 拿到左侧菜单父元素
            const leftDom = document.getElementById(`${data.targetId}`);
            // 拿到右侧内容父元素
            const mainContainer = document.getElementsByClassName("main-container")[0];
            this.$nextTick(() => {
                mainContainer.style.marginLeft = leftDom.clientWidth + "px";
            })
        },
    },
    computed: {
        ...mapState(["settings"]),
        ...mapGetters(["sidebarRouters", "sidebar"]),
        activeMenu() {
            const route = this.$route;
            const { meta, path } = route;
            // if set path, the sidebar will highlight the path you set
            if (meta.activeMenu) {
                return meta.activeMenu;
            }
            return path;
        },
        showLogo() {
            return this.$store.state.settings.sidebarLogo;
        },
        variables() {
            return variables;
        },
        isCollapse() {
            return !this.sidebar.opened;
        }
    }
};
</script>
<style lang="scss" scoped>
.sidebar-container {
    width: 250px;
}

.sidebar-menu-container {
    height: 100%;
    overflow-y: auto;
    display: flex;

    .el-scrollbar {
        flex: 1;
    }

    #drap-meuline {
        background: transparent;
        width: 4px;
        cursor: e-resize; //设置鼠标悬浮上去显示可拖拽样式
    }
}
</style>
