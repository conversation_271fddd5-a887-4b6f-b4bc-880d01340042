import JSEncrypt from 'jsencrypt/bin/jsencrypt.min';

const publicKey = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtyqw3SdbPAo+4rewgkFy\n' +
	'bq9xJV/HeouGKTxGLBt45HYe1KajsVG9bn//K5cEDA/SPY0d42xKqG8A8dxV+56n\n' +
	'sFmhz+BnzzLzzQ6zF7tcS3gAlcxuJDM3u3EylknJr4JQ316hohwUNuK8pW8ntM20\n' +
	'SQTPOY0bLwjmJf/UV6xBJk3mXLS5503b0xmK/tua/75r9qMaq76f6Qfk0UMQGEgd\n' +
	'jDIkmAuNyaKJBTJCIjhf9NZYx19YiRrt/80hsJ4EIKGet+W8auhioNiqSpPHPr/j\n' +
	'ZMCaYuugKsJQ93lYfPPy8GmlDvoklg4TRci2jU1UpR4IZGZ7Vnll7pdaetkYdkwS\n' +
	'QwIDAQAB'

const privateKey = "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQC3KrDdJ1s8Cj7i\n" +
	"t7CCQXJur3ElX8d6i4YpPEYsG3jkdh7UpqOxUb1uf/8rlwQMD9I9jR3jbEqobwDx\n" +
	"3FX7nqewWaHP4GfPMvPNDrMXu1xLeACVzG4kMze7cTKWScmvglDfXqGiHBQ24ryl\n" +
	"bye0zbRJBM85jRsvCOYl/9RXrEEmTeZctLnnTdvTGYr+25r/vmv2oxqrvp/pB+TR\n" +
	"QxAYSB2MMiSYC43JookFMkIiOF/01ljHX1iJGu3/zSGwngQgoZ635bxq6GKg2KpK\n" +
	"k8c+v+NkwJpi66AqwlD3eVh88/LwaaUO+iSWDhNFyLaNTVSlHghkZntWeWXul1p6\n" +
	"2Rh2TBJDAgMBAAECggEAfFF0Y1Xv/WsyzWxBV9cV1PwsvmIooQsLzvcC5bLrtZxa\n" +
	"Rpinp5VEDji/txUzgQBIANDDxXTdoz5cFMwoa7R+OhpL0iyMKLx7buUYNvZKqUpU\n" +
	"k1eCNf2xSP6GiCyiwAWh4El5SO7n5Wy0/0/XUkb/o0OpZnVYSu8uTgI6XBskyNqU\n" +
	"ekeKZBUootAuo3+XHRW5IK9mUfeT59lqFHhQejBSVr4EtnBzjZVwvr5W8ENA0K/k\n" +
	"HRf4XoE1FHuLau/+aUwDEopn/Ar/xksSFJ9fi/a7o50i7/sXRkXUTAb+RIbWUhXq\n" +
	"3n+e6hnUaFE84g4fhOXyZTRWOa7K6OjYczpypzuPuQKBgQD0MuH+ZWpd4ThMkDVI\n" +
	"pLUO4RvwmYm0ZtLSfWe0mjjuvuvyK5FrkM1k4G5p2WXaYclBXgQ3NPDNRME5+maT\n" +
	"S2KtefMVF+xCHoLPoMdhJ1F8wOtlHrwZTg6pWdO3tVUkKMF/8edQ5zy3XnZgBLBx\n" +
	"9phtNjFAfKAZ2SDC8l4OSCbmVQKBgQDABL9liwrHbxqfDDdPg65jjnFus1ffVuuM\n" +
	"aLBL+qUMAspV09c5o9Wj1ySsXNCmnxq3HspOyXEq4QQi1q92/sX/wmQcIt712XKu\n" +
	"HQ/yh5OmvAAgr1HceyqrndXSKaeqfNZEl6zXWEb/3VIudUIBqv87A8938uJV1zoq\n" +
	"Uh3xeYg+NwKBgQDkY4p2EYfP0cqD4QPUsycbiXJGLnNaJCjc8KjDgtHoiOvHjPPu\n" +
	"9HkE9dqOaOMXwgkmd7XdpEfq03kcrpKtA/6AXOP6QGuY00xov+h5dvqBgTUnV/14\n" +
	"dUbFINwgnxnIPc3ryeUcB4kDvvdbikcz7vxUeDDMLULXqfk4GtHY7aZkGQKBgQCZ\n" +
	"ITAhooiuEwR/4hYZ60GfU3FSfuOM0Jx4FOTDMtd8hJ0nZRE2fllZl7E/Y6Vhu3Bq\n" +
	"qLtdqXf+YgQSZHL5yqPMGxIK/iQgm5DBGZwmJciyJytS5X26/t5AjlZ+i4mShIu7\n" +
	"l9nWwiec1Ls9IXXKLrvsLFW/qoqlOkbAiUwnzmvAcQKBgQDNi5aLCQcbRPR9r/hT\n" +
	"dtKuaf1TrLPiu+6VUmRl36d58IVIWsqnIEIbYNDIdm2KK6N9p1BwAUv5kmy6ydxy\n" +
	"B2JtsV/qxfF3qwcB/PACZkN1nrFSLo7c1fySbsQ8MGomwC7qmSadXjUfvUtT7M3p\n" +
	"UteFU3hagpQqeKWyMqgqqAFghw==";


// RSA加密
function encrypt(txt) {
	const encryptor = new JSEncrypt()
	encryptor.setPublicKey(publicKey) // 设置公钥
	return encryptor.encrypt(txt) // 对数据进行加密
}
// RSA解密
function decrypt(txt) {
	const encryptor = new JSEncrypt()
	encryptor.setPrivateKey(privateKey) // 设置私钥
	return encryptor.decrypt(txt) // 对数据进行解密
}
// 创建sign信息
function createSign(username) {
	const userInfo = {
		username: username,
		timestamp: new Date().getTime()
	}
	const json = JSON.stringify(userInfo);
	const sign = encodeURIComponent(encrypt(json));
	return sign;
}
// 获取用户信息
function getUserInfo(sign) {
	let userInfo = null;
	if (sign) {
		try {
			sign = decodeURIComponent(sign);
			sign = decrypt(sign);
			if (sign) {
				sign = JSON.parse(sign);
				if (sign.username && sign.timestamp) {
					if (new Date().getTime() - sign.timestamp <= 60000) {
						return sign;
					}
				}
			}
		} catch (e) {

		}

	}
	return userInfo;
}


export {encrypt,decrypt,getUserInfo,createSign};

