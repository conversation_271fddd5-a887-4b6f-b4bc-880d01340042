export default {
    data() {
        return {
            tableHeight: 0,
            observers: [],
        }
    },
    mounted() {
        if (!this.$route.path.includes('/codeDev/formData')) {
            setTimeout(() => {
                this.initAutoHeight();
            }, 1000);
        }
    },
    activated() {
        setTimeout(() => {
            this.initAutoHeight();
        }, 1000);
    },
    deactivated() {
        this.cleanupObservers();
    },
    beforeDestroy() {
        this.cleanupObservers();
    },
    methods: {
        initAutoHeight() {
            // this.$nextTick(() => {
                this.calculateHeight();
                if (this.$refs.queryFormRef) {
                    const observer = new ResizeObserver(() => {
                        this.calculateHeight();
                    });
                    [this.$refs.queryFormRef].forEach(item => {
                        if (item.$el) {
                            observer.observe(item.$el);
                            this.observers.push({ observer, el: item.$el });
                        }
                    });
                }
                window.addEventListener('resize', this.calculateHeight);
            // });
        },

        calculateHeight() {
            const fullHeightTable = this.$refs.fullHeightTableRef?.$el;

            if (!fullHeightTable) return;

            // 分页高度
            const paginationHeight = 32;

            const tableTop = fullHeightTable.getBoundingClientRect().top - this.$root.$el.getBoundingClientRect().top;

            const containerHeight = this.$root.$el.offsetHeight;

            const height = containerHeight - tableTop - paginationHeight;

            // 设置最小高度
            this.tableHeight = Math.max(300, height);
        },

        cleanupObservers() {
            this.observers.forEach(({ observer, el }) => {
                observer.unobserve(el);
                observer.disconnect();
            });
            window.removeEventListener('resize', this.calculateHeight);
        },
        /**
         * plan b start
         * @returns {void}
         */
        startPositionWatcher() {
            const fullHeightTable = this.$refs.fullHeightTableRef?.$el;

            if (!fullHeightTable) return;

            this.lastTop = fullHeightTable.getBoundingClientRect().top;

            this.positionTimer = setInterval(() => {
                const currentTop = fullHeightTable.getBoundingClientRect().top;
                if (currentTop !== this.lastTop) {
                    this.lastTop = currentTop;
                    this.calculateHeight();
                }
            }, 100);
        },

        stopPositionWatcher() {
            if (this.positionTimer) {
                clearInterval(this.positionTimer);
                this.positionTimer = null;
            }
        }
        /**
         * plan b end
         */
    }
}