import CryptoJS from "crypto-js";

// 密钥与 IV，必须是 16 字节
const secretKey = "mengniusecretKey"; // 16 个字符 = 16 字节
const iv = "mengniusecretKey";        // 16 字节

/**
 * 加密 SQL 为 AES-CBC + Base64 字符串
 * @param {string} sql - 原始 SQL 语句
 * @returns {string} - Base64 编码的加密密文
 */
export function encodeSqlToEncryptedBase64(sql) {
  const key = CryptoJS.enc.Utf8.parse(secretKey);
  const ivBytes = CryptoJS.enc.Utf8.parse(iv);

  const encrypted = CryptoJS.AES.encrypt(sql, key, {
    iv: ivBytes,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });

  return encrypted.toString(); // Base64 字符串
}

/**
 * 解密 Base64 + AES-CBC 字符串为原始 SQL
 * @param {string} base64Str - 加密后的 Base64 字符串
 * @returns {string} - 解密后的原始 SQL
 */
export function decodeEncryptedBase64ToSql(base64Str) {
  const key = CryptoJS.enc.Utf8.parse(secretKey);
  const ivBytes = CryptoJS.enc.Utf8.parse(iv);

  const decrypted = CryptoJS.AES.decrypt(base64Str, key, {
    iv: ivBytes,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });

  return decrypted.toString(CryptoJS.enc.Utf8);
}
