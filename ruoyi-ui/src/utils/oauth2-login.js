import { getLoginConfig, getAccessToken } from '@/api/system/security/login-config/login-config'
import { setToken, setExpiresIn } from '@/utils/auth'

/**
 * 获取登录配置
 */
export default {
    name: 'oauth2-login',
    data() {
        return {
            code: this.$route.query.code
        }
    },
    methods: {
        // 创建点击事件
        handleSimulationClickEvent(url) {
            // 创建一个隐藏的a标签
            const link = document.createElement('a');
            link.style.display = 'none';
            link.href = url;
            // link.target = '_blank';
            link.rel = 'noopener noreferrer';

            // 添加到body中
            document.body.appendChild(link);

            // 创建并触发点击事件
            const event = document.createEvent('MouseEvents');
            event.initEvent('click', true, true);
            link.dispatchEvent(event);

            // 延迟移除a标签
            setTimeout(() => {
                document.body.removeChild(link);
            }, 100);
        },
        // 解析路由地址
        async handleParseUrl() {
            const { query } = this.$route
            const { code } = query
            const result = await getAccessToken(code)
            if (result.code === 200) {
                const { data } = result
                const { access_token, expires_in } = data
                setToken(access_token)
                this.$store.commit('SET_TOKEN', access_token)
                setExpiresIn(expires_in)
                this.$store.commit('SET_EXPIRES_IN', expires_in)
                const isMobile = /Mobi|Android|iPhone/i.test(navigator.userAgent)
                if (isMobile) {
                    this.$router.replace({ path: "/app/menu" })
                } else {
                    const redirect = this.$route.query.redirect
                    this.$router.push({ path: redirect || "/" })
                }
            }
        },
        async handleOAuth2Login() {
            const result = await getLoginConfig()
            if (result.code === 200) {
                const { data } = result
                const { enableOAuth2, clientId, clientSecret, authorizeUri, redirectUri, authorizeStateType, accessTokenUri, profileUri } = data
                if (enableOAuth2) {
                    // &state=目标访问url
                    const oauthTimestamp = new Date().getTime()
                    const url = `${authorizeUri}?client_id=${clientId}&response_type=code&redirect_uri=${encodeURIComponent(redirectUri)}&oauth_timestamp=${oauthTimestamp}`
                    this.handleSimulationClickEvent(url)
                    // this.handleParseUrl()
                } else {
                    // 跳转登录
                    // this.$router.push({
                    //     path: '/login',
                    // })
                    this.$router.push({
                        path: '/internal-login',
                    })
                }
            }
        },
        // 退出登录
        async handleLogout() {
            const result = await getLoginConfig()
            if (result.code === 200) {
                const { data } = result
                const { enableOAuth2, logoutUri } = data
                const isLocalhost = window.location.host.includes("localhost")
                if (enableOAuth2 && !isLocalhost) {
                    const redirectUri = `${window.location.origin}${window.location.pathname}#/oauth2-login`
                    const url = `${logoutUri}?service=${encodeURIComponent(redirectUri)}`
                    window.location.href = url
                }
                this.$store.dispatch('LogOut').then(() => {
                    if (isLocalhost) {
                        // this.$router.replace({
                        //     path: "/login"
                        // })
                        this.$router.replace({
                            path: "/internal-login"
                        })
                    } else {
                        this.$router.replace({
                            path: "/oauth2-login"
                        })
                    }
                })
            }
        }
    }
}

