/**
 * @desc springreport has自定义指令
 */
export default {
    inserted: function (el, binding) {
        if (!permissionJudge(binding.value)) {
            el.parentNode.removeChild(el)
        }

        function permissionJudge(value) {
            if (value == 'ignore') {
                return true
            }
            // 此处store.getters.getMenuBtnList代表vuex中储存的按钮菜单数据
            const apis = localStorage.getItem('apiList') || []
            if (!apis || !apis.length) return false
            const list = apis.split(',')
            return list.include(value)
        }
    }
}
