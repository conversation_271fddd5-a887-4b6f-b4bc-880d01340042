{"name": "raisetech", "version": "3.6.4", "description": "常温供应链管理中心运营指挥平台", "author": "RaiseTech", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "postinstall": "patch-package"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Cloud.git"}, "dependencies": {"@logicflow/core": "^1.2.28", "@logicflow/extension": "^1.2.16", "@riophae/vue-treeselect": "0.4.0", "@visactor/vchart": "^1.11.9", "@visactor/vchart-theme": "^1.11.5", "@waves/waves-api": "^1.5.1", "ace-builds": "^1.4.12", "axios": "0.28.1", "clipboard": "2.0.8", "codemirror": "^5.49.2", "core-js": "3.37.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "dingtalk-jsapi": "^2.15.6", "echarts": "5.4.0", "element-ui": "2.15.14", "file-saver": "2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "i": "^0.3.7", "jquery": "^3.4.1", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "json-bigint": "^1.0.0", "jsonlint": "^1.6.3", "mavon-editor": "^2.10.4", "mobx": "^6.13.5", "moment": "^2.30.1", "npm": "^9.6.3", "nprogress": "0.2.0", "quill": "1.3.7", "screenfull": "5.0.2", "script-loader": "^0.7.2", "sortablejs": "1.10.2", "sql-formatter": "^2.3.3", "toastr": "^2.1.4", "vant": "^2.13.6", "vform-builds": "^2.2.9", "vue": "^2.6.14", "vue-codemirror": "^4.0.6", "vue-count-to": "1.0.13", "vue-cron": "^1.0.9", "vue-cropper": "0.5.5", "vue-meta": "2.4.0", "vue-router": "3.4.9", "vuedraggable": "^2.24.3", "vuex": "3.6.0", "waves": "^0.1.1"}, "devDependencies": {"@babel/plugin-transform-react-jsx": "^7.27.1", "@types/codemirror": "^5.60.15", "@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "4.1.0", "compression-webpack-plugin": "6.1.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "filemanager-webpack-plugin": "3.1.1", "lint-staged": "10.5.3", "patch-package": "^8.0.0", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "^2.6.14"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}