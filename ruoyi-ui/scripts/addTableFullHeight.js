const fs = require('fs');
const path = require('path');

// 递归获取所有 .vue 文件
function getAllVueFiles(dir) {
    let results = [];
    const list = fs.readdirSync(dir);
    
    list.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
            results = results.concat(getAllVueFiles(filePath));
        } else if (file.endsWith('.vue')) {
            results.push(filePath);
        }
    });
    
    return results;
}

// 处理单个 .vue 文件
function processVueFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf-8');
    
    // 检查是否包含 fullHeightTableRef
    if (content.includes('ref="fullHeightTableRef"')) {
        console.log(`处理文件: ${filePath}`);
        
        // 检查是否已经导入了 tableFullHeight
        if (!content.includes("import tableFullHeight from '@/utils/tableFullHeight'")) {
            let newContent = content;
            
            // 添加 import 语句
            const importStatement = "import tableFullHeight from '@/utils/tableFullHeight'";
            const scriptStartIndex = content.indexOf('<script');
            if (scriptStartIndex !== -1) {
                const scriptEndIndex = content.indexOf('>', scriptStartIndex);
                if (scriptEndIndex !== -1) {
                    newContent = content.slice(0, scriptEndIndex + 1) + '\n' + importStatement + content.slice(scriptEndIndex + 1);
                }
            }
            
            // 添加 mixins
            if (newContent.includes('export default {')) {
                newContent = newContent.replace(
                    'export default {',
                    'export default {\n  mixins: [tableFullHeight],'
                );
            }
            
            // 写入文件
            fs.writeFileSync(filePath, newContent, 'utf-8');
            console.log(`已更新文件: ${filePath}`);
        } else {
            console.log(`文件已包含 tableFullHeight: ${filePath}`);
        }
    }
}

// 主函数
function main() {
    const viewsDir = path.join(__dirname, '../src/views');
    const vueFiles = getAllVueFiles(viewsDir);
    
    console.log(`找到 ${vueFiles.length} 个 .vue 文件`);
    
    vueFiles.forEach(file => {
        processVueFile(file);
    });
    
    console.log('处理完成！');
}

main(); 