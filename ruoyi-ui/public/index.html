<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
  <title>
    <%= webpackConfig.name %>
  </title>
  <!--[if lt IE 11]><script>window.location.href='/html/ie.html';</script><![endif]-->
  <script type="text/javascript">
    window._AMapSecurityConfig = {
      securityJsCode: "048dc24dae162796a6ae7e34cbbbc2c7",
    }
  </script>
  <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=533feea45cff8d69e6da31a8951a42c0"></script>
  <script type="text/javascript"
    src="https://webapi.amap.com/maps?v=2.0&key=44939c9031f8625b6853dfd10dd8a7a7&plugin=AMap.Geocoder"></script>
  <style>
    html,
    body,
    #app {
      height: 100%;
      margin: 0;
      padding: 0;
      background: #f7f9fa;
    }

    #custom-loader-wrapper {
      width: 100vw;
      height: 100vh;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    .custom-loader {
      width: 48px;
      height: 48px;
      position: relative;
      margin-bottom: 24px;
      animation: rotate 1s linear infinite;
    }

    .custom-loader .dot {
      width: 12px;
      height: 12px;
      background: #6fc08c;
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      margin: -6px 0 0 -6px;
      opacity: 0.8;
    }

    .custom-loader .dot1 {
      transform: rotate(0deg) translate(20px);
    }

    .custom-loader .dot2 {
      transform: rotate(90deg) translate(20px);
    }

    .custom-loader .dot3 {
      transform: rotate(180deg) translate(20px);
    }

    .custom-loader .dot4 {
      transform: rotate(270deg) translate(20px);
    }

    @keyframes rotate {
      100% {
        transform: rotate(360deg);
      }
    }

    .custom-title {
      font-size: 22px;
      color: #222;
      letter-spacing: 2px;
      font-weight: 500;
      text-align: center;
    }
  </style>
</head>

<body>
  <div id="app">
    <div id="custom-loader-wrapper">
      <div class="custom-loader">
        <span class="dot dot1"></span>
        <span class="dot dot2"></span>
        <span class="dot dot3"></span>
        <span class="dot dot4"></span>
      </div>
      <div class="custom-title">ALP OPSYNDEX 平台</div>
    </div>
  </div>
  <script>
    const baseUrl = '<%= process.env.VUE_APP_BASE_API %>'
    fetch(baseUrl + "/system/baseConfig/queryBaseConfig").then(async (res) => {
      const data = await res.json()
      const loaderWrapper = document.getElementById("custom-loader-wrapper")
      if (loaderWrapper) {
        const { loginPageName } = data.data
        const customTitle = document.querySelector(".custom-title")
        customTitle && (customTitle.innerHTML = loginPageName)
      }
    })
  </script>
</body>

</html>